<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API Tuần Dạy</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; }
        button { margin: 5px; padding: 8px 15px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>Test API Thiết đặt Tuần Dạy</h1>
    
    <div class="test-section">
        <h3>1. Test API Lấy Niên <PERSON></h3>
        <button onclick="testLayNienKhoa()">Test /lay-nien-khoa</button>
        <div id="result-nien-khoa" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>2. Test API Lấy Học Kỳ</h3>
        <input type="number" id="idNienKhoa" placeholder="ID Niên Khóa" value="1">
        <button onclick="testLayHocKy()">Test /lay-hoc-ky-theo-nien-khoa</button>
        <div id="result-hoc-ky" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>3. Test API Lấy Thông Tin Tuần Dạy</h3>
        <input type="number" id="idHocKy" placeholder="ID Học Kỳ" value="1">
        <button onclick="testLayThongTinTuanDay()">Test /lay-thong-tin-tuan-day</button>
        <div id="result-thong-tin" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>4. Test API Thiết Đặt Tuần Dạy</h3>
        <input type="number" id="idHocKyThietDat" placeholder="ID Học Kỳ" value="1">
        <input type="date" id="ngayBatDauTuanDay" value="2024-09-02">
        <button onclick="testThietDatTuanDay()">Test /thiet-dat-tuan-day</button>
        <div id="result-thiet-dat" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>5. Test API Tính Tuần Học</h3>
        <input type="number" id="idHocKyTinh" placeholder="ID Học Kỳ" value="1">
        <input type="date" id="ngayCanTinh" value="2024-09-16">
        <button onclick="testTinhTuanHoc()">Test /tinh-tuan-hoc</button>
        <div id="result-tinh-tuan" class="result"></div>
    </div>

    <script>
        function testLayNienKhoa() {
            $('#result-nien-khoa').html('Đang test...');
            
            $.ajax({
                url: '/lich-giang-day/lay-nien-khoa',
                type: 'GET',
                success: function(response) {
                    console.log('Nien Khoa Response:', response);
                    var html = '<div class="success">✓ Success</div>';
                    html += '<strong>Response Code:</strong> ' + response.resCode + '<br>';
                    html += '<strong>Message:</strong> ' + (response.resMsg || 'N/A') + '<br>';
                    html += '<strong>Data Count:</strong> ' + (response.resData ? response.resData.length : 0) + '<br>';
                    if (response.resData && response.resData.length > 0) {
                        html += '<strong>First Item:</strong> ' + JSON.stringify(response.resData[0], null, 2);
                    }
                    $('#result-nien-khoa').html(html);
                },
                error: function(xhr, status, error) {
                    console.error('Error:', xhr, status, error);
                    $('#result-nien-khoa').html('<div class="error">✗ Error: ' + error + '</div>');
                }
            });
        }
        
        function testLayHocKy() {
            var idNienKhoa = $('#idNienKhoa').val();
            $('#result-hoc-ky').html('Đang test...');
            
            $.ajax({
                url: '/lich-giang-day/lay-hoc-ky-theo-nien-khoa',
                type: 'GET',
                data: { idNienKhoa: idNienKhoa },
                success: function(response) {
                    console.log('Hoc Ky Response:', response);
                    var html = '<div class="success">✓ Success</div>';
                    html += '<strong>Response Code:</strong> ' + response.resCode + '<br>';
                    html += '<strong>Message:</strong> ' + (response.resMsg || 'N/A') + '<br>';
                    html += '<strong>Data Count:</strong> ' + (response.resData ? response.resData.length : 0) + '<br>';
                    if (response.resData && response.resData.length > 0) {
                        html += '<strong>First Item:</strong> ' + JSON.stringify(response.resData[0], null, 2);
                    }
                    $('#result-hoc-ky').html(html);
                },
                error: function(xhr, status, error) {
                    console.error('Error:', xhr, status, error);
                    $('#result-hoc-ky').html('<div class="error">✗ Error: ' + error + '</div>');
                }
            });
        }
        
        function testLayThongTinTuanDay() {
            var idHocKy = $('#idHocKy').val();
            $('#result-thong-tin').html('Đang test...');
            
            $.ajax({
                url: '/lich-giang-day/lay-thong-tin-tuan-day',
                type: 'GET',
                data: { idHocKy: idHocKy },
                success: function(response) {
                    console.log('Thong Tin Response:', response);
                    var html = '<div class="success">✓ Success</div>';
                    html += '<strong>Response Code:</strong> ' + response.resCode + '<br>';
                    html += '<strong>Message:</strong> ' + (response.resMsg || 'N/A') + '<br>';
                    html += '<strong>Data:</strong> ' + JSON.stringify(response.resData, null, 2);
                    $('#result-thong-tin').html(html);
                },
                error: function(xhr, status, error) {
                    console.error('Error:', xhr, status, error);
                    $('#result-thong-tin').html('<div class="error">✗ Error: ' + error + '</div>');
                }
            });
        }
        
        function testThietDatTuanDay() {
            var idHocKy = $('#idHocKyThietDat').val();
            var ngayBatDauTuanDay = $('#ngayBatDauTuanDay').val();
            $('#result-thiet-dat').html('Đang test...');
            
            $.ajax({
                url: '/lich-giang-day/thiet-dat-tuan-day',
                type: 'POST',
                data: {
                    idHocKy: idHocKy,
                    ngayBatDauTuanDay: ngayBatDauTuanDay
                },
                success: function(response) {
                    console.log('Thiet Dat Response:', response);
                    var html = '<div class="success">✓ Success</div>';
                    html += '<strong>Response Code:</strong> ' + response.resCode + '<br>';
                    html += '<strong>Message:</strong> ' + (response.resMsg || 'N/A') + '<br>';
                    html += '<strong>Data:</strong> ' + JSON.stringify(response.resData, null, 2);
                    $('#result-thiet-dat').html(html);
                },
                error: function(xhr, status, error) {
                    console.error('Error:', xhr, status, error);
                    $('#result-thiet-dat').html('<div class="error">✗ Error: ' + error + '</div>');
                }
            });
        }
        
        function testTinhTuanHoc() {
            var idHocKy = $('#idHocKyTinh').val();
            var ngayCanTinh = $('#ngayCanTinh').val();
            $('#result-tinh-tuan').html('Đang test...');
            
            $.ajax({
                url: '/lich-giang-day/tinh-tuan-hoc',
                type: 'GET',
                data: {
                    idHocKy: idHocKy,
                    ngayCanTinh: ngayCanTinh
                },
                success: function(response) {
                    console.log('Tinh Tuan Response:', response);
                    var html = '<div class="success">✓ Success</div>';
                    html += '<strong>Response Code:</strong> ' + response.resCode + '<br>';
                    html += '<strong>Message:</strong> ' + (response.resMsg || 'N/A') + '<br>';
                    html += '<strong>Data:</strong> ' + JSON.stringify(response.resData, null, 2);
                    $('#result-tinh-tuan').html(html);
                },
                error: function(xhr, status, error) {
                    console.error('Error:', xhr, status, error);
                    $('#result-tinh-tuan').html('<div class="error">✗ Error: ' + error + '</div>');
                }
            });
        }
    </script>
</body>
</html>
