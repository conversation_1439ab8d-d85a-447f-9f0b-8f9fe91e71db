# Hướng dẫn sử dụng chức năng Thiết đặt tuần dạy

## Tổng quan
Chức năng "Thiết đặt tuần dạy" cho phép quản trị viên cấu hình ngày bắt đầu tuần dạy cho từng học kỳ. Dựa trên thiết đặt này, hệ thống sẽ tự động tính toán và hiển thị tuần học cho các lịch giảng dạy.

## Các tính năng chính

### 1. Thiết đặt tuần dạy cho học kỳ
- Chọn niên khóa và học kỳ cần thiết đặt
- Chọn ngày bắt đầu tuần 1 (thư<PERSON><PERSON> là thứ 2 đầu tiên của học kỳ)
- <PERSON>ệ thống tự động tính ngày kết thúc dựa trên số tuần của học kỳ

### 2. Hi<PERSON><PERSON> thị thông tin tuần dạy
- <PERSON><PERSON> danh sách tất cả các tuần trong học kỳ
- Hiển thị ngày bắt đầu và kết thúc của từng tuần
- Đánh dấu tuần hiện tại, tuần đã qua và tuần sắp tới

### 3. Công cụ tính tuần học
- Nhập ngày bất kỳ để tính tuần học tương ứng
- Hiển thị khoảng thời gian của tuần đó

### 4. Tự động tính tuần trong lịch giảng dạy
- Khi tạo/sửa lịch giảng, hệ thống tự động tính tuần dựa trên ngày giảng
- Hiển thị cột "Tuần" trong danh sách lịch giảng dạy

## Hướng dẫn sử dụng

### Bước 1: Truy cập trang thiết đặt
1. Đăng nhập với quyền quản trị viên
2. Vào menu "Lịch giảng dạy" > "Thiết đặt tuần dạy"
3. URL: `/lich-giang-day/thiet-dat-tuan-day`

### Bước 2: Thiết đặt tuần dạy
1. **Chọn niên khóa**: Chọn niên khóa cần thiết đặt
2. **Chọn học kỳ**: Chọn học kỳ trong niên khóa đã chọn
3. **Chọn ngày bắt đầu tuần 1**: 
   - Chọn ngày bắt đầu của tuần dạy đầu tiên
   - Thường là thứ 2 đầu tiên của học kỳ
   - Ví dụ: 02/09/2024 (Thứ 2)
4. **Lưu thiết đặt**: Nhấn nút "Lưu thiết đặt"

### Bước 3: Xem thông tin tuần dạy
1. Sau khi thiết đặt, nhấn "Xem thông tin tuần dạy"
2. Hệ thống hiển thị bảng với các cột:
   - **Tuần**: Tuần 1, Tuần 2, ...
   - **Ngày bắt đầu**: Ngày bắt đầu tuần (thứ 2)
   - **Ngày kết thúc**: Ngày kết thúc tuần (chủ nhật)
   - **Trạng thái**: Đã qua / Hiện tại / Sắp tới

### Bước 4: Sử dụng công cụ tính tuần
1. Nhấn "Tính tuần hiện tại" để mở công cụ
2. Chọn ngày cần tính tuần
3. Nhấn "Tính tuần" để xem kết quả

## Ví dụ thực tế

### Thiết đặt học kỳ 1 năm 2024-2025
- **Niên khóa**: 2024-2025
- **Học kỳ**: 1
- **Ngày bắt đầu tuần 1**: 02/09/2024 (Thứ 2)
- **Số tuần**: 20 tuần

### Kết quả sau thiết đặt:
- Tuần 1: 02/09/2024 - 08/09/2024
- Tuần 2: 09/09/2024 - 15/09/2024
- ...
- Tuần 20: 06/01/2025 - 12/01/2025

## Lưu ý quan trọng

### 1. Quyền truy cập
- Chỉ quản trị viên mới có quyền thiết đặt tuần dạy
- Giảng viên có thể xem thông tin tuần dạy

### 2. Thời điểm thiết đặt
- Nên thiết đặt trước khi bắt đầu học kỳ
- Có thể thay đổi thiết đặt trong quá trình học kỳ nếu cần

### 3. Ảnh hưởng đến lịch giảng dạy
- Lịch giảng dạy đã tạo sẽ được cập nhật tuần học tự động
- Lịch giảng dạy mới sẽ tự động tính tuần khi tạo

### 4. Xử lý trường hợp đặc biệt
- Nếu ngày giảng nằm ngoài khoảng tuần dạy, hệ thống sẽ hiển thị tuần gần nhất
- Ngày nghỉ lễ không ảnh hưởng đến việc tính tuần

## Khắc phục sự cố

### Không hiển thị tuần trong lịch giảng dạy
- Kiểm tra xem học kỳ đã được thiết đặt tuần dạy chưa
- Đảm bảo ngày giảng nằm trong khoảng thời gian học kỳ

### Tuần hiển thị không chính xác
- Kiểm tra lại ngày bắt đầu tuần 1
- Đảm bảo số tuần của học kỳ được cấu hình đúng

### Lỗi khi lưu thiết đặt
- Kiểm tra quyền truy cập
- Đảm bảo ngày bắt đầu tuần 1 hợp lệ
- Kiểm tra kết nối database

## API endpoints

### Thiết đặt tuần dạy
```
POST /lich-giang-day/thiet-dat-tuan-day
Parameters: idHocKy, ngayBatDauTuanDay
```

### Lấy thông tin tuần dạy
```
GET /lich-giang-day/lay-thong-tin-tuan-day
Parameters: idHocKy
```

### Tính tuần học
```
GET /lich-giang-day/tinh-tuan-hoc
Parameters: idHocKy, ngayCanTinh
```

## Liên hệ hỗ trợ
Nếu gặp vấn đề trong quá trình sử dụng, vui lòng liên hệ bộ phận IT để được hỗ trợ.
