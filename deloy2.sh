#!/bin/bash

# Variables
LOCAL_REPO_DIR="$(pwd)"                     # Set to the current directory containing deploy.sh
JAR_NAME="qldt-1.0.0.jar"                   # The JAR file generated by the build
REMOTE_USER="root"                 # Replace with your SSH username
REMOTE_PASS="j0-BIzx|Fv2"                 # Replace with your SSH password
REMOTE_HOST="************"                # Replace with your server IP or hostname
REMOTE_PATH="/opt/cdytcamau/"               # Replace with the directory on your server where you want to deploy the JAR
REMOTE_SERVICE_NAME="cdytcamau"     # Replace with your service name if using a systemd service
REMOTE_JAR_NAME="qldt.jar"                  # The name you want the JAR file to have on the server

# Navigate to the local repository directory (the current directory)
cd $LOCAL_REPO_DIR

# Pull the latest changes from git
echo "Pulling the latest changes from git..."
git pull

# Build the project using Maven Wrapper
echo "Building the project..."
./mvnw clean package

# Check if the build was successful
if [ $? -ne 0 ]; then
  echo "Build failed. Exiting..."
  exit 1
fi

# Rename the JAR file locally before copying to the server
echo "Renaming the JAR file..."
mv target/$JAR_NAME target/$REMOTE_JAR_NAME

# Copy the JAR file to the server
pscp -pw $REMOTE_PASS target/$REMOTE_JAR_NAME $REMOTE_USER@$REMOTE_HOST:$REMOTE_PATH

# SSH into the server and execute commands
plink -pw $REMOTE_PASS $REMOTE_USER@$REMOTE_HOST << EOF

# Navigate to the deployment directory
cd $REMOTE_PATH

# Change file permissions so only the owner has read and execute permissions
echo "Setting file permissions..."
chmod 500 $REMOTE_JAR_NAME

# Restart the service using your preferred command
echo "Restarting the service..."
sudo service $REMOTE_SERVICE_NAME restart

EOF

echo "Deployment complete!"