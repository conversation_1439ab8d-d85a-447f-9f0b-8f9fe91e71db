# 🔧 Khắc phục sự cố: Thiết đặt tuần dạy

## 🚨 Vấn đề hiện tại
- **Lỗi**: "Có lỗi xảy ra" khi lưu thiết lập
- **Giao diện**: Cần cải thiện layout với box design

## ✅ Đã thực hiện
1. **Cải thiện giao diện**: Chuyển từ card sang box layout
2. **Sửa lỗi JavaScript**: Cập nhật cấu trúc Response (resCode, resData, resMsg)
3. **Thêm debug logging**: Console.log để theo dõi lỗi
4. **Tạo file test**: debug_thiet_dat_tuan_day.html và test_api_tuan_day.html

## 🔍 Các bước debug

### Bước 1: Kiểm tra Database Schema
```sql
-- Chạy file check_database_schema.sql để kiểm tra
-- Hoặc chạy lệnh sau:
DESCRIBE HOC_KY;
```

**C<PERSON><PERSON> có các cột:**
- `NGAY_BAT_DAU_TUAN_DAY` (DATE)
- `NGAY_KET_THUC_TUAN_DAY` (DATE)

**Nếu chưa có, chạy:**
```sql
-- Chạy file database_update_tuan_day.sql
```

### Bước 2: Kiểm tra API Endpoints
Mở file `debug_thiet_dat_tuan_day.html` trong browser và test từng API:

1. **Test API Niên Khóa**: `/lich-giang-day/lay-nien-khoa`
2. **Test API Học Kỳ**: `/lich-giang-day/lay-hoc-ky-theo-nien-khoa`
3. **Test API Thiết Đặt**: `/lich-giang-day/thiet-dat-tuan-day`

### Bước 3: Kiểm tra Console Logs
1. Mở Developer Tools (F12)
2. Vào tab Console
3. Truy cập trang thiết đặt tuần dạy
4. Xem logs để tìm lỗi cụ thể

### Bước 4: Kiểm tra Network Tab
1. Mở Developer Tools > Network tab
2. Thực hiện thao tác lưu thiết đặt
3. Xem request/response để tìm lỗi

## 🛠️ Các lỗi thường gặp và cách khắc phục

### Lỗi 1: Database Schema
**Triệu chứng**: Lỗi SQL khi lưu
**Khắc phục**:
```sql
-- Chạy script cập nhật database
SOURCE database_update_tuan_day.sql;
```

### Lỗi 2: API Response Structure
**Triệu chứng**: JavaScript không nhận được dữ liệu
**Khắc phục**: Đã sửa trong code, sử dụng `resCode`, `resData`, `resMsg`

### Lỗi 3: Missing Dependencies
**Triệu chứng**: Select2 hoặc DatePicker không hoạt động
**Khắc phục**: Kiểm tra file template có load đúng thư viện không

### Lỗi 4: CSRF Token
**Triệu chứng**: 403 Forbidden khi POST
**Khắc phục**: Thêm CSRF token vào form (nếu cần)

### Lỗi 5: Date Format
**Triệu chứng**: Lỗi parse date
**Khắc phục**: Đã sửa, chuyển từ dd/mm/yyyy sang yyyy-mm-dd

## 📋 Checklist Debug

### ✅ Database
- [ ] Bảng HOC_KY có cột NGAY_BAT_DAU_TUAN_DAY
- [ ] Bảng HOC_KY có cột NGAY_KET_THUC_TUAN_DAY
- [ ] Có dữ liệu niên khóa và học kỳ

### ✅ Backend
- [ ] API `/lay-nien-khoa` hoạt động
- [ ] API `/lay-hoc-ky-theo-nien-khoa` hoạt động  
- [ ] API `/thiet-dat-tuan-day` hoạt động
- [ ] Method `luuHocKy` trong service hoạt động

### ✅ Frontend
- [ ] jQuery loaded
- [ ] Select2 loaded
- [ ] DatePicker loaded
- [ ] JavaScript không có lỗi syntax
- [ ] AJAX calls sử dụng đúng cấu trúc Response

### ✅ Network
- [ ] Request được gửi đến đúng endpoint
- [ ] Response trả về đúng format
- [ ] Không có lỗi 404, 500, 403

## 🔧 Công cụ debug

### 1. File debug_thiet_dat_tuan_day.html
- Test từng API riêng lẻ
- Kiểm tra components
- Xem console logs

### 2. File test_api_tuan_day.html  
- Test API với UI đơn giản
- Xem raw response

### 3. File check_database_schema.sql
- Kiểm tra cấu trúc database
- Xem dữ liệu mẫu

## 📞 Các bước tiếp theo

### Nếu vẫn lỗi:
1. **Chạy debug file**: Mở `debug_thiet_dat_tuan_day.html`
2. **Kiểm tra từng bước**: Theo checklist trên
3. **Xem logs**: Console và Network tab
4. **Kiểm tra database**: Chạy `check_database_schema.sql`

### Thông tin cần thu thập:
- Console error logs
- Network request/response
- Database schema status
- Browser và version

## 🎯 Kết quả mong đợi

Sau khi khắc phục:
- ✅ Giao diện hiển thị đúng với box layout
- ✅ Dropdown niên khóa và học kỳ load được dữ liệu
- ✅ DatePicker hoạt động bình thường
- ✅ Lưu thiết đặt thành công
- ✅ Hiển thị thông tin tuần dạy
- ✅ Công cụ tính tuần hoạt động

## 📝 Ghi chú
- Backup database trước khi chạy script cập nhật
- Test trên môi trường development trước
- Kiểm tra quyền truy cập database
- Đảm bảo server restart sau khi deploy code mới
