package vn.vnpt.camau.qldt.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

/**
 * LichGiangDay - Teaching schedule entity for manual scheduling
 */
@Entity
@Table(name = "LICH_GIANG_DAY")
@JsonIgnoreProperties(ignoreUnknown = true)
public class LichGiangDay implements java.io.Serializable {

    private Integer idLichGiang;
    private BaiHoc baiHoc;
    private MonHoc monHoc;
    private Lop lop;
    private Nhom nhom;
    private CanBo giangVien;
    private HocKy hocKy;
    private String hinhThuc; // LT: <PERSON><PERSON> thuyết, TH: Thực hành
    private Integer soTiet;
    private BigDecimal heSo;
    private Integer thu; // 2-8 tương ứng thứ 2 đến chủ nhật
    private String buoi; // SANG, CHIEU, TOI
    private Integer tietBatDau; // Tiết bắt đầu (1-12)
    private Integer tietKetThuc; // Tiết kết thúc (1-12)
    private PhongHoc phongHoc; // ID phòng học - chỉ lưu ID_PHONG
    private Date ngayGiang; // Ngày giảng cụ thể
    private Integer tuanGiang; // Tuần giảng trong học kỳ
    private Integer trangThai; // 0: Chưa dạy, 1: Đã dạy, 2: Hủy, 3: Hoãn
    private String ghiChu;

    // Thêm các trường theo dõi tiến độ
    private String noiDungDay; // Nội dung đã dạy
    private Integer soSvVang; // Số sinh viên vắng
    private CanBo nguoiTao;
    private Date ngayTao;
    private CanBo nguoiCapNhat;
    private Date ngayCapNhat;


    public LichGiangDay() {
    }

    public LichGiangDay(BaiHoc baiHoc, MonHoc monHoc, Lop lop, CanBo giangVien, HocKy hocKy, 
                       String hinhThuc, Integer soTiet, Integer thu, String buoi) {
        this.baiHoc = baiHoc;
        this.monHoc = monHoc;
        this.lop = lop;
        this.giangVien = giangVien;
        this.hocKy = hocKy;
        this.hinhThuc = hinhThuc;
        this.soTiet = soTiet;
        this.thu = thu;
        this.buoi = buoi;
        this.heSo = new BigDecimal("1.00");
        this.trangThai = 0;
        this.ngayTao = new Date();
    }

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "ID_LICH_GIANG", unique = true, nullable = false)
    public Integer getIdLichGiang() {
        return this.idLichGiang;
    }

    public void setIdLichGiang(Integer idLichGiang) {
        this.idLichGiang = idLichGiang;
    }

    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ID_BAI_HOC", nullable = false)
    public BaiHoc getBaiHoc() {
        return this.baiHoc;
    }

    public void setBaiHoc(BaiHoc baiHoc) {
        this.baiHoc = baiHoc;
    }

    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ID_MON_HOC", nullable = false)
    public MonHoc getMonHoc() {
        return this.monHoc;
    }

    public void setMonHoc(MonHoc monHoc) {
        this.monHoc = monHoc;
    }

    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ID_LOP", nullable = false)
    public Lop getLop() {
        return this.lop;
    }

    public void setLop(Lop lop) {
        this.lop = lop;
    }

    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ID_NHOM")
    public Nhom getNhom() {
        return this.nhom;
    }

    public void setNhom(Nhom nhom) {
        this.nhom = nhom;
    }

    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ID_GIANG_VIEN", nullable = false)
    public CanBo getGiangVien() {
        return this.giangVien;
    }

    public void setGiangVien(CanBo giangVien) {
        this.giangVien = giangVien;
    }

    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ID_HOC_KY", nullable = false)
    public HocKy getHocKy() {
        return this.hocKy;
    }

    public void setHocKy(HocKy hocKy) {
        this.hocKy = hocKy;
    }

    @Column(name = "HINH_THUC", nullable = false, length = 2)
    public String getHinhThuc() {
        return this.hinhThuc;
    }

    public void setHinhThuc(String hinhThuc) {
        this.hinhThuc = hinhThuc;
    }

    @Column(name = "SO_TIET", nullable = false)
    public Integer getSoTiet() {
        return this.soTiet;
    }

    public void setSoTiet(Integer soTiet) {
        this.soTiet = soTiet;
    }

    @Column(name = "HE_SO", nullable = false, precision = 4, scale = 2)
    public BigDecimal getHeSo() {
        return this.heSo;
    }

    public void setHeSo(BigDecimal heSo) {
        this.heSo = heSo;
    }

    @Column(name = "THU", nullable = false)
    public Integer getThu() {
        return this.thu;
    }

    public void setThu(Integer thu) {
        this.thu = thu;
    }

    @Column(name = "BUOI", nullable = false, length = 10)
    public String getBuoi() {
        return this.buoi;
    }

    public void setBuoi(String buoi) {
        this.buoi = buoi;
    }

    @Column(name = "TIET_BAT_DAU")
    public Integer getTietBatDau() {
        return this.tietBatDau;
    }

    public void setTietBatDau(Integer tietBatDau) {
        this.tietBatDau = tietBatDau;
    }

    @Column(name = "TIET_KET_THUC")
    public Integer getTietKetThuc() {
        return this.tietKetThuc;
    }

    public void setTietKetThuc(Integer tietKetThuc) {
        this.tietKetThuc = tietKetThuc;
    }



    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ID_PHONG", nullable = false)
    public PhongHoc getPhongHoc() {
        return this.phongHoc;
    }

    public void setPhongHoc(PhongHoc phongHoc) {
        this.phongHoc = phongHoc;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "NGAY_GIANG", length = 10)
    public Date getNgayGiang() {
        return this.ngayGiang;
    }

    public void setNgayGiang(Date ngayGiang) {
        this.ngayGiang = ngayGiang;
    }

    @Column(name = "TUAN_GIANG")
    public Integer getTuanGiang() {
        return this.tuanGiang;
    }

    public void setTuanGiang(Integer tuanGiang) {
        this.tuanGiang = tuanGiang;
    }

    @Column(name = "TRANG_THAI", nullable = false, columnDefinition = "TINYINT(1) DEFAULT 0")
    public Integer getTrangThai() {
        return this.trangThai;
    }

    public void setTrangThai(Integer trangThai) {
        this.trangThai = trangThai;
    }

    @Column(name = "GHI_CHU", columnDefinition = "TEXT")
    public String getGhiChu() {
        return this.ghiChu;
    }

    public void setGhiChu(String ghiChu) {
        this.ghiChu = ghiChu;
    }

    @Column(name = "NOI_DUNG_DAY", length = 1000)
    public String getNoiDungDay() {
        return this.noiDungDay;
    }

    public void setNoiDungDay(String noiDungDay) {
        this.noiDungDay = noiDungDay;
    }

    @Column(name = "SO_SV_VANG")
    public Integer getSoSvVang() {
        return this.soSvVang;
    }

    public void setSoSvVang(Integer soSvVang) {
        this.soSvVang = soSvVang;
    }



    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "NGUOI_TAO")
    public CanBo getNguoiTao() {
        return this.nguoiTao;
    }

    public void setNguoiTao(CanBo nguoiTao) {
        this.nguoiTao = nguoiTao;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "NGAY_TAO", nullable = false, insertable = false, updatable = false,
            columnDefinition = "DATETIME DEFAULT CURRENT_TIMESTAMP")
    public Date getNgayTao() {
        return this.ngayTao;
    }

    public void setNgayTao(Date ngayTao) {
        this.ngayTao = ngayTao;
    }

    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "NGUOI_CAP_NHAT")
    public CanBo getNguoiCapNhat() {
        return this.nguoiCapNhat;
    }

    public void setNguoiCapNhat(CanBo nguoiCapNhat) {
        this.nguoiCapNhat = nguoiCapNhat;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "NGAY_CAP_NHAT", insertable = false, updatable = false,
            columnDefinition = "DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP")
    public Date getNgayCapNhat() {
        return this.ngayCapNhat;
    }

    public void setNgayCapNhat(Date ngayCapNhat) {
        this.ngayCapNhat = ngayCapNhat;
    }



    // Transient methods for JSON serialization
    @Transient
    public String getTenBaiHoc() {
        return this.baiHoc == null ? "" : this.baiHoc.getTenBaiHoc();
    }

    @Transient
    public String getMaBaiHoc() {
        return this.baiHoc == null ? "" : this.baiHoc.getMaBaiHoc();
    }

    @Transient
    public String getTenMonHoc() {
        return this.monHoc == null ? "" : this.monHoc.getTenMonHoc();
    }

    @Transient
    public String getMaMonHoc() {
        return this.monHoc == null ? "" : this.monHoc.getMaMonHoc();
    }

    @Transient
    public String getTenLop() {
        return this.lop == null ? "" : this.lop.getTenLop();
    }

    @Transient
    public String getMaLop() {
        return this.lop == null ? "" : this.lop.getMaLop();
    }

    @Transient
    public String getTenNhom() {
        return this.nhom == null ? "" : this.nhom.getTenNhom();
    }

    @Transient
    public String getTenGiangVien() {
        return this.giangVien == null ? "" : this.giangVien.getHoTen();
    }

    @Transient
    public String getMaGiangVien() {
        return this.giangVien == null ? "" : this.giangVien.getMaCanBo();
    }

    @Transient
    public String getTenHocKy() {
        return this.hocKy == null ? "" : this.hocKy.getTenHocKy();
    }

    @Transient
    public String getTenPhongHoc() {
        return this.phongHoc == null ? "" : this.phongHoc.getTenPhong();
    }

    @Transient
    public String getPhong() {
        return this.phongHoc == null ? "" : this.phongHoc.getTenPhong();
    }

    @Transient
    public String getCoSo() {
        return this.phongHoc == null ? "" : this.phongHoc.getCoSo();
    }

    @Transient
    public String getKhu() {
        return this.phongHoc == null ? "" : this.phongHoc.getKhu();
    }

    @Transient
    public String getHinhThucText() {
        if ("LT".equals(this.hinhThuc)) {
            return "Lý thuyết";
        } else if ("TH".equals(this.hinhThuc)) {
            return "Thực hành";
        }
        return this.hinhThuc;
    }

    @Transient
    public String getThuText() {
        if (this.thu == null) return "";
        switch (this.thu) {
            case 1: return "Chủ nhật";
            case 2: return "Thứ 2";
            case 3: return "Thứ 3";
            case 4: return "Thứ 4";
            case 5: return "Thứ 5";
            case 6: return "Thứ 6";
            case 7: return "Thứ 7";
            default: return "Thứ " + this.thu;
        }
    }

    @Transient
    public String getBuoiText() {
        if ("SANG".equals(this.buoi)) {
            return "Sáng";
        } else if ("CHIEU".equals(this.buoi)) {
            return "Chiều";
        } else if ("TOI".equals(this.buoi)) {
            return "Tối";
        }
        return this.buoi;
    }

    @Transient
    public String getTrangThaiText() {
        switch (this.trangThai) {
            case 0: return "Chưa dạy";
            case 1: return "Đã dạy";
            case 2: return "Hủy";
            case 3: return "Hoãn";
            default: return "Không xác định";
        }
    }

    @Transient
    public String getTietHoc() {
        if (this.tietBatDau != null && this.tietKetThuc != null) {
            return "Tiết " + this.tietBatDau + " - " + this.tietKetThuc;
        }
        return "";
    }

    @Transient
    public String getTietText() {
        if (this.tietBatDau != null && this.tietKetThuc != null) {
            return "Tiết " + this.tietBatDau + " - " + this.tietKetThuc;
        }
        return "";
    }

    @Transient
    public String getTenPhong() {
        return this.phongHoc == null ? "" : this.phongHoc.getTenPhong();
    }

    @Transient
    public String getNgayGiangText() {
        if (this.ngayGiang == null) return "";

        java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("dd/MM/yyyy");
        return sdf.format(this.ngayGiang);
    }

    @Transient
    public String getCoSoText() {
        if (this.phongHoc == null) return "";
        String coSo = this.phongHoc.getCoSo();
        if ("CS1".equals(coSo)) {
            return "Cơ sở 1";
        } else if ("CS2".equals(coSo)) {
            return "Cơ sở 2";
        }
        return coSo;
    }

    // ID getters for editing purposes
    @Transient
    public Integer getIdBaiHoc() {
        return this.baiHoc == null ? null : this.baiHoc.getIdBaiHoc();
    }

    @Transient
    public Integer getIdMonHoc() {
        return this.monHoc == null ? null : this.monHoc.getIdMonHoc();
    }

    @Transient
    public Integer getIdLop() {
        return this.lop == null ? null : this.lop.getIdLop();
    }

    @Transient
    public Integer getIdNhom() {
        return this.nhom == null ? null : this.nhom.getIdNhom();
    }

    @Transient
    public Integer getIdGiangVien() {
        return this.giangVien == null ? null : this.giangVien.getIdCanBo();
    }

    @Transient
    public Integer getIdHocKy() {
        return this.hocKy == null ? null : this.hocKy.getIdHocKy();
    }

    @Transient
    public Integer getIdPhong() {
        return this.phongHoc == null ? null : this.phongHoc.getIdPhong();
    }

    @Transient
    public String getTuanGiangText() {
        if (this.tuanGiang == null) {
            return "";
        }
        return "T" + this.tuanGiang;
    }

    @Override
    public String toString() {
        return "LichGiangDay{" +
                "idLichGiang=" + idLichGiang +
                ", hinhThuc='" + hinhThuc + '\'' +
                ", soTiet=" + soTiet +
                ", thu=" + thu +
                ", buoi='" + buoi + '\'' +
                ", ngayGiang=" + ngayGiang +
                ", trangThai=" + trangThai +
                '}';
    }
}
