package vn.vnpt.camau.qldt.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import vn.vnpt.camau.qldt.Utility;
import vn.vnpt.camau.qldt.model.CanBo;
import vn.vnpt.camau.qldt.model.LichGiangDay;
import vn.vnpt.camau.qldt.model.NguoiDung;
import vn.vnpt.camau.qldt.model.PhongHoc;
import vn.vnpt.camau.qldt.repository.LichGiangDayRepository;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Transactional
public class LichGiangDayServiceImp implements LichGiangDayService {

    @Autowired
    private LichGiangDayRepository lichGiangDayRepo;

    @Autowired
    private PhongHocService phongHocSer;

    @Override
    public LichGiangDay luu(LichGiangDay lichGiangDay) {
        // Set update information
        if (lichGiangDay.getIdLichGiang() != null) {
            lichGiangDay.setNgayCapNhat(new Date());
            
            NguoiDung nguoiDung = Utility.layNguoiDungHienTai();
            if (nguoiDung != null && nguoiDung instanceof CanBo) {
                lichGiangDay.setNguoiCapNhat((CanBo) nguoiDung);
            }
        } else {
            lichGiangDay.setNgayTao(new Date());
            
            NguoiDung nguoiDung = Utility.layNguoiDungHienTai();
            if (nguoiDung != null && nguoiDung instanceof CanBo) {
                lichGiangDay.setNguoiTao((CanBo) nguoiDung);
            }
        }
        
        // Set day of week based on date
        if (lichGiangDay.getNgayGiang() != null) {
            Calendar cal = Calendar.getInstance();
            cal.setTime(lichGiangDay.getNgayGiang());
            int dayOfWeek = cal.get(Calendar.DAY_OF_WEEK);
            lichGiangDay.setThu(dayOfWeek);

            // Tự động tính tuần giảng dựa trên thiết đặt tuần dạy của học kỳ
            if (lichGiangDay.getHocKy() != null && lichGiangDay.getTuanGiang() == null) {
                Integer tuanGiang = lichGiangDay.getHocKy().tinhTuanHoc(lichGiangDay.getNgayGiang());
                lichGiangDay.setTuanGiang(tuanGiang);
            }
        }
        
        // Auto assign classroom if not set
        if (lichGiangDay.getPhongHoc() == null && lichGiangDay.getHinhThuc() != null) {
            PhongHoc phongGanTuDong = tuDongPhanPhongHoc(lichGiangDay);
            if (phongGanTuDong != null) {
                lichGiangDay.setPhongHoc(phongGanTuDong);
            }
        }
        
        return lichGiangDayRepo.save(lichGiangDay);
    }

    @Override
    public LichGiangDay layTheoId(Integer idLichGiang) {
        // Use the method with EntityGraph to load all relationships
        LichGiangDay result = lichGiangDayRepo.findByIdWithDetails(idLichGiang);
        if (result != null) {
            return result;
        }
        // Fallback to regular findById if the above doesn't work
        return lichGiangDayRepo.findById(idLichGiang).orElse(null);
    }

    @Override
    public Page<LichGiangDay> timKiem(Integer idHocKy, Integer idGiangVien, Integer idMonHoc,
                                     Integer idLop, Integer idPhong, String hinhThuc,
                                     Integer thu, String buoi, String coSo, Integer trangThai,
                                     Date tuNgay, Date denNgay, String keyword, Pageable pageable) {
        return lichGiangDayRepo.timKiem(idHocKy, idGiangVien, idMonHoc, idLop, idPhong,
                                       hinhThuc, thu, buoi, coSo, trangThai,
                                       tuNgay, denNgay, keyword, pageable);
    }

    @Override
    public List<LichGiangDay> layTheoGiangVienVaKhoangThoiGian(Integer idGiangVien, Date tuNgay, Date denNgay) {
        return lichGiangDayRepo.findByGiangVienAndDateRange(idGiangVien, tuNgay, denNgay);
    }

    @Override
    public List<LichGiangDay> layTheoPhongVaKhoangThoiGian(Integer idPhong, String phong, Date tuNgay, Date denNgay) {
        return lichGiangDayRepo.findByPhongAndDateRange(idPhong, phong, tuNgay, denNgay);
    }

    @Override
    public List<LichGiangDay> layTheoLopVaKhoangThoiGian(Integer idLop, Date tuNgay, Date denNgay) {
        return lichGiangDayRepo.findByLopAndDateRange(idLop, tuNgay, denNgay);
    }

    @Override
    public boolean kiemTraTrungPhong(Integer idPhong, String phong, Date ngayGiang, Integer thu,
                                    String buoi, Integer tietBatDau, Integer tietKetThuc, Integer idLichGiang) {
        return lichGiangDayRepo.existsConflictPhong(idPhong, phong, ngayGiang, thu, buoi,
                                                   tietBatDau, tietKetThuc, idLichGiang);
    }

    @Override
    public boolean kiemTraTrungGiangVien(Integer idGiangVien, Date ngayGiang, Integer thu,
                                        String buoi, Integer tietBatDau, Integer tietKetThuc, Integer idLichGiang) {
        return lichGiangDayRepo.existsConflictGiangVien(idGiangVien, ngayGiang, thu, buoi,
                                                       tietBatDau, tietKetThuc, idLichGiang);
    }

    @Override
    public List<LichGiangDay> layTheoHocKy(Integer idHocKy) {
        return lichGiangDayRepo.findByHocKy(idHocKy);
    }

    @Override
    public List<LichGiangDay> layTheoNgayGiang(Date ngayGiang) {
        return lichGiangDayRepo.findByNgayGiang(ngayGiang);
    }

    @Override
    public List<LichGiangDay> layTheoTuanGiangVaHocKy(Integer tuanGiang, Integer idHocKy) {
        return lichGiangDayRepo.findByTuanGiangAndHocKy(tuanGiang, idHocKy);
    }

    @Override
    public Long demTheoHocKy(Integer idHocKy) {
        return lichGiangDayRepo.countByHocKy(idHocKy);
    }

    @Override
    public Long demTheoGiangVienVaHocKy(Integer idGiangVien, Integer idHocKy) {
        return lichGiangDayRepo.countByGiangVienAndHocKy(idGiangVien, idHocKy);
    }

    @Override
    public Double tinhTongGioGiang(Integer idGiangVien, Integer idHocKy) {
        return lichGiangDayRepo.calculateTotalTeachingHours(idGiangVien, idHocKy);
    }

    @Override
    public List<LichGiangDay> layTheoBaiHoc(Integer idBaiHoc) {
        return lichGiangDayRepo.findByBaiHoc(idBaiHoc);
    }

    @Override
    public void xoa(Integer idLichGiang) {
        lichGiangDayRepo.deleteById(idLichGiang);
    }

    @Override
    public String kiemTraHopLe(LichGiangDay lichGiangDay) {
        if (lichGiangDay.getBaiHoc() == null) {
            return "Vui lòng chọn bài học";
        }
        
        if (lichGiangDay.getMonHoc() == null) {
            return "Vui lòng chọn môn học";
        }
        
        if (lichGiangDay.getLop() == null) {
            return "Vui lòng chọn lớp";
        }
        
        if (lichGiangDay.getGiangVien() == null) {
            return "Vui lòng chọn giảng viên";
        }
        
        if (lichGiangDay.getHocKy() == null) {
            return "Vui lòng chọn học kỳ";
        }
        
        if (lichGiangDay.getHinhThuc() == null || lichGiangDay.getHinhThuc().trim().isEmpty()) {
            return "Vui lòng chọn hình thức";
        }
        
        if (lichGiangDay.getThu() == null || lichGiangDay.getThu() < 1 || lichGiangDay.getThu() > 7) {
            return "Vui lòng chọn thứ";
        }
        
        if (lichGiangDay.getBuoi() == null || lichGiangDay.getBuoi().trim().isEmpty()) {
            return "Vui lòng chọn buổi";
        }
        
        if (lichGiangDay.getTietBatDau() == null || lichGiangDay.getTietKetThuc() == null) {
            return "Vui lòng chọn tiết bắt đầu và kết thúc";
        }
        
        if (lichGiangDay.getTietBatDau() > lichGiangDay.getTietKetThuc()) {
            return "Tiết bắt đầu phải nhỏ hơn hoặc bằng tiết kết thúc";
        }
        
        if (lichGiangDay.getCoSo() == null || lichGiangDay.getCoSo().trim().isEmpty()) {
            return "Vui lòng chọn cơ sở";
        }
        
        if (lichGiangDay.getKhu() == null || lichGiangDay.getKhu().trim().isEmpty()) {
            return "Vui lòng chọn khu";
        }
        
        if (lichGiangDay.getPhong() == null || lichGiangDay.getPhong().trim().isEmpty()) {
            return "Vui lòng chọn phòng học";
        }
        
        // Check for classroom conflicts
        if (kiemTraTrungPhong(lichGiangDay.getPhongHoc() != null ? lichGiangDay.getPhongHoc().getIdPhong() : null,
                             lichGiangDay.getPhong(),
                             lichGiangDay.getNgayGiang(),
                             lichGiangDay.getThu(),
                             lichGiangDay.getBuoi(),
                             lichGiangDay.getTietBatDau(),
                             lichGiangDay.getTietKetThuc(),
                             lichGiangDay.getIdLichGiang())) {
            return "Phòng học đã được sử dụng vào thời gian này";
        }
        
        // Check for teacher conflicts
        if (kiemTraTrungGiangVien(lichGiangDay.getGiangVien().getIdCanBo(),
                                 lichGiangDay.getNgayGiang(),
                                 lichGiangDay.getThu(),
                                 lichGiangDay.getBuoi(),
                                 lichGiangDay.getTietBatDau(),
                                 lichGiangDay.getTietKetThuc(),
                                 lichGiangDay.getIdLichGiang())) {
            return "Giảng viên đã có lịch dạy vào thời gian này";
        }
        
        return null; // No errors
    }

    @Override
    public List<LichGiangDay> saoChepLichTuan(Integer tuanGoc, Integer tuanMoi, Integer idHocKy) {
        List<LichGiangDay> lichGocList = layTheoTuanGiangVaHocKy(tuanGoc, idHocKy);
        List<LichGiangDay> lichMoiList = new ArrayList<>();
        
        for (LichGiangDay lichGoc : lichGocList) {
            LichGiangDay lichMoi = new LichGiangDay();
            
            // Copy properties
            lichMoi.setBaiHoc(lichGoc.getBaiHoc());
            lichMoi.setMonHoc(lichGoc.getMonHoc());
            lichMoi.setLop(lichGoc.getLop());
            lichMoi.setNhom(lichGoc.getNhom());
            lichMoi.setGiangVien(lichGoc.getGiangVien());
            lichMoi.setHocKy(lichGoc.getHocKy());
            lichMoi.setHinhThuc(lichGoc.getHinhThuc());
            lichMoi.setSoTiet(lichGoc.getSoTiet());
            lichMoi.setHeSo(lichGoc.getHeSo());
            lichMoi.setThu(lichGoc.getThu());
            lichMoi.setBuoi(lichGoc.getBuoi());
            lichMoi.setTietBatDau(lichGoc.getTietBatDau());
            lichMoi.setTietKetThuc(lichGoc.getTietKetThuc());
            // Chỉ copy PhongHoc (ID_PHONG), không copy coSo, khu, phong nữa
            lichMoi.setPhongHoc(lichGoc.getPhongHoc());
            lichMoi.setTuanGiang(tuanMoi);
            lichMoi.setTrangThai(0); // Reset status
            lichMoi.setGhiChu(lichGoc.getGhiChu());
            
            // Save new schedule
            String error = kiemTraHopLe(lichMoi);
            if (error == null) {
                lichMoi = luu(lichMoi);
                lichMoiList.add(lichMoi);
            }
        }
        
        return lichMoiList;
    }

    @Override
    public List<LichGiangDay> taoLichNhieuTuan(LichGiangDay lichMau, List<Date> danhSachNgay) {
        List<LichGiangDay> lichList = new ArrayList<>();
        
        for (Date ngay : danhSachNgay) {
            LichGiangDay lichMoi = new LichGiangDay();
            
            // Copy properties from template
            lichMoi.setBaiHoc(lichMau.getBaiHoc());
            lichMoi.setMonHoc(lichMau.getMonHoc());
            lichMoi.setLop(lichMau.getLop());
            lichMoi.setNhom(lichMau.getNhom());
            lichMoi.setGiangVien(lichMau.getGiangVien());
            lichMoi.setHocKy(lichMau.getHocKy());
            lichMoi.setHinhThuc(lichMau.getHinhThuc());
            lichMoi.setSoTiet(lichMau.getSoTiet());
            lichMoi.setHeSo(lichMau.getHeSo());
            lichMoi.setThu(lichMau.getThu());
            lichMoi.setBuoi(lichMau.getBuoi());
            lichMoi.setTietBatDau(lichMau.getTietBatDau());
            lichMoi.setTietKetThuc(lichMau.getTietKetThuc());
            // Chỉ copy PhongHoc (ID_PHONG), không copy coSo, khu, phong nữa
            lichMoi.setPhongHoc(lichMau.getPhongHoc());
            lichMoi.setNgayGiang(ngay);
            lichMoi.setTrangThai(0);
            lichMoi.setGhiChu(lichMau.getGhiChu());
            
            // Validate before saving
            String error = kiemTraHopLe(lichMoi);
            if (error == null) {
                lichMoi = luu(lichMoi);
                lichList.add(lichMoi);
            }
        }
        
        return lichList;
    }

    @Override
    public LichGiangDay capNhatTrangThai(Integer idLichGiang, Integer trangThai) {
        LichGiangDay lichGiang = layTheoId(idLichGiang);
        if (lichGiang != null) {
            lichGiang.setTrangThai(trangThai);
            return luu(lichGiang);
        }
        return null;
    }

    @Override
    public List<LichGiangDay> layTheoCoSoVaKhoangThoiGian(String coSo, Date tuNgay, Date denNgay) {
        return timKiem(null, null, null, null, null, null, null, null, coSo, null,
                      tuNgay, denNgay, null, Pageable.unpaged()).getContent();
    }

    @Override
    public List<String> layBaoCaoXungDot(Date tuNgay, Date denNgay) {
        List<String> conflicts = new ArrayList<>();

        try {
            // Get all schedules in the date range
            List<LichGiangDay> schedules = lichGiangDayRepo.findByDateRange(tuNgay, denNgay);

            // Group schedules by date, session, and period for conflict detection
            Map<String, List<LichGiangDay>> groupedSchedules = new HashMap<>();

            for (LichGiangDay schedule : schedules) {
                if (schedule.getNgayGiang() != null && schedule.getTrangThai() != 2 && schedule.getTrangThai() != 3) {
                    String key = schedule.getNgayGiang().toString() + "_" +
                                schedule.getBuoi() + "_" +
                                schedule.getTietBatDau() + "_" +
                                schedule.getTietKetThuc();

                    groupedSchedules.computeIfAbsent(key, k -> new ArrayList<>()).add(schedule);
                }
            }

            // Check for conflicts
            for (Map.Entry<String, List<LichGiangDay>> entry : groupedSchedules.entrySet()) {
                List<LichGiangDay> samePeriodSchedules = entry.getValue();

                if (samePeriodSchedules.size() > 1) {
                    // Check for classroom conflicts
                    Map<String, List<LichGiangDay>> roomConflicts = new HashMap<>();
                    Map<Integer, List<LichGiangDay>> teacherConflicts = new HashMap<>();

                    for (LichGiangDay schedule : samePeriodSchedules) {
                        // Group by classroom
                        String roomKey = schedule.getPhong() != null ? schedule.getPhong() : "UNKNOWN";
                        roomConflicts.computeIfAbsent(roomKey, k -> new ArrayList<>()).add(schedule);

                        // Group by teacher
                        if (schedule.getGiangVien() != null) {
                            teacherConflicts.computeIfAbsent(schedule.getGiangVien().getIdCanBo(),
                                                           k -> new ArrayList<>()).add(schedule);
                        }
                    }

                    // Report classroom conflicts
                    for (Map.Entry<String, List<LichGiangDay>> roomEntry : roomConflicts.entrySet()) {
                        if (roomEntry.getValue().size() > 1) {
                            StringBuilder conflict = new StringBuilder();
                            conflict.append("XUNG ĐỘT PHÒNG HỌC - Phòng: ").append(roomEntry.getKey());
                            conflict.append(" - Ngày: ").append(roomEntry.getValue().get(0).getNgayGiang());
                            conflict.append(" - Buổi: ").append(roomEntry.getValue().get(0).getBuoi());
                            conflict.append(" - Tiết: ").append(roomEntry.getValue().get(0).getTietBatDau())
                                   .append("-").append(roomEntry.getValue().get(0).getTietKetThuc());
                            conflict.append(" | Các lịch trùng: ");

                            for (LichGiangDay sch : roomEntry.getValue()) {
                                conflict.append("[").append(sch.getMonHoc() != null ? sch.getMonHoc().getTenMonHoc() : "N/A")
                                       .append(" - ").append(sch.getLop() != null ? sch.getLop().getTenLop() : "N/A")
                                       .append(" - ").append(sch.getGiangVien() != null ? sch.getGiangVien().getHoTen() : "N/A")
                                       .append("] ");
                            }

                            conflicts.add(conflict.toString());
                        }
                    }

                    // Report teacher conflicts
                    for (Map.Entry<Integer, List<LichGiangDay>> teacherEntry : teacherConflicts.entrySet()) {
                        if (teacherEntry.getValue().size() > 1) {
                            StringBuilder conflict = new StringBuilder();
                            LichGiangDay firstSchedule = teacherEntry.getValue().get(0);
                            conflict.append("XUNG ĐỘT GIẢNG VIÊN - GV: ")
                                   .append(firstSchedule.getGiangVien() != null ? firstSchedule.getGiangVien().getHoTen() : "N/A");
                            conflict.append(" - Ngày: ").append(firstSchedule.getNgayGiang());
                            conflict.append(" - Buổi: ").append(firstSchedule.getBuoi());
                            conflict.append(" - Tiết: ").append(firstSchedule.getTietBatDau())
                                   .append("-").append(firstSchedule.getTietKetThuc());
                            conflict.append(" | Các lịch trùng: ");

                            for (LichGiangDay sch : teacherEntry.getValue()) {
                                conflict.append("[").append(sch.getMonHoc() != null ? sch.getMonHoc().getTenMonHoc() : "N/A")
                                       .append(" - ").append(sch.getLop() != null ? sch.getLop().getTenLop() : "N/A")
                                       .append(" - ").append(sch.getPhong() != null ? sch.getPhong() : "N/A")
                                       .append("] ");
                            }

                            conflicts.add(conflict.toString());
                        }
                    }
                }
            }

        } catch (Exception e) {
            conflicts.add("Lỗi khi tạo báo cáo xung đột: " + e.getMessage());
        }

        return conflicts;
    }

    @Override
    public List<String> kiemTraTrungLich(Integer idLichGiang, Integer idGiangVien, Integer idPhong,
                                        Date ngayGiang, Integer tietBatDau, Integer tietKetThuc) {
        List<String> conflicts = new ArrayList<>();

        // Check teacher conflicts
        if (idGiangVien != null) {
            boolean hasTeacherConflict = lichGiangDayRepo.existsConflictGiangVien(
                idGiangVien, ngayGiang, null, null, tietBatDau, tietKetThuc, idLichGiang);
            if (hasTeacherConflict) {
                conflicts.add("Giảng viên đã có lịch dạy vào thời gian này");
            }
        }

        // Check classroom conflicts
        if (idPhong != null) {
            boolean hasRoomConflict = lichGiangDayRepo.existsConflictPhong(
                idPhong, null, ngayGiang, null, null, tietBatDau, tietKetThuc, idLichGiang);
            if (hasRoomConflict) {
                conflicts.add("Phòng học đã được sử dụng vào thời gian này");
            }
        }

        return conflicts;
    }

    @Override
    public Double tinhKhoiLuongGiangDay(Integer idGiangVien, Integer idHocKy) {
        return tinhTongGioGiang(idGiangVien, idHocKy);
    }

    @Override
    public List<LichGiangDay> layTatCa() {
        return lichGiangDayRepo.findAll();
    }

    /**
     * Tự động phân phòng học (trả về PhongHoc entity)
     */
    public PhongHoc tuDongPhanPhongHoc(LichGiangDay lichGiangDay) {
        if (lichGiangDay.getHinhThuc() == null) {
            return null;
        }

        String loaiPhong = "LT".equals(lichGiangDay.getHinhThuc()) ? "LT" : "TH";

        // Tìm phòng học phù hợp
        List<PhongHoc> phongHocs = phongHocSer.layDanhSachHoatDong();
        for (PhongHoc phong : phongHocs) {
            if (loaiPhong.equals(phong.getLoaiPhong())) {
                return phong;
            }
        }

        return null;
    }

    @Override
    public String tuDongPhanPhong(LichGiangDay lichGiangDay) {
        PhongHoc phongHoc = tuDongPhanPhongHoc(lichGiangDay);
        return phongHoc != null ? phongHoc.getTenPhong() : null;
    }

    @Override
    public List<String> layPhongTrong(String loaiPhong, String coSo, Date ngayGiang,
                                     Integer thu, String buoi, Integer tietBatDau, Integer tietKetThuc) {
        List<String> phongTrong = new ArrayList<>();
        
        // Get all classrooms of the specified type and campus
        String khu = "LT".equals(loaiPhong) ? "KHU_LT" : "KHU_TH";
        List<PhongHoc> danhSachPhong = phongHocSer.layTheoLoaiVaViTri(loaiPhong, coSo, khu);
        
        for (PhongHoc phong : danhSachPhong) {
            boolean isAvailable = !kiemTraTrungPhong(phong.getIdPhong(), phong.getMaPhong(),
                                                    ngayGiang, thu, buoi, tietBatDau, tietKetThuc, null);
            if (isAvailable) {
                phongTrong.add(phong.getMaPhong());
            }
        }
        
        return phongTrong;
    }

    @Override
    public List<Object> layMonHocCuaGiangVien(Integer idGiangVien) {
        return lichGiangDayRepo.layMonHocCuaGiangVien(idGiangVien);
    }

    @Override
    public List<Object> layLopCuaGiangVien(Integer idGiangVien) {
        return lichGiangDayRepo.layLopCuaGiangVien(idGiangVien);
    }

    @Override
    public List<Object> layHocKyCuaGiangVien(Integer idGiangVien) {
        return lichGiangDayRepo.layHocKyCuaGiangVien(idGiangVien);
    }
}
