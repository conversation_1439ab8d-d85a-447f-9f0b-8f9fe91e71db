package vn.vnpt.camau.qldt.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import vn.vnpt.camau.qldt.ModelAttr;
import vn.vnpt.camau.qldt.Response;
import vn.vnpt.camau.qldt.Utility;
import vn.vnpt.camau.qldt.model.*;
import vn.vnpt.camau.qldt.service.*;
import vn.vnpt.camau.qldt.model.BaiHoc;
import vn.vnpt.camau.qldt.FileUtil;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.LinkedHashMap;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import org.springframework.core.io.ClassPathResource;

@Controller
@RequestMapping("/lich-giang-day")
public class LichGiangDayController {

    /**
     * Show teaching schedule management page
     */
    @RequestMapping("")
    public String index(Model model) {
        // Add cache-busting parameter to JavaScript file
        String timestamp = String.valueOf(System.currentTimeMillis());
        ModelAttr modelAttr = new ModelAttr(Utility.layNguoiDungHienTai(),
                "Lịch giảng dạy",
                "sub/lich-giang-day",
                new String[]{"bower_components/select2/js/select2.min.js",
                           "bower_components/bootstrap-datepicker/dist/js/bootstrap-datepicker.min.js",
                           "bower_components/jdgrid/js/jdgrid-v4.js",
                           "bower_components/jdpage/js/jdpage.js",
                           "js/lich-giang-day.js?v=" + timestamp},
                new String[]{"bower_components/select2/css/select2.min.css",
                           "bower_components/bootstrap-datepicker/dist/css/bootstrap-datepicker.min.css",
                           "bower_components/jdgrid/css/jdgrid.css"});
        model.addAttribute("MODEL", modelAttr);
        return "layout";
    }

    @RequestMapping("/calendar")
    public String calendar(Model model) {
        // Add cache-busting parameter to JavaScript file
        String timestamp = String.valueOf(System.currentTimeMillis());
        ModelAttr modelAttr = new ModelAttr(Utility.layNguoiDungHienTai(),
                "Lịch giảng dạy - Calendar View",
                "sub/lich-giang-day-calendar",
                new String[]{"bower_components/select2/js/select2.min.js",
                           "js/lich-giang-day-calendar.js?v=" + timestamp},
                new String[]{"bower_components/select2/css/select2.min.css"});
        model.addAttribute("MODEL", modelAttr);
        return "layout";
    }

    @RequestMapping("/thiet-dat-tuan-day")
    public String thietDatTuanDay(Model model) {
        // Add cache-busting parameter to JavaScript file
        String timestamp = String.valueOf(System.currentTimeMillis());
        ModelAttr modelAttr = new ModelAttr(Utility.layNguoiDungHienTai(),
                "Thiết đặt tuần dạy",
                "sub/thiet-dat-tuan-day",
                new String[]{"bower_components/select2/js/select2.min.js",
                           "bower_components/bootstrap-datepicker/dist/js/bootstrap-datepicker.min.js",
                           "js/thiet-dat-tuan-day.js?v=" + timestamp},
                new String[]{"bower_components/select2/css/select2.min.css",
                           "bower_components/bootstrap-datepicker/dist/css/bootstrap-datepicker.min.css"});
        model.addAttribute("MODEL", modelAttr);
        return "layout";
    }

    @Autowired
    private LichGiangDayService lichGiangDaySer;

    @Autowired
    private BaiHocService baiHocSer;

    @Autowired
    private MonHocService monHocSer;

    @Autowired
    private CanBoService canBoSer;

    @Autowired
    private LopService lopSer;

    @Autowired
    private NhomService nhomSer;

    @Autowired
    private HocKyService hocKySer;

    @Autowired
    private NienKhoaService nienKhoaSer;

    @Autowired
    private PhongHocService phongHocSer;

    /**
     * Test endpoint
     */
    @RequestMapping(value = "/test", produces = "application/json")
    @ResponseBody
    public Response test() {
        return new Response(1, "API hoạt động bình thường");
    }

    /**
     * Initialize data for teaching schedule management
     */
    @RequestMapping(value = "/init", produces = "application/json")
    @ResponseBody
    public Response init() {
        try {
            HeDaoTao hdt = Utility.layHeDaoTaoHienTai();

            List<CanBo> canBos = canBoSer.layDsCanBo();
            List<MonHoc> monHocs = monHocSer.layDsMonHocTheoHeDT(hdt.getIdHeDaoTao());
            List<NienKhoa> nienKhoas = nienKhoaSer.layDanhSach();
            List<PhongHoc> phongHocs = phongHocSer.layDanhSachHoatDong();

            InitData initData = new InitData();
            initData.canBo = canBos;
            initData.monHoc = monHocs;
            initData.nienKhoa = nienKhoas;
            initData.phongHoc = phongHocs;

            return new Response(1, initData);
        } catch (Exception e) {
            e.printStackTrace();
            return new Response(0, "Đã có lỗi xảy ra: " + e.getMessage());
        }
    }

    /**
     * Search teaching schedules with pagination
     */
    @RequestMapping(value = "/tim-kiem", produces = "application/json")
    @ResponseBody
    public Response timKiem(@RequestParam(required = false) Integer idHocKy,
                           @RequestParam(required = false) Integer idGiangVien,
                           @RequestParam(required = false) Integer idMonHoc,
                           @RequestParam(required = false) Integer idLop,
                           @RequestParam(required = false) Integer idPhong,
                           @RequestParam(required = false) String hinhThuc,
                           @RequestParam(required = false) Integer thu,
                           @RequestParam(required = false) String buoi,
                           @RequestParam(required = false) String coSo,
                           @RequestParam(required = false) Integer trangThai,
                           @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date tuNgay,
                           @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date denNgay,
                           @RequestParam(required = false) String keyword,
                           @RequestParam(defaultValue = "0") int page,
                           @RequestParam(defaultValue = "20") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);

            // Call service to search teaching schedules
            Page<LichGiangDay> result = lichGiangDaySer.timKiem(
                idHocKy, idGiangVien, idMonHoc, idLop, idPhong,
                hinhThuc, thu, buoi, coSo, trangThai,
                tuNgay, denNgay, keyword, pageable);

            // Prepare response structure
            java.util.Map<String, Object> responseData = new java.util.HashMap<>();
            responseData.put("content", result.getContent());
            responseData.put("totalElements", result.getTotalElements());
            responseData.put("totalPages", result.getTotalPages());
            responseData.put("size", result.getSize());
            responseData.put("number", result.getNumber());

            return new Response(1, responseData);
        } catch (Exception e) {
            e.printStackTrace();
            return new Response(0, "Đã có lỗi xảy ra: " + e.getMessage());
        }
    }

    /**
     * Get teaching schedule detail by ID
     */
    @RequestMapping(value = "/lay-chi-tiet", produces = "application/json")
    @ResponseBody
    public Response layChiTiet(@RequestParam Integer idLichGiang) {
        try {
            LichGiangDay lichGiang = lichGiangDaySer.layTheoId(idLichGiang);
            if (lichGiang == null) {
                return new Response(0, "Không tìm thấy lịch giảng");
            }

            // Create a detailed response with all necessary IDs for editing
            Map<String, Object> editData = new HashMap<>();
            editData.put("idLichGiang", lichGiang.getIdLichGiang());
            editData.put("hinhThuc", lichGiang.getHinhThuc());
            editData.put("soTiet", lichGiang.getSoTiet());
            editData.put("heSo", lichGiang.getHeSo());
            editData.put("thu", lichGiang.getThu());
            editData.put("buoi", lichGiang.getBuoi());
            editData.put("tietBatDau", lichGiang.getTietBatDau());
            editData.put("tietKetThuc", lichGiang.getTietKetThuc());
            editData.put("ngayGiang", lichGiang.getNgayGiang());
            editData.put("tuanGiang", lichGiang.getTuanGiang());
            editData.put("trangThai", lichGiang.getTrangThai());
            editData.put("ghiChu", lichGiang.getGhiChu());
            editData.put("noiDungDay", lichGiang.getNoiDungDay());
            editData.put("soSvVang", lichGiang.getSoSvVang());
            editData.put("ngayGiangText", lichGiang.getNgayGiangText());


            // Add relationship IDs
            if (lichGiang.getHocKy() != null) {
                editData.put("idHocKy", lichGiang.getHocKy().getIdHocKy());
                if (lichGiang.getHocKy().getNienKhoa() != null) {
                    editData.put("idNienKhoa", lichGiang.getHocKy().getNienKhoa().getIdNienKhoa());
                }
            }

            if (lichGiang.getMonHoc() != null) {
                editData.put("idMonHoc", lichGiang.getMonHoc().getIdMonHoc());
            }

            if (lichGiang.getBaiHoc() != null) {
                editData.put("idBaiHoc", lichGiang.getBaiHoc().getIdBaiHoc());
            }

            if (lichGiang.getLop() != null) {
                editData.put("idLop", lichGiang.getLop().getIdLop());
            }

            if (lichGiang.getNhom() != null) {
                editData.put("idNhom", lichGiang.getNhom().getIdNhom());
            }

            if (lichGiang.getGiangVien() != null) {
                editData.put("idGiangVien", lichGiang.getGiangVien().getIdCanBo());
            }

            if (lichGiang.getPhongHoc() != null) {
                editData.put("idPhong", lichGiang.getPhongHoc().getIdPhong());
                editData.put("coSo", lichGiang.getPhongHoc().getCoSo());
            }

            return new Response(1, editData);
        } catch (Exception e) {
            e.printStackTrace();
            return new Response(0, "Đã có lỗi xảy ra: " + e.getMessage());
        }
    }

    /**
     * Save teaching schedule
     */
    @RequestMapping(value = "/luu", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public Response luu(@RequestBody Map<String, Object> requestData) {
        try {
            // Create LichGiangDay object and populate from request data
            LichGiangDay lichGiang = new LichGiangDay();

            // Set basic fields
            if (requestData.get("idLichGiang") != null) {
                lichGiang.setIdLichGiang(Integer.valueOf(requestData.get("idLichGiang").toString()));
            }

            // Load and set BaiHoc
            if (requestData.get("idBaiHoc") != null) {
                Integer idBaiHoc = Integer.valueOf(requestData.get("idBaiHoc").toString());
                BaiHoc baiHoc = baiHocSer.layTheoId(idBaiHoc);
                if (baiHoc == null) {
                    return new Response(0, "Không tìm thấy bài học với ID: " + idBaiHoc);
                }
                lichGiang.setBaiHoc(baiHoc);
            }

            // Load and set MonHoc
            if (requestData.get("idMonHoc") != null) {
                Integer idMonHoc = Integer.valueOf(requestData.get("idMonHoc").toString());
                MonHoc monHoc = monHocSer.layMonHocTheoIdMonHoc(idMonHoc);
                if (monHoc == null) {
                    return new Response(0, "Không tìm thấy môn học với ID: " + idMonHoc);
                }
                lichGiang.setMonHoc(monHoc);
            }

            // Load and set Lop
            if (requestData.get("idLop") != null) {
                Integer idLop = Integer.valueOf(requestData.get("idLop").toString());
                Lop lop = lopSer.layLopTheoIdLop(idLop);
                if (lop == null) {
                    return new Response(0, "Không tìm thấy lớp với ID: " + idLop);
                }
                lichGiang.setLop(lop);
            }

            // Load and set Nhom (optional)
            if (requestData.get("idNhom") != null && !requestData.get("idNhom").toString().isEmpty()) {
                Integer idNhom = Integer.valueOf(requestData.get("idNhom").toString());
                Nhom nhom = nhomSer.layTheoId(idNhom);
                if (nhom != null) {
                    lichGiang.setNhom(nhom);
                }
            }

            // Load and set GiangVien
            if (requestData.get("idGiangVien") != null) {
                Integer idGiangVien = Integer.valueOf(requestData.get("idGiangVien").toString());
                CanBo giangVien = canBoSer.layTheoIdCanBo(idGiangVien);
                if (giangVien == null) {
                    return new Response(0, "Không tìm thấy giảng viên với ID: " + idGiangVien);
                }
                lichGiang.setGiangVien(giangVien);
            }

            // Load and set HocKy
            if (requestData.get("idHocKy") != null) {
                Integer idHocKy = Integer.valueOf(requestData.get("idHocKy").toString());
                HocKy hocKy = hocKySer.layTheoIdHocKy(idHocKy);
                if (hocKy == null) {
                    return new Response(0, "Không tìm thấy học kỳ với ID: " + idHocKy);
                }
                lichGiang.setHocKy(hocKy);
            }

            // Load and set PhongHoc (optional)
            if (requestData.get("idPhong") != null && !requestData.get("idPhong").toString().isEmpty()) {
                Integer idPhong = Integer.valueOf(requestData.get("idPhong").toString());
                PhongHoc phongHoc = phongHocSer.layTheoId(idPhong);
                if (phongHoc != null) {
                    lichGiang.setPhongHoc(phongHoc);
                    // Không cần set phong, khu nữa - sẽ lấy từ relationship
                }
            }

            // Set other fields
            if (requestData.get("hinhThuc") != null) {
                lichGiang.setHinhThuc(requestData.get("hinhThuc").toString());
            }

            if (requestData.get("ngayGiang") != null) {
                try {
                    String ngayGiangStr = requestData.get("ngayGiang").toString();
                    Date ngayGiang = java.sql.Date.valueOf(ngayGiangStr);
                    lichGiang.setNgayGiang(ngayGiang);
                } catch (Exception e) {
                    return new Response(0, "Định dạng ngày giảng không hợp lệ");
                }
            }

            if (requestData.get("thu") != null) {
                lichGiang.setThu(Integer.valueOf(requestData.get("thu").toString()));
            }

            if (requestData.get("buoi") != null) {
                lichGiang.setBuoi(requestData.get("buoi").toString());
            }

            if (requestData.get("tietBatDau") != null) {
                lichGiang.setTietBatDau(Integer.valueOf(requestData.get("tietBatDau").toString()));
            }

            if (requestData.get("tietKetThuc") != null) {
                lichGiang.setTietKetThuc(Integer.valueOf(requestData.get("tietKetThuc").toString()));
            }

            // Không cần set coSo nữa - sẽ lấy từ PhongHoc relationship

            if (requestData.get("soTiet") != null) {
                lichGiang.setSoTiet(Integer.valueOf(requestData.get("soTiet").toString()));
            }

            if (requestData.get("heSo") != null) {
                lichGiang.setHeSo(new java.math.BigDecimal(requestData.get("heSo").toString()));
            }

            if (requestData.get("tuanGiang") != null) {
                lichGiang.setTuanGiang(Integer.valueOf(requestData.get("tuanGiang").toString()));
            }

            if (requestData.get("ghiChu") != null) {
                lichGiang.setGhiChu(requestData.get("ghiChu").toString());
            }

            if (requestData.get("trangThai") != null) {
                lichGiang.setTrangThai(Integer.valueOf(requestData.get("trangThai").toString()));
            }

            if (requestData.get("noiDungDay") != null) {
                lichGiang.setNoiDungDay(requestData.get("noiDungDay").toString());
            }

            if (requestData.get("soSvVang") != null) {
                lichGiang.setSoSvVang(Integer.valueOf(requestData.get("soSvVang").toString()));
            }

            // Set default values
            if (lichGiang.getTrangThai() == null) {
                lichGiang.setTrangThai(0); // Chưa dạy
            }

            // Validate schedule data
            String error = lichGiangDaySer.kiemTraHopLe(lichGiang);
            if (error != null) {
                return new Response(0, error);
            }

            LichGiangDay saved = lichGiangDaySer.luu(lichGiang);
            return new Response(1, "Lưu lịch giảng thành công", saved);
        } catch (Exception e) {
            e.printStackTrace();
            return new Response(0, "Đã có lỗi xảy ra: " + e.getMessage());
        }
    }

    /**
     * Delete teaching schedule
     */
    @RequestMapping(value = "/xoa", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public Response xoa(@RequestParam Integer idLichGiang) {
        try {
            LichGiangDay lichGiang = lichGiangDaySer.layTheoId(idLichGiang);
            if (lichGiang == null) {
                return new Response(0, "Không tìm thấy lịch giảng");
            }

            lichGiangDaySer.xoa(idLichGiang);
            return new Response(1, "Xóa lịch giảng thành công");
        } catch (Exception e) {
            e.printStackTrace();
            return new Response(0, "Đã có lỗi xảy ra: " + e.getMessage());
        }
    }

    /**
     * Get semesters by academic year
     */
    @RequestMapping(value = "/lay-hoc-ky", produces = "application/json")
    @ResponseBody
    public Response layHocKy(@RequestParam Integer idNienKhoa) {
        try {
            List<HocKy> hocKys = hocKySer.LayHocKyTheoNienKhoa(idNienKhoa);
            return new Response(1, hocKys);
        } catch (Exception e) {
            e.printStackTrace();
            return new Response(0, "Đã có lỗi xảy ra: " + e.getMessage());
        }
    }

    /**
     * Get groups by semester and subject
     */
    @RequestMapping(value = "/lay-nhom", produces = "application/json")
    @ResponseBody
    public Response layNhom(@RequestParam Integer idHocKy, @RequestParam Integer idMonHoc) {
        try {
            List<Nhom> nhoms = nhomSer.layDsNhomTheoMonHoc(idHocKy, idMonHoc);
            return new Response(1, nhoms);
        } catch (Exception e) {
            e.printStackTrace();
            return new Response(0, "Đã có lỗi xảy ra: " + e.getMessage());
        }
    }

    /**
     * Get classes by education system
     */
    @RequestMapping(value = "/lay-lop", produces = "application/json")
    @ResponseBody
    public Response layLop() {
        try {
            HeDaoTao hdt = Utility.layHeDaoTaoHienTai();
            List<Lop> lops = lopSer.layTheoHeDaoTao(hdt.getIdHeDaoTao());
            return new Response(1, lops);
        } catch (Exception e) {
            e.printStackTrace();
            return new Response(0, "Đã có lỗi xảy ra: " + e.getMessage());
        }
    }

    /**
     * Get lessons by subject
     */
    @RequestMapping(value = "/lay-bai-hoc", produces = "application/json")
    @ResponseBody
    public Response layBaiHoc(@RequestParam Integer idMonHoc) {
        try {
            List<BaiHoc> baiHocs = baiHocSer.layTheoMonHoc(idMonHoc);
            return new Response(1, baiHocs);
        } catch (Exception e) {
            e.printStackTrace();
            return new Response(0, "Đã có lỗi xảy ra: " + e.getMessage());
        }
    }

    /**
     * Check for schedule conflicts
     */
    @RequestMapping(value = "/kiem-tra-trung", produces = "application/json")
    @ResponseBody
    public Response kiemTraTrung(@RequestParam Integer idPhong,
                                @RequestParam Integer idGiangVien,
                                @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date ngayGiang,
                                @RequestParam Integer tietBatDau,
                                @RequestParam Integer tietKetThuc,
                                @RequestParam(required = false) Integer idLichGiang) {
        try {
            PhongHoc phongHoc = phongHocSer.layTheoId(idPhong);
            if (phongHoc == null) {
                return new Response(0, "Không tìm thấy phòng học");
            }
            
            // Calculate day of week and session
            java.util.Calendar cal = java.util.Calendar.getInstance();
            cal.setTime(ngayGiang);
            int thu = cal.get(java.util.Calendar.DAY_OF_WEEK);
            
            String buoi = "SANG";
            if (tietBatDau >= 6 && tietBatDau <= 10) {
                buoi = "CHIEU";
            } else if (tietBatDau >= 11) {
                buoi = "TOI";
            }
            
            boolean trungPhong = lichGiangDaySer.kiemTraTrungPhong(idPhong, phongHoc.getMaPhong(),
                                                                  ngayGiang, thu, buoi,
                                                                  tietBatDau, tietKetThuc, idLichGiang);
            
            boolean trungCanBo = lichGiangDaySer.kiemTraTrungGiangVien(idGiangVien, ngayGiang, thu, buoi,
                                                                      tietBatDau, tietKetThuc, idLichGiang);
            
            Map<String, Boolean> result = new HashMap<>();
            result.put("trungPhong", trungPhong);
            result.put("trungCanBo", trungCanBo);
            
            return new Response(1, result);
        } catch (Exception e) {
            e.printStackTrace();
            return new Response(0, "Đã có lỗi xảy ra: " + e.getMessage());
        }
    }

    /**
     * Copy schedule from one week to another
     */
    @RequestMapping(value = "/sao-chep-tuan", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public Response saoChepTuan(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date tuanGoc,
                               @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date tuanMoi,
                               @RequestParam Integer idHocKy) {
        try {
            // Calculate week numbers
            java.util.Calendar cal = java.util.Calendar.getInstance();
            cal.setTime(tuanGoc);
            int tuanGocNum = cal.get(java.util.Calendar.WEEK_OF_YEAR);
            
            cal.setTime(tuanMoi);
            int tuanMoiNum = cal.get(java.util.Calendar.WEEK_OF_YEAR);
            
            List<LichGiangDay> copied = lichGiangDaySer.saoChepLichTuan(tuanGocNum, tuanMoiNum, idHocKy);
            return new Response(1, "Sao chép lịch tuần thành công", copied);
        } catch (Exception e) {
            e.printStackTrace();
            return new Response(0, "Đã có lỗi xảy ra: " + e.getMessage());
        }
    }

    /**
     * Get available classrooms
     */
    @RequestMapping(value = "/lay-phong-trong", produces = "application/json")
    @ResponseBody
    public Response layPhongTrong(@RequestParam String loaiPhong,
                                 @RequestParam String coSo,
                                 @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date ngayGiang,
                                 @RequestParam Integer tietBatDau,
                                 @RequestParam Integer tietKetThuc) {
        try {
            // Calculate day of week and session
            java.util.Calendar cal = java.util.Calendar.getInstance();
            cal.setTime(ngayGiang);
            int thu = cal.get(java.util.Calendar.DAY_OF_WEEK);

            String buoi = "SANG";
            if (tietBatDau >= 6 && tietBatDau <= 10) {
                buoi = "CHIEU";
            } else if (tietBatDau >= 11) {
                buoi = "TOI";
            }

            List<String> phongTrong = lichGiangDaySer.layPhongTrong(loaiPhong, coSo, ngayGiang,
                                                                   thu, buoi, tietBatDau, tietKetThuc);
            return new Response(1, phongTrong);
        } catch (Exception e) {
            e.printStackTrace();
            return new Response(0, "Đã có lỗi xảy ra: " + e.getMessage());
        }
    }

    /**
     * Get all classrooms
     */
    @RequestMapping(value = "/lay-phong-hoc", produces = "application/json")
    @ResponseBody
    public Response layPhongHoc() {
        try {
            List<PhongHoc> phongHocs = phongHocSer.layDanhSachHoatDong();
            return new Response(1, phongHocs);
        } catch (Exception e) {
            e.printStackTrace();
            return new Response(0, "Đã có lỗi xảy ra: " + e.getMessage());
        }
    }

    /**
     * Get classrooms by campus
     */
    @RequestMapping(value = "/lay-phong-hoc-theo-co-so", produces = "application/json")
    @ResponseBody
    public Response layPhongHocTheoCoSo(@RequestParam(required = false) String coSo) {
        try {
            List<PhongHoc> phongHocs;
            if (coSo != null && !coSo.trim().isEmpty()) {
                phongHocs = phongHocSer.layTheoCoSo(coSo);
            } else {
                phongHocs = phongHocSer.layDanhSachHoatDong();
            }
            return new Response(1, phongHocs);
        } catch (Exception e) {
            e.printStackTrace();
            return new Response(0, "Đã có lỗi xảy ra: " + e.getMessage());
        }
    }

    /**
     * Get semesters by academic year
     */
    @RequestMapping(value = "/lay-hoc-ky-theo-nien-khoa", produces = "application/json")
    @ResponseBody
    public Response layHocKyTheoNienKhoa(@RequestParam Integer idNienKhoa) {
        try {
            List<HocKy> hocKys = hocKySer.layTheoNienKhoa(idNienKhoa);
            return new Response(1, hocKys);
        } catch (Exception e) {
            e.printStackTrace();
            return new Response(0, "Đã có lỗi xảy ra: " + e.getMessage());
        }
    }



    /**
     * Check schedule conflicts
     */
    @RequestMapping(value = "/kiem-tra-trung-lich", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public Response kiemTraTrungLich(@RequestBody Map<String, Object> requestData) {
        try {
            Integer idLichGiang = requestData.get("idLichGiang") != null ?
                Integer.valueOf(requestData.get("idLichGiang").toString()) : null;
            Integer idGiangVien = Integer.valueOf(requestData.get("idGiangVien").toString());
            Integer idPhong = Integer.valueOf(requestData.get("idPhong").toString());
            String ngayGiang = requestData.get("ngayGiang").toString();
            Integer tietBatDau = Integer.valueOf(requestData.get("tietBatDau").toString());
            Integer tietKetThuc = Integer.valueOf(requestData.get("tietKetThuc").toString());

            // Parse date
            Date ngayGiangDate = java.sql.Date.valueOf(ngayGiang);

            // Check for conflicts
            List<String> conflicts = lichGiangDaySer.kiemTraTrungLich(
                idLichGiang, idGiangVien, idPhong, ngayGiangDate, tietBatDau, tietKetThuc);

            return new Response(1, conflicts);
        } catch (Exception e) {
            e.printStackTrace();
            return new Response(0, "Đã có lỗi xảy ra: " + e.getMessage());
        }
    }

    /**
     * Update teaching status with detailed information
     */
    @RequestMapping(value = "/cap-nhat-trang-thai-chi-tiet", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public Response capNhatTrangThaiChiTiet(@RequestBody Map<String, Object> requestData) {
        try {
            Integer idLichGiang = Integer.valueOf(requestData.get("idLichGiang").toString());
            Integer trangThai = Integer.valueOf(requestData.get("trangThai").toString());
            String noiDungDay = requestData.get("noiDungDay") != null ?
                requestData.get("noiDungDay").toString() : null;
            Integer soSvVang = requestData.get("soSvVang") != null ?
                Integer.valueOf(requestData.get("soSvVang").toString()) : 0;
            String ghiChu = requestData.get("ghiChu") != null ?
                requestData.get("ghiChu").toString() : null;

            LichGiangDay lichGiang = lichGiangDaySer.layTheoId(idLichGiang);
            if (lichGiang == null) {
                return new Response(0, "Không tìm thấy lịch giảng");
            }
            CanBo canBo = (CanBo) Utility.layNguoiDungHienTai();

            lichGiang.setTrangThai(trangThai);
            lichGiang.setNoiDungDay(noiDungDay);
            lichGiang.setSoSvVang(soSvVang);
            lichGiang.setGhiChu(ghiChu);
            lichGiang.setNguoiCapNhat(canBo);
            lichGiang.setNgayCapNhat(new Date());

            lichGiangDaySer.luu(lichGiang);

            return new Response(1, "Cập nhật trạng thái thành công");
        } catch (Exception e) {
            e.printStackTrace();
            return new Response(0, "Đã có lỗi xảy ra: " + e.getMessage());
        }
    }

    /**
     * Get classroom detail by ID for debugging
     */
    @RequestMapping(value = "/lay-phong-hoc-chi-tiet", produces = "application/json")
    @ResponseBody
    public Response layPhongHocChiTiet(@RequestParam Integer idPhong) {
        try {
            PhongHoc phongHoc = phongHocSer.layTheoId(idPhong);
            if (phongHoc == null) {
                return new Response(0, "Không tìm thấy phòng học với ID: " + idPhong);
            }
            return new Response(1, phongHoc);
        } catch (Exception e) {
            e.printStackTrace();
            return new Response(0, "Đã có lỗi xảy ra: " + e.getMessage());
        }
    }

    /**
     * Get all lecturers
     */
    @RequestMapping(value = "/lay-giang-vien", produces = "application/json")
    @ResponseBody
    public Response layGiangVien() {
        try {
            List<CanBo> canBos = canBoSer.layDsCanBo();
            return new Response(1, canBos);
        } catch (Exception e) {
            e.printStackTrace();
            return new Response(0, "Đã có lỗi xảy ra: " + e.getMessage());
        }
    }

    /**
     * Get all academic years
     */
    @RequestMapping(value = "/lay-nien-khoa", produces = "application/json")
    @ResponseBody
    public Response layNienKhoa() {
        try {
            List<NienKhoa> nienKhoas = nienKhoaSer.layDanhSach();
            return new Response(1, nienKhoas);
        } catch (Exception e) {
            e.printStackTrace();
            return new Response(0, "Đã có lỗi xảy ra: " + e.getMessage());
        }
    }

    /**
     * Get all subjects
     */
    @RequestMapping(value = "/lay-mon-hoc", produces = "application/json")
    @ResponseBody
    public Response layMonHoc() {
        try {
            HeDaoTao hdt = Utility.layHeDaoTaoHienTai();
            List<MonHoc> monHocs = monHocSer.layDsMonHocTheoHeDT(hdt.getIdHeDaoTao());
            return new Response(1, monHocs);
        } catch (Exception e) {
            e.printStackTrace();
            return new Response(0, "Đã có lỗi xảy ra: " + e.getMessage());
        }
    }

    /**
     * Thiết đặt tuần dạy cho học kỳ
     */
    @RequestMapping(value = "/thiet-dat-tuan-day", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public Response thietDatTuanDay(@RequestParam Integer idHocKy,
                                   @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date ngayBatDauTuanDay) {
        try {
            HocKy hocKy = hocKySer.layTheoIdHocKy(idHocKy);
            if (hocKy == null) {
                return new Response(0, "Không tìm thấy học kỳ");
            }

            // Thiết đặt ngày bắt đầu tuần dạy
            hocKy.setNgayBatDauTuanDay(ngayBatDauTuanDay);

            // Tính ngày kết thúc tuần dạy dựa trên số tuần
            if (hocKy.getSoTuan() != null) {
                java.util.Calendar cal = java.util.Calendar.getInstance();
                cal.setTime(ngayBatDauTuanDay);
                cal.add(java.util.Calendar.WEEK_OF_YEAR, hocKy.getSoTuan() - 1);
                cal.add(java.util.Calendar.DAY_OF_MONTH, 6); // Thêm 6 ngày để có tuần đầy đủ
                hocKy.setNgayKetThucTuanDay(cal.getTime());
            }

            HocKy saved = hocKySer.luuHocKy(hocKy);
            return new Response(1, saved);
        } catch (Exception e) {
            e.printStackTrace();
            return new Response(0, "Đã có lỗi xảy ra: " + e.getMessage());
        }
    }

    /**
     * Lấy thông tin tuần dạy của học kỳ
     */
    @RequestMapping(value = "/lay-thong-tin-tuan-day", produces = "application/json")
    @ResponseBody
    public Response layThongTinTuanDay(@RequestParam Integer idHocKy) {
        try {
            HocKy hocKy = hocKySer.layTheoIdHocKy(idHocKy);
            if (hocKy == null) {
                return new Response(0, "Không tìm thấy học kỳ");
            }

            Map<String, Object> thongTin = new HashMap<>();
            thongTin.put("idHocKy", hocKy.getIdHocKy());
            thongTin.put("tenHocKy", hocKy.getTenHocKy());
            thongTin.put("soTuan", hocKy.getSoTuan());
            thongTin.put("ngayBatDau", hocKy.getNgayBatDau());
            thongTin.put("ngayKetThuc", hocKy.getNgayKetThuc());
            thongTin.put("ngayBatDauTuanDay", hocKy.getNgayBatDauTuanDay());
            thongTin.put("ngayKetThucTuanDay", hocKy.getNgayKetThucTuanDay());

            // Tính tuần hiện tại
            Date ngayHienTai = new Date();
            Integer tuanHienTai = hocKy.tinhTuanHoc(ngayHienTai);
            thongTin.put("tuanHienTai", tuanHienTai);

            return new Response(1, thongTin);
        } catch (Exception e) {
            e.printStackTrace();
            return new Response(0, "Đã có lỗi xảy ra: " + e.getMessage());
        }
    }

    /**
     * Tính tuần học dựa trên ngày cụ thể
     */
    @RequestMapping(value = "/tinh-tuan-hoc", produces = "application/json")
    @ResponseBody
    public Response tinhTuanHoc(@RequestParam Integer idHocKy,
                               @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date ngayCanTinh) {
        try {
            HocKy hocKy = hocKySer.layTheoIdHocKy(idHocKy);
            if (hocKy == null) {
                return new Response(0, "Không tìm thấy học kỳ");
            }

            if (hocKy.getNgayBatDauTuanDay() == null) {
                return new Response(0, "Học kỳ chưa được thiết đặt tuần dạy");
            }

            Integer tuanHoc = hocKy.tinhTuanHoc(ngayCanTinh);

            Map<String, Object> ketQua = new HashMap<>();
            ketQua.put("ngayCanTinh", ngayCanTinh);
            ketQua.put("tuanHoc", tuanHoc);
            ketQua.put("ngayBatDauTuan", hocKy.layNgayBatDauTuan(tuanHoc));
            ketQua.put("ngayKetThucTuan", hocKy.layNgayKetThucTuan(tuanHoc));

            return new Response(1, ketQua);
        } catch (Exception e) {
            e.printStackTrace();
            return new Response(0, "Đã có lỗi xảy ra: " + e.getMessage());
        }
    }

    /**
     * Get initial data for dropdowns
     */
    @RequestMapping(value = "/lay-du-lieu-ban-dau", produces = "application/json")
    @ResponseBody
    public Response layDuLieuBanDau() {
        try {
            HeDaoTao hdt = Utility.layHeDaoTaoHienTai();

            // Create response data object
            Map<String, Object> data = new HashMap<>();

            // Get academic years
            List<NienKhoa> nienKhoas = nienKhoaSer.layDanhSach();
            data.put("nienKhoa", nienKhoas);

            // Get lecturers
            List<CanBo> canBos = canBoSer.layDsCanBo();
            data.put("canBo", canBos);

            // Get subjects
            List<MonHoc> monHocs = monHocSer.layDsMonHocTheoHeDT(hdt.getIdHeDaoTao());
            data.put("monHoc", monHocs);

            // Get classrooms
            List<PhongHoc> phongHocs = phongHocSer.layDanhSachHoatDong();
            data.put("phongHoc", phongHocs);

            return new Response(1, data);
        } catch (Exception e) {
            e.printStackTrace();
            return new Response(0, "Đã có lỗi xảy ra: " + e.getMessage());
        }
    }

    /**
     * Get template schedules for multiple weeks creation
     */
    @RequestMapping(value = "/lay-lich-mau", produces = "application/json")
    @ResponseBody
    public Response layLichMau() {
        try {
            // Get recent schedules that can be used as templates
            Pageable pageable = PageRequest.of(0, 50); // Get latest 50 records
            Page<LichGiangDay> schedules = lichGiangDaySer.timKiem(
                null, null, null, null, null, null, null, null, null, null,
                null, null, null, pageable);

            return new Response(1, schedules.getContent());
        } catch (Exception e) {
            e.printStackTrace();
            return new Response(0, "Đã có lỗi xảy ra: " + e.getMessage());
        }
    }

    /**
     * Get conflict report for teaching schedules
     */
    @RequestMapping(value = "/bao-cao-xung-dot", produces = "application/json")
    @ResponseBody
    public Response baoCaoXungDot(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date tuNgay,
                                  @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date denNgay) {
        try {
            List<String> conflicts = lichGiangDaySer.layBaoCaoXungDot(tuNgay, denNgay);

            Map<String, Object> result = new HashMap<>();
            result.put("conflicts", conflicts);
            result.put("totalConflicts", conflicts.size());
            result.put("fromDate", tuNgay);
            result.put("toDate", denNgay);

            return new Response(1, result);
        } catch (Exception e) {
            e.printStackTrace();
            return new Response(0, "Đã có lỗi xảy ra: " + e.getMessage());
        }
    }

    /**
     * Generate schedule for multiple weeks based on template
     */
    @RequestMapping(value = "/tao-lich-nhieu-tuan", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public Response taoLichNhieuTuan(@RequestParam Integer idLichMau,
                                     @RequestParam String danhSachNgay) {
        try {
            LichGiangDay lichMau = lichGiangDaySer.layTheoId(idLichMau);
            if (lichMau == null) {
                return new Response(0, "Không tìm thấy lịch mẫu");
            }

            // Parse dates from comma-separated string
            List<Date> dates = new ArrayList<>();
            String[] dateStrings = danhSachNgay.split(",");

            for (String dateStr : dateStrings) {
                try {
                    Date date = java.sql.Date.valueOf(dateStr.trim());
                    dates.add(date);
                } catch (Exception e) {
                    return new Response(0, "Định dạng ngày không hợp lệ: " + dateStr);
                }
            }

            List<LichGiangDay> createdSchedules = lichGiangDaySer.taoLichNhieuTuan(lichMau, dates);

            Map<String, Object> result = new HashMap<>();
            result.put("schedules", createdSchedules);
            result.put("totalCreated", createdSchedules.size());

            return new Response(1, "Tạo lịch cho nhiều tuần thành công", result);
        } catch (Exception e) {
            e.printStackTrace();
            return new Response(0, "Đã có lỗi xảy ra: " + e.getMessage());
        }
    }









    /**
     * Mark teaching session as completed
     */
    @RequestMapping(value = "/danh-dau-hoan-thanh", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public Response danhDauHoanThanh(@RequestParam Integer idLichGiang,
                                     @RequestParam(required = false) String noiDungDay,
                                     @RequestParam(required = false, defaultValue = "0") Integer soSvVang) {
        try {
            LichGiangDay lichGiang = lichGiangDaySer.layTheoId(idLichGiang);
            if (lichGiang == null) {
                return new Response(0, "Không tìm thấy lịch giảng dạy");
            }

            // Update status and progress info
            lichGiang.setTrangThai(1); // Đã dạy
            lichGiang.setNoiDungDay(noiDungDay);
            lichGiang.setSoSvVang(soSvVang);
            lichGiang.setNgayCapNhat(new Date());

            LichGiangDay saved = lichGiangDaySer.luu(lichGiang);
            return new Response(1, "Đánh dấu hoàn thành thành công", saved);
        } catch (Exception e) {
            e.printStackTrace();
            return new Response(0, "Đã có lỗi xảy ra: " + e.getMessage());
        }
    }

    /**
     * Update teaching session status
     */
    @RequestMapping(value = "/cap-nhat-trang-thai", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public Response capNhatTrangThai(@RequestParam Integer idLichGiang,
                                     @RequestParam Integer trangThai) {
        try {
            LichGiangDay lichGiang = lichGiangDaySer.layTheoId(idLichGiang);
            if (lichGiang == null) {
                return new Response(0, "Không tìm thấy lịch giảng dạy");
            }

            lichGiang.setTrangThai(trangThai);
            lichGiang.setNgayCapNhat(new Date());

            LichGiangDay saved = lichGiangDaySer.luu(lichGiang);
            return new Response(1, "Cập nhật trạng thái thành công", saved);
        } catch (Exception e) {
            e.printStackTrace();
            return new Response(0, "Đã có lỗi xảy ra: " + e.getMessage());
        }
    }

    /**
     * Export teacher schedule to Excel by week in calendar format
     */
    @RequestMapping(value = "/xuat-excel-lich-giang-vien", produces = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
    public void xuatExcelLichGiangVien(@RequestParam Integer idGiangVien,
                                       @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date tuNgay,
                                       @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date denNgay,
                                       HttpServletResponse response) throws IOException {
        try {
            // Get lecturer information
            CanBo giangVien = canBoSer.layTheoIdCanBo(idGiangVien);
            if (giangVien == null) {
                response.sendError(HttpServletResponse.SC_BAD_REQUEST, "Không tìm thấy giảng viên");
                return;
            }

            // Get teaching schedules for the lecturer in the specified period
            Pageable pageable = PageRequest.of(0, 1000); // Get all records
            Page<LichGiangDay> schedules = lichGiangDaySer.timKiem(
                null, idGiangVien, null, null, null, null, null, null, null, null,
                tuNgay, denNgay, null, pageable);

            // Create Excel data in calendar format like the image
            List<Map<String, Object>> excelData = createWeeklyScheduleExcel(schedules.getContent(), giangVien, tuNgay, denNgay);

            // Generate file name
            String fileName = "lich-giang-tuan-" + giangVien.getMaCanBo() + "-" +
                            new SimpleDateFormat("yyyyMMdd").format(tuNgay);

            // Create title
            SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
            String title = "LỊCH GIẢNG TUẦN - " + giangVien.getHoTen().toUpperCase() +
                          " (Từ ngày " + dateFormat.format(tuNgay) + " đến ngày " + dateFormat.format(denNgay) + ")";

            // Export to Excel
            FileUtil.downloadExcel(excelData, title, fileName, response);

        } catch (Exception e) {
            e.printStackTrace();
            response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "Lỗi khi xuất Excel: " + e.getMessage());
        }
    }

    /**
     * Create weekly schedule Excel data in calendar format
     */
    private List<Map<String, Object>> createWeeklyScheduleExcel(List<LichGiangDay> schedules, CanBo giangVien, Date tuNgay, Date denNgay) {
        List<Map<String, Object>> excelData = new ArrayList<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");

        // Create header information rows
        Map<String, Object> headerInfo1 = new LinkedHashMap<>();
        headerInfo1.put("col1", "UBND TỈNH CÀ MAU");
        headerInfo1.put("col2", "");
        headerInfo1.put("col3", "CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM");
        headerInfo1.put("col4", "");
        excelData.add(headerInfo1);

        Map<String, Object> headerInfo2 = new LinkedHashMap<>();
        headerInfo2.put("col1", "TRƯỜNG CAO ĐẲNG Y TẾ");
        headerInfo2.put("col2", "");
        headerInfo2.put("col3", "Độc lập - Tự do - Hạnh phúc");
        headerInfo2.put("col4", "");
        excelData.add(headerInfo2);

        Map<String, Object> headerInfo3 = new LinkedHashMap<>();
        headerInfo3.put("col1", "");
        headerInfo3.put("col2", "");
        headerInfo3.put("col3", "Cà Mau, ngày " + new SimpleDateFormat("dd").format(new Date()) +
                              " tháng " + new SimpleDateFormat("MM").format(new Date()) +
                              " năm " + new SimpleDateFormat("yyyy").format(new Date()));
        headerInfo3.put("col4", "");
        excelData.add(headerInfo3);

        // Empty row
        Map<String, Object> emptyRow1 = new LinkedHashMap<>();
        emptyRow1.put("col1", "");
        emptyRow1.put("col2", "");
        emptyRow1.put("col3", "");
        emptyRow1.put("col4", "");
        excelData.add(emptyRow1);

        // Title
        Map<String, Object> titleRow = new LinkedHashMap<>();
        titleRow.put("col1", "LỊCH GIẢNG TUẦN");
        titleRow.put("col2", "");
        titleRow.put("col3", "");
        titleRow.put("col4", "");
        excelData.add(titleRow);

        Map<String, Object> dateRangeRow = new LinkedHashMap<>();
        dateRangeRow.put("col1", "Từ ngày " + dateFormat.format(tuNgay) + " đến ngày " + dateFormat.format(denNgay));
        dateRangeRow.put("col2", "");
        dateRangeRow.put("col3", "");
        dateRangeRow.put("col4", "");
        excelData.add(dateRangeRow);

        Map<String, Object> lecturerRow = new LinkedHashMap<>();
        lecturerRow.put("col1", "Gv: " + giangVien.getHoTen() + " (" + giangVien.getMaCanBo() + ")");
        lecturerRow.put("col2", "");
        lecturerRow.put("col3", "");
        lecturerRow.put("col4", "");
        excelData.add(lecturerRow);

        // Empty row
        excelData.add(emptyRow1);

        // Create schedule table header
        Map<String, Object> tableHeader = new LinkedHashMap<>();
        tableHeader.put("col1", "Thứ-Ngày");
        tableHeader.put("col2", "Buổi/Tiết");
        tableHeader.put("col3", "Thời gian");
        tableHeader.put("col4", "NỘI DUNG GIẢNG DẠY");
        excelData.add(tableHeader);

        // Group schedules by date and session
        Map<String, Map<String, List<LichGiangDay>>> schedulesByDate = groupSchedulesByDateAndSession(schedules);

        // Create schedule rows for each day of the week
        String[] daysOfWeek = {"THỨ 2", "THỨ 3", "THỨ 4", "THỨ 5", "THỨ 6", "THỨ 7", "CHỦ NHẬT"};

        for (int i = 0; i < 7; i++) {
            java.util.Calendar cal = java.util.Calendar.getInstance();
            cal.setTime(tuNgay);
            cal.add(java.util.Calendar.DAY_OF_MONTH, i);
            Date currentDate = cal.getTime();
            String dateKey = dateFormat.format(currentDate);

            Map<String, List<LichGiangDay>> daySchedules = schedulesByDate.get(dateKey);

            if (daySchedules != null && !daySchedules.isEmpty()) {
                boolean firstSession = true;
                for (String session : new String[]{"SANG", "CHIEU", "TOI"}) {
                    List<LichGiangDay> sessionSchedules = daySchedules.get(session);
                    if (sessionSchedules != null && !sessionSchedules.isEmpty()) {
                        for (LichGiangDay schedule : sessionSchedules) {
                            Map<String, Object> scheduleRow = new LinkedHashMap<>();

                            if (firstSession) {
                                scheduleRow.put("col1", daysOfWeek[i] + " - " + dateKey);
                                firstSession = false;
                            } else {
                                scheduleRow.put("col1", "");
                            }

                            scheduleRow.put("col2", getSessionText(session));
                            scheduleRow.put("col3", schedule.getTietBatDau() + "h30");

                            String content = buildScheduleContent(schedule);
                            scheduleRow.put("col4", content);

                            excelData.add(scheduleRow);
                        }
                    }
                }
            } else {
                // Empty day
                Map<String, Object> emptyDayRow = new LinkedHashMap<>();
                emptyDayRow.put("col1", daysOfWeek[i] + " - " + dateKey);
                emptyDayRow.put("col2", "");
                emptyDayRow.put("col3", "");
                emptyDayRow.put("col4", "");
                excelData.add(emptyDayRow);
            }
        }

        return excelData;
    }

    /**
     * Helper method to get day of week text
     */
    private String getThuText(Integer thu) {
        if (thu == null) return "";
        switch(thu) {
            case 1: return "Chủ nhật";
            case 2: return "Thứ 2";
            case 3: return "Thứ 3";
            case 4: return "Thứ 4";
            case 5: return "Thứ 5";
            case 6: return "Thứ 6";
            case 7: return "Thứ 7";
            default: return "";
        }
    }

    /**
     * Helper method to get status text
     */
    private String getTrangThaiText(Integer trangThai) {
        if (trangThai == null) return "Chưa dạy";
        switch(trangThai) {
            case 0: return "Chưa dạy";
            case 1: return "Đã dạy";
            case 2: return "Hủy";
            case 3: return "Hoãn";
            default: return "Chưa dạy";
        }
    }

    /**
     * Group schedules by date and session with enhanced logic
     */
    private Map<String, Map<String, List<LichGiangDay>>> groupSchedulesByDateAndSession(List<LichGiangDay> schedules) {
        Map<String, Map<String, List<LichGiangDay>>> result = new HashMap<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");

        System.out.println("Grouping " + schedules.size() + " schedules by date and session...");

        for (LichGiangDay schedule : schedules) {
            if (schedule.getNgayGiang() != null) {
                String dateKey = dateFormat.format(schedule.getNgayGiang());

                // Determine session based on buoi or tietBatDau
                String session = determineSession(schedule);

                result.computeIfAbsent(dateKey, k -> new HashMap<>())
                      .computeIfAbsent(session, k -> new ArrayList<>())
                      .add(schedule);

                System.out.println("Added schedule: " + dateKey + " - " + session +
                                 " - " + (schedule.getMonHoc() != null ? schedule.getMonHoc().getTenMonHoc() : "No subject"));
            } else {
                System.out.println("WARNING: Schedule with null ngayGiang found!");
            }
        }

        return result;
    }

    /**
     * Determine session based on schedule data
     */
    private String determineSession(LichGiangDay schedule) {
        // First try to use buoi field
        if (schedule.getBuoi() != null && !schedule.getBuoi().trim().isEmpty()) {
            String buoi = schedule.getBuoi().toUpperCase().trim();
            if (buoi.equals("SANG") || buoi.equals("S") || buoi.equals("MORNING")) {
                return "SANG";
            } else if (buoi.equals("CHIEU") || buoi.equals("C") || buoi.equals("AFTERNOON")) {
                return "CHIEU";
            } else if (buoi.equals("TOI") || buoi.equals("T") || buoi.equals("EVENING")) {
                return "TOI";
            }
        }

        // If buoi is not available, determine by tietBatDau
        if (schedule.getTietBatDau() != null) {
            int tiet = schedule.getTietBatDau();
            if (tiet >= 1 && tiet <= 5) {
                return "SANG";
            } else if (tiet >= 6 && tiet <= 10) {
                return "CHIEU";
            } else if (tiet >= 11 && tiet <= 15) {
                return "TOI";
            }
        }

        // Default to morning session
        return "SANG";
    }

    /**
     * Get session text for display
     */
    private String getSessionText(String session) {
        if (session == null) return "S";
        switch(session) {
            case "SANG": return "S";
            case "CHIEU": return "C";
            case "TOI": return "T";
            default: return "S";
        }
    }

    /**
     * Build schedule content string
     */
    private String buildScheduleContent(LichGiangDay schedule) {
        StringBuilder content = new StringBuilder();

        // Add subject and lesson info
        if (schedule.getMonHoc() != null) {
            content.append("LỚP: ").append(schedule.getMonHoc().getMaMonHoc()).append(" (SS)\n");
            content.append("MÔN: ").append(schedule.getMonHoc().getTenMonHoc()).append("\n");
        }

        if (schedule.getBaiHoc() != null) {
            content.append("Bài: ").append(schedule.getBaiHoc().getTenBaiHoc()).append("\n");
        }

        // Add class info
        if (schedule.getLop() != null) {
            content.append("Lớp: ").append(schedule.getLop().getTenLop()).append(". ");
        }

        // Add period info
        if (schedule.getTietBatDau() != null && schedule.getTietKetThuc() != null) {
            content.append(schedule.getTietBatDau()).append(" tiết. GD ").append(schedule.getTietKetThuc()).append(".\n");
        }

        // Add room info if available
        if (schedule.getPhongHoc() != null) {
            content.append("Phòng: ").append(schedule.getPhongHoc().getTenPhong());
        }

        return content.toString();
    }

    /**
     * Export teacher schedule to Excel with exact format like the image
     */
    @RequestMapping(value = "/xuat-excel-lich-giang-vien-format", produces = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
    public void xuatExcelLichGiangVienFormat(@RequestParam Integer idGiangVien,
                                           @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date tuNgay,
                                           @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date denNgay,
                                           HttpServletResponse response) throws IOException {
        try {
            // Get lecturer information
            CanBo giangVien = canBoSer.layTheoIdCanBo(idGiangVien);
            if (giangVien == null) {
                response.sendError(HttpServletResponse.SC_BAD_REQUEST, "Không tìm thấy giảng viên");
                return;
            }

            // Get teaching schedules for the lecturer in the specified period
            Pageable pageable = PageRequest.of(0, 1000);
            Page<LichGiangDay> schedules = lichGiangDaySer.timKiem(
                null, idGiangVien, null, null, null, null, null, null, null, null,
                tuNgay, denNgay, null, pageable);

            // Create Excel workbook
            XSSFWorkbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("Lịch Giảng Tuần");

            // Create the Excel with exact format
            createFormattedWeeklySchedule(workbook, sheet, schedules.getContent(), giangVien, tuNgay, denNgay);

            // Set response headers
            String fileName = "lich-giang-tuan-" + giangVien.getMaCanBo() + "-" +
                            new SimpleDateFormat("yyyyMMdd").format(tuNgay) + ".xlsx";
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName);

            // Write to response
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            response.getOutputStream().write(outputStream.toByteArray());
            workbook.close();

        } catch (Exception e) {
            e.printStackTrace();
            response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "Lỗi khi xuất Excel: " + e.getMessage());
        }
    }

    /**
     * Create formatted weekly schedule exactly like the image
     */
    private void createFormattedWeeklySchedule(XSSFWorkbook workbook, Sheet sheet, List<LichGiangDay> schedules,
                                             CanBo giangVien, Date tuNgay, Date denNgay) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
        SimpleDateFormat dayFormat = new SimpleDateFormat("dd");
        SimpleDateFormat monthFormat = new SimpleDateFormat("MM");
        SimpleDateFormat yearFormat = new SimpleDateFormat("yyyy");

        // Create styles
        CellStyle headerLeftStyle = createHeaderLeftStyle(workbook);
        CellStyle headerRightStyle = createHeaderRightStyle(workbook);
        CellStyle titleStyle = createTitleStyle(workbook);
        CellStyle tableHeaderStyle = createTableHeaderStyle(workbook);
        CellStyle tableCellStyle = createTableCellStyle(workbook);
        CellStyle centerStyle = createCenterStyle(workbook);
        CellStyle yellowCellStyle = createYellowCellStyle(workbook);

        int rowNum = 0;

        // Header information - exactly like the image
        Row row1 = sheet.createRow(rowNum++);
        Cell cell1 = row1.createCell(0);
        cell1.setCellValue("UBND TỈNH CÀ MAU");
        cell1.setCellStyle(headerLeftStyle);

        Cell cell2 = row1.createCell(2);
        cell2.setCellValue("CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM");
        cell2.setCellStyle(headerRightStyle);

        Row row2 = sheet.createRow(rowNum++);
        Cell cell3 = row2.createCell(0);
        cell3.setCellValue("TRƯỜNG CAO ĐẲNG Y TẾ");
        cell3.setCellStyle(headerLeftStyle);

        Cell cell4 = row2.createCell(2);
        cell4.setCellValue("Độc lập - Tự do - Hạnh phúc");
        cell4.setCellStyle(headerRightStyle);

        // Empty row
        sheet.createRow(rowNum++);

        Row row3 = sheet.createRow(rowNum++);
        Cell cell5 = row3.createCell(2);
        cell5.setCellValue("Cà Mau, ngày " + dayFormat.format(new Date()) +
                          " tháng " + monthFormat.format(new Date()) +
                          " năm " + yearFormat.format(new Date()));
        cell5.setCellStyle(headerRightStyle);

        // Empty row
        sheet.createRow(rowNum++);

        // Title - dynamic based on week
        Row titleRow = sheet.createRow(rowNum++);
        Cell titleCell = titleRow.createCell(1);

        // Calculate week number
        java.util.Calendar cal = java.util.Calendar.getInstance();
        cal.setTime(tuNgay);
        int weekOfYear = cal.get(java.util.Calendar.WEEK_OF_YEAR);

        titleCell.setCellValue("LỊCH GIẢNG TUẦN " + weekOfYear + "- HK II");
        titleCell.setCellStyle(titleStyle);
        sheet.addMergedRegion(new CellRangeAddress(rowNum-1, rowNum-1, 1, 3));

        Row dateRow = sheet.createRow(rowNum++);
        Cell dateCell = dateRow.createCell(1);
        dateCell.setCellValue("Từ ngày " + dateFormat.format(tuNgay) + " đến ngày " + dateFormat.format(denNgay));
        dateCell.setCellStyle(centerStyle);
        sheet.addMergedRegion(new CellRangeAddress(rowNum-1, rowNum-1, 1, 3));

        // Empty row
        sheet.createRow(rowNum++);

        Row lecturerRow = sheet.createRow(rowNum++);
        Cell lecturerCell = lecturerRow.createCell(1);
        lecturerCell.setCellValue("Gv: " + giangVien.getHoTen() + " (" + giangVien.getMaCanBo() + ")");
        lecturerCell.setCellStyle(centerStyle);
        sheet.addMergedRegion(new CellRangeAddress(rowNum-1, rowNum-1, 1, 3));

        // Empty row
        sheet.createRow(rowNum++);

        // Create schedule table exactly like the image
        int tableEndRow = createExactScheduleTable(sheet, schedules, tuNgay, denNgay, rowNum, tableHeaderStyle, tableCellStyle, yellowCellStyle);

        // Add footer signature like in the image
        createFooterSignature(sheet, tableEndRow + 2, headerLeftStyle, headerRightStyle);

        // Set column widths to match the image
        sheet.setColumnWidth(0, 3500);  // Thứ-Ngày
        sheet.setColumnWidth(1, 1500);  // Buổi
        sheet.setColumnWidth(2, 2000);  // Thời gian
        sheet.setColumnWidth(3, 12000); // Nội dung
    }

    /**
     * Create cell styles exactly like the image
     */
    private CellStyle createHeaderLeftStyle(XSSFWorkbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 11);
        font.setFontName("Times New Roman");
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.LEFT);
        return style;
    }

    private CellStyle createHeaderRightStyle(XSSFWorkbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 11);
        font.setFontName("Times New Roman");
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        return style;
    }

    private CellStyle createTitleStyle(XSSFWorkbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 14);
        font.setFontName("Times New Roman");
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        return style;
    }

    private CellStyle createTableHeaderStyle(XSSFWorkbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 10);
        font.setFontName("Times New Roman");
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setWrapText(true);
        return style;
    }

    private CellStyle createTableCellStyle(XSSFWorkbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 9);
        font.setFontName("Times New Roman");
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.LEFT);
        style.setVerticalAlignment(VerticalAlignment.TOP);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setWrapText(true);
        return style;
    }

    private CellStyle createYellowCellStyle(XSSFWorkbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 9);
        font.setFontName("Times New Roman");
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.LEFT);
        style.setVerticalAlignment(VerticalAlignment.TOP);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setWrapText(true);
        return style;
    }

    private CellStyle createCenterStyle(XSSFWorkbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 11);
        font.setFontName("Times New Roman");
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        return style;
    }

    /**
     * Create schedule table exactly like the image
     */
    private int createExactScheduleTable(Sheet sheet, List<LichGiangDay> schedules, Date tuNgay, Date denNgay,
                                        int startRow, CellStyle headerStyle, CellStyle cellStyle, CellStyle yellowStyle) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
        int rowNum = startRow;

        // Table header exactly like image
        Row headerRow = sheet.createRow(rowNum++);
        Cell headerCell1 = headerRow.createCell(0);
        headerCell1.setCellValue("Thứ-Ngày");
        headerCell1.setCellStyle(headerStyle);

        Cell headerCell2 = headerRow.createCell(1);
        headerCell2.setCellValue("Buổi\nphan");
        headerCell2.setCellStyle(headerStyle);

        Cell headerCell3 = headerRow.createCell(2);
        headerCell3.setCellValue("Thời\ngian");
        headerCell3.setCellStyle(headerStyle);

        Cell headerCell4 = headerRow.createCell(3);
        headerCell4.setCellValue("NỘI DUNG GIẢNG DẠY");
        headerCell4.setCellStyle(headerStyle);

        // Set header row height
        headerRow.setHeightInPoints(30);

        // Group schedules by date and session
        Map<String, Map<String, List<LichGiangDay>>> schedulesByDate = groupSchedulesByDateAndSession(schedules);

        // Create schedule rows for each day of the week exactly like image
        String[] daysOfWeek = {"THỨ 2", "THỨ 3", "THỨ 4", "THỨ 5", "THỨ 6", "THỨ 7", "CHỦ NHẬT"};
        String[] sessions = {"S", "C", "T"};
        String[] times = {"7h30", "13h30", "18h30"};

        for (int i = 0; i < 7; i++) {
            java.util.Calendar cal = java.util.Calendar.getInstance();
            cal.setTime(tuNgay);
            cal.add(java.util.Calendar.DAY_OF_MONTH, i);
            Date currentDate = cal.getTime();
            String dateKey = dateFormat.format(currentDate);

            Map<String, List<LichGiangDay>> daySchedules = schedulesByDate.get(dateKey);

            // Create 3 rows for each day (S, C, T) like in the image
            for (int j = 0; j < 3; j++) {
                Row scheduleRow = sheet.createRow(rowNum++);

                // Day cell - only show on first row (S)
                Cell dayCell = scheduleRow.createCell(0);
                if (j == 0) {
                    dayCell.setCellValue(daysOfWeek[i] + " -\n" + dateKey);
                } else {
                    dayCell.setCellValue("");
                }
                dayCell.setCellStyle(cellStyle);

                // Session cell
                Cell sessionCell = scheduleRow.createCell(1);
                sessionCell.setCellValue(sessions[j]);
                sessionCell.setCellStyle(cellStyle);

                // Time cell
                Cell timeCell = scheduleRow.createCell(2);
                timeCell.setCellValue(times[j]);
                timeCell.setCellStyle(cellStyle);

                // Content cell
                Cell contentCell = scheduleRow.createCell(3);
                String content = "";
                boolean hasContent = false;

                if (daySchedules != null) {
                    String sessionKey = (j == 0) ? "SANG" : (j == 1) ? "CHIEU" : "TOI";
                    List<LichGiangDay> sessionSchedules = daySchedules.get(sessionKey);
                    if (sessionSchedules != null && !sessionSchedules.isEmpty()) {
                        content = buildExactScheduleContent(sessionSchedules.get(0));
                        hasContent = true;
                    }
                }

                contentCell.setCellValue(content);
                // Use yellow background for cells with content like in the image
                contentCell.setCellStyle(hasContent ? yellowStyle : cellStyle);

                // Set row height
                scheduleRow.setHeightInPoints(hasContent ? 80 : 25);
            }
        }

        return rowNum;
    }

    /**
     * Create footer signature like in the image
     */
    private void createFooterSignature(Sheet sheet, int startRow, CellStyle leftStyle, CellStyle rightStyle) {
        int rowNum = startRow;

        // Empty row
        sheet.createRow(rowNum++);

        // Ghi chú row
        Row noteRow = sheet.createRow(rowNum++);
        Cell noteCell = noteRow.createCell(0);
        noteCell.setCellValue("Ghi chú:");
        noteCell.setCellStyle(leftStyle);

        // Contact info row
        Row contactRow = sheet.createRow(rowNum++);
        Cell contactCell = contactRow.createCell(0);
        contactCell.setCellValue("Mọi thắc mắc xin liên hệ về Phòng Đào tạo (02903.828304) hoặc gặp CN. Thơi (0944.269.629).");
        contactCell.setCellStyle(leftStyle);
        sheet.addMergedRegion(new CellRangeAddress(rowNum-1, rowNum-1, 0, 3));

        // Empty row
        sheet.createRow(rowNum++);

        // Signature row
        Row signatureRow = sheet.createRow(rowNum++);
        Cell leftSignCell = signatureRow.createCell(0);
        leftSignCell.setCellValue("HIỆU TRƯỞNG");
        leftSignCell.setCellStyle(leftStyle);

        Cell rightSignCell = signatureRow.createCell(2);
        rightSignCell.setCellValue("TRƯỞNG PHÒNG");
        rightSignCell.setCellStyle(rightStyle);

        // Empty rows for signature space
        sheet.createRow(rowNum++);
        sheet.createRow(rowNum++);
        sheet.createRow(rowNum++);

        // Name rows
        Row nameRow = sheet.createRow(rowNum++);
        Cell leftNameCell = nameRow.createCell(0);
        leftNameCell.setCellValue("(Bá ký)");
        leftNameCell.setCellStyle(leftStyle);

        Cell rightNameCell = nameRow.createCell(2);
        rightNameCell.setCellValue("(Bá ký)");
        rightNameCell.setCellStyle(rightStyle);

        // Empty row
        sheet.createRow(rowNum++);

        // Final name row
        Row finalNameRow = sheet.createRow(rowNum++);
        Cell leftFinalCell = finalNameRow.createCell(0);
        leftFinalCell.setCellValue("TS. Huỳnh Ngọc Linh");
        leftFinalCell.setCellStyle(leftStyle);

        Cell rightFinalCell = finalNameRow.createCell(2);
        rightFinalCell.setCellValue("ThS. Vũ Văn Hướng");
        rightFinalCell.setCellStyle(rightStyle);
    }

    /**
     * Build exact schedule content like in the image
     */
    private String buildExactScheduleContent(LichGiangDay schedule) {
        StringBuilder content = new StringBuilder();

        // Format exactly like the image
        if (schedule.getMonHoc() != null) {
            // LỚP CS: ĐIỀU DƯỠNG CHÍNH QUY 23A (SS)
            String lopInfo = "LỚP CS: ";
            if (schedule.getLop() != null) {
                lopInfo += schedule.getLop().getTenLop();
            } else {
                lopInfo += "ĐIỀU DƯỠNG CHÍNH QUY 23A";
            }
            lopInfo += " (SS)";
            content.append(lopInfo).append("\n");

            // MÔN: Y HỌC CỔ TRUYỀN- PHCN
            String monHocDisplay = schedule.getMonHoc().getTenMonHoc();
            if (monHocDisplay.length() > 30) {
                monHocDisplay = monHocDisplay.substring(0, 30) + "...";
            }
            content.append("MÔN: ").append(monHocDisplay).append("- PHCN").append("\n");
        }

        // Add lesson content exactly like image
        if (schedule.getBaiHoc() != null) {
            String baiHocTen = schedule.getBaiHoc().getTenBaiHoc();
            if (baiHocTen == null || baiHocTen.trim().isEmpty()) {
                baiHocTen = "Học chuyên đề tăng trưởng";
            }
            content.append("Bài: ").append(baiHocTen).append("\n");

            // Add second lesson line like in image
            String noiDungBai = "Nguyên nhân gây bệnh";
            if (schedule.getBaiHoc().getNoiDung() != null && !schedule.getBaiHoc().getNoiDung().trim().isEmpty()) {
                noiDungBai = schedule.getBaiHoc().getNoiDung();
                if (noiDungBai.length() > 25) {
                    noiDungBai = noiDungBai.substring(0, 25);
                }
            }

            // Add period info exactly like image format
            if (schedule.getTietBatDau() != null && schedule.getTietKetThuc() != null) {
                int soTiet = schedule.getTietKetThuc() - schedule.getTietBatDau() + 1;
                content.append("Bài: ").append(noiDungBai).append(". ").append(soTiet).append(" tiết. GD ").append(schedule.getTietKetThuc()).append(".");
            } else {
                content.append("Bài: ").append(noiDungBai).append(". 4 tiết. GD 9.");
            }
        }

        return content.toString();
    }

    /**
     * Build detailed schedule content like in the image
     */
    private String buildDetailedScheduleContent(LichGiangDay schedule) {
        StringBuilder content = new StringBuilder();

        // Add subject code and type
        if (schedule.getMonHoc() != null) {
            content.append("LỚP: ").append(schedule.getMonHoc().getMaMonHoc()).append(" (SS)\n");
            content.append("MÔN: ").append(schedule.getMonHoc().getTenMonHoc()).append("\n");
        }

        // Add lesson content
        if (schedule.getBaiHoc() != null) {
            content.append("Bài: ").append(schedule.getBaiHoc().getTenBaiHoc()).append("\n");
        }

        // Add class and period info
        if (schedule.getLop() != null) {
            content.append("Lớp: ").append(schedule.getLop().getTenLop()).append(". ");
        }

        if (schedule.getTietBatDau() != null && schedule.getTietKetThuc() != null) {
            int soTiet = schedule.getTietKetThuc() - schedule.getTietBatDau() + 1;
            content.append(soTiet).append(" tiết. GD ").append(schedule.getTietKetThuc()).append(".");
        }

        return content.toString();
    }

    /**
     * Export teacher schedule using template file
     */
    @RequestMapping(value = "/xuat-excel-lich-giang-vien-mau", produces = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
    public void xuatExcelLichGiangVienMau(@RequestParam Integer idGiangVien,
                                         @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date tuNgay,
                                         @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date denNgay,
                                         HttpServletResponse response) throws IOException {
        try {
            // Get lecturer information
            CanBo giangVien = canBoSer.layTheoIdCanBo(idGiangVien);
            if (giangVien == null) {
                response.sendError(HttpServletResponse.SC_BAD_REQUEST, "Không tìm thấy giảng viên");
                return;
            }

            // Get teaching schedules for the lecturer in the specified period
            Pageable pageable = PageRequest.of(0, 1000);
            Page<LichGiangDay> schedules = lichGiangDaySer.timKiem(
                null, idGiangVien, null, null, null, null, null, null, null, null,
                tuNgay, denNgay, null, pageable);

            // Load template file with error handling
            XSSFWorkbook workbook = null;
            InputStream templateStream = null;

            try {
                ClassPathResource templateResource = new ClassPathResource("/static/mau/lich-giang-vien-tuan.xlsx");

                if (!templateResource.exists()) {
                    System.out.println("Template file not found, creating blank workbook");
                    workbook = createBlankWorkbook();
                } else {
                    templateStream = templateResource.getInputStream();
                    System.out.println("Loading template file: " + templateResource.getFilename());
                    workbook = new XSSFWorkbook(templateStream);
                }

            } catch (Exception e) {
                System.out.println("Error loading template file: " + e.getMessage());
                System.out.println("Creating blank workbook as fallback");

                // Close the problematic stream if it was opened
                if (templateStream != null) {
                    try {
                        templateStream.close();
                    } catch (Exception closeEx) {
                        // Ignore close exception
                    }
                }

                // Create blank workbook as fallback
                workbook = createBlankWorkbook();
            }

            Sheet sheet = workbook.getSheetAt(0); // Get first sheet

            // Fill data into template
            fillDataIntoTemplate(sheet, schedules.getContent(), giangVien, tuNgay, denNgay);

            // Set response headers
            String fileName = "lich-giang-tuan-" + giangVien.getMaCanBo() + "-" +
                            new SimpleDateFormat("yyyyMMdd").format(tuNgay) + ".xlsx";
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName);

            // Write to response
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            response.getOutputStream().write(outputStream.toByteArray());

            // Close resources
            workbook.close();
            if (templateStream != null) {
                templateStream.close();
            }

        } catch (Exception e) {
            e.printStackTrace();
            response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "Lỗi khi xuất Excel: " + e.getMessage());
        }
    }

    /**
     * Export class schedule using template file
     */
    @RequestMapping(value = "/xuat-excel-lich-lop-mau", produces = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
    public void xuatExcelLichLopMau(@RequestParam Integer idLop,
                                   @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date tuNgay,
                                   @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date denNgay,
                                   HttpServletResponse response) throws IOException {
        try {
            // Get class information
            Lop lop = lopSer.layLopTheoIdLop(idLop);
            if (lop == null) {
                response.sendError(HttpServletResponse.SC_BAD_REQUEST, "Không tìm thấy lớp");
                return;
            }

            // Get teaching schedules for the class in the specified period
            Pageable pageable = PageRequest.of(0, 1000);
            Page<LichGiangDay> schedules = lichGiangDaySer.timKiem(
                null, null, null, idLop, null, null, null, null, null, null,
                tuNgay, denNgay, null, pageable);

            // Load template file with error handling
            XSSFWorkbook workbook = null;
            InputStream templateStream = null;

            try {
                ClassPathResource templateResource = new ClassPathResource("static/mau/lich-giang-lop-tuan.xlsx");

                if (!templateResource.exists()) {
                    System.out.println("Class template file not found, creating blank workbook");
                    workbook = createBlankClassWorkbook();
                } else {
                    templateStream = templateResource.getInputStream();
                    System.out.println("Loading class template file: " + templateResource.getFilename());
                    workbook = new XSSFWorkbook(templateStream);
                }

            } catch (Exception e) {
                System.out.println("Error loading class template file: " + e.getMessage());
                System.out.println("Creating blank class workbook as fallback");

                // Close the problematic stream if it was opened
                if (templateStream != null) {
                    try {
                        templateStream.close();
                    } catch (Exception closeEx) {
                        // Ignore close exception
                    }
                }

                // Create blank workbook as fallback
                workbook = createBlankClassWorkbook();
            }

            Sheet sheet = workbook.getSheetAt(0); // Get first sheet

            // Fill data into template
            fillDataIntoClassTemplate(sheet, schedules.getContent(), lop, tuNgay, denNgay);

            // Set response headers
            String fileName = "lich-giang-lop-" + lop.getMaLop() + "-" +
                            new SimpleDateFormat("yyyyMMdd").format(tuNgay) + ".xlsx";
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName);

            // Write to response
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            response.getOutputStream().write(outputStream.toByteArray());

            // Close resources
            workbook.close();
            if (templateStream != null) {
                templateStream.close();
            }

        } catch (Exception e) {
            e.printStackTrace();
            response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "Lỗi khi xuất Excel: " + e.getMessage());
        }
    }

    /**
     * Export all classes schedule using dynamic table creation
     */
    @RequestMapping(value = "/xuat-excel-lich-toan-truong", produces = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
    public void xuatExcelLichToanTruong(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date tuNgay,
                                       @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date denNgay,
                                       HttpServletResponse response) throws IOException {
        try {
            // Get teaching schedules for all classes in the specified period
            Page<LichGiangDay> schedules = lichGiangDaySer.timKiem(
                null, null, null, null, null, null, null, null, null, null,
                tuNgay, denNgay, null, PageRequest.of(0, 10000));

            // Get only classes that have schedules in this period
            List<Lop> classesWithSchedules = getClassesWithSchedules(schedules.getContent());

            if (classesWithSchedules.isEmpty()) {
                response.sendError(HttpServletResponse.SC_BAD_REQUEST, "Không có lớp nào có lịch giảng trong khoảng thời gian này");
                return;
            }

            // Create workbook with dynamic tables
            XSSFWorkbook workbook = createSchoolWideScheduleWorkbook(classesWithSchedules, schedules.getContent(), tuNgay, denNgay);

            // Set response headers
            String fileName = "lich-giang-toan-truong-" +
                            new SimpleDateFormat("yyyyMMdd").format(tuNgay) + ".xlsx";
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName);

            // Write to response
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            response.getOutputStream().write(outputStream.toByteArray());

            // Close resources
            workbook.close();

        } catch (Exception e) {
            e.printStackTrace();
            response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "Lỗi khi xuất Excel: " + e.getMessage());
        }
    }

    /**
     * Create blank workbook with basic template structure
     */
    private XSSFWorkbook createBlankWorkbook() {
        System.out.println("Creating blank workbook with basic template structure...");

        XSSFWorkbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Lịch Giảng Tuần");

        // Create basic template structure
        int rowNum = 0;

        // Header
        Row row1 = sheet.createRow(rowNum++);
        row1.createCell(0).setCellValue("UBND TỈNH CÀ MAU");
        row1.createCell(3).setCellValue("CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM");

        Row row2 = sheet.createRow(rowNum++);
        row2.createCell(0).setCellValue("TRƯỜNG CAO ĐẲNG Y TẾ");
        row2.createCell(3).setCellValue("Độc lập - Tự do - Hạnh phúc");

        sheet.createRow(rowNum++); // Empty row

        Row row3 = sheet.createRow(rowNum++);
        row3.createCell(3).setCellValue("Cà Mau, ngày {{CURRENT_DAY}} tháng {{CURRENT_MONTH}} năm {{CURRENT_YEAR}}");

        sheet.createRow(rowNum++); // Empty row

        // Title
        Row titleRow = sheet.createRow(rowNum++);
        titleRow.createCell(1).setCellValue("LỊCH GIẢNG TUẦN {{WEEK_NUMBER}}- {{HOC_KY}}");

        Row dateRow = sheet.createRow(rowNum++);
        dateRow.createCell(1).setCellValue("Từ ngày {{START_DATE}} đến ngày {{END_DATE}}");

        sheet.createRow(rowNum++); // Empty row

        Row lecturerRow = sheet.createRow(rowNum++);
        lecturerRow.createCell(1).setCellValue("{{LECTURER_FULL}}");

        sheet.createRow(rowNum++); // Empty row

        // Table header
        Row headerRow = sheet.createRow(rowNum++);
        headerRow.createCell(0).setCellValue("Thứ-Ngày");
        headerRow.createCell(1).setCellValue("Buổi\nphan");
        headerRow.createCell(2).setCellValue("Thời\ngian");
        headerRow.createCell(3).setCellValue("NỘI DUNG GIẢNG DẠY");

        // Table content - 21 rows (7 days x 3 sessions)
        String[] sessions = {"S", "C", "T"};
        String[] times = {"7h30", "13h30", "18h30"};

        for (int day = 1; day <= 7; day++) {
            for (int session = 0; session < 3; session++) {
                Row dataRow = sheet.createRow(rowNum++);

                if (session == 0) {
                    dataRow.createCell(0).setCellValue("{{DAY_" + day + "_DATE}}");
                } else {
                    dataRow.createCell(0).setCellValue("");
                }

                dataRow.createCell(1).setCellValue(sessions[session]);
                dataRow.createCell(2).setCellValue(times[session]);
                dataRow.createCell(3).setCellValue("{{DAY_" + day + "_" +
                    (session == 0 ? "SANG" : session == 1 ? "CHIEU" : "TOI") + "}}");
            }
        }

        // Set column widths
        sheet.setColumnWidth(0, 3500);
        sheet.setColumnWidth(1, 1500);
        sheet.setColumnWidth(2, 2000);
        sheet.setColumnWidth(3, 12000);

        System.out.println("Created blank workbook with " + rowNum + " rows");
        return workbook;
    }

    /**
     * Create blank class workbook with basic template structure
     */
    private XSSFWorkbook createBlankClassWorkbook() {
        System.out.println("Creating blank class workbook with basic template structure...");

        XSSFWorkbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Lịch Giảng Lớp");

        // Create basic template structure
        int rowNum = 0;

        // Header
        Row row1 = sheet.createRow(rowNum++);
        row1.createCell(0).setCellValue("UBND TỈNH CÀ MAU");
        row1.createCell(3).setCellValue("CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM");

        Row row2 = sheet.createRow(rowNum++);
        row2.createCell(0).setCellValue("TRƯỜNG CAO ĐẲNG Y TẾ");
        row2.createCell(3).setCellValue("Độc lập - Tự do - Hạnh phúc");

        sheet.createRow(rowNum++); // Empty row

        Row row3 = sheet.createRow(rowNum++);
        row3.createCell(3).setCellValue("Cà Mau, ngày {{CURRENT_DAY}} tháng {{CURRENT_MONTH}} năm {{CURRENT_YEAR}}");

        sheet.createRow(rowNum++); // Empty row

        // Title
        Row titleRow = sheet.createRow(rowNum++);
        titleRow.createCell(1).setCellValue("LỊCH GIẢNG TUẦN {{WEEK_NUMBER}}- {{HOC_KY}}");

        Row dateRow = sheet.createRow(rowNum++);
        dateRow.createCell(1).setCellValue("Từ ngày {{START_DATE}} đến ngày {{END_DATE}}");

        sheet.createRow(rowNum++); // Empty row

        Row classRow = sheet.createRow(rowNum++);
        classRow.createCell(1).setCellValue("Lớp: {{TEN_LOP}}");

        sheet.createRow(rowNum++); // Empty row

        // Table header
        Row headerRow = sheet.createRow(rowNum++);
        headerRow.createCell(0).setCellValue("Thứ-Ngày");
        headerRow.createCell(1).setCellValue("Buổi\nphan");
        headerRow.createCell(2).setCellValue("Thời\ngian");
        headerRow.createCell(3).setCellValue("NỘI DUNG GIẢNG DẠY");

        // Table content - 21 rows (7 days x 3 sessions)
        String[] sessions = {"S", "C", "T"};
        String[] times = {"7h30", "13h30", "18h30"};

        for (int day = 1; day <= 7; day++) {
            for (int session = 0; session < 3; session++) {
                Row dataRow = sheet.createRow(rowNum++);

                if (session == 0) {
                    dataRow.createCell(0).setCellValue("{{DAY_" + day + "_DATE}}");
                } else {
                    dataRow.createCell(0).setCellValue("");
                }

                dataRow.createCell(1).setCellValue(sessions[session]);
                dataRow.createCell(2).setCellValue(times[session]);
                dataRow.createCell(3).setCellValue("{{DAY_" + day + "_" +
                    (session == 0 ? "SANG" : session == 1 ? "CHIEU" : "TOI") + "}}");
            }
        }

        // Set column widths
        sheet.setColumnWidth(0, 3500);
        sheet.setColumnWidth(1, 1500);
        sheet.setColumnWidth(2, 2000);
        sheet.setColumnWidth(3, 12000);

        System.out.println("Created blank class workbook with " + rowNum + " rows");
        return workbook;
    }

    /**
     * Fill data into class template using variable replacement approach
     */
    private void fillDataIntoClassTemplate(Sheet sheet, List<LichGiangDay> schedules, Lop lop, Date tuNgay, Date denNgay) {
        System.out.println("=== STARTING CLASS TEMPLATE VARIABLE REPLACEMENT ===");
        System.out.println("Class: " + lop.getTenLop() + " (" + lop.getMaLop() + ")");
        System.out.println("Date range: " + tuNgay + " to " + denNgay);
        System.out.println("Number of schedules: " + schedules.size());

        SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
        SimpleDateFormat dayFormat = new SimpleDateFormat("dd");
        SimpleDateFormat monthFormat = new SimpleDateFormat("MM");
        SimpleDateFormat yearFormat = new SimpleDateFormat("yyyy");

        // Create replacement map
        Map<String, String> replacements = createClassReplacementMap(schedules, lop, tuNgay, denNgay, dateFormat, dayFormat, monthFormat, yearFormat);

        // Replace all variables in the template
        replaceVariablesInTemplate(sheet, replacements);

        System.out.println("\n=== CLASS TEMPLATE REPLACEMENT COMPLETED ===");
    }

    /**
     * Create replacement map for class template variables
     */
    private Map<String, String> createClassReplacementMap(List<LichGiangDay> schedules, Lop lop, Date tuNgay, Date denNgay,
                                                         SimpleDateFormat dateFormat, SimpleDateFormat dayFormat,
                                                         SimpleDateFormat monthFormat, SimpleDateFormat yearFormat) {
        Map<String, String> replacements = new HashMap<>();

        // Basic information
        replacements.put("{{CURRENT_DAY}}", dayFormat.format(new Date()));
        replacements.put("{{CURRENT_MONTH}}", monthFormat.format(new Date()));
        replacements.put("{{CURRENT_YEAR}}", yearFormat.format(new Date()));
        replacements.put("{{START_DATE}}", dateFormat.format(tuNgay));
        replacements.put("{{END_DATE}}", dateFormat.format(denNgay));

        // Week calculation
        java.util.Calendar cal = java.util.Calendar.getInstance();
        cal.setTime(tuNgay);
        int weekOfYear = cal.get(java.util.Calendar.WEEK_OF_YEAR);
        replacements.put("{{WEEK_NUMBER}}", String.valueOf(weekOfYear));

        // Semester information
        String hocKy = determineHocKy(tuNgay);
        replacements.put("{{HOC_KY}}", hocKy);

        // Class information
        replacements.put("{{TEN_LOP}}", lop.getTenLop());

        // Group schedules by date and session
        Map<String, Map<String, List<LichGiangDay>>> schedulesByDate = groupSchedulesByDateAndSession(schedules);

        // Create schedule content for each day and session
        String[] dayNames = {"THỨ 2", "THỨ 3", "THỨ 4", "THỨ 5", "THỨ 6", "THỨ 7", "CHỦ NHẬT"};
        String[] sessions = {"SANG", "CHIEU", "TOI"};

        for (int dayIndex = 0; dayIndex < 7; dayIndex++) {
            cal.setTime(tuNgay);
            cal.add(java.util.Calendar.DAY_OF_MONTH, dayIndex);
            Date currentDate = cal.getTime();
            String dateKey = dateFormat.format(currentDate);

            Map<String, List<LichGiangDay>> daySchedules = schedulesByDate.get(dateKey);

            // Day-Date variable
            replacements.put("{{DAY_" + (dayIndex + 1) + "_DATE}}", dayNames[dayIndex] + " -\n" + dateKey);

            // Session content variables
            for (int sessionIndex = 0; sessionIndex < 3; sessionIndex++) {
                String sessionKey = sessions[sessionIndex];
                String content = "";

                if (daySchedules != null) {
                    List<LichGiangDay> sessionSchedules = daySchedules.get(sessionKey);
                    if (sessionSchedules != null && !sessionSchedules.isEmpty()) {
                        content = buildClassScheduleContent(sessionSchedules.get(0));
                    }
                }

                String variableKey = "{{DAY_" + (dayIndex + 1) + "_" + sessionKey + "}}";
                replacements.put(variableKey, content);

                System.out.println("Created variable: " + variableKey + " = " +
                                 (content.isEmpty() ? "EMPTY" : content.substring(0, Math.min(30, content.length())) + "..."));
            }
        }

        System.out.println("Created " + replacements.size() + " replacement variables for class template");
        return replacements;
    }

    /**
     * Build schedule content for class template
     */
    private String buildClassScheduleContent(LichGiangDay schedule) {
        StringBuilder content = new StringBuilder();

        try {
            // Format for class schedule (focus on subject and lecturer)
            if (schedule.getMonHoc() != null) {
                // MÔN: [Subject Name]- PHCN
                String monHocDisplay = schedule.getMonHoc().getTenMonHoc();
                if (monHocDisplay != null) {
                    if (monHocDisplay.length() > 35) {
                        monHocDisplay = monHocDisplay.substring(0, 35) + "...";
                    }
                    content.append("MÔN: ").append(monHocDisplay);

                    // Add subject type based on hinhThuc
                    if (schedule.getHinhThuc() != null) {
                        if (schedule.getHinhThuc().equals("TH")) {
                            content.append("- THỰC HÀNH");
                        } else {
                            content.append("- PHCN");
                        }
                    } else {
                        content.append("- PHCN");
                    }
                    content.append("\n");
                }
            }

            // Add lecturer information
            if (schedule.getGiangVien() != null) {
                content.append("GV: ").append(schedule.getGiangVien().getHoTen());
                if (schedule.getGiangVien().getMaCanBo() != null) {
                    content.append(" (").append(schedule.getGiangVien().getMaCanBo()).append(")");
                }
                content.append("\n");
            }

            // Add lesson content
            if (schedule.getBaiHoc() != null) {
                String baiHocTen = schedule.getBaiHoc().getTenBaiHoc();
                if (baiHocTen != null && !baiHocTen.trim().isEmpty()) {
                    content.append("Bài: ").append(baiHocTen).append("\n");
                }

                // Add detailed lesson content
                String noiDungBai = "Nguyên nhân gây bệnh";
                if (schedule.getBaiHoc().getNoiDung() != null && !schedule.getBaiHoc().getNoiDung().trim().isEmpty()) {
                    noiDungBai = schedule.getBaiHoc().getNoiDung();
                    if (noiDungBai.length() > 30) {
                        noiDungBai = noiDungBai.substring(0, 30) + "...";
                    }
                }

                // Add period info
                if (schedule.getTietBatDau() != null && schedule.getTietKetThuc() != null) {
                    int soTiet = schedule.getTietKetThuc() - schedule.getTietBatDau() + 1;
                    content.append("Bài: ").append(noiDungBai).append(". ")
                           .append(soTiet).append(" tiết. GD ").append(schedule.getTietKetThuc()).append(".");
                } else {
                    content.append("Bài: ").append(noiDungBai).append(". 4 tiết. GD 9.");
                }
            }

            // Add room info if available
            if (schedule.getPhongHoc() != null && schedule.getPhongHoc().getTenPhong() != null) {
                content.append("\nPhòng: ").append(schedule.getPhongHoc().getTenPhong());
            }

        } catch (Exception e) {
            System.out.println("Error building class schedule content: " + e.getMessage());
            content.append("Lỗi hiển thị nội dung");
        }

        return content.toString();
    }

    /**
     * Fill data into template using variable replacement approach
     */
    private void fillDataIntoTemplate(Sheet sheet, List<LichGiangDay> schedules, CanBo giangVien, Date tuNgay, Date denNgay) {
        System.out.println("=== STARTING TEMPLATE VARIABLE REPLACEMENT ===");
        System.out.println("Lecturer: " + giangVien.getHoTen() + " (" + giangVien.getMaCanBo() + ")");
        System.out.println("Date range: " + tuNgay + " to " + denNgay);
        System.out.println("Number of schedules: " + schedules.size());

        SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
        SimpleDateFormat dayFormat = new SimpleDateFormat("dd");
        SimpleDateFormat monthFormat = new SimpleDateFormat("MM");
        SimpleDateFormat yearFormat = new SimpleDateFormat("yyyy");

        // Create replacement map
        Map<String, String> replacements = createReplacementMap(schedules, giangVien, tuNgay, denNgay, dateFormat, dayFormat, monthFormat, yearFormat);

        // Replace all variables in the template
        replaceVariablesInTemplate(sheet, replacements);

        System.out.println("\n=== TEMPLATE REPLACEMENT COMPLETED ===");
    }

    /**
     * Create replacement map for all template variables
     */
    private Map<String, String> createReplacementMap(List<LichGiangDay> schedules, CanBo giangVien, Date tuNgay, Date denNgay,
                                                    SimpleDateFormat dateFormat, SimpleDateFormat dayFormat,
                                                    SimpleDateFormat monthFormat, SimpleDateFormat yearFormat) {
        Map<String, String> replacements = new HashMap<>();

        // Basic information
        replacements.put("{{CURRENT_DAY}}", dayFormat.format(new Date()));
        replacements.put("{{CURRENT_MONTH}}", monthFormat.format(new Date()));
        replacements.put("{{CURRENT_YEAR}}", yearFormat.format(new Date()));
        replacements.put("{{START_DATE}}", dateFormat.format(tuNgay));
        replacements.put("{{END_DATE}}", dateFormat.format(denNgay));

        // Week calculation
        java.util.Calendar cal = java.util.Calendar.getInstance();
        cal.setTime(tuNgay);
        int weekOfYear = cal.get(java.util.Calendar.WEEK_OF_YEAR);
        replacements.put("{{WEEK_NUMBER}}", String.valueOf(weekOfYear));

        // Semester information
        String hocKy = determineHocKy(tuNgay);
        replacements.put("{{HOC_KY}}", hocKy);

        // Lecturer information
        replacements.put("{{LECTURER_NAME}}", giangVien.getHoTen());
        replacements.put("{{LECTURER_CODE}}", giangVien.getMaCanBo());
        replacements.put("{{LECTURER_FULL}}", "Gv: " + giangVien.getHoTen() + " (" + giangVien.getMaCanBo() + ")");

        // Group schedules by date and session
        Map<String, Map<String, List<LichGiangDay>>> schedulesByDate = groupSchedulesByDateAndSession(schedules);

        // Create schedule content for each day and session
        String[] dayNames = {"THỨ 2", "THỨ 3", "THỨ 4", "THỨ 5", "THỨ 6", "THỨ 7", "CHỦ NHẬT"};
        String[] sessions = {"SANG", "CHIEU", "TOI"};

        for (int dayIndex = 0; dayIndex < 7; dayIndex++) {
            cal.setTime(tuNgay);
            cal.add(java.util.Calendar.DAY_OF_MONTH, dayIndex);
            Date currentDate = cal.getTime();
            String dateKey = dateFormat.format(currentDate);

            Map<String, List<LichGiangDay>> daySchedules = schedulesByDate.get(dateKey);

            // Day-Date variable
            replacements.put("{{DAY_" + (dayIndex + 1) + "_DATE}}", dayNames[dayIndex] + " -\n" + dateKey);

            // Session content variables
            for (int sessionIndex = 0; sessionIndex < 3; sessionIndex++) {
                String sessionKey = sessions[sessionIndex];
                String content = "";

                if (daySchedules != null) {
                    List<LichGiangDay> sessionSchedules = daySchedules.get(sessionKey);
                    if (sessionSchedules != null && !sessionSchedules.isEmpty()) {
                        content = buildTemplateScheduleContent(sessionSchedules.get(0));
                    }
                }

                String variableKey = "{{DAY_" + (dayIndex + 1) + "_" + sessionKey + "}}";
                replacements.put(variableKey, content);

                System.out.println("Created variable: " + variableKey + " = " +
                                 (content.isEmpty() ? "EMPTY" : content.substring(0, Math.min(30, content.length())) + "..."));
            }
        }

        System.out.println("Created " + replacements.size() + " replacement variables");
        return replacements;
    }

    /**
     * Replace all variables in the template with actual data
     */
    private void replaceVariablesInTemplate(Sheet sheet, Map<String, String> replacements) {
        System.out.println("Starting variable replacement in template...");

        int replacementCount = 0;

        // Iterate through all rows and cells
        for (int rowIndex = 0; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
            Row row = sheet.getRow(rowIndex);
            if (row != null) {
                for (int cellIndex = 0; cellIndex < 10; cellIndex++) { // Check first 10 columns
                    Cell cell = row.getCell(cellIndex);
                    if (cell != null && cell.getCellType() == Cell.CELL_TYPE_STRING) {
                        String cellValue = cell.getStringCellValue();

                        // Check if cell contains any variables
                        if (cellValue.contains("{{") && cellValue.contains("}}")) {
                            String originalValue = cellValue;

                            // Replace all variables in this cell
                            for (Map.Entry<String, String> entry : replacements.entrySet()) {
                                String variable = entry.getKey();
                                String replacement = entry.getValue();

                                if (cellValue.contains(variable)) {
                                    cellValue = cellValue.replace(variable, replacement);
                                    replacementCount++;
                                    System.out.println("Replaced " + variable + " at row " + rowIndex + ", col " + cellIndex);
                                }
                            }

                            // Update cell value if it was changed
                            if (!cellValue.equals(originalValue)) {
                                cell.setCellValue(cellValue);
                            }
                        }
                    }
                }
            }
        }

        System.out.println("Completed variable replacement. Total replacements: " + replacementCount);

        // If no replacements were made, log warning
        if (replacementCount == 0) {
            System.out.println("WARNING: No variables were found in template! Expected variables like {{LECTURER_NAME}}, {{WEEK_NUMBER}}, etc.");
            System.out.println("Please ensure your Excel template contains placeholder variables in double curly braces format.");
        }
    }

    /**
     * Debug template structure to understand layout
     */
    private void debugTemplateStructure(Sheet sheet) {
        System.out.println("Template structure analysis:");
        System.out.println("Sheet name: " + sheet.getSheetName());
        System.out.println("Total rows: " + sheet.getLastRowNum());

        // Print first 30 rows to understand structure
        for (int i = 0; i <= Math.min(30, sheet.getLastRowNum()); i++) {
            Row row = sheet.getRow(i);
            if (row != null) {
                StringBuilder rowContent = new StringBuilder("Row " + i + ": ");
                for (int j = 0; j < 6; j++) {
                    Cell cell = row.getCell(j);
                    if (cell != null && cell.getCellType() == Cell.CELL_TYPE_STRING) {
                        String cellValue = cell.getStringCellValue().trim();
                        if (!cellValue.isEmpty()) {
                            rowContent.append("[").append(j).append("]=").append(cellValue.replace("\n", "\\n")).append(" | ");
                        }
                    }
                }
                if (rowContent.length() > 10) { // Only print rows with content
                    System.out.println(rowContent.toString());
                }
            }
        }
    }

    /**
     * Update header information in template with comprehensive search
     */
    private void updateHeaderInfo(Sheet sheet, SimpleDateFormat dayFormat, SimpleDateFormat monthFormat, SimpleDateFormat yearFormat) {
        boolean dateUpdated = false;

        // Search more thoroughly for header cells
        for (int i = 0; i < 15; i++) {
            Row row = sheet.getRow(i);
            if (row != null) {
                for (int j = 0; j < 10; j++) {
                    Cell cell = row.getCell(j);
                    if (cell != null && cell.getCellType() == Cell.CELL_TYPE_STRING) {
                        String cellValue = cell.getStringCellValue().trim();

                        // Update date information
                        if (cellValue.contains("Cà Mau") && cellValue.contains("ngày")) {
                            String newDate = "Cà Mau, ngày " + dayFormat.format(new Date()) +
                                           " tháng " + monthFormat.format(new Date()) +
                                           " năm " + yearFormat.format(new Date());
                            cell.setCellValue(newDate);
                            System.out.println("Updated date at row " + i + ", col " + j + ": " + newDate);
                            dateUpdated = true;
                        }

                        // Update institution name if needed
                        if (cellValue.contains("UBND") && cellValue.contains("CÀ MAU")) {
                            cell.setCellValue("UBND TỈNH CÀ MAU");
                            System.out.println("Updated institution name at row " + i + ", col " + j);
                        }

                        if (cellValue.contains("TRƯỜNG") && cellValue.contains("CAO ĐẲNG")) {
                            cell.setCellValue("TRƯỜNG CAO ĐẲNG Y TẾ");
                            System.out.println("Updated school name at row " + i + ", col " + j);
                        }

                        if (cellValue.contains("CỘNG HÒA") && cellValue.contains("VIỆT NAM")) {
                            cell.setCellValue("CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM");
                            System.out.println("Updated country name at row " + i + ", col " + j);
                        }

                        if (cellValue.contains("Độc lập") && cellValue.contains("Tự do")) {
                            cell.setCellValue("Độc lập - Tự do - Hạnh phúc");
                            System.out.println("Updated motto at row " + i + ", col " + j);
                        }
                    }
                }
            }
        }

        if (!dateUpdated) {
            System.out.println("WARNING: Date field not found in header!");
        }
    }

    /**
     * Update title and date range in template with enhanced search
     */
    private void updateTitleAndDateRange(Sheet sheet, Date tuNgay, Date denNgay, SimpleDateFormat dateFormat) {
        // Calculate week number
        java.util.Calendar cal = java.util.Calendar.getInstance();
        cal.setTime(tuNgay);
        int weekOfYear = cal.get(java.util.Calendar.WEEK_OF_YEAR);

        boolean titleUpdated = false;
        boolean dateRangeUpdated = false;

        // Search more thoroughly for title and date range cells
        for (int i = 0; i < 25; i++) {
            Row row = sheet.getRow(i);
            if (row != null) {
                for (int j = 0; j < 10; j++) {
                    Cell cell = row.getCell(j);
                    if (cell != null && cell.getCellType() == Cell.CELL_TYPE_STRING) {
                        String cellValue = cell.getStringCellValue().trim();

                        // Update title
                        if (cellValue.contains("LỊCH GIẢNG TUẦN") || cellValue.contains("LỊCH GIẢNG")) {
                            String newTitle = "LỊCH GIẢNG TUẦN " + weekOfYear + "- HK II";
                            cell.setCellValue(newTitle);
                            System.out.println("Updated title at row " + i + ", col " + j + ": " + newTitle);
                            titleUpdated = true;
                        }

                        // Update date range
                        if ((cellValue.contains("Từ ngày") && cellValue.contains("đến ngày")) ||
                            cellValue.contains("Từ") || cellValue.contains("đến")) {
                            String newDateRange = "Từ ngày " + dateFormat.format(tuNgay) + " đến ngày " + dateFormat.format(denNgay);
                            cell.setCellValue(newDateRange);
                            System.out.println("Updated date range at row " + i + ", col " + j + ": " + newDateRange);
                            dateRangeUpdated = true;
                        }

                        // Also check for any cell that might contain week info
                        if (cellValue.matches(".*\\d+.*HK.*") || cellValue.matches(".*TUẦN.*\\d+.*")) {
                            String newTitle = "LỊCH GIẢNG TUẦN " + weekOfYear + "- HK II";
                            cell.setCellValue(newTitle);
                            System.out.println("Updated week info at row " + i + ", col " + j + ": " + newTitle);
                            titleUpdated = true;
                        }
                    }
                }
            }
        }

        if (!titleUpdated) {
            System.out.println("WARNING: Title field not found!");
        }
        if (!dateRangeUpdated) {
            System.out.println("WARNING: Date range field not found!");
        }
    }

    /**
     * Update lecturer information in template with comprehensive search
     */
    private void updateLecturerInfo(Sheet sheet, CanBo giangVien) {
        boolean lecturerUpdated = false;

        // Search more thoroughly for lecturer info
        for (int i = 0; i < 30; i++) {
            Row row = sheet.getRow(i);
            if (row != null) {
                for (int j = 0; j < 10; j++) {
                    Cell cell = row.getCell(j);
                    if (cell != null && cell.getCellType() == Cell.CELL_TYPE_STRING) {
                        String cellValue = cell.getStringCellValue().trim();

                        // Look for lecturer field patterns
                        if (cellValue.contains("Gv:") || cellValue.contains("GV:") ||
                            cellValue.contains("Giảng viên:") || cellValue.contains("GIẢNG VIÊN:") ||
                            cellValue.toLowerCase().contains("lecturer") ||
                            (cellValue.contains("Bs.") || cellValue.contains("ThS.") || cellValue.contains("TS."))) {

                            String newLecturerInfo = "Gv: " + giangVien.getHoTen() + " (" + giangVien.getMaCanBo() + ")";
                            cell.setCellValue(newLecturerInfo);
                            System.out.println("Updated lecturer info at row " + i + ", col " + j + ": " + newLecturerInfo);
                            lecturerUpdated = true;
                        }

                        // Also check for placeholder patterns
                        if (cellValue.contains("{{") && cellValue.contains("}}")) {
                            if (cellValue.toLowerCase().contains("giang") || cellValue.toLowerCase().contains("lecturer")) {
                                String newLecturerInfo = "Gv: " + giangVien.getHoTen() + " (" + giangVien.getMaCanBo() + ")";
                                cell.setCellValue(newLecturerInfo);
                                System.out.println("Updated lecturer placeholder at row " + i + ", col " + j + ": " + newLecturerInfo);
                                lecturerUpdated = true;
                            }
                        }
                    }
                }
            }
        }

        if (!lecturerUpdated) {
            System.out.println("WARNING: Lecturer field not found! Searching for empty cells to insert...");
            // Try to find an appropriate empty cell to insert lecturer info
            insertLecturerInfoInEmptyCell(sheet, giangVien);
        }
    }

    /**
     * Insert lecturer info in an appropriate empty cell
     */
    private void insertLecturerInfoInEmptyCell(Sheet sheet, CanBo giangVien) {
        // Look for empty cells around rows 8-15 (typical lecturer info area)
        for (int i = 8; i < 16; i++) {
            Row row = sheet.getRow(i);
            if (row == null) {
                row = sheet.createRow(i);
            }

            for (int j = 1; j < 4; j++) { // Check columns 1-3
                Cell cell = row.getCell(j);
                if (cell == null || cell.getCellType() == Cell.CELL_TYPE_BLANK ||
                    (cell.getCellType() == Cell.CELL_TYPE_STRING && cell.getStringCellValue().trim().isEmpty())) {

                    if (cell == null) {
                        cell = row.createCell(j);
                    }
                    String lecturerInfo = "Gv: " + giangVien.getHoTen() + " (" + giangVien.getMaCanBo() + ")";
                    cell.setCellValue(lecturerInfo);
                    System.out.println("Inserted lecturer info at empty cell row " + i + ", col " + j + ": " + lecturerInfo);
                    return;
                }
            }
        }
    }

    /**
     * Fill schedule data into template with comprehensive debugging
     */
    private void fillScheduleData(Sheet sheet, List<LichGiangDay> schedules, Date tuNgay, SimpleDateFormat dateFormat) {
        System.out.println("Starting to fill schedule data...");

        // Group schedules by date and session
        Map<String, Map<String, List<LichGiangDay>>> schedulesByDate = groupSchedulesByDateAndSession(schedules);
        System.out.println("Grouped schedules by date: " + schedulesByDate.size() + " dates");

        // Debug: Print grouped schedules
        for (Map.Entry<String, Map<String, List<LichGiangDay>>> dateEntry : schedulesByDate.entrySet()) {
            System.out.println("Date " + dateEntry.getKey() + " has " + dateEntry.getValue().size() + " sessions");
            for (Map.Entry<String, List<LichGiangDay>> sessionEntry : dateEntry.getValue().entrySet()) {
                System.out.println("  Session " + sessionEntry.getKey() + " has " + sessionEntry.getValue().size() + " schedules");
            }
        }

        // Find the table start row (look for "Thứ-Ngày" header)
        int tableStartRow = findTableStartRow(sheet);
        if (tableStartRow == -1) {
            System.out.println("ERROR: Could not find schedule table in template!");
            return;
        }
        System.out.println("Found schedule table starting at row: " + tableStartRow);

        // Fill data for each day of the week (7 days)
        int currentRow = tableStartRow + 1; // Start after header row
        System.out.println("Starting to fill data from row: " + currentRow);

        for (int dayIndex = 0; dayIndex < 7; dayIndex++) {
            java.util.Calendar cal = java.util.Calendar.getInstance();
            cal.setTime(tuNgay);
            cal.add(java.util.Calendar.DAY_OF_MONTH, dayIndex);
            Date currentDate = cal.getTime();
            String dateKey = dateFormat.format(currentDate);

            Map<String, List<LichGiangDay>> daySchedules = schedulesByDate.get(dateKey);
            String dayName = getDayName(dayIndex + 2); // Monday = 2

            System.out.println("Processing " + dayName + " (" + dateKey + ")");

            // Fill 3 sessions (S, C, T) for each day
            String[] sessions = {"SANG", "CHIEU", "TOI"};
            String[] sessionLabels = {"S", "C", "T"};
            String[] times = {"7h30", "13h30", "18h30"};

            for (int sessionIndex = 0; sessionIndex < 3; sessionIndex++) {
                Row row = sheet.getRow(currentRow);
                if (row == null) {
                    row = sheet.createRow(currentRow);
                    System.out.println("Created new row: " + currentRow);
                }

                // Column 0: Day-Date (only for first session)
                Cell dayCell = row.getCell(0);
                if (dayCell == null) {
                    dayCell = row.createCell(0);
                }
                if (sessionIndex == 0) {
                    String dayDateValue = dayName + " -\n" + dateKey;
                    dayCell.setCellValue(dayDateValue);
                    System.out.println("  Set day-date: " + dayDateValue.replace("\n", " "));
                } else {
                    dayCell.setCellValue("");
                }

                // Column 1: Session
                Cell sessionCell = row.getCell(1);
                if (sessionCell == null) {
                    sessionCell = row.createCell(1);
                }
                sessionCell.setCellValue(sessionLabels[sessionIndex]);

                // Column 2: Time
                Cell timeCell = row.getCell(2);
                if (timeCell == null) {
                    timeCell = row.createCell(2);
                }
                timeCell.setCellValue(times[sessionIndex]);

                // Column 3: Content
                Cell contentCell = row.getCell(3);
                if (contentCell == null) {
                    contentCell = row.createCell(3);
                }

                String content = "";
                if (daySchedules != null) {
                    List<LichGiangDay> sessionSchedules = daySchedules.get(sessions[sessionIndex]);
                    if (sessionSchedules != null && !sessionSchedules.isEmpty()) {
                        content = buildTemplateScheduleContent(sessionSchedules.get(0));
                        System.out.println("  " + sessionLabels[sessionIndex] + " session has content: " + content.substring(0, Math.min(50, content.length())) + "...");
                    } else {
                        System.out.println("  " + sessionLabels[sessionIndex] + " session is empty");
                    }
                } else {
                    System.out.println("  No schedules for " + dateKey);
                }
                contentCell.setCellValue(content);

                currentRow++;
            }
        }

        System.out.println("Completed filling schedule data. Final row: " + currentRow);
    }

    /**
     * Find table start row in template with enhanced search
     */
    private int findTableStartRow(Sheet sheet) {
        System.out.println("Searching for table header...");

        for (int i = 0; i < 60; i++) { // Search first 60 rows
            Row row = sheet.getRow(i);
            if (row != null) {
                for (int j = 0; j < 6; j++) { // Check first 6 columns
                    Cell cell = row.getCell(j);
                    if (cell != null && cell.getCellType() == Cell.CELL_TYPE_STRING) {
                        String cellValue = cell.getStringCellValue().trim();

                        // Look for various table header patterns
                        if (cellValue.contains("Thứ-Ngày") || cellValue.contains("Thứ-Ngày") ||
                            cellValue.contains("THỨ-NGÀY") || cellValue.contains("Thứ") ||
                            cellValue.contains("Ngày") || cellValue.contains("Day") ||
                            cellValue.contains("Buổi") || cellValue.contains("Session") ||
                            cellValue.contains("NỘI DUNG") || cellValue.contains("Content")) {

                            System.out.println("Found potential table header at row " + i + ", col " + j + ": " + cellValue);

                            // Verify this is actually the table header by checking adjacent cells
                            if (isTableHeaderRow(row)) {
                                System.out.println("Confirmed table header at row " + i);
                                return i;
                            }
                        }
                    }
                }
            }
        }

        System.out.println("Table header not found, creating one...");
        return createTableHeader(sheet);
    }

    /**
     * Verify if a row is the table header row
     */
    private boolean isTableHeaderRow(Row row) {
        int headerCellCount = 0;
        String[] expectedHeaders = {"Thứ", "Buổi", "Thời", "NỘI DUNG", "Day", "Session", "Time", "Content"};

        for (int j = 0; j < 6; j++) {
            Cell cell = row.getCell(j);
            if (cell != null && cell.getCellType() == Cell.CELL_TYPE_STRING) {
                String cellValue = cell.getStringCellValue().trim();
                for (String header : expectedHeaders) {
                    if (cellValue.contains(header)) {
                        headerCellCount++;
                        break;
                    }
                }
            }
        }

        return headerCellCount >= 2; // At least 2 header-like cells
    }

    /**
     * Get day name from day index
     */
    private String getDayName(int dayOfWeek) {
        switch(dayOfWeek) {
            case 1: return "CHỦ NHẬT";
            case 2: return "THỨ 2";
            case 3: return "THỨ 3";
            case 4: return "THỨ 4";
            case 5: return "THỨ 5";
            case 6: return "THỨ 6";
            case 7: return "THỨ 7";
            default: return "THỨ 2";
        }
    }

    /**
     * Build comprehensive schedule content for template
     */
    private String buildTemplateScheduleContent(LichGiangDay schedule) {
        StringBuilder content = new StringBuilder();

        try {
            // Format exactly like the template expects
            if (schedule.getMonHoc() != null) {
                // LỚP CS: [Class Name] (SS)
                String lopInfo = "LỚP CS: ";
                if (schedule.getLop() != null && schedule.getLop().getTenLop() != null) {
                    lopInfo += schedule.getLop().getTenLop();
                } else {
                    lopInfo += schedule.getMonHoc().getTenMonHoc();
                }
                lopInfo += " (SS)";
                content.append(lopInfo).append("\n");

                // MÔN: [Subject Name]- PHCN
                String monHocDisplay = schedule.getMonHoc().getTenMonHoc();
                if (monHocDisplay != null) {
                    if (monHocDisplay.length() > 35) {
                        monHocDisplay = monHocDisplay.substring(0, 35) + "...";
                    }
                    content.append("MÔN: ").append(monHocDisplay);

                    // Add subject type based on hinhThuc
                    if (schedule.getHinhThuc() != null) {
                        if (schedule.getHinhThuc().equals("TH")) {
                            content.append("- THỰC HÀNH");
                        } else {
                            content.append("- PHCN");
                        }
                    } else {
                        content.append("- PHCN");
                    }
                    content.append("\n");
                }
            }

            // Add lesson content
            if (schedule.getBaiHoc() != null) {
                String baiHocTen = schedule.getBaiHoc().getTenBaiHoc();
                if (baiHocTen != null && !baiHocTen.trim().isEmpty()) {
                    content.append("Bài: ").append(baiHocTen).append("\n");
                } else {
                    content.append("Bài: Học chuyên đề tăng trưởng\n");
                }

                // Add detailed lesson content
                String noiDungBai = "Nguyên nhân gây bệnh";
                if (schedule.getBaiHoc().getNoiDung() != null && !schedule.getBaiHoc().getNoiDung().trim().isEmpty()) {
                    noiDungBai = schedule.getBaiHoc().getNoiDung();
                    if (noiDungBai.length() > 30) {
                        noiDungBai = noiDungBai.substring(0, 30) + "...";
                    }
                }

                // Add period info
                if (schedule.getTietBatDau() != null && schedule.getTietKetThuc() != null) {
                    int soTiet = schedule.getTietKetThuc() - schedule.getTietBatDau() + 1;
                    content.append("Bài: ").append(noiDungBai).append(". ")
                           .append(soTiet).append(" tiết. GD ").append(schedule.getTietKetThuc()).append(".");
                } else {
                    content.append("Bài: ").append(noiDungBai).append(". 4 tiết. GD 9.");
                }
            } else {
                // Default content if no lesson info
                content.append("Bài: Học chuyên đề tăng trưởng\n");
                content.append("Bài: Nguyên nhân gây bệnh. 4 tiết. GD 9.");
            }

            // Add room info if available
            if (schedule.getPhongHoc() != null && schedule.getPhongHoc().getTenPhong() != null) {
                content.append("\nPhòng: ").append(schedule.getPhongHoc().getTenPhong());
            }

        } catch (Exception e) {
            System.out.println("Error building schedule content: " + e.getMessage());
            content.append("Lỗi hiển thị nội dung");
        }

        return content.toString();
    }

    /**
     * Update footer information in template with 3-column format
     */
    private void updateFooterInfo(Sheet sheet) {
        System.out.println("Updating 3-column footer information...");

        boolean footerUpdated = false;

        // Search for footer elements in the lower part of the sheet
        int startRow = Math.max(0, sheet.getLastRowNum() - 20); // Start from 20 rows before the end

        for (int i = startRow; i <= sheet.getLastRowNum() + 5; i++) {
            Row row = sheet.getRow(i);
            if (row != null) {
                for (int j = 0; j < 8; j++) { // Increased to 8 to cover column 6
                    Cell cell = row.getCell(j);
                    if (cell != null && cell.getCellType() == Cell.CELL_TYPE_STRING) {
                        String cellValue = cell.getStringCellValue().trim();

                        // Update 3-column signature titles
                        if (cellValue.contains("GIÁO VỤ") || cellValue.contains("Giáo vụ")) {
                            cell.setCellValue("GIÁO VỤ");
                            System.out.println("Updated academic affairs title at row " + i + ", col " + j);
                            footerUpdated = true;
                        }

                        if (cellValue.contains("KT. TRƯỞNG PHÒNG") || cellValue.contains("Kt. Trưởng phòng")) {
                            cell.setCellValue("KT. TRƯỞNG PHÒNG");
                            System.out.println("Updated acting department head title at row " + i + ", col " + j);
                            footerUpdated = true;
                        }

                        if (cellValue.contains("P. TRƯỞNG PHÒNG") || cellValue.contains("P. Trưởng phòng")) {
                            cell.setCellValue("P. TRƯỞNG PHÒNG");
                            System.out.println("Updated deputy department head title at row " + i + ", col " + j);
                            footerUpdated = true;
                        }

                        if (cellValue.contains("HIỆU TRƯỞNG") || cellValue.contains("Hiệu trưởng")) {
                            cell.setCellValue("HIỆU TRƯỞNG");
                            System.out.println("Updated director title at row " + i + ", col " + j);
                            footerUpdated = true;
                        }

                        // Update names
                        if (cellValue.contains("LÊ MINH THƠI") || cellValue.contains("Lê Minh Thơi")) {
                            cell.setCellValue("Cn. LÊ MINH THƠI");
                            System.out.println("Updated academic affairs name at row " + i + ", col " + j);
                            footerUpdated = true;
                        }

                        if (cellValue.contains("HUỲNH THANH BÌNH") || cellValue.contains("Huỳnh Thanh Bình")) {
                            cell.setCellValue("Ds.CK1. HUỲNH THANH BÌNH");
                            System.out.println("Updated deputy department head name at row " + i + ", col " + j);
                            footerUpdated = true;
                        }

                        if (cellValue.contains("HUỲNH NGỌC LINH") || cellValue.contains("Huỳnh Ngọc Linh")) {
                            cell.setCellValue("TS. HUỲNH NGỌC LINH");
                            System.out.println("Updated director name at row " + i + ", col " + j);
                            footerUpdated = true;
                        }

                        // Remove contact info and notes (set to empty)
                        if (cellValue.contains("liên hệ") || cellValue.contains("Phòng Đào tạo") ||
                            cellValue.contains("Ghi chú") || cellValue.contains("thắc mắc")) {
                            cell.setCellValue(""); // Remove notes and contact info
                            System.out.println("Removed notes/contact info at row " + i + ", col " + j);
                            footerUpdated = true;
                        }
                    }
                }
            }
        }

        if (!footerUpdated) {
            System.out.println("WARNING: Footer information not found in template!");
        }
    }

    /**
     * Create table header if not found in template
     */
    private int createTableHeader(Sheet sheet) {
        System.out.println("Creating table header...");

        // Find a good position to insert the table (after lecturer info)
        int insertRow = 15; // Default position

        // Try to find a better position by looking for empty rows
        for (int i = 10; i < 25; i++) {
            Row row = sheet.getRow(i);
            if (row == null || isRowEmpty(row)) {
                insertRow = i;
                break;
            }
        }

        System.out.println("Creating table header at row: " + insertRow);

        // Create header row
        Row headerRow = sheet.getRow(insertRow);
        if (headerRow == null) {
            headerRow = sheet.createRow(insertRow);
        }

        // Create header cells
        Cell cell0 = headerRow.getCell(0);
        if (cell0 == null) cell0 = headerRow.createCell(0);
        cell0.setCellValue("Thứ-Ngày");

        Cell cell1 = headerRow.getCell(1);
        if (cell1 == null) cell1 = headerRow.createCell(1);
        cell1.setCellValue("Buổi\nphan");

        Cell cell2 = headerRow.getCell(2);
        if (cell2 == null) cell2 = headerRow.createCell(2);
        cell2.setCellValue("Thời\ngian");

        Cell cell3 = headerRow.getCell(3);
        if (cell3 == null) cell3 = headerRow.createCell(3);
        cell3.setCellValue("NỘI DUNG GIẢNG DẠY");

        System.out.println("Table header created successfully at row: " + insertRow);
        return insertRow;
    }

    /**
     * Check if a row is empty
     */
    private boolean isRowEmpty(Row row) {
        if (row == null) return true;

        for (int i = 0; i < 6; i++) {
            Cell cell = row.getCell(i);
            if (cell != null && cell.getCellType() != Cell.CELL_TYPE_BLANK) {
                if (cell.getCellType() == Cell.CELL_TYPE_STRING && !cell.getStringCellValue().trim().isEmpty()) {
                    return false;
                }
                if (cell.getCellType() == Cell.CELL_TYPE_NUMERIC) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * Determine semester based on date
     */
    private String determineHocKy(Date date) {
        try {
            // Try to get semester from database first
            java.util.Calendar cal = java.util.Calendar.getInstance();
            cal.setTime(date);

            // Get current academic year
            HeDaoTao hdt = Utility.layHeDaoTaoHienTai();
            List<NienKhoa> nienKhoas = nienKhoaSer.layDanhSach();

            if (!nienKhoas.isEmpty()) {
                NienKhoa currentNienKhoa = nienKhoas.get(0); // Get first/current academic year
                List<HocKy> hocKys = hocKySer.LayHocKyTheoNienKhoa(currentNienKhoa.getIdNienKhoa());

                // Find semester that contains the given date
                for (HocKy hocKy : hocKys) {
                    if (hocKy.getNgayBatDau() != null && hocKy.getNgayKetThuc() != null) {
                        if (date.compareTo(hocKy.getNgayBatDau()) >= 0 &&
                            date.compareTo(hocKy.getNgayKetThuc()) <= 0) {
                            return hocKy.getTenHocKy();
                        }
                    }
                }

                // If no exact match, return the latest semester
                if (!hocKys.isEmpty()) {
                    return hocKys.get(hocKys.size() - 1).getTenHocKy();
                }
            }

        } catch (Exception e) {
            System.out.println("Error determining semester: " + e.getMessage());
        }

        // Fallback: determine by month
        java.util.Calendar cal = java.util.Calendar.getInstance();
        cal.setTime(date);
        int month = cal.get(java.util.Calendar.MONTH) + 1; // Calendar.MONTH is 0-based

        if (month >= 9 || month <= 1) {
            return "HK I"; // September to January
        } else if (month >= 2 && month <= 6) {
            return "HK II"; // February to June
        } else {
            return "HK HÈ"; // July to August (Summer semester)
        }
    }

    /**
     * Export all classes schedule using template file
     */
    @RequestMapping(value = "/xuat-excel-lich-toan-truong-mau", produces = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
    public void xuatExcelLichToanTruongMau(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date tuNgay,
                                          @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date denNgay,
                                          HttpServletResponse response) throws IOException {
        try {
            // Get teaching schedules for all classes in the specified period
            Page<LichGiangDay> schedules = lichGiangDaySer.timKiem(
                null, null, null, null, null, null, null, null, null, null,
                tuNgay, denNgay, null, PageRequest.of(0, 10000));

            // Get only classes that have schedules in this period
            List<Lop> classesWithSchedules = getClassesWithSchedules(schedules.getContent());

            if (classesWithSchedules.isEmpty()) {
                response.sendError(HttpServletResponse.SC_BAD_REQUEST, "Không có lớp nào có lịch giảng trong khoảng thời gian này");
                return;
            }

            // Load template file with error handling
            XSSFWorkbook workbook = null;
            InputStream templateStream = null;


            try {
                ClassPathResource templateResource = new ClassPathResource("static/mau/lich-giang-toan-truong-tuan.xlsx");

                if (!templateResource.exists()) {
                    System.out.println("School-wide template file not found, creating dynamic workbook");
                    workbook = createSchoolWideScheduleWorkbook(classesWithSchedules, schedules.getContent(), tuNgay, denNgay);
                } else {
                    templateStream = templateResource.getInputStream();
                    System.out.println("Loading school-wide template file: " + templateResource.getFilename());
                    workbook = new XSSFWorkbook(templateStream);

                    // Fill data into template
                    fillDataIntoSchoolWideTemplate(workbook.getSheetAt(0), classesWithSchedules, schedules.getContent(), tuNgay, denNgay);
                }

            } catch (Exception e) {
                System.out.println("Error loading school-wide template file: " + e.getMessage());
                System.out.println("Creating dynamic workbook as fallback");

                if (templateStream != null) {
                    try {
                        templateStream.close();
                    } catch (Exception closeEx) {
                        // Ignore close exception
                    }
                }

                // Create dynamic workbook as fallback
                workbook = createSchoolWideScheduleWorkbook(classesWithSchedules, schedules.getContent(), tuNgay, denNgay);
            }

            // Set response headers
            String fileName = "lich-giang-toan-truong-" +
                            new SimpleDateFormat("yyyyMMdd").format(tuNgay) + ".xlsx";
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName);

            // Write to response
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            response.getOutputStream().write(outputStream.toByteArray());

            // Close resources
            workbook.close();
            if (templateStream != null) {
                templateStream.close();
            }

        } catch (Exception e) {
            e.printStackTrace();
            response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "Lỗi khi xuất Excel: " + e.getMessage());
        }
    }

    /**
     * Create school-wide schedule workbook with dynamic tables
     */
    private XSSFWorkbook createSchoolWideScheduleWorkbook(List<Lop> allClasses, List<LichGiangDay> schedules, Date tuNgay, Date denNgay) {
        System.out.println("Creating school-wide schedule for " + allClasses.size() + " classes");

        XSSFWorkbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Lịch Giảng Toàn Trường");

        SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
        SimpleDateFormat dayFormat = new SimpleDateFormat("dd");
        SimpleDateFormat monthFormat = new SimpleDateFormat("MM");
        SimpleDateFormat yearFormat = new SimpleDateFormat("yyyy");

        int rowNum = 0;

        // Header information
        rowNum = createSchoolWideHeader(sheet, rowNum, tuNgay, denNgay, dateFormat, dayFormat, monthFormat, yearFormat);

        // Create tables dynamically
        int classesPerTable = 4;
        int numberOfTables = (int) Math.ceil((double) allClasses.size() / classesPerTable);

        // Group schedules by class
        Map<Integer, List<LichGiangDay>> schedulesByClass = groupSchedulesByClass(schedules);

        for (int tableIndex = 0; tableIndex < numberOfTables; tableIndex++) {
            // Get classes for this table
            List<Lop> tableClasses = getClassesForTable(allClasses, tableIndex, classesPerTable);

            // Create table
            rowNum = createDynamicTable(sheet, rowNum, tableClasses, schedulesByClass, tuNgay, dateFormat, tableIndex + 1);

            // Add spacing between tables
            rowNum += 2;
        }

        // Set column widths
        sheet.setColumnWidth(0, 3000);  // Thứ-Ngày
        sheet.setColumnWidth(1, 1500);  // Buổi
        sheet.setColumnWidth(2, 2000);  // Thời gian
        for (int i = 3; i < 7; i++) {   // Class columns
            sheet.setColumnWidth(i, 6000);
        }

        return workbook;
    }

    /**
     * Create header for school-wide schedule
     */
    private int createSchoolWideHeader(Sheet sheet, int startRow, Date tuNgay, Date denNgay,
                                     SimpleDateFormat dateFormat, SimpleDateFormat dayFormat,
                                     SimpleDateFormat monthFormat, SimpleDateFormat yearFormat) {
        int rowNum = startRow;

        // Header information
        Row row1 = sheet.createRow(rowNum++);
        row1.createCell(0).setCellValue("UBND TỈNH CÀ MAU");
        row1.createCell(4).setCellValue("CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM");

        Row row2 = sheet.createRow(rowNum++);
        row2.createCell(0).setCellValue("TRƯỜNG CAO ĐẲNG Y TẾ");
        row2.createCell(4).setCellValue("Độc lập - Tự do - Hạnh phúc");

        // Empty row
        sheet.createRow(rowNum++);

        Row row3 = sheet.createRow(rowNum++);
        row3.createCell(4).setCellValue("Cà Mau, ngày " + dayFormat.format(new Date()) +
                                       " tháng " + monthFormat.format(new Date()) +
                                       " năm " + yearFormat.format(new Date()));

        // Empty row
        sheet.createRow(rowNum++);

        // Title
        java.util.Calendar cal = java.util.Calendar.getInstance();
        cal.setTime(tuNgay);
        int weekOfYear = cal.get(java.util.Calendar.WEEK_OF_YEAR);
        String hocKy = determineHocKy(tuNgay);

        Row titleRow = sheet.createRow(rowNum++);
        titleRow.createCell(2).setCellValue("LỊCH GIẢNG TOÀN TRƯỜNG TUẦN " + weekOfYear + "- " + hocKy);
        sheet.addMergedRegion(new CellRangeAddress(rowNum-1, rowNum-1, 2, 5)); // Merge title

        Row dateRow = sheet.createRow(rowNum++);
        dateRow.createCell(2).setCellValue("Từ ngày " + dateFormat.format(tuNgay) + " đến ngày " + dateFormat.format(denNgay));
        sheet.addMergedRegion(new CellRangeAddress(rowNum-1, rowNum-1, 2, 5)); // Merge date range

        // Empty row
        sheet.createRow(rowNum++);

        return rowNum;
    }

    /**
     * Group schedules by class ID
     */
    private Map<Integer, List<LichGiangDay>> groupSchedulesByClass(List<LichGiangDay> schedules) {
        Map<Integer, List<LichGiangDay>> result = new HashMap<>();

        for (LichGiangDay schedule : schedules) {
            if (schedule.getLop() != null && schedule.getLop().getIdLop() != null) {
                Integer classId = schedule.getLop().getIdLop();
                result.computeIfAbsent(classId, k -> new ArrayList<>()).add(schedule);
            }
        }

        return result;
    }

    /**
     * Get classes for specific table
     */
    private List<Lop> getClassesForTable(List<Lop> allClasses, int tableIndex, int classesPerTable) {
        int startIndex = tableIndex * classesPerTable;
        int endIndex = Math.min(startIndex + classesPerTable, allClasses.size());

        List<Lop> tableClasses = new ArrayList<>();
        for (int i = startIndex; i < endIndex; i++) {
            tableClasses.add(allClasses.get(i));
        }

        // Fill empty slots if needed
        while (tableClasses.size() < classesPerTable) {
            tableClasses.add(null); // Empty slot
        }

        return tableClasses;
    }

    /**
     * Create dynamic table for classes
     */
    private int createDynamicTable(Sheet sheet, int startRow, List<Lop> tableClasses,
                                 Map<Integer, List<LichGiangDay>> schedulesByClass,
                                 Date tuNgay, SimpleDateFormat dateFormat, int tableNumber) {
        int rowNum = startRow;

        // Table title
        Row titleRow = sheet.createRow(rowNum++);
        titleRow.createCell(0).setCellValue("");

        // Table header
        Row headerRow = sheet.createRow(rowNum++);
        headerRow.createCell(0).setCellValue("Thứ-Ngày");
        headerRow.createCell(1).setCellValue("Buổi");
        headerRow.createCell(2).setCellValue("Thời gian");

        // Class name headers
        for (int i = 0; i < tableClasses.size(); i++) {
            Lop lop = tableClasses.get(i);
            String className = (lop != null) ? lop.getTenLop() : "";
            headerRow.createCell(3 + i).setCellValue(className);
        }

        // Create schedule rows (7 days x 3 sessions = 21 rows)
        String[] dayNames = {"THỨ 2", "THỨ 3", "THỨ 4", "THỨ 5", "THỨ 6", "THỨ 7", "CHỦ NHẬT"};
        String[] sessions = {"S", "C", "T"};
        String[] times = {"7h30", "13h30", "18h30"};
        String[] sessionKeys = {"SANG", "CHIEU", "TOI"};

        for (int dayIndex = 0; dayIndex < 7; dayIndex++) {
            // Calculate date for this day
            java.util.Calendar cal = java.util.Calendar.getInstance();
            cal.setTime(tuNgay);
            cal.add(java.util.Calendar.DAY_OF_MONTH, dayIndex);
            Date currentDate = cal.getTime();
            String dateKey = dateFormat.format(currentDate);

            for (int sessionIndex = 0; sessionIndex < 3; sessionIndex++) {
                Row dataRow = sheet.createRow(rowNum++);

                // Day-Date column (only for first session)
                if (sessionIndex == 0) {
                    dataRow.createCell(0).setCellValue(dayNames[dayIndex] + " -\n" + dateKey);
                } else {
                    dataRow.createCell(0).setCellValue("");
                }

                // Session column
                dataRow.createCell(1).setCellValue(sessions[sessionIndex]);

                // Time column
                dataRow.createCell(2).setCellValue(times[sessionIndex]);

                // Class schedule columns
                for (int classIndex = 0; classIndex < tableClasses.size(); classIndex++) {
                    Lop lop = tableClasses.get(classIndex);
                    String content = "";

                    if (lop != null) {
                        content = getClassScheduleContent(lop, schedulesByClass, currentDate, sessionKeys[sessionIndex]);
                    }

                    dataRow.createCell(3 + classIndex).setCellValue(content);
                }
            }
        }

        return rowNum;
    }

    /**
     * Get schedule content for specific class, date and session
     */
    private String getClassScheduleContent(Lop lop, Map<Integer, List<LichGiangDay>> schedulesByClass,
                                         Date date, String session) {
        List<LichGiangDay> classSchedules = schedulesByClass.get(lop.getIdLop());
        if (classSchedules == null || classSchedules.isEmpty()) {
            return "";
        }

        SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
        String dateKey = dateFormat.format(date);

        // Find schedule for this date and session
        for (LichGiangDay schedule : classSchedules) {
            if (schedule.getNgayGiang() != null) {
                String scheduleDate = dateFormat.format(schedule.getNgayGiang());
                String scheduleSession = determineSession(schedule);

                if (scheduleDate.equals(dateKey) && scheduleSession.equals(session)) {
                    return buildSchoolWideScheduleContent(schedule);
                }
            }
        }

        return "";
    }

    /**
     * Build schedule content for school-wide view
     */
    private String buildSchoolWideScheduleContent(LichGiangDay schedule) {
        StringBuilder content = new StringBuilder();

        try {
            // Subject name
            if (schedule.getMonHoc() != null) {
                String monHoc = schedule.getMonHoc().getTenMonHoc();
                if (monHoc.length() > 20) {
                    monHoc = monHoc.substring(0, 20) + "...";
                }
                content.append(monHoc).append("\n");
            }

            // Lecturer name
            if (schedule.getGiangVien() != null) {
                String giangVien = schedule.getGiangVien().getHoTen();
                if (giangVien.length() > 15) {
                    giangVien = giangVien.substring(0, 15) + "...";
                }
                content.append("GV: ").append(giangVien).append("\n");
            }

            // Room
            if (schedule.getPhongHoc() != null) {
                content.append("P: ").append(schedule.getPhongHoc().getTenPhong());
            }

        } catch (Exception e) {
            System.out.println("Error building school-wide schedule content: " + e.getMessage());
            return "Lỗi";
        }

        return content.toString();
    }

    /**
     * Fill data into school-wide template using dynamic table duplication
     */
    private void fillDataIntoSchoolWideTemplate(Sheet sheet, List<Lop> allClasses, List<LichGiangDay> schedules, Date tuNgay, Date denNgay) {
        System.out.println("=== STARTING SCHOOL-WIDE TEMPLATE DYNAMIC PROCESSING ===");
        System.out.println("Total classes: " + allClasses.size());
        System.out.println("Total schedules: " + schedules.size());

        SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
        SimpleDateFormat dayFormat = new SimpleDateFormat("dd");
        SimpleDateFormat monthFormat = new SimpleDateFormat("MM");
        SimpleDateFormat yearFormat = new SimpleDateFormat("yyyy");

        // First, replace basic header variables
        Map<String, String> basicReplacements = createBasicReplacements(tuNgay, denNgay, dateFormat, dayFormat, monthFormat, yearFormat);
        replaceVariablesInTemplate(sheet, basicReplacements);

        // Find template table in the sheet
        int templateTableStart = findTemplateTable(sheet);
        if (templateTableStart == -1) {
            System.out.println("No template table found, using variable replacement approach");
            Map<String, String> replacements = createSchoolWideReplacementMap(allClasses, schedules, tuNgay, denNgay, dateFormat, dayFormat, monthFormat, yearFormat);
            replaceVariablesInTemplate(sheet, replacements);
            return;
        }

        // Duplicate template table for each group of classes
        duplicateAndFillTables(sheet, templateTableStart, allClasses, schedules, tuNgay, dateFormat);

        System.out.println("\n=== SCHOOL-WIDE TEMPLATE DYNAMIC PROCESSING COMPLETED ===");
    }

    /**
     * Create basic replacement variables (header info)
     */
    private Map<String, String> createBasicReplacements(Date tuNgay, Date denNgay, SimpleDateFormat dateFormat,
                                                       SimpleDateFormat dayFormat, SimpleDateFormat monthFormat, SimpleDateFormat yearFormat) {
        Map<String, String> replacements = new HashMap<>();

        replacements.put("{{CURRENT_DAY}}", dayFormat.format(new Date()));
        replacements.put("{{CURRENT_MONTH}}", monthFormat.format(new Date()));
        replacements.put("{{CURRENT_YEAR}}", yearFormat.format(new Date()));
        replacements.put("{{START_DATE}}", dateFormat.format(tuNgay));
        replacements.put("{{END_DATE}}", dateFormat.format(denNgay));

        java.util.Calendar cal = java.util.Calendar.getInstance();
        cal.setTime(tuNgay);
        int weekOfYear = cal.get(java.util.Calendar.WEEK_OF_YEAR);
        replacements.put("{{WEEK_NUMBER}}", String.valueOf(weekOfYear));

        String hocKy = determineHocKy(tuNgay);
        replacements.put("{{HOC_KY}}", hocKy);

        return replacements;
    }

    /**
     * Find template table in sheet
     */
    private int findTemplateTable(Sheet sheet) {
        for (int i = 0; i < sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row != null) {
                for (int j = 0; j < 6; j++) {
                    Cell cell = row.getCell(j);
                    if (cell != null && cell.getCellType() == Cell.CELL_TYPE_STRING) {
                        String cellValue = cell.getStringCellValue().trim();
                        // Look for template table markers
                        if (cellValue.contains("{{TABLE_TEMPLATE}}") ||
                            cellValue.contains("BẢNG MẪU") ||
                            (cellValue.contains("{{COL_1}}") && cellValue.contains("NAME"))) {
                            System.out.println("Found template table at row: " + i);
                            return i;
                        }
                    }
                }
            }
        }
        return -1;
    }

    /**
     * Duplicate template table and fill with data
     */
    private void duplicateAndFillTables(Sheet sheet, int templateStart, List<Lop> allClasses,
                                      List<LichGiangDay> schedules, Date tuNgay, SimpleDateFormat dateFormat) {

        int classesPerTable = 4;
        int numberOfTables = (int) Math.ceil((double) allClasses.size() / classesPerTable);

        // Find template table end
        int templateEnd = findTemplateTableEnd(sheet, templateStart);
        int templateHeight = templateEnd - templateStart + 1;

        System.out.println("Template table: rows " + templateStart + " to " + templateEnd + " (height: " + templateHeight + ")");
        System.out.println("Will create " + numberOfTables + " tables");

        // Group schedules by class
        Map<Integer, List<LichGiangDay>> schedulesByClass = groupSchedulesByClass(schedules);

        // Create tables by duplicating template
        int currentInsertRow = templateEnd + 3; // Start after template with some spacing

        for (int tableIndex = 0; tableIndex < numberOfTables; tableIndex++) {
            List<Lop> tableClasses = getClassesForTable(allClasses, tableIndex, classesPerTable);

            // Copy template to new location
            int newTableStart = copyTemplateTable(sheet, templateStart, templateEnd, currentInsertRow);

            // Fill the copied table with data
            fillCopiedTable(sheet, newTableStart, tableClasses, schedulesByClass, tuNgay, dateFormat, tableIndex + 1);

            // Update position for next table (1 row spacing between tables)
            currentInsertRow = newTableStart + templateHeight + 1;
        }

        // Remove original template
        removeTemplateTable(sheet, templateStart, templateEnd);

        // Add footer once at the end
        addFooterToSheet(sheet);
    }

    /**
     * Create replacement map for school-wide template variables
     */
    private Map<String, String> createSchoolWideReplacementMap(List<Lop> allClasses, List<LichGiangDay> schedules, Date tuNgay, Date denNgay,
                                                              SimpleDateFormat dateFormat, SimpleDateFormat dayFormat,
                                                              SimpleDateFormat monthFormat, SimpleDateFormat yearFormat) {
        Map<String, String> replacements = new HashMap<>();

        // Basic information
        replacements.put("{{CURRENT_DAY}}", dayFormat.format(new Date()));
        replacements.put("{{CURRENT_MONTH}}", monthFormat.format(new Date()));
        replacements.put("{{CURRENT_YEAR}}", yearFormat.format(new Date()));
        replacements.put("{{START_DATE}}", dateFormat.format(tuNgay));
        replacements.put("{{END_DATE}}", dateFormat.format(denNgay));

        // Week calculation
        java.util.Calendar cal = java.util.Calendar.getInstance();
        cal.setTime(tuNgay);
        int weekOfYear = cal.get(java.util.Calendar.WEEK_OF_YEAR);
        replacements.put("{{WEEK_NUMBER}}", String.valueOf(weekOfYear));

        // Semester information
        String hocKy = determineHocKy(tuNgay);
        replacements.put("{{HOC_KY}}", hocKy);

        // Group schedules by class
        Map<Integer, List<LichGiangDay>> schedulesByClass = groupSchedulesByClass(schedules);

        // Create variables for tables
        int classesPerTable = 4;
        int numberOfTables = (int) Math.ceil((double) allClasses.size() / classesPerTable);

        for (int tableIndex = 0; tableIndex < numberOfTables; tableIndex++) {
            List<Lop> tableClasses = getClassesForTable(allClasses, tableIndex, classesPerTable);

            // Table header variables (class names)
            for (int colIndex = 0; colIndex < 4; colIndex++) {
                String className = "";
                if (colIndex < tableClasses.size() && tableClasses.get(colIndex) != null) {
                    className = tableClasses.get(colIndex).getTenLop();
                }
                String headerKey = "{{TABLE_" + (tableIndex + 1) + "_COL_" + (colIndex + 1) + "_NAME}}";
                replacements.put(headerKey, className);
            }

            // Table content variables (schedule data)
            createTableContentVariables(replacements, tableClasses, schedulesByClass, tuNgay, dateFormat, tableIndex + 1);
        }

        // Day variables
        String[] dayNames = {"THỨ 2", "THỨ 3", "THỨ 4", "THỨ 5", "THỨ 6", "THỨ 7", "CHỦ NHẬT"};
        for (int dayIndex = 0; dayIndex < 7; dayIndex++) {
            cal.setTime(tuNgay);
            cal.add(java.util.Calendar.DAY_OF_MONTH, dayIndex);
            Date currentDate = cal.getTime();
            String dateKey = dateFormat.format(currentDate);

            replacements.put("{{DAY_" + (dayIndex + 1) + "}}", dayNames[dayIndex] + " -\n" + dateKey);
        }

        System.out.println("Created " + replacements.size() + " replacement variables for school-wide template");
        return replacements;
    }

    /**
     * Create table content variables for specific table
     */
    private void createTableContentVariables(Map<String, String> replacements, List<Lop> tableClasses,
                                           Map<Integer, List<LichGiangDay>> schedulesByClass,
                                           Date tuNgay, SimpleDateFormat dateFormat, int tableNumber) {

        String[] sessionKeys = {"S", "C", "T"};
        String[] sessionNames = {"SANG", "CHIEU", "TOI"};

        for (int dayIndex = 0; dayIndex < 7; dayIndex++) {
            // Calculate date for this day
            java.util.Calendar cal = java.util.Calendar.getInstance();
            cal.setTime(tuNgay);
            cal.add(java.util.Calendar.DAY_OF_MONTH, dayIndex);
            Date currentDate = cal.getTime();

            for (int sessionIndex = 0; sessionIndex < 3; sessionIndex++) {
                for (int colIndex = 0; colIndex < 4; colIndex++) {
                    String content = "";

                    if (colIndex < tableClasses.size() && tableClasses.get(colIndex) != null) {
                        Lop lop = tableClasses.get(colIndex);
                        content = getClassScheduleContent(lop, schedulesByClass, currentDate, sessionNames[sessionIndex]);
                    }

                    // Variable format: {{T1_D1_S_C1}} = Table1_Day1_Session(S/C/T)_Column1
                    String variableKey = "{{T" + tableNumber + "_D" + (dayIndex + 1) + "_" +
                                       sessionKeys[sessionIndex] + "_C" + (colIndex + 1) + "}}";
                    replacements.put(variableKey, content);
                }
            }
        }
    }

    /**
     * Find template table end row (excluding footer)
     */
    private int findTemplateTableEnd(Sheet sheet, int startRow) {
        // Look for end markers or footer start
        for (int i = startRow + 1; i <= sheet.getLastRowNum() + 5; i++) {
            Row row = sheet.getRow(i);
            if (row == null) {
                return i - 1;
            }

            // Check if this row has template end marker
            Cell cell = row.getCell(0);
            if (cell != null && cell.getCellType() == Cell.CELL_TYPE_STRING) {
                String cellValue = cell.getStringCellValue().trim();

                // End table markers
                if (cellValue.contains("{{END_TABLE}}") || cellValue.contains("KẾT THÚC BẢNG")) {
                    return i;
                }

                // Footer start markers - stop before footer
                if (cellValue.contains("GIÁO VỤ") || cellValue.contains("HIỆU TRƯỞNG") ||
                    cellValue.contains("TRƯỞNG PHÒNG") || cellValue.contains("LÊ MINH THƠI") ||
                    cellValue.contains("HUỲNH THANH BÌNH") || cellValue.contains("HUỲNH NGỌC LINH")) {
                    return i - 1;
                }
            }

            // If we find 3 consecutive empty rows, consider it end of table
            if (isRowEmpty(row) && isRowEmpty(sheet.getRow(i + 1)) && isRowEmpty(sheet.getRow(i + 2))) {
                return i - 1;
            }
        }

        // Default: assume table is 25 rows (header + 21 data rows + some spacing)
        return startRow + 24;
    }

    /**
     * Copy template table to new location
     */
    private int copyTemplateTable(Sheet sheet, int templateStart, int templateEnd, int insertRow) {
        System.out.println("Copying template from rows " + templateStart + "-" + templateEnd + " to row " + insertRow);

        // Shift existing rows down to make space
        int templateHeight = templateEnd - templateStart + 1;
        sheet.shiftRows(insertRow, sheet.getLastRowNum(), templateHeight, true, false);

        // Copy each row from template
        for (int i = 0; i < templateHeight; i++) {
            Row sourceRow = sheet.getRow(templateStart + i);
            Row targetRow = sheet.createRow(insertRow + i);

            if (sourceRow != null) {
                copyRow(sourceRow, targetRow);
            }
        }

        return insertRow;
    }

    /**
     * Copy row content and formatting
     */
    private void copyRow(Row sourceRow, Row targetRow) {
        targetRow.setHeight(sourceRow.getHeight());

        for (int i = 0; i < sourceRow.getLastCellNum(); i++) {
            Cell sourceCell = sourceRow.getCell(i);
            if (sourceCell != null) {
                Cell targetCell = targetRow.createCell(i);
                copyCell(sourceCell, targetCell);
            }
        }
    }

    /**
     * Copy cell content and formatting
     */
    private void copyCell(Cell sourceCell, Cell targetCell) {
        // Copy cell style
        targetCell.setCellStyle(sourceCell.getCellStyle());

        // Copy cell value based on type
        switch (sourceCell.getCellType()) {
            case Cell.CELL_TYPE_STRING:
                targetCell.setCellValue(sourceCell.getStringCellValue());
                break;
            case Cell.CELL_TYPE_NUMERIC:
                targetCell.setCellValue(sourceCell.getNumericCellValue());
                break;
            case Cell.CELL_TYPE_BOOLEAN:
                targetCell.setCellValue(sourceCell.getBooleanCellValue());
                break;
            case Cell.CELL_TYPE_FORMULA:
                targetCell.setCellFormula(sourceCell.getCellFormula());
                break;
            default:
                break;
        }
    }

    /**
     * Fill copied table with actual data
     */
    private void fillCopiedTable(Sheet sheet, int tableStart, List<Lop> tableClasses,
                                Map<Integer, List<LichGiangDay>> schedulesByClass,
                                Date tuNgay, SimpleDateFormat dateFormat, int tableNumber) {

        System.out.println("Filling table " + tableNumber + " starting at row " + tableStart);

        // Create replacement map for this specific table
        Map<String, String> tableReplacements = new HashMap<>();

        // Table title - remove table numbering
        tableReplacements.put("{{TABLE_TEMPLATE}}", "");
        tableReplacements.put("{{BANG_MAU}}", "");

        // Class names
        for (int i = 0; i < 4; i++) {
            String className = "";
            if (i < tableClasses.size() && tableClasses.get(i) != null) {
                className = tableClasses.get(i).getTenLop();
            }
            tableReplacements.put("{{COL_" + (i + 1) + "_NAME}}", className);
            tableReplacements.put("{{LOP_" + (i + 1) + "}}", className);
        }

        // Day variables
        String[] dayNames = {"THỨ 2", "THỨ 3", "THỨ 4", "THỨ 5", "THỨ 6", "THỨ 7", "CHỦ NHẬT"};
        for (int dayIndex = 0; dayIndex < 7; dayIndex++) {
            java.util.Calendar cal = java.util.Calendar.getInstance();
            cal.setTime(tuNgay);
            cal.add(java.util.Calendar.DAY_OF_MONTH, dayIndex);
            Date currentDate = cal.getTime();
            String dateKey = dateFormat.format(currentDate);

            tableReplacements.put("{{DAY_" + (dayIndex + 1) + "}}", dayNames[dayIndex] + " -\n" + dateKey);
        }

        // Schedule content
        createTableScheduleReplacements(tableReplacements, tableClasses, schedulesByClass, tuNgay, dateFormat);

        // Apply replacements to the copied table
        replaceVariablesInTableRange(sheet, tableStart, tableStart + 24, tableReplacements);
    }

    /**
     * Create schedule content replacements for table
     */
    private void createTableScheduleReplacements(Map<String, String> replacements, List<Lop> tableClasses,
                                                Map<Integer, List<LichGiangDay>> schedulesByClass,
                                                Date tuNgay, SimpleDateFormat dateFormat) {

        String[] sessionKeys = {"S", "C", "T"};
        String[] sessionNames = {"SANG", "CHIEU", "TOI"};

        for (int dayIndex = 0; dayIndex < 7; dayIndex++) {
            java.util.Calendar cal = java.util.Calendar.getInstance();
            cal.setTime(tuNgay);
            cal.add(java.util.Calendar.DAY_OF_MONTH, dayIndex);
            Date currentDate = cal.getTime();

            for (int sessionIndex = 0; sessionIndex < 3; sessionIndex++) {
                for (int colIndex = 0; colIndex < 4; colIndex++) {
                    String content = "";

                    if (colIndex < tableClasses.size() && tableClasses.get(colIndex) != null) {
                        Lop lop = tableClasses.get(colIndex);
                        content = getClassScheduleContent(lop, schedulesByClass, currentDate, sessionNames[sessionIndex]);
                    }

                    // Multiple variable formats for flexibility
                    String key1 = "{{D" + (dayIndex + 1) + "_" + sessionKeys[sessionIndex] + "_C" + (colIndex + 1) + "}}";
                    String key2 = "{{DAY_" + (dayIndex + 1) + "_" + sessionKeys[sessionIndex] + "_COL_" + (colIndex + 1) + "}}";

                    replacements.put(key1, content);
                    replacements.put(key2, content);
                }
            }
        }
    }

    /**
     * Replace variables in specific table range
     */
    private void replaceVariablesInTableRange(Sheet sheet, int startRow, int endRow, Map<String, String> replacements) {
        for (int rowIndex = startRow; rowIndex <= endRow && rowIndex <= sheet.getLastRowNum(); rowIndex++) {
            Row row = sheet.getRow(rowIndex);
            if (row != null) {
                for (int cellIndex = 0; cellIndex < 10; cellIndex++) {
                    Cell cell = row.getCell(cellIndex);
                    if (cell != null && cell.getCellType() == Cell.CELL_TYPE_STRING) {
                        String cellValue = cell.getStringCellValue();

                        if (cellValue.contains("{{") && cellValue.contains("}}")) {
                            String originalValue = cellValue;

                            for (Map.Entry<String, String> entry : replacements.entrySet()) {
                                String variable = entry.getKey();
                                String replacement = entry.getValue();

                                if (cellValue.contains(variable)) {
                                    cellValue = cellValue.replace(variable, replacement);
                                }
                            }

                            if (!cellValue.equals(originalValue)) {
                                cell.setCellValue(cellValue);
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * Remove original template table
     */
    private void removeTemplateTable(Sheet sheet, int templateStart, int templateEnd) {
        System.out.println("Removing original template table rows " + templateStart + "-" + templateEnd);

        // Clear template rows
        for (int i = templateStart; i <= templateEnd; i++) {
            Row row = sheet.getRow(i);
            if (row != null) {
                sheet.removeRow(row);
            }
        }

        // Shift remaining rows up
        int templateHeight = templateEnd - templateStart + 1;
        sheet.shiftRows(templateEnd + 1, sheet.getLastRowNum(), -templateHeight, true, false);
    }

    /**
     * Add footer to sheet once at the end with 3 columns
     */
    private void addFooterToSheet(Sheet sheet) {
        System.out.println("Adding 3-column footer to sheet...");

        // Find actual last row with content
        int actualLastRow = findActualLastRow(sheet);
        int footerStartRow = actualLastRow + 3; // 2 rows spacing before footer

        System.out.println("Actual last row: " + actualLastRow + ", Footer starts at: " + footerStartRow);
        // 3-column signature structure
        // Row 1: Main titles
        Row titleRow = sheet.createRow(footerStartRow++);
        titleRow.createCell(0).setCellValue("GIÁO VỤ");
        titleRow.createCell(3).setCellValue("KT. TRƯỞNG PHÒNG");  // Changed from column 2 to 3
        titleRow.createCell(5).setCellValue("HIỆU TRƯỞNG");       // Changed from column 4 to 6

        // Row 2: Sub-title for middle column
        Row subTitleRow = sheet.createRow(footerStartRow++);
        subTitleRow.createCell(3).setCellValue("P. TRƯỞNG PHÒNG"); // Changed from column 2 to 3

        // Empty rows for signature space
        sheet.createRow(footerStartRow++);
        sheet.createRow(footerStartRow++);

        // Row 3: Names
        Row nameRow = sheet.createRow(footerStartRow++);
        nameRow.createCell(0).setCellValue("Cn. LÊ MINH THƠI");
        nameRow.createCell(3).setCellValue("Ds.CK1. HUỲNH THANH BÌNH");  // Changed from column 2 to 3
        nameRow.createCell(5).setCellValue("TS. HUỲNH NGỌC LINH");        // Changed from column 4 to 6

        System.out.println("3-column footer added from row " + (footerStartRow - 5) + " to " + (footerStartRow - 1));
    }

    /**
     * Find actual last row with content (not just getLastRowNum which can be wrong)
     */
    private int findActualLastRow(Sheet sheet) {
        int lastRowWithContent = 0;

        // Scan from the end backwards to find last row with actual content
        for (int i = sheet.getLastRowNum(); i >= 0; i--) {
            Row row = sheet.getRow(i);
            if (row != null && !isRowEmpty(row)) {
                lastRowWithContent = i;
                break;
            }
        }

        System.out.println("Sheet.getLastRowNum(): " + sheet.getLastRowNum() + ", Actual last row with content: " + lastRowWithContent);
        return lastRowWithContent;
    }

    /**
     * Get only classes that have schedules in the given period
     */
    private List<Lop> getClassesWithSchedules(List<LichGiangDay> schedules) {
        Map<Integer, Lop> classMap = new HashMap<>();

        for (LichGiangDay schedule : schedules) {
            if (schedule.getLop() != null && schedule.getLop().getIdLop() != null) {
                classMap.put(schedule.getLop().getIdLop(), schedule.getLop());
            }
        }

        List<Lop> result = new ArrayList<>(classMap.values());
        System.out.println("Found " + result.size() + " classes with schedules out of total schedules: " + schedules.size());

        return result;
    }

    // Inner class for response data
    public static class InitData {
        public List<CanBo> canBo;
        public List<MonHoc> monHoc;
        public List<NienKhoa> nienKhoa;
        public List<PhongHoc> phongHoc;
    }
}
