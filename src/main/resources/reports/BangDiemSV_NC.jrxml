<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.10.0.final using JasperReports Library version 6.10.0  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="bangDiemSv" pageWidth="595" pageHeight="842" whenNoDataType="AllSectionsNoDetail" columnWidth="555" columnSpacing="3" leftMargin="20" rightMargin="20" topMargin="25" bottomMargin="10" uuid="17cc378a-d42d-40ba-b8b5-7b9e64456a0a">
	<subDataset name="MyDataset" uuid="150f38fc-c918-42fd-b541-37fd5eb1dccd">
		<queryString>
			<![CDATA[]]>
		</queryString>
		<field name="namHoc" class="java.lang.String"/>
		<field name="hocKy" class="java.lang.String"/>
		<field name="tenMonHoc" class="java.lang.String"/>
	</subDataset>
	<parameter name="hoTen" class="java.lang.String"/>
	<parameter name="tenDv" class="java.lang.String"/>
	<parameter name="nienKhoa" class="java.lang.String"/>
	<parameter name="tenNganh" class="java.lang.String"/>
	<parameter name="maSinhVien" class="java.lang.String"/>
	<parameter name="ngaySinh" class="java.sql.Date"/>
	<parameter name="heDaoTao" class="java.lang.String"/>
	<parameter name="soMonHoc" class="java.lang.Integer"/>
	<parameter name="diemTbtk" class="java.lang.String"/>
	<parameter name="maLop" class="java.lang.String"/>
	<parameter name="diemXltn" class="java.lang.String"/>
	<parameter name="ngayKy" class="java.lang.String"/>
	<parameter name="nguoiKy" class="java.lang.String"/>
	<parameter name="diemXltn4" class="java.lang.String"/>
	<parameter name="logo" class="java.lang.String"/>
	<parameter name="xltn" class="java.lang.String"/>
	<parameter name="tongDvht" class="java.lang.Integer"/>
	<parameter name="xltk" class="java.lang.String"/>
	<parameter name="soMonKTtn" class="java.lang.Integer"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="namHoc" class="java.lang.String"/>
	<field name="tenMonHoc" class="java.lang.String"/>
	<field name="hocKy" class="java.lang.String"/>
	<field name="maMonHoc" class="java.lang.String"/>
	<field name="soDVHT" class="java.lang.Integer"/>
	<field name="diemTb1" class="java.lang.String"/>
	<field name="diemTb2" class="java.lang.String"/>
	<field name="diemTbHk" class="java.lang.String"/>
	<field name="diemTbTl" class="java.lang.String"/>
	<field name="soDvhtTl" class="java.lang.Integer"/>
	<field name="ghiChu" class="java.lang.String"/>
	<field name="ngaySinh" class="java.sql.Date"/>
	<field name="hoTen" class="java.lang.String"/>
	<field name="maSv" class="java.lang.String"/>
	<field name="diem4Tb1" class="java.lang.String"/>
	<field name="diem4Tb2" class="java.lang.String"/>
	<field name="diem4TbHk" class="java.lang.String"/>
	<field name="diem4TbTl" class="java.lang.String"/>
	<field name="monTtn" class="java.lang.Boolean"/>
	<field name="xepLoaiHocKy" class="java.lang.String"/>
	<field name="xepLoaiNamHoc" class="java.lang.String"/>
	<field name="cuoiNam" class="java.lang.Boolean"/>
	<field name="diemTbNh" class="java.lang.String"/>
	<field name="soDVHTHk" class="java.lang.Integer"/>
	<field name="soDVHTNh" class="java.lang.Integer"/>
	<field name="tenNamHocHocKy" class="java.lang.String"/>
	<variable name="sum_dvht" class="java.lang.Integer" resetType="Group" resetGroup="namHoc_grp" calculation="Sum">
		<variableExpression><![CDATA[$F{soDVHT}]]></variableExpression>
	</variable>
	<variable name="CUR_PAGE_NUM" class="java.lang.Integer" resetType="None" incrementType="Page">
		<variableExpression><![CDATA[$V{PAGE_NUMBER}]]></variableExpression>
	</variable>
	<variable name="STT" class="java.lang.Integer" resetType="Group" resetGroup="namHoc_grp" calculation="Count">
		<variableExpression><![CDATA[$F{maMonHoc}]]></variableExpression>
	</variable>
	<variable name="stt_monhoc" class="java.lang.Integer" calculation="Count">
		<variableExpression><![CDATA[$F{maMonHoc}]]></variableExpression>
	</variable>
	<variable name="diemTb_hk" class="java.lang.String" resetType="Group" resetGroup="namHoc_grp" incrementType="Group" incrementGroup="namHoc_grp" calculation="Highest">
		<variableExpression><![CDATA[$F{diemTbHk}]]></variableExpression>
	</variable>
	<variable name="diem4Tb_hk" class="java.lang.String" resetType="Group" resetGroup="namHoc_grp" incrementType="Group" incrementGroup="namHoc_grp" calculation="Highest">
		<variableExpression><![CDATA[$F{diem4TbHk}]]></variableExpression>
	</variable>
	<variable name="dvhtTl" class="java.lang.Integer" resetType="Group" resetGroup="namHoc_grp" incrementType="Group" incrementGroup="namHoc_grp" calculation="Highest">
		<variableExpression><![CDATA[$F{soDvhtTl}]]></variableExpression>
	</variable>
	<variable name="diemTb_Tl" class="java.lang.String" resetType="Group" resetGroup="namHoc_grp" incrementType="Group" incrementGroup="namHoc_grp" calculation="Highest">
		<variableExpression><![CDATA[$F{diemTbTl}]]></variableExpression>
	</variable>
	<variable name="diemTb4_Tl" class="java.lang.String" resetType="Group" resetGroup="namHoc_grp" incrementType="Group" incrementGroup="namHoc_grp" calculation="Highest">
		<variableExpression><![CDATA[$F{diem4TbTl}]]></variableExpression>
	</variable>
	<variable name="XepLoaiNamHoc" class="java.lang.String" resetType="Group" resetGroup="namHoc_grp" incrementType="Group" incrementGroup="namHoc_grp" calculation="Highest">
		<variableExpression><![CDATA[$F{xepLoaiNamHoc}]]></variableExpression>
	</variable>
	<variable name="dvhtHk" class="java.lang.Integer" resetType="Group" resetGroup="namHoc_grp" incrementType="Group" incrementGroup="namHoc_grp" calculation="Highest">
		<variableExpression><![CDATA[$F{soDVHTHk}]]></variableExpression>
	</variable>
	<variable name="dvhtNh" class="java.lang.Integer" resetType="Group" resetGroup="namHoc_grp" incrementType="Group" incrementGroup="namHoc_grp" calculation="Highest">
		<variableExpression><![CDATA[$F{soDVHTNh}]]></variableExpression>
	</variable>
	<variable name="diemTb_nh" class="java.lang.String" resetType="Group" resetGroup="namHoc_grp" incrementType="Group" incrementGroup="namHoc_grp" calculation="Highest">
		<variableExpression><![CDATA[$F{diemTbNh}]]></variableExpression>
	</variable>
	<variable name="XepLoaiHocKy" class="java.lang.String" resetType="Group" resetGroup="namHoc_grp" incrementType="Group" incrementGroup="namHoc_grp" calculation="Highest">
		<variableExpression><![CDATA[$F{xepLoaiHocKy}]]></variableExpression>
	</variable>
	<variable name="dvht_ttn" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[$F{monTtn}?$F{soDVHT}:0]]></variableExpression>
	</variable>
	<group name="namHoc_grp">
		<groupExpression><![CDATA[$F{tenNamHocHocKy}]]></groupExpression>
		<groupHeader>
			<band height="18">
				<textField isBlankWhenNull="true">
					<reportElement x="0" y="0" width="555" height="18" uuid="c3bca2bd-c0d6-4023-87bf-e7dec3143c44">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box padding="2">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{tenNamHocHocKy}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="54">
				<textField isStretchWithOverflow="true">
					<reportElement stretchType="RelativeToTallestObject" x="0" y="0" width="190" height="18" isRemoveLineWhenBlank="true" uuid="8bc7dccd-bef4-47f5-995d-cfde1fd8c45e">
						<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<printWhenExpression><![CDATA[!$F{monTtn} && $F{hocKy}.indexOf( "Hè" ) == -1]]></printWhenExpression>
					</reportElement>
					<box padding="1">
						<pen lineWidth="0.5" lineStyle="Solid"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="9" isBold="true"/>
						<paragraph leftIndent="0"/>
					</textElement>
					<textFieldExpression><![CDATA["Tổng số ĐVHT học kỳ: " +$V{dvhtHk}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement stretchType="RelativeToTallestObject" x="0" y="18" width="190" height="18" isRemoveLineWhenBlank="true" uuid="79f4490a-4f5a-469c-aae7-4bd78ee87cf2">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<printWhenExpression><![CDATA[!$F{monTtn}&&$F{cuoiNam}]]></printWhenExpression>
					</reportElement>
					<box padding="1">
						<pen lineWidth="0.5" lineStyle="Solid"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="9" isBold="true"/>
						<paragraph leftIndent="0"/>
					</textElement>
					<textFieldExpression><![CDATA["Tổng số ĐVHT năm học: " +$V{dvhtNh}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement stretchType="RelativeToTallestObject" x="0" y="36" width="190" height="18" isRemoveLineWhenBlank="true" uuid="5fea698f-301e-4045-a41f-5b1ab4fe55d3">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<printWhenExpression><![CDATA[ $V{dvht_ttn} > 0]]></printWhenExpression>
					</reportElement>
					<box padding="1">
						<pen lineWidth="0.5" lineStyle="Solid"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="9" isBold="true"/>
						<paragraph leftIndent="0"/>
					</textElement>
					<textFieldExpression><![CDATA["Tổng số ĐVHT toàn khóa: " +$P{tongDvht}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement stretchType="RelativeToTallestObject" x="190" y="36" width="217" height="18" isRemoveLineWhenBlank="true" uuid="f74c250d-dfbb-48cf-aa22-b057307e6013">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<printWhenExpression><![CDATA[$V{dvht_ttn} > 0]]></printWhenExpression>
					</reportElement>
					<box padding="1">
						<pen lineWidth="0.5" lineStyle="Solid"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="9" isBold="true"/>
						<paragraph leftIndent="0"/>
					</textElement>
					<textFieldExpression><![CDATA["Điểm trung bình chung toàn khóa: " +$P{diemTbtk}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement stretchType="RelativeToTallestObject" x="407" y="36" width="148" height="18" isRemoveLineWhenBlank="true" uuid="53badf8b-28bf-4e1f-9035-af296e13d6bc">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<printWhenExpression><![CDATA[ $V{dvht_ttn} > 0]]></printWhenExpression>
					</reportElement>
					<box padding="1">
						<pen lineWidth="0.5" lineStyle="Solid"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="9" isBold="true"/>
						<paragraph leftIndent="0"/>
					</textElement>
					<textFieldExpression><![CDATA["Xếp loại: " + $P{xltk}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement stretchType="RelativeToTallestObject" x="190" y="18" width="217" height="18" isRemoveLineWhenBlank="true" uuid="d1187c1c-5293-40e8-b1a2-4c4f4c36038a">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<printWhenExpression><![CDATA[!$F{monTtn}&&$F{cuoiNam}]]></printWhenExpression>
					</reportElement>
					<box padding="1">
						<pen lineWidth="0.5" lineStyle="Solid"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="9" isBold="true"/>
						<paragraph leftIndent="0"/>
					</textElement>
					<textFieldExpression><![CDATA["Điểm trung bình chung năm học: " +$V{diemTb_nh}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement stretchType="RelativeToTallestObject" x="407" y="18" width="148" height="18" isRemoveLineWhenBlank="true" uuid="e55ab3b2-6bbb-49bd-b738-714225cc8a6d">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<printWhenExpression><![CDATA[!$F{monTtn}&&$F{cuoiNam}]]></printWhenExpression>
					</reportElement>
					<box padding="1">
						<pen lineWidth="0.5" lineStyle="Solid"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="9" isBold="true"/>
						<paragraph leftIndent="0"/>
					</textElement>
					<textFieldExpression><![CDATA["Xếp loại: " + $V{XepLoaiNamHoc}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement stretchType="RelativeToTallestObject" x="190" y="0" width="217" height="18" isRemoveLineWhenBlank="true" uuid="bf0a1bfc-4865-4343-8b03-37d9ecb97951">
						<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<printWhenExpression><![CDATA[!$F{monTtn} && $F{hocKy}.indexOf( "Hè" ) == -1]]></printWhenExpression>
					</reportElement>
					<box padding="1">
						<pen lineWidth="0.5" lineStyle="Solid"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="9" isBold="true"/>
						<paragraph leftIndent="0"/>
					</textElement>
					<textFieldExpression><![CDATA["Điểm trung bình chung học kỳ: " +$V{diemTb_hk}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement stretchType="RelativeToTallestObject" x="407" y="0" width="148" height="18" isRemoveLineWhenBlank="true" uuid="055b8a50-5caf-43d7-844b-7f155016e922">
						<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<printWhenExpression><![CDATA[!$F{monTtn} && $F{hocKy}.indexOf( "Hè" ) == -1]]></printWhenExpression>
					</reportElement>
					<box padding="1">
						<pen lineWidth="0.5" lineStyle="Solid"/>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="9" isBold="true"/>
						<paragraph leftIndent="0"/>
					</textElement>
					<textFieldExpression><![CDATA["Xếp loại: " + $V{XepLoaiHocKy}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<title>
		<band height="124" splitType="Stretch">
			<textField>
				<reportElement stretchType="RelativeToTallestObject" x="31" y="12" width="141" height="12" uuid="c0a7accf-a08d-4537-9a25-fbf69a903954">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{tenDv}.toUpperCase()]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="35" width="555" height="12" uuid="6719fcf0-a34b-4c5b-b69c-73828eb2eddc">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[BẢNG ĐIỂM TOÀN KHÓA]]></text>
			</staticText>
			<textField>
				<reportElement x="52" y="47" width="222" height="12" uuid="dff88797-ae71-4c0c-8e39-94fb830ea6e1">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{hoTen}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="52" y="71" width="222" height="12" uuid="690e7d44-e705-4801-9158-1d058e083349">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{maSinhVien}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="321" y="47" width="234" height="12" uuid="02748db0-9b01-47dd-abb6-d6441d85579d">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{maLop}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement stretchType="RelativeToTallestObject" x="31" y="0" width="141" height="12" uuid="cec58423-ccab-4f0f-aca3-c4343e2b985e">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<text><![CDATA[UBND TỈNH CÀ MAU]]></text>
			</staticText>
			<staticText>
				<reportElement x="355" y="12" width="200" height="12" uuid="4c654aed-6e4d-469a-a051-b6661b2f0f8a">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Độc lập - Tự do - Hạnh phúc]]></text>
			</staticText>
			<staticText>
				<reportElement x="355" y="0" width="200" height="12" uuid="b77b82c7-f8e9-4542-9c00-7fa2abbbf195">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM]]></text>
			</staticText>
			<line>
				<reportElement x="72" y="25" width="57" height="1" uuid="2391ca85-6365-4a7d-aa68-3caece6bb5e0">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</line>
			<staticText>
				<reportElement x="0" y="47" width="52" height="12" uuid="5769b2d9-1d94-465b-9a89-73c9983c36f6">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<text><![CDATA[Sinh viên]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="71" width="52" height="12" uuid="bd402ec9-866b-48b3-a04e-0a4e9d602d45">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<text><![CDATA[Mã sinh viên]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="59" width="52" height="12" uuid="860fa91f-77ed-4237-ae5f-a9902aebfdb6">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<text><![CDATA[Ngày sinh]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToTallestObject" x="279" y="71" width="42" height="12" uuid="4e134712-ba17-4e8f-997a-36a163462143">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<text><![CDATA[Hệ đào tạo]]></text>
			</staticText>
			<staticText>
				<reportElement x="279" y="47" width="42" height="12" uuid="b1107ef7-1804-416a-b3ed-c8044a03911e">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<text><![CDATA[Lớp]]></text>
			</staticText>
			<textField>
				<reportElement x="52" y="59" width="222" height="12" uuid="faec9c5d-f61a-48d7-a8b4-f77dc2734675">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[DATEFORMAT($P{ngaySinh},"dd/MM/yyyy")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement stretchType="RelativeToTallestObject" x="321" y="71" width="234" height="12" uuid="5a18aa67-abd2-4dfe-9a55-e3cde3d63c77">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{heDaoTao}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="407" y="24" width="95" height="1" uuid="80c1b187-edd5-489c-9619-8da49a9d6710">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
			</line>
			<staticText>
				<reportElement stretchType="RelativeToTallestObject" x="279" y="59" width="42" height="12" uuid="c67a42c4-3ddf-4d93-8f9e-e43ab3680c44">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<text><![CDATA[Ngành]]></text>
			</staticText>
			<textField>
				<reportElement stretchType="RelativeToTallestObject" x="321" y="59" width="234" height="12" uuid="103176ff-82ed-4a00-b8b1-626d0c520a1e">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{tenNganh}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="420" y="106" width="80" height="18" isRemoveLineWhenBlank="true" uuid="4614796b-147d-48fc-a90a-e481147d9f04">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box padding="0">
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Lần 2]]></text>
			</staticText>
			<staticText>
				<reportElement x="300" y="83" width="40" height="41" uuid="c811146a-fc3b-4bc4-b8cb-376a9e62fe42">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box padding="0">
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[ĐVHT]]></text>
			</staticText>
			<staticText>
				<reportElement x="25" y="83" width="55" height="41" isRemoveLineWhenBlank="true" uuid="40b881f4-f30e-41f9-80cd-c34dfcc70ecb">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box padding="0">
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Mã MH]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="83" width="25" height="41" uuid="6cad9724-ea5e-4f1a-89d2-66c5d3449ec2">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box padding="0">
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[STT]]></text>
			</staticText>
			<staticText>
				<reportElement x="500" y="83" width="55" height="41" uuid="37fa930d-a97d-472d-8472-198cc7cef7df">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box padding="0">
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Ghi chú]]></text>
			</staticText>
			<staticText>
				<reportElement x="80" y="83" width="220" height="41" uuid="9afd19ba-3570-4aa2-a80c-07203cad614f">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box padding="0">
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Tên môn học]]></text>
			</staticText>
			<staticText>
				<reportElement x="340" y="83" width="160" height="23" uuid="ab12989e-3f5f-4407-a3a6-5fc1ee6667f5">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="0">
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Điểm Môn học]]></text>
			</staticText>
			<staticText>
				<reportElement x="340" y="106" width="80" height="18" uuid="cf70d9fb-e19a-47a8-bdd0-e2c0ced9fa34">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box padding="0">
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Lần 1]]></text>
			</staticText>
			<image>
				<reportElement x="-3" y="-3" width="34" height="29" uuid="fe4d2150-ada9-4f69-b847-9f5a89c84d78"/>
				<imageExpression><![CDATA[new java.io.ByteArrayInputStream(org.apache.commons.codec.binary.Base64.decodeBase64($P{logo}.getBytes()))]]></imageExpression>
			</image>
		</band>
	</title>
	<detail>
		<band height="18">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="80" y="0" width="220" height="18" uuid="7814467d-aa09-41d9-8486-b2b79a2a71db">
					<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="1" leftPadding="3" bottomPadding="1" rightPadding="1">
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="false"/>
					<paragraph leftIndent="0"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{tenMonHoc}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement stretchType="RelativeToTallestObject" x="0" y="0" width="25" height="18" uuid="3ca0df67-69c7-4bbf-9777-839551ea62d4"/>
				<box topPadding="1" leftPadding="3" bottomPadding="1" rightPadding="1">
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{STT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="25" y="0" width="55" height="18" uuid="109ccc6f-52fe-4039-9c58-0b2733b12b70"/>
				<box topPadding="1" leftPadding="3" bottomPadding="1" rightPadding="1">
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{maMonHoc}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="300" y="0" width="40" height="18" uuid="ec2a1c9c-b904-4ac5-b7d9-d3da3433e1ad">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="1" leftPadding="3" bottomPadding="1" rightPadding="1">
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{soDVHT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement stretchType="RelativeToTallestObject" x="500" y="0" width="55" height="18" uuid="26f6cdfc-b0f2-41ba-a664-f766c51cec01">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="1" leftPadding="3" bottomPadding="1" rightPadding="1">
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ghiChu}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="340" y="0" width="80" height="18" uuid="9dcf9944-6d56-4358-8a79-1547ed2a422c">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="1" leftPadding="3" bottomPadding="1" rightPadding="1">
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{diemTb1}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="420" y="0" width="80" height="18" uuid="bb9b84b7-4637-4d5a-a052-670fa3eb0840">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="1" leftPadding="3" bottomPadding="1" rightPadding="1">
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{diemTb2}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<pageFooter>
		<band height="15">
			<textField evaluationTime="Auto">
				<reportElement x="0" y="0" width="555" height="15" uuid="dc2dda64-73b2-471f-aaa5-3b8effca7ab0">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA["Trang "+$V{CUR_PAGE_NUM}+"/"+$V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
	<summary>
		<band height="152">
			<textField>
				<reportElement x="281" y="18" width="274" height="12" isRemoveLineWhenBlank="true" uuid="f2bd5adc-8fff-4340-b8ef-b5e51dbde8ca">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{ngayKy}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="281" y="54" width="274" height="58" isRemoveLineWhenBlank="true" uuid="158147dc-180c-494d-a472-16fa4339000e">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Times New Roman" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{nguoiKy}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="281" y="30" width="274" height="12" isRemoveLineWhenBlank="true" uuid="90b90a61-2610-42f6-9e8b-5564e9272377">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["KT. HIỆU TRƯỞNG"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="281" y="42" width="274" height="12" isRemoveLineWhenBlank="true" uuid="059bb29e-8bc1-48a4-a6db-4b91f18f4a2a">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["PHÓ HIỆU TRƯỞNG"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="0" width="555" height="18" isRemoveLineWhenBlank="true" uuid="e17bc050-ae39-4632-8e3f-6dbad971f808">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA[$V{dvht_ttn} > 0]]></printWhenExpression>
				</reportElement>
				<box padding="1"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true" isItalic="false"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<textFieldExpression><![CDATA["Điểm đánh giá xếp loại tốt nghiệp: " + $P{diemXltn}+"\t\t"+ "Xếp loại tốt nghiệp: "+ $P{xltn}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="27" width="200" height="12" uuid="06b7b840-e4a3-4422-9c4d-5bc100910d88">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[TRƯỞNG PHÒNG ĐÀO TẠO]]></text>
			</staticText>
		</band>
	</summary>
</jasperReport>
