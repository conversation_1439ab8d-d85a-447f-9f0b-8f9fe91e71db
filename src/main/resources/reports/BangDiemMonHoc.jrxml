<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.11.0.final using JasperReports Library version 6.10.0  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="BangDiemMonHoc" pageWidth="842" pageHeight="595" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="758" leftMargin="42" rightMargin="42" topMargin="42" bottomMargin="42" uuid="17cc378a-d42d-40ba-b8b5-7b9e64456a0a">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<property name="net.sf.jasperreports.export.xls.detect.cell.type" value="true"/>
	<property name="net.sf.jasperreports.export.xls.white.page.background" value="false"/>
	<property name="net.sf.jasperreports.export.xls.remove.empty.space.between.rows" value="true"/>
	<subDataset name="MyDataset" uuid="150f38fc-c918-42fd-b541-37fd5eb1dccd">
		<queryString>
			<![CDATA[]]>
		</queryString>
		<field name="namHoc" class="java.lang.String"/>
		<field name="hocKy" class="java.lang.String"/>
		<field name="tenMonHoc" class="java.lang.String"/>
	</subDataset>
	<parameter name="tenHocky" class="java.lang.String"/>
	<parameter name="tenDv" class="java.lang.String"/>
	<parameter name="tenNamHoc" class="java.lang.String"/>
	<parameter name="tenMonHoc" class="java.lang.String"/>
	<parameter name="tenNhom" class="java.lang.String"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="hoTen" class="java.lang.String"/>
	<field name="maSinhVien" class="java.lang.String"/>
	<field name="maLop" class="java.lang.String"/>
	<field name="tenLop" class="java.lang.String"/>
	<field name="lyDo" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="190" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<staticText>
				<reportElement x="0" y="0" width="360" height="20" uuid="69298ada-b9ee-44c0-8ccf-16122442d97a">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="13"/>
				</textElement>
				<text><![CDATA[UBND TỈNH CÀ MAU]]></text>
			</staticText>
			<textField>
				<reportElement x="0" y="20" width="360" height="30" uuid="c0a7accf-a08d-4537-9a25-fbf69a903954">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Times New Roman" size="13" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{tenDv}.toUpperCase()]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="360" y="20" width="400" height="30" uuid="1d8423cf-95b2-4743-add1-d65e366adcd8">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="14" isBold="true" isUnderline="false"/>
				</textElement>
				<text><![CDATA[Độc lập - Tự do - Hạnh phúc]]></text>
			</staticText>
			<staticText>
				<reportElement x="360" y="0" width="400" height="20" uuid="b710e624-6f91-42b6-bf4c-147348b055ba">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="50" width="760" height="50" uuid="3752416f-b941-4231-8da3-5c2c1dd34324">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Times New Roman" size="15" isBold="true"/>
				</textElement>
				<text><![CDATA[DANH SÁCH ĐIỂM SINH VIÊN]]></text>
			</staticText>
			<textField>
				<reportElement x="0" y="100" width="760" height="30" uuid="262ba185-7340-4ecd-a5d5-4a6504c18a76"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["HỌC KỲ "+ $P{tenHocky} +" NĂM HỌC "+$P{tenNamHoc}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="130" width="760" height="30" uuid="d2b6cb4e-44e5-4c25-b0bf-4a21c4f6d9ae">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Môn học: "+$P{tenMonHoc}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="160" width="760" height="30" uuid="2689bcc5-8986-439b-8158-047f909b32a0">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Lớp: "+$P{tenNhom}]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<pageFooter>
		<band height="65">
			<textField>
				<reportElement x="0" y="45" width="760" height="20" uuid="fe35cf79-1478-431b-975f-196952c75b8b"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA["Trang: "+ $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
	<summary>
		<band height="85">
			<staticText>
				<reportElement x="360" y="20" width="400" height="65" uuid="60d3bb65-99f2-48e7-ba08-831c4d8a9687"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="13" isBold="true" isItalic="false"/>
				</textElement>
				<text><![CDATA[Người lập biểu]]></text>
			</staticText>
			<textField>
				<reportElement x="0" y="0" width="360" height="20" uuid="94d54a3c-53f0-4b1e-8846-584cda4fb135"/>
				<textElement textAlignment="Left">
					<font fontName="Times New Roman" size="13" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["In ngày "+((DAY( TODAY( )))  >= 10 ? DAY( TODAY( )) :"0"+DAY( TODAY( )))+" tháng "+(MONTH(TODAY( ) ) >= 10? MONTH(TODAY( ) ): "0"+MONTH(TODAY( ) ))+" năm "+YEAR(TODAY( ) )]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="490" y="0" width="270" height="20" uuid="9c62fd4c-9d29-460e-b193-d3e17d5706b4"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="13" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Cà Mau, ngày "+((DAY( TODAY( )))  >= 10 ? DAY( TODAY( )) :"0"+DAY( TODAY( )))+" tháng "+(MONTH(TODAY( ) ) >= 10? MONTH(TODAY( ) ): "0"+MONTH(TODAY( ) ))+" năm "+YEAR(TODAY( ) )]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="360" y="0" width="130" height="20" uuid="024e56f4-8d07-49c6-a040-5dd2c3661d68"/>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="20" width="360" height="65" uuid="b90660fc-4c09-4d66-a4b4-36f6aa87db10"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="13" isBold="true" isItalic="false"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
		</band>
	</summary>
</jasperReport>
