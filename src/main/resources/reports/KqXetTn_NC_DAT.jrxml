<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.10.0.final using JasperReports Library version 6.10.0  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="KqXetTn_NC_DAT" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" isIgnorePagination="true" uuid="3d3d9ff2-71e0-4410-bae9-8c1a502a96d3">
	<parameter name="tenTieuDe" class="java.lang.String"/>
	<parameter name="tenKhoi" class="java.lang.String"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="l1" class="java.util.List"/>
	<field name="l2" class="java.util.List"/>
	<field name="s2" class="java.lang.String"/>
	<field name="s3" class="java.lang.String"/>
	<field name="s4" class="java.lang.String"/>
	<field name="s5" class="java.lang.String"/>
	<field name="s6" class="java.lang.String"/>
	<field name="s7" class="java.lang.String"/>
	<field name="s8" class="java.lang.String"/>
	<field name="s9" class="java.lang.String"/>
	<field name="s10" class="java.lang.String"/>
	<field name="b1" class="java.lang.Boolean"/>
	<field name="s11" class="java.lang.String"/>
	<field name="s12" class="java.lang.String"/>
	<field name="s13" class="java.lang.String"/>
	<variable name="slXs" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[$F{s9}.equals( "Xuất sắc" )?1:0]]></variableExpression>
	</variable>
	<variable name="slKha" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[$F{s9}.equals( "Khá" )?1:0]]></variableExpression>
	</variable>
	<variable name="slTbKha" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[$F{s9}.equals( "Trung bình khá" )?1:0]]></variableExpression>
	</variable>
	<variable name="slTb" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[$F{s9}.equals( "Trung bình" )?1:0]]></variableExpression>
	</variable>
	<variable name="slGioi" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[$F{s9}.equals( "Giỏi" )?1:0]]></variableExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="183" splitType="Stretch">
			<staticText>
				<reportElement x="0" y="20" width="400" height="20" uuid="dd837cfc-f490-44d6-a13b-8f9e6dbabed8">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="13" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[TRƯỜNG CAO ĐẲNG Y TẾ]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="0" width="400" height="20" uuid="97d0538e-950d-48ee-a4dc-249df273c322">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="13"/>
				</textElement>
				<text><![CDATA[UBND TỈNH CÀ MAU]]></text>
			</staticText>
			<staticText>
				<reportElement x="400" y="0" width="402" height="20" uuid="544377fb-69ff-49e1-ba20-dc9f3fab3cda">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM]]></text>
			</staticText>
			<staticText>
				<reportElement x="400" y="20" width="402" height="20" uuid="bf199151-0dda-44a4-9296-0b529a95a9cf">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Độc lập - Tự do - Hạnh phúc]]></text>
			</staticText>
			<textField>
				<reportElement x="400" y="40" width="402" height="21" uuid="20b6df32-9aea-4b3f-93e2-8cbce3398104">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Cà Mau, ngày "+((DAY( TODAY( )))  >= 10 ? DAY( TODAY( )) :"0"+DAY( TODAY( )))+" tháng "+(MONTH(TODAY( ) ) >= 10? MONTH(TODAY( ) ): "0"+MONTH(TODAY( ) ))+" năm "+YEAR(TODAY( ) )]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="61" width="802" height="20" uuid="c2def95e-04a5-4a24-9f14-017a6788c6e9">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{tenTieuDe}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="81" width="802" height="20" uuid="94a6dd8b-0e61-43c3-b956-35cf5b0cc636">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13" isBold="false"/>
				</textElement>
				<text><![CDATA[(Kèm theo quyết định số........./QĐ-CĐYT ngày                 của hiệu trưởng trường CĐYT Cà Mau)]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="101" width="802" height="20" uuid="d0c87655-185f-4044-8d7a-f13114023aad">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13" isBold="true"/>
				</textElement>
				<text><![CDATA[KỲ THI: NGÀY]]></text>
			</staticText>
			<textField isStretchWithOverflow="true">
				<reportElement x="0" y="121" width="802" height="20" uuid="69be5fd7-b98a-47c0-add7-bba68b4de3a7">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["LỚP: " + $P{tenKhoi}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="600" y="141" width="68" height="42" uuid="d4d25dbe-d1fd-4448-baf0-8cc0c1bc387b"/>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Xếp loại TN]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="141" width="40" height="42" uuid="de4a459d-b84e-4cdf-83e7-d9d128a43c94"/>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[STT]]></text>
			</staticText>
			<textField>
				<reportElement x="514" y="163" width="40" height="20" uuid="3e26c8ff-2cfe-44be-bc26-947b8561fbeb">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{l1}.size() >= 3?$F{l1}.get( 2 ):""]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="668" y="141" width="134" height="42" uuid="13ddf588-38d1-413f-b22d-abc1aa97a1c8">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Ghi chú]]></text>
			</staticText>
			<staticText>
				<reportElement x="554" y="141" width="46" height="42" uuid="deca69cc-b166-4874-b185-b234c5b64a23"/>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[ĐXLTN]]></text>
			</staticText>
			<staticText>
				<reportElement x="299" y="141" width="61" height="42" uuid="5422d9be-458a-438a-a667-31485ad58cc7">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Ngày sinh]]></text>
			</staticText>
			<textField>
				<reportElement x="434" y="163" width="40" height="20" uuid="670d0a58-5077-45f1-aebe-739cdc1cc3ce">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{l1}.size() >= 1?$F{l1}.get( 0 ):""]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="40" y="141" width="60" height="42" uuid="31e545c2-a84f-4be5-a6a4-a817bfee48ea"/>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Mã SV]]></text>
			</staticText>
			<staticText>
				<reportElement x="250" y="141" width="49" height="42" uuid="ae8ec1af-a451-4320-8865-a45883e8a47d"/>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Mã Lớp]]></text>
			</staticText>
			<staticText>
				<reportElement x="400" y="141" width="34" height="42" uuid="*************-445d-9194-d8a4f3050159"/>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Điểm TBTK]]></text>
			</staticText>
			<textField>
				<reportElement x="474" y="163" width="40" height="20" uuid="ab66c245-41e0-4f59-8644-a9f5b7899c3f">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{l1}.size() >= 2?$F{l1}.get( 1 ):""]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="100" y="141" width="150" height="42" uuid="5daefcd4-d2ee-4ffa-ba3f-c0c72c13e161">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Họ tên sinh viên]]></text>
			</staticText>
			<staticText>
				<reportElement x="360" y="141" width="40" height="42" uuid="cbc1e992-419a-4ab4-b020-847beab46943">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Giới]]></text>
			</staticText>
			<staticText>
				<reportElement x="434" y="141" width="120" height="22" uuid="4de74bbc-4282-4707-8a5f-62732d4a378a"/>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Kết quả thi tốt nghiệp]]></text>
			</staticText>
		</band>
	</title>
	<detail>
		<band height="20" splitType="Stretch">
			<textField isStretchWithOverflow="true">
				<reportElement stretchType="RelativeToTallestObject" x="434" y="0" width="40" height="20" uuid="51730852-ccf5-45b7-8e68-8ef37f9ff879"/>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{l2}.size() >= 1?$F{l2}.get( 0 ):""]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement stretchType="RelativeToTallestObject" x="474" y="0" width="40" height="20" uuid="1daebf17-67c7-41c0-8d47-f378ba74ec44"/>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{l2}.size() >= 2?$F{l2}.get( 1 ):""]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement stretchType="RelativeToTallestObject" x="600" y="0" width="68" height="20" uuid="533a9588-b2b5-4734-914f-65aa65a653ae"/>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{s9}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement stretchType="RelativeToTallestObject" x="554" y="0" width="46" height="20" uuid="fc546959-7292-492f-a0f4-4f6e0d82fc7b"/>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{s8}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement stretchType="RelativeToTallestObject" x="250" y="0" width="49" height="20" uuid="0ea0b9c6-8562-4ec2-ad81-ee79a514b8cd"/>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{s5}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement stretchType="RelativeToTallestObject" x="400" y="0" width="34" height="20" uuid="28c3e79c-7628-40ce-8fdc-4c3502d271d5"/>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{s7}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement stretchType="RelativeToTallestObject" x="299" y="0" width="61" height="20" uuid="650349f1-a9fb-4f90-bc39-2c4a115ffdf8"/>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{s6}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement stretchType="RelativeToTallestObject" x="100" y="0" width="150" height="20" uuid="5ad95867-9a9f-4be6-bfcb-a80d6f5cb485"/>
				<box padding="3">
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{s3}+ " " + $F{s4}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement stretchType="RelativeToTallestObject" x="514" y="0" width="40" height="20" uuid="8e46d07a-8786-454c-9f29-e87cd0ac0c6e"/>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{l2}.size() >= 3?$F{l2}.get( 2 ):""]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement stretchType="RelativeToTallestObject" x="360" y="0" width="40" height="20" uuid="0609aef1-4ab0-48e9-b212-0956e09fce21">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{b1}?"Nữ":"Nam"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement stretchType="RelativeToTallestObject" x="40" y="0" width="60" height="20" uuid="8e0f42c1-c918-40d7-9195-ccab9f438133"/>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{s2}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement stretchType="RelativeToTallestObject" x="0" y="0" width="40" height="20" uuid="1149a5c7-2b69-431f-b0a3-564eb18a411b"/>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{REPORT_COUNT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement stretchType="RelativeToTallestObject" x="668" y="0" width="134" height="20" uuid="85057f5e-c811-4699-b88a-f9f08c8d899a">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{s13}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="241" splitType="Stretch">
			<textField>
				<reportElement x="0" y="0" width="299" height="100" uuid="72626464-cc49-4fef-b503-e831449a7f85">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="13" isBold="true" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["HIỆU TRƯỞNG" +"\n"+"CHỦ TỊCH HỘI ĐỒNG"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="474" y="0" width="328" height="100" uuid="cb4900aa-30b9-4638-8efd-0be53ebd67a1">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="13" isBold="true" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["\n"+"TRƯỞNG BAN THƯ KÝ"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="101" width="60" height="20" uuid="eaa43441-b882-42b4-8d88-57aba6224fc5">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Xếp loại]]></text>
			</staticText>
			<staticText>
				<reportElement x="60" y="101" width="60" height="20" uuid="68207b03-5590-45a4-b01c-8879817b7959">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Số lượng]]></text>
			</staticText>
			<staticText>
				<reportElement x="120" y="101" width="60" height="20" uuid="6ed8c08f-e3fc-4565-8df7-14bea58043a0">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Tỉ lệ]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="121" width="60" height="20" uuid="a77d52b3-140f-49a8-afe0-aafd67d7a536">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<text><![CDATA[Xuất sắc]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="141" width="60" height="20" uuid="d3da7770-bd9d-4276-bdb5-9bdaca6fff46">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<text><![CDATA[Giỏi]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="161" width="60" height="20" uuid="db262222-2c1a-4cdf-ac36-b8c78e5838ea">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<text><![CDATA[Khá]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="181" width="60" height="20" uuid="9220ed19-a88c-431b-943b-a30fbe2c04f1">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<text><![CDATA[TB khá]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="201" width="60" height="20" uuid="53f535e7-29f0-4d79-852e-d43c1389e264">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<text><![CDATA[Trung bình]]></text>
			</staticText>
			<textField>
				<reportElement x="60" y="121" width="60" height="20" uuid="9e02308a-f738-47a0-9e2e-7cddf119d7c3">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{slXs}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="60" y="141" width="60" height="20" uuid="8b02e4a8-8d63-44de-a150-dfc1132094be">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{slGioi}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="60" y="161" width="60" height="20" uuid="4460db89-42c5-4bb1-825b-fff3193a7871">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{slKha}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="60" y="181" width="60" height="20" uuid="4044a2ed-8a42-4ede-8c63-4d0d71139c76">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{slTbKha}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="60" y="201" width="60" height="20" uuid="c748c142-bdfb-41cc-9ae0-8306013989e0">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{slTb}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="120" y="121" width="60" height="20" uuid="0fc09845-be4f-46c7-b9a9-07e5168d59b4">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{slXs} == 0?0 + " %":(new BigDecimal(new Double( ($V{slXs}.doubleValue() / $V{REPORT_COUNT}.doubleValue() )*100)).setScale(2, BigDecimal.ROUND_HALF_UP)) + " %"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="120" y="141" width="60" height="20" uuid="bc2939be-3f42-43e9-b1f4-8da267a2453f">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{slGioi} == 0?0 + " %":(new BigDecimal(new Double( ($V{slGioi} .doubleValue() / $V{REPORT_COUNT}.doubleValue() )*100)).setScale(2, BigDecimal.ROUND_HALF_UP)) + " %"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="120" y="161" width="60" height="20" uuid="82394d88-db4b-48b7-ba8e-6111052a480d">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{slKha} == 0?0 + " %":(new BigDecimal(new Double( ($V{slKha} .doubleValue() / $V{REPORT_COUNT}.doubleValue() )*100)).setScale(2, BigDecimal.ROUND_HALF_UP)) + " %"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="120" y="181" width="60" height="20" uuid="55ab374c-615a-452d-bd23-0f73ce0a1e75">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{slTbKha} == 0?0 + " %":(new BigDecimal(new Double( ($V{slTbKha} .doubleValue() / $V{REPORT_COUNT}.doubleValue() )*100)).setScale(2, BigDecimal.ROUND_HALF_UP)) + " %"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="120" y="201" width="60" height="20" uuid="4487218d-40f9-4d58-9878-83e977d368cb">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{slTb} == 0?0 + " %":(new BigDecimal(new Double( ($V{slTb} .doubleValue() / $V{REPORT_COUNT}.doubleValue() )*100)).setScale(2, BigDecimal.ROUND_HALF_UP)) + " %"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="221" width="60" height="20" uuid="63ce96d9-1eaa-435b-ab22-d962caaed09e">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Tổng]]></text>
			</staticText>
			<textField>
				<reportElement x="60" y="221" width="60" height="20" uuid="93bd8759-18f2-4cb0-b646-c558a5dd66a2">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{slXs}+$V{slKha}+$V{slTbKha}+$V{slTb}+$V{slGioi}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="120" y="221" width="60" height="20" uuid="c42cdd29-e9ee-4e1c-aa30-44d4fc08a7ad">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[100%]]></text>
			</staticText>
		</band>
	</summary>
</jasperReport>
