<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.10.0.final using JasperReports Library version 6.10.0  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="BangDiemTongHop" pageWidth="1191" pageHeight="842" orientation="Landscape" columnWidth="1191" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="a8408340-5f30-460a-b1d9-04831373f2aa">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<style name="Crosstab_CH" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Crosstab_CG" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Crosstab_CT" mode="Opaque" backcolor="#005FB3">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Crosstab_CD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<parameter name="tenDv" class="java.lang.String"/>
	<parameter name="tenTieuDe" class="java.lang.String"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="maMonHoc" class="java.lang.String"/>
	<field name="hoLot" class="java.lang.String"/>
	<field name="ten" class="java.lang.String"/>
	<field name="diemTb1" class="java.lang.String"/>
	<field name="maSv" class="java.lang.String"/>
	<field name="ngaySinh" class="java.sql.Date"/>
	<field name="loaiDiem" class="java.lang.String"/>
	<field name="soTT" class="java.lang.Integer"/>
	<variable name="CUR_PAGE_NUM" class="java.lang.Integer" resetType="None" incrementType="Page">
		<variableExpression><![CDATA[$V{PAGE_NUMBER}]]></variableExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<summary>
		<band height="60" splitType="Stretch">
			<crosstab horizontalPosition="Center" ignoreWidth="true">
				<reportElement x="0" y="0" width="1150" height="60" uuid="22df96bd-e468-4efc-9abf-7e9c49b1f8cc">
					<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.VerticalRowLayout"/>
					<property name="com.jaspersoft.studio.crosstab.style.header" value="Crosstab_CH"/>
					<property name="com.jaspersoft.studio.crosstab.style.group" value="Crosstab_CG"/>
					<property name="com.jaspersoft.studio.crosstab.style.total" value="Crosstab_CT"/>
					<property name="com.jaspersoft.studio.crosstab.style.detail" value="Crosstab_CD"/>
					<property name="com.jaspersoft.studio.components.autoresize.proportional" value="true"/>
				</reportElement>
				<crosstabHeaderCell>
					<cellContents>
						<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
						<staticText>
							<reportElement x="0" y="0" width="40" height="40" uuid="1ef89a5b-4161-42cb-b573-06891c6a76da"/>
							<box>
								<pen lineWidth="0.5"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Times New Roman" size="9"/>
							</textElement>
							<text><![CDATA[STT]]></text>
						</staticText>
						<staticText>
							<reportElement x="40" y="0" width="50" height="40" uuid="ba9c2357-c3e6-4f36-ab74-88f8ac6c799e"/>
							<box>
								<pen lineWidth="0.5"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Times New Roman" size="9"/>
							</textElement>
							<text><![CDATA[Mã SV]]></text>
						</staticText>
						<staticText>
							<reportElement x="90" y="0" width="110" height="40" uuid="c0904eab-6426-486f-91ce-9d149ddcb3c8"/>
							<box>
								<pen lineWidth="0.5"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Times New Roman" size="9"/>
							</textElement>
							<text><![CDATA[Họ và tên]]></text>
						</staticText>
						<staticText>
							<reportElement x="200" y="0" width="60" height="40" uuid="f42f4230-3161-4c7e-a426-0df0acbb28c5"/>
							<box>
								<pen lineWidth="0.5"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Times New Roman" size="9"/>
							</textElement>
							<text><![CDATA[Ngày sinh]]></text>
						</staticText>
					</cellContents>
				</crosstabHeaderCell>
				<rowGroup name="soTT" width="40">
					<bucket class="java.lang.Integer">
						<bucketExpression><![CDATA[$F{soTT}]]></bucketExpression>
					</bucket>
					<crosstabRowHeader>
						<cellContents mode="Opaque" style="Crosstab_CH">
							<textField>
								<reportElement x="0" y="0" width="40" height="20" uuid="c033efe9-03d1-4b0c-b863-4a595f34ee81"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="Times New Roman" size="9"/>
								</textElement>
								<textFieldExpression><![CDATA[$V{soTT}]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabRowHeader>
					<crosstabTotalRowHeader>
						<cellContents>
							<staticText>
								<reportElement x="0" y="0" width="-2147483648" height="-2147483648" uuid="eedaa1fd-0005-4691-9487-253d61a640a8"/>
								<text><![CDATA[Total soTT]]></text>
							</staticText>
						</cellContents>
					</crosstabTotalRowHeader>
				</rowGroup>
				<rowGroup name="maSv" width="50">
					<bucket class="java.lang.String">
						<bucketExpression><![CDATA[$F{maSv}]]></bucketExpression>
					</bucket>
					<crosstabRowHeader>
						<cellContents mode="Opaque" style="Crosstab_CH">
							<textField>
								<reportElement x="0" y="0" width="50" height="20" uuid="403a52aa-a660-4954-a367-658ecea8c56b"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="Times New Roman" size="9"/>
								</textElement>
								<textFieldExpression><![CDATA[$V{maSv}]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabRowHeader>
					<crosstabTotalRowHeader>
						<cellContents>
							<staticText>
								<reportElement x="0" y="0" width="-2147483648" height="-2147483648" uuid="e4954c45-e5f0-4f5c-a1fd-b4650b3d9d89"/>
								<text><![CDATA[Total maSv]]></text>
							</staticText>
						</cellContents>
					</crosstabTotalRowHeader>
				</rowGroup>
				<rowGroup name="hoLot" width="70">
					<bucket class="java.lang.String">
						<bucketExpression><![CDATA[$F{hoLot}]]></bucketExpression>
					</bucket>
					<crosstabRowHeader>
						<cellContents mode="Opaque" style="Crosstab_CH">
							<textField>
								<reportElement x="0" y="0" width="70" height="20" uuid="b6494a6b-b178-4c66-8245-207e2b866041"/>
								<box leftPadding="3"/>
								<textElement textAlignment="Left" verticalAlignment="Middle">
									<font fontName="Times New Roman" size="9"/>
								</textElement>
								<textFieldExpression><![CDATA[$V{hoLot}]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabRowHeader>
					<crosstabTotalRowHeader>
						<cellContents>
							<staticText>
								<reportElement x="0" y="0" width="-2147483648" height="-2147483648" uuid="a3c65572-4170-4d98-8693-d7709c26a29c"/>
								<text><![CDATA[Total hoLot]]></text>
							</staticText>
						</cellContents>
					</crosstabTotalRowHeader>
				</rowGroup>
				<rowGroup name="ten" width="40">
					<bucket class="java.lang.String">
						<bucketExpression><![CDATA[$F{ten}]]></bucketExpression>
					</bucket>
					<crosstabRowHeader>
						<cellContents mode="Opaque" style="Crosstab_CH">
							<textField>
								<reportElement x="0" y="0" width="40" height="20" uuid="4bb78b1c-5177-4f4b-8fd8-35e7e561642e"/>
								<box leftPadding="3"/>
								<textElement textAlignment="Left" verticalAlignment="Middle">
									<font fontName="Times New Roman" size="9"/>
								</textElement>
								<textFieldExpression><![CDATA[$V{ten}]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabRowHeader>
					<crosstabTotalRowHeader>
						<cellContents>
							<staticText>
								<reportElement x="0" y="0" width="-2147483648" height="-2147483648" uuid="e7cfdfe4-9056-446b-9eb9-ca01c7e5e8f9"/>
								<text><![CDATA[Total ten]]></text>
							</staticText>
						</cellContents>
					</crosstabTotalRowHeader>
				</rowGroup>
				<rowGroup name="ngaySinh" width="60">
					<bucket class="java.sql.Date">
						<bucketExpression><![CDATA[$F{ngaySinh}]]></bucketExpression>
					</bucket>
					<crosstabRowHeader>
						<cellContents mode="Opaque" style="Crosstab_CH">
							<textField>
								<reportElement x="0" y="0" width="60" height="20" uuid="3aef1fb5-33f9-4d93-b1f6-607390b4dc09"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="Times New Roman" size="9"/>
								</textElement>
								<textFieldExpression><![CDATA[DATEFORMAT($V{ngaySinh},"dd/MM/yyyy")]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabRowHeader>
					<crosstabTotalRowHeader>
						<cellContents>
							<staticText>
								<reportElement x="0" y="0" width="-2147483648" height="-2147483648" uuid="f1167927-f180-4b0f-89a3-17b7b90ee109"/>
								<text><![CDATA[Total ngaySinh]]></text>
							</staticText>
						</cellContents>
					</crosstabTotalRowHeader>
				</rowGroup>
				<columnGroup name="maMonHoc" height="20" headerPosition="Center" mergeHeaderCells="true">
					<bucket order="None" class="java.lang.String">
						<bucketExpression><![CDATA[$F{maMonHoc}]]></bucketExpression>
					</bucket>
					<crosstabColumnHeader>
						<cellContents mode="Opaque" style="Crosstab_CH">
							<textField>
								<reportElement x="0" y="0" width="39" height="20" uuid="f44539f4-a82a-4515-8db1-4e2e6cb21bc7"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="Times New Roman" size="8"/>
								</textElement>
								<textFieldExpression><![CDATA[$V{maMonHoc}]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabColumnHeader>
					<crosstabTotalColumnHeader>
						<cellContents>
							<staticText>
								<reportElement x="0" y="0" width="-2147483648" height="-2147483648" uuid="2734bb11-ab74-4578-95f3-24edbeb6c2bd"/>
								<text><![CDATA[Total maMonHoc]]></text>
							</staticText>
						</cellContents>
					</crosstabTotalColumnHeader>
				</columnGroup>
				<columnGroup name="loaiDiem" height="20">
					<bucket order="None" class="java.lang.String">
						<bucketExpression><![CDATA[$F{loaiDiem}]]></bucketExpression>
					</bucket>
					<crosstabColumnHeader>
						<cellContents mode="Opaque" style="Crosstab_CH">
							<textField>
								<reportElement x="0" y="0" width="39" height="20" uuid="379cb453-8fb2-4ca7-976f-43444d5e8f9e"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="Times New Roman" size="9"/>
								</textElement>
								<textFieldExpression><![CDATA[$V{loaiDiem}]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabColumnHeader>
					<crosstabTotalColumnHeader>
						<cellContents>
							<staticText>
								<reportElement x="0" y="0" width="-2147483648" height="-2147483648" uuid="3e789c12-14a9-42ab-a6e3-54e2029cd4b1"/>
								<text><![CDATA[Total loaiDiem]]></text>
							</staticText>
						</cellContents>
					</crosstabTotalColumnHeader>
				</columnGroup>
				<measure name="diemTb1_MEASURE" class="java.lang.String" calculation="First">
					<measureExpression><![CDATA[$F{diemTb1}]]></measureExpression>
				</measure>
				<crosstabCell width="39" height="20">
					<cellContents mode="Opaque" style="Crosstab_CD">
						<textField>
							<reportElement x="0" y="0" width="39" height="20" uuid="76da94f3-9222-41d4-b3b8-8b090bb25eac"/>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Times New Roman" size="9"/>
							</textElement>
							<textFieldExpression><![CDATA[$V{diemTb1_MEASURE}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell width="60" height="20" columnTotalGroup="maMonHoc">
					<cellContents mode="Opaque" style="Crosstab_CT">
						<textField>
							<reportElement x="0" y="0" width="60" height="20" forecolor="#FFFFFF" uuid="cf2c33f7-fc9e-4092-aebe-e53ff8222045"/>
							<textFieldExpression><![CDATA[$V{diemTb1_MEASURE}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell width="60" height="20" columnTotalGroup="loaiDiem">
					<cellContents mode="Opaque" style="Crosstab_CG">
						<textField>
							<reportElement x="0" y="0" width="60" height="20" uuid="e4fa7676-54e6-46b8-a922-c63d48c4be18"/>
							<textFieldExpression><![CDATA[$V{diemTb1_MEASURE}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell width="60" height="20" rowTotalGroup="soTT">
					<cellContents mode="Opaque" style="Crosstab_CT">
						<textField>
							<reportElement x="0" y="0" width="60" height="20" forecolor="#FFFFFF" uuid="c2ed18a3-eb56-48f8-b23f-cd5a0ce64b69"/>
							<textFieldExpression><![CDATA[$V{diemTb1_MEASURE}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell width="60" height="20" rowTotalGroup="soTT" columnTotalGroup="maMonHoc">
					<cellContents mode="Opaque" style="Crosstab_CT">
						<textField>
							<reportElement x="0" y="0" width="60" height="20" forecolor="#FFFFFF" uuid="b94ee23b-8a78-45fb-b8e6-5c257ed1c761"/>
							<textFieldExpression><![CDATA[$V{diemTb1_MEASURE}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell width="60" height="20" rowTotalGroup="soTT" columnTotalGroup="loaiDiem">
					<cellContents mode="Opaque" style="Crosstab_CT">
						<textField>
							<reportElement x="0" y="0" width="60" height="20" forecolor="#FFFFFF" uuid="e11dc122-59f8-4afa-b9ba-8173b7ab80d0"/>
							<textFieldExpression><![CDATA[$V{diemTb1_MEASURE}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell width="60" height="20" rowTotalGroup="maSv">
					<cellContents mode="Opaque" style="Crosstab_CG">
						<textField>
							<reportElement x="0" y="0" width="60" height="20" uuid="dbb8e48f-4a44-40a9-ad83-ed57e20168db"/>
							<textFieldExpression><![CDATA[$V{diemTb1_MEASURE}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell width="60" height="20" rowTotalGroup="maSv" columnTotalGroup="maMonHoc">
					<cellContents mode="Opaque" style="Crosstab_CT">
						<textField>
							<reportElement x="0" y="0" width="60" height="20" forecolor="#FFFFFF" uuid="4fa0c3bf-7baf-4779-b515-4fa5aa21dea1"/>
							<textFieldExpression><![CDATA[$V{diemTb1_MEASURE}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell width="60" height="20" rowTotalGroup="maSv" columnTotalGroup="loaiDiem">
					<cellContents mode="Opaque" style="Crosstab_CG">
						<textField>
							<reportElement x="0" y="0" width="60" height="20" uuid="abff8ad4-7681-46bc-be05-1c10f6efa101"/>
							<textFieldExpression><![CDATA[$V{diemTb1_MEASURE}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell width="60" height="20" rowTotalGroup="hoLot">
					<cellContents mode="Opaque" style="Crosstab_CG">
						<textField>
							<reportElement x="0" y="0" width="60" height="20" uuid="90b39537-f7ef-4c15-b69b-0ed2f04e7036"/>
							<textFieldExpression><![CDATA[$V{diemTb1_MEASURE}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell width="60" height="20" rowTotalGroup="hoLot" columnTotalGroup="maMonHoc">
					<cellContents mode="Opaque" style="Crosstab_CT">
						<textField>
							<reportElement x="0" y="0" width="60" height="20" forecolor="#FFFFFF" uuid="e3168987-8dff-4e6a-a958-30bb76da29e2"/>
							<textFieldExpression><![CDATA[$V{diemTb1_MEASURE}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell width="60" height="20" rowTotalGroup="hoLot" columnTotalGroup="loaiDiem">
					<cellContents mode="Opaque" style="Crosstab_CG">
						<textField>
							<reportElement x="0" y="0" width="60" height="20" uuid="d29b34f1-ac40-4588-8c94-b15d8b7454ca"/>
							<textFieldExpression><![CDATA[$V{diemTb1_MEASURE}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell width="60" height="20" rowTotalGroup="ten">
					<cellContents mode="Opaque" style="Crosstab_CG">
						<textField>
							<reportElement x="0" y="0" width="60" height="20" uuid="0b1cd469-8236-4c65-acd3-a950206db949"/>
							<textFieldExpression><![CDATA[$V{diemTb1_MEASURE}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell width="60" height="20" rowTotalGroup="ten" columnTotalGroup="maMonHoc">
					<cellContents mode="Opaque" style="Crosstab_CT">
						<textField>
							<reportElement x="0" y="0" width="60" height="20" forecolor="#FFFFFF" uuid="76e66b28-c646-48f5-9b89-734c4468b173"/>
							<textFieldExpression><![CDATA[$V{diemTb1_MEASURE}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell width="60" height="20" rowTotalGroup="ten" columnTotalGroup="loaiDiem">
					<cellContents mode="Opaque" style="Crosstab_CG">
						<textField>
							<reportElement x="0" y="0" width="60" height="20" uuid="70c33b5e-132e-451b-9959-cb233cf0c3e4"/>
							<textFieldExpression><![CDATA[$V{diemTb1_MEASURE}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell width="60" height="20" rowTotalGroup="ngaySinh">
					<cellContents mode="Opaque" style="Crosstab_CG">
						<textField>
							<reportElement x="0" y="0" width="60" height="20" uuid="f8b899b6-67ed-4950-9130-d477276a34d7"/>
							<textFieldExpression><![CDATA[$V{diemTb1_MEASURE}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell width="60" height="20" rowTotalGroup="ngaySinh" columnTotalGroup="maMonHoc">
					<cellContents mode="Opaque" style="Crosstab_CT">
						<textField>
							<reportElement x="0" y="0" width="60" height="20" forecolor="#FFFFFF" uuid="76523c00-8031-49cb-aaf8-67a5ad83799a"/>
							<textFieldExpression><![CDATA[$V{diemTb1_MEASURE}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell width="60" height="20" rowTotalGroup="ngaySinh" columnTotalGroup="loaiDiem">
					<cellContents mode="Opaque" style="Crosstab_CG">
						<textField>
							<reportElement x="0" y="0" width="60" height="20" uuid="59ae529e-edeb-4870-ba08-bdb1490795c9"/>
							<textFieldExpression><![CDATA[$V{diemTb1_MEASURE}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
			</crosstab>
		</band>
	</summary>
</jasperReport>
