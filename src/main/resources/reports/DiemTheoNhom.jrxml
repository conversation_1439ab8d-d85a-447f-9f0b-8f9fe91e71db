<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.9.0.final using JasperReports Library version 6.9.0-cb8f9004be492ccc537180b49c026951f4220bf3  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="DiemTheoNhom" pageWidth="842" pageHeight="595" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" isTitleNewPage="true" uuid="3542d54d-af18-4463-971f-22a74d96de0a">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<style name="Crosstab_CH" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.0" lineColor="#000000"/>
			<topPen lineWidth="0.0" lineColor="#000000"/>
			<leftPen lineWidth="0.0" lineColor="#000000"/>
			<bottomPen lineWidth="0.0" lineColor="#000000"/>
			<rightPen lineWidth="0.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="Crosstab_CG" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.0" lineColor="#000000"/>
			<topPen lineWidth="0.0" lineColor="#000000"/>
			<leftPen lineWidth="0.0" lineColor="#000000"/>
			<bottomPen lineWidth="0.0" lineColor="#000000"/>
			<rightPen lineWidth="0.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="Crosstab_CT" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.0" lineColor="#000000"/>
			<topPen lineWidth="0.0" lineColor="#000000"/>
			<leftPen lineWidth="0.0" lineColor="#000000"/>
			<bottomPen lineWidth="0.0" lineColor="#000000"/>
			<rightPen lineWidth="0.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="Crosstab_CD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.0" lineColor="#000000"/>
			<topPen lineWidth="0.0" lineColor="#000000"/>
			<leftPen lineWidth="0.0" lineColor="#000000"/>
			<bottomPen lineWidth="0.0" lineColor="#000000"/>
			<rightPen lineWidth="0.0" lineColor="#000000"/>
		</box>
	</style>
	<parameter name="tenDv" class="java.lang.String"/>
	<parameter name="tenTieuDe" class="java.lang.String"/>
	<parameter name="hocKy" class="java.lang.String"/>
	<parameter name="monHoc" class="java.lang.String"/>
	<parameter name="lop" class="java.lang.String"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="int1" class="java.lang.Integer"/>
	<field name="s1" class="java.lang.String"/>
	<field name="s2" class="java.lang.String"/>
	<field name="s3" class="java.lang.String"/>
	<field name="s4" class="java.lang.String"/>
	<field name="s5" class="java.lang.String"/>
	<field name="s6" class="java.lang.String"/>
	<field name="s7" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<summary>
		<band height="240" splitType="Stretch">
			<crosstab ignoreWidth="true">
				<reportElement x="0" y="200" width="800" height="40" uuid="241b5cc6-95d7-4347-ad40-4764dbcf8996">
					<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.VerticalRowLayout"/>
					<property name="com.jaspersoft.studio.crosstab.style.header" value="Crosstab_CH"/>
					<property name="com.jaspersoft.studio.crosstab.style.group" value="Crosstab_CG"/>
					<property name="com.jaspersoft.studio.crosstab.style.total" value="Crosstab_CT"/>
					<property name="com.jaspersoft.studio.crosstab.style.detail" value="Crosstab_CD"/>
				</reportElement>
				<crosstabHeaderCell>
					<cellContents mode="Opaque" style="Crosstab_CD">
						<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
						<staticText>
							<reportElement x="0" y="0" width="30" height="60" uuid="3bc24326-dd24-416c-9e0e-6c145a3596b5"/>
							<box>
								<pen lineWidth="0.5"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Times New Roman"/>
							</textElement>
							<text><![CDATA[STT]]></text>
						</staticText>
						<staticText>
							<reportElement x="30" y="0" width="50" height="60" uuid="0946d6a1-7454-43a9-bdb0-2e57392587a2"/>
							<box>
								<pen lineWidth="0.5"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Times New Roman"/>
							</textElement>
							<text><![CDATA[Mã SV]]></text>
						</staticText>
						<staticText>
							<reportElement x="80" y="0" width="120" height="60" uuid="f65f3d00-858f-4194-a95d-cceece9b2d15"/>
							<box>
								<pen lineWidth="0.5"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Times New Roman"/>
							</textElement>
							<text><![CDATA[Họ và tên]]></text>
						</staticText>
						<staticText>
							<reportElement x="200" y="0" width="60" height="60" uuid="91e0a7d7-9887-44bf-8c68-267981c5dfe3"/>
							<box>
								<pen lineWidth="0.5"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Times New Roman"/>
							</textElement>
							<text><![CDATA[Ngày sinh]]></text>
						</staticText>
						<staticText>
							<reportElement x="260" y="0" width="40" height="60" uuid="fd970870-4845-4c9b-9f9a-0fc359235e3c"/>
							<box>
								<pen lineWidth="0.5"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Times New Roman"/>
							</textElement>
							<text><![CDATA[Mã lớp]]></text>
						</staticText>
					</cellContents>
				</crosstabHeaderCell>
				<rowGroup name="Int1" width="30">
					<bucket class="java.lang.Integer">
						<bucketExpression><![CDATA[$F{int1}]]></bucketExpression>
					</bucket>
					<crosstabRowHeader>
						<cellContents mode="Opaque" style="Crosstab_CH">
							<textField>
								<reportElement x="0" y="0" width="30" height="20" uuid="bd4d55e4-4ffa-4fab-a2f1-f377284eafc7"/>
								<box>
									<pen lineWidth="0.5"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="Times New Roman" size="10"/>
								</textElement>
								<textFieldExpression><![CDATA[$V{Int1}]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabRowHeader>
					<crosstabTotalRowHeader>
						<cellContents>
							<staticText>
								<reportElement x="0" y="0" width="-2147483648" height="-2147483648" uuid="66b9a8c2-f7a5-4d02-b2ad-3e4b7597286e"/>
								<text><![CDATA[Total Int1]]></text>
							</staticText>
						</cellContents>
					</crosstabTotalRowHeader>
				</rowGroup>
				<rowGroup name="s1" width="50">
					<bucket class="java.lang.String">
						<bucketExpression><![CDATA[$F{s1}]]></bucketExpression>
					</bucket>
					<crosstabRowHeader>
						<cellContents mode="Opaque" style="Crosstab_CH">
							<textField>
								<reportElement x="0" y="0" width="50" height="20" uuid="27afb707-3ca5-4eb2-a699-f6f05c1fce52"/>
								<box>
									<pen lineWidth="0.5"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="Times New Roman" size="10"/>
								</textElement>
								<textFieldExpression><![CDATA[$V{s1}]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabRowHeader>
					<crosstabTotalRowHeader>
						<cellContents>
							<staticText>
								<reportElement x="0" y="0" width="-2147483648" height="-2147483648" uuid="529a5bb0-3151-44df-aa67-829b3f1d125f"/>
								<text><![CDATA[Total s1]]></text>
							</staticText>
						</cellContents>
					</crosstabTotalRowHeader>
				</rowGroup>
				<rowGroup name="s2" width="80">
					<bucket class="java.lang.String">
						<bucketExpression><![CDATA[$F{s2}]]></bucketExpression>
					</bucket>
					<crosstabRowHeader>
						<cellContents mode="Opaque" style="Crosstab_CH">
							<textField>
								<reportElement x="0" y="0" width="80" height="20" uuid="3d2eecad-bf5f-4524-9f85-f7e3b4513f1b">
									<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
								</reportElement>
								<box>
									<pen lineWidth="0.5"/>
									<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement verticalAlignment="Middle">
									<font fontName="Times New Roman" size="10"/>
									<paragraph leftIndent="3"/>
								</textElement>
								<textFieldExpression><![CDATA[$V{s2}]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabRowHeader>
					<crosstabTotalRowHeader>
						<cellContents>
							<staticText>
								<reportElement x="0" y="0" width="-2147483648" height="-2147483648" uuid="ceeab4a4-3782-461d-8301-c844d3cfb776"/>
								<text><![CDATA[Total s2]]></text>
							</staticText>
						</cellContents>
					</crosstabTotalRowHeader>
				</rowGroup>
				<rowGroup name="s3" width="40">
					<bucket class="java.lang.String">
						<bucketExpression><![CDATA[$F{s3}]]></bucketExpression>
					</bucket>
					<crosstabRowHeader>
						<cellContents mode="Opaque" style="Crosstab_CH">
							<textField>
								<reportElement x="0" y="0" width="40" height="20" uuid="37055fe9-**************-eaaf2d92790f"/>
								<box>
									<pen lineWidth="0.5"/>
									<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
									<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement verticalAlignment="Middle">
									<font fontName="Times New Roman" size="10"/>
								</textElement>
								<textFieldExpression><![CDATA[$V{s3}]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabRowHeader>
					<crosstabTotalRowHeader>
						<cellContents>
							<staticText>
								<reportElement x="0" y="0" width="-2147483648" height="-2147483648" uuid="c8660342-95af-4f50-9606-b7343e5e7c80"/>
								<text><![CDATA[Total s3]]></text>
							</staticText>
						</cellContents>
					</crosstabTotalRowHeader>
				</rowGroup>
				<rowGroup name="s4" width="60">
					<bucket class="java.lang.String">
						<bucketExpression><![CDATA[$F{s4}]]></bucketExpression>
					</bucket>
					<crosstabRowHeader>
						<cellContents mode="Opaque" style="Crosstab_CH">
							<textField>
								<reportElement x="0" y="0" width="60" height="20" uuid="93867a35-333b-4d84-9359-8997f562ab37"/>
								<box>
									<pen lineWidth="0.5"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="Times New Roman" size="10"/>
								</textElement>
								<textFieldExpression><![CDATA[$V{s4}]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabRowHeader>
					<crosstabTotalRowHeader>
						<cellContents>
							<staticText>
								<reportElement x="0" y="0" width="-2147483648" height="-2147483648" uuid="b8dc0aa1-4fb1-448d-ab81-83f942cf11bd"/>
								<text><![CDATA[Total s4]]></text>
							</staticText>
						</cellContents>
					</crosstabTotalRowHeader>
				</rowGroup>
				<rowGroup name="s5" width="40">
					<bucket class="java.lang.String">
						<bucketExpression><![CDATA[$F{s5}]]></bucketExpression>
					</bucket>
					<crosstabRowHeader>
						<cellContents mode="Opaque" style="Crosstab_CH">
							<textField>
								<reportElement x="0" y="0" width="40" height="20" uuid="290737c0-a1f7-4026-b81e-80620196f2d9"/>
								<box>
									<pen lineWidth="0.5"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="Times New Roman" size="10"/>
								</textElement>
								<textFieldExpression><![CDATA[$V{s5}]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabRowHeader>
					<crosstabTotalRowHeader>
						<cellContents>
							<staticText>
								<reportElement x="0" y="0" width="-2147483648" height="-2147483648" uuid="b6943dcb-06e2-4486-9175-8b450c277a7e"/>
								<text><![CDATA[Total s5]]></text>
							</staticText>
						</cellContents>
					</crosstabTotalRowHeader>
				</rowGroup>
				<columnGroup name="s6" height="60">
					<bucket order="None" class="java.lang.String">
						<bucketExpression><![CDATA[$F{s6}]]></bucketExpression>
					</bucket>
					<crosstabColumnHeader>
						<cellContents mode="Opaque" style="Crosstab_CH">
							<textField>
								<reportElement x="0" y="0" width="50" height="60" uuid="006c7b77-5b0f-42cf-a166-37de1daa0138"/>
								<box padding="1">
									<pen lineWidth="0.5"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="Times New Roman" size="10"/>
								</textElement>
								<textFieldExpression><![CDATA[$V{s6}]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabColumnHeader>
					<crosstabTotalColumnHeader>
						<cellContents>
							<staticText>
								<reportElement x="0" y="0" width="-2147483648" height="-2147483648" uuid="459f5fb2-de2e-4553-b731-9683653de1c7"/>
								<text><![CDATA[Total s6]]></text>
							</staticText>
						</cellContents>
					</crosstabTotalColumnHeader>
				</columnGroup>
				<measure name="s7_MEASURE" class="java.lang.String" calculation="First">
					<measureExpression><![CDATA[$F{s7}]]></measureExpression>
				</measure>
				<crosstabCell width="50" height="20">
					<cellContents mode="Opaque" style="Crosstab_CD">
						<textField isBlankWhenNull="true">
							<reportElement x="0" y="0" width="50" height="20" uuid="49a3b89f-3b49-45b7-943b-c4b6954aff5f"/>
							<box>
								<pen lineWidth="0.5"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Times New Roman" size="10"/>
							</textElement>
							<textFieldExpression><![CDATA[$V{s7_MEASURE}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell width="60" height="20" columnTotalGroup="s6">
					<cellContents mode="Opaque" style="Crosstab_CT">
						<textField>
							<reportElement x="0" y="0" width="60" height="20" forecolor="#FFFFFF" uuid="e9ac7050-acaa-4d99-8aef-6161ccbb6017"/>
							<textFieldExpression><![CDATA[$V{s7_MEASURE}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell width="60" height="20" rowTotalGroup="Int1">
					<cellContents mode="Opaque" style="Crosstab_CT">
						<textField>
							<reportElement x="0" y="0" width="60" height="20" forecolor="#FFFFFF" uuid="f2c1c86a-daa3-46f0-84e2-f199b0f4217a"/>
							<textFieldExpression><![CDATA[$V{s7_MEASURE}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell width="60" height="20" rowTotalGroup="Int1" columnTotalGroup="s6">
					<cellContents mode="Opaque" style="Crosstab_CT">
						<textField>
							<reportElement x="0" y="0" width="60" height="20" forecolor="#FFFFFF" uuid="0a9ff9bf-0f73-4446-8f53-e058826b6ceb"/>
							<textFieldExpression><![CDATA[$V{s7_MEASURE}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell width="60" height="20" rowTotalGroup="s1">
					<cellContents mode="Opaque" style="Crosstab_CG">
						<textField>
							<reportElement x="0" y="0" width="60" height="20" uuid="d1f1a81a-f96b-47ce-94e2-41a162b28470"/>
							<textFieldExpression><![CDATA[$V{s7_MEASURE}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell width="60" height="20" rowTotalGroup="s1" columnTotalGroup="s6">
					<cellContents mode="Opaque" style="Crosstab_CT">
						<textField>
							<reportElement x="0" y="0" width="60" height="20" forecolor="#FFFFFF" uuid="c1bb3b7b-c225-48be-86d7-965abafa521e"/>
							<textFieldExpression><![CDATA[$V{s7_MEASURE}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell width="60" height="20" rowTotalGroup="s2">
					<cellContents mode="Opaque" style="Crosstab_CG">
						<textField>
							<reportElement x="0" y="0" width="60" height="20" uuid="9ed93ac4-88df-4fe4-9238-ea02afe8fd0c"/>
							<textFieldExpression><![CDATA[$V{s7_MEASURE}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell width="60" height="20" rowTotalGroup="s2" columnTotalGroup="s6">
					<cellContents mode="Opaque" style="Crosstab_CT">
						<textField>
							<reportElement x="0" y="0" width="60" height="20" forecolor="#FFFFFF" uuid="b97d917e-8334-4c05-80be-cdacc4c80e40"/>
							<textFieldExpression><![CDATA[$V{s7_MEASURE}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell width="60" height="20" rowTotalGroup="s3">
					<cellContents mode="Opaque" style="Crosstab_CG">
						<textField>
							<reportElement x="0" y="0" width="60" height="20" uuid="0f563e72-0e8c-48ef-aed5-5b3202d641d7"/>
							<textFieldExpression><![CDATA[$V{s7_MEASURE}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell width="60" height="20" rowTotalGroup="s3" columnTotalGroup="s6">
					<cellContents mode="Opaque" style="Crosstab_CT">
						<textField>
							<reportElement x="0" y="0" width="60" height="20" forecolor="#FFFFFF" uuid="86338cb3-2b0c-428e-bbf5-2d640cff636a"/>
							<textFieldExpression><![CDATA[$V{s7_MEASURE}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell width="60" height="20" rowTotalGroup="s4">
					<cellContents mode="Opaque" style="Crosstab_CG">
						<textField>
							<reportElement x="0" y="0" width="60" height="20" uuid="0f2797dc-141e-4308-8ac3-f7dbc728f6c0"/>
							<textFieldExpression><![CDATA[$V{s7_MEASURE}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell width="60" height="20" rowTotalGroup="s4" columnTotalGroup="s6">
					<cellContents mode="Opaque" style="Crosstab_CT">
						<textField>
							<reportElement x="0" y="0" width="60" height="20" forecolor="#FFFFFF" uuid="36fd5e0b-db5b-40c4-9f2e-e59f8bf4a198"/>
							<textFieldExpression><![CDATA[$V{s7_MEASURE}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell width="60" height="20" rowTotalGroup="s5">
					<cellContents mode="Opaque" style="Crosstab_CG">
						<textField>
							<reportElement x="0" y="0" width="60" height="20" uuid="3ac60936-b489-40a1-a20a-f229f94a09e0"/>
							<textFieldExpression><![CDATA[$V{s7_MEASURE}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell width="60" height="20" rowTotalGroup="s5" columnTotalGroup="s6">
					<cellContents mode="Opaque" style="Crosstab_CT">
						<textField>
							<reportElement x="0" y="0" width="60" height="20" forecolor="#FFFFFF" uuid="6d609623-12d5-4a18-a429-7e43d13ff424"/>
							<textFieldExpression><![CDATA[$V{s7_MEASURE}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
			</crosstab>
			<staticText>
				<reportElement x="0" y="0" width="380" height="20" uuid="e393d655-e355-4494-a782-c79e9f92539f">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="13"/>
				</textElement>
				<text><![CDATA[UBND TỈNH CÀ MAU]]></text>
			</staticText>
			<staticText>
				<reportElement x="380" y="0" width="420" height="20" uuid="35ffc150-1bdb-4fcc-8a13-986a60fe008f">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM]]></text>
			</staticText>
			<staticText>
				<reportElement x="380" y="20" width="420" height="20" uuid="d080ffb2-1266-44f2-803d-bfc1633c4d1b">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[Độc lập - Tự do - Hạnh phúc]]></text>
			</staticText>
			<textField>
				<reportElement x="0" y="20" width="380" height="20" uuid="5bd20eaa-4061-4f13-a2ab-869eac056e2e">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="13" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{tenDv}.toUpperCase()]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="136" y="38" width="119" height="1" uuid="6359094d-c34b-4cfd-b664-d3078be777d9"/>
			</line>
			<line>
				<reportElement x="496" y="38" width="190" height="1" uuid="a4c5341a-417e-4767-b5a7-5c4ee826d743"/>
			</line>
			<textField>
				<reportElement x="0" y="40" width="800" height="70" uuid="008d1d4e-d27f-4e5f-b3ce-8fae97f71b69">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{tenTieuDe}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="1" y="110" width="800" height="20" uuid="40b6cc2f-266f-4609-b138-e3e3f460fad5">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="14" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{hocKy}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="150" width="800" height="20" uuid="36155b70-46ff-4719-afa7-0139a3be8827">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Left">
					<font fontName="Times New Roman" size="14" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{monHoc}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="170" width="800" height="20" uuid="6e1e8fee-8161-4e45-a970-0e3b9dac662f">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Left">
					<font fontName="Times New Roman" size="14" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{lop}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="130" width="800" height="20" uuid="b66177aa-14e7-42bd-ac57-46a087f4d396">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Left">
					<font fontName="Times New Roman" size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
		</band>
	</summary>
</jasperReport>
