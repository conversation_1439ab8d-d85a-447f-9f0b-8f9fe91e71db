<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.9.0.final using JasperReports Library version 6.9.0-cb8f9004be492ccc537180b49c026951f4220bf3  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="BangDiemTongHop" pageWidth="1191" pageHeight="842" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="a8408340-5f30-460a-b1d9-04831373f2aa">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="Crosstab_CH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Crosstab_CT" mode="Opaque" backcolor="#005FB3">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Crosstab_CD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Crosstab 1_CH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Crosstab 1_CG" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Crosstab 1_CT" mode="Opaque" backcolor="#005FB3">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Crosstab 1_CD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<parameter name="tenDv" class="java.lang.String"/>
	<parameter name="tenTieuDe" class="java.lang.String"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String"/>
	<parameter name="SubReportParam" class="java.lang.String" isForPrompting="false"/>
	<parameter name="datasource" class="net.sf.jasperreports.engine.data.JRBeanCollectionDataSource"/>
	<parameter name="tenLop" class="java.lang.String"/>
	<parameter name="hocKy" class="java.lang.String"/>
	<parameter name="monHocs" class="net.sf.jasperreports.engine.data.JRBeanCollectionDataSource"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<variable name="CUR_PAGE_NUM" class="java.lang.Integer" resetType="None" incrementType="Page">
		<variableExpression><![CDATA[$V{PAGE_NUMBER}]]></variableExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="400" splitType="Stretch">
			<staticText>
				<reportElement x="0" y="0" width="380" height="20" uuid="539ed791-5771-49f5-8889-0d175847f85d">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="13"/>
				</textElement>
				<text><![CDATA[UBND TỈNH CÀ MAU]]></text>
			</staticText>
			<line>
				<reportElement x="874" y="38" width="170" height="1" uuid="8bc5412f-2cf1-4e47-8089-6f6d9e71cf8b"/>
			</line>
			<textField>
				<reportElement x="0" y="20" width="380" height="20" uuid="f6658925-510a-4bbe-9029-cfc914aa1e03">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="13" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{tenDv}.toUpperCase()]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="770" y="20" width="380" height="20" uuid="599fc02e-bac7-415b-8ddc-4c5444d62718">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[Độc lập - Tự do - Hạnh phúc]]></text>
			</staticText>
			<staticText>
				<reportElement x="770" y="0" width="380" height="20" uuid="d2827200-bfc3-457f-bb0d-cf08e3fffebb">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM]]></text>
			</staticText>
			<line>
				<reportElement x="131" y="38" width="119" height="1" uuid="607c6ca5-9697-433d-854a-1a6992f76b65"/>
			</line>
			<staticText>
				<reportElement x="380" y="0" width="389" height="20" uuid="65f6fbd8-0a46-43c7-b949-0a885860c726">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="380" y="20" width="389" height="20" uuid="727261b6-7492-40e1-aed8-2a3496236553">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<textField>
				<reportElement x="-1" y="40" width="1150" height="50" uuid="e9484ab0-2ea9-4b44-9445-2d8cfbaf58ef">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Times New Roman" size="16" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{tenTieuDe}]]></textFieldExpression>
			</textField>
			<subreport>
				<reportElement x="1" y="160" width="1149" height="120" uuid="829f07f2-bdc7-4696-9994-9e9879eeaf21"/>
				<dataSourceExpression><![CDATA[$P{datasource}]]></dataSourceExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR}+"BangDiemTH_SUB1.jasper"]]></subreportExpression>
			</subreport>
			<textField>
				<reportElement x="0" y="90" width="1150" height="20" uuid="d96dc5d0-7ba4-4a97-a929-e25d534a824e">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{tenLop}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="110" width="1150" height="20" uuid="192dc75d-36e9-45c0-93fa-6da75f9b135d">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{hocKy}]]></textFieldExpression>
			</textField>
			<subreport>
				<reportElement positionType="Float" x="0" y="280" width="1149" height="120" uuid="a861f4e9-ffba-4745-82e2-32734db755c5"/>
				<dataSourceExpression><![CDATA[$P{monHocs}]]></dataSourceExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR}+"BangDiemTH_SUB2.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</title>
	<summary>
		<band height="211">
			<staticText>
				<reportElement x="820" y="50" width="329" height="110" uuid="66481d57-34eb-4a63-9110-4ab56e09b785"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Times New Roman" size="13" isBold="true" isItalic="false"/>
				</textElement>
				<text><![CDATA[Người lập biểu]]></text>
			</staticText>
			<textField>
				<reportElement x="820" y="0" width="329" height="50" uuid="c0832978-ba20-4798-ac91-52829a03ecd1"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Cà Mau, ngày "+((DAY( TODAY( )))  >= 10 ? DAY( TODAY( )) :"0"+DAY( TODAY( )))+" tháng "+(MONTH(TODAY( ) ) >= 10? MONTH(TODAY( ) ): "0"+MONTH(TODAY( ) ))+" năm "+YEAR(TODAY( ) )]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="400" y="50" width="329" height="110" uuid="8622af0a-60aa-46f7-9d12-b1ab8d765d2b"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Times New Roman" size="13" isBold="true" isItalic="false"/>
				</textElement>
				<text><![CDATA[TRƯỞNG PHÒNG ĐÀO TẠO]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="50" width="329" height="110" uuid="9a0ff124-ee55-457b-b3ff-c5c9fe7b6f9e"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Times New Roman" size="13" isBold="true" isItalic="false"/>
				</textElement>
				<text><![CDATA[HIỆU TRƯỞNG]]></text>
			</staticText>
		</band>
	</summary>
</jasperReport>
