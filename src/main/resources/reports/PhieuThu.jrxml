<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.9.0.final using JasperReports Library version 6.9.0-cb8f9004be492ccc537180b49c026951f4220bf3  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="PhieuThu" pageWidth="595" pageHeight="842" whenNoDataType="AllSectionsNoDetail" columnWidth="511" leftMargin="42" rightMargin="42" topMargin="42" bottomMargin="42" uuid="17cc378a-d42d-40ba-b8b5-7b9e64456a0a">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<property name="com.jaspersoft.studio.unit." value="cm"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="cm"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="cm"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="cm"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="cm"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="cm"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="cm"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="cm"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="cm"/>
	<parameter name="hoTen" class="java.lang.String"/>
	<parameter name="diaChi" class="java.lang.String"/>
	<parameter name="noiDung" class="java.lang.String"/>
	<parameter name="soTien" class="java.lang.String"/>
	<parameter name="tienBangChu" class="java.lang.String"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="60" splitType="Stretch">
			<staticText>
				<reportElement x="0" y="0" width="185" height="30" uuid="cab7ebe1-bde3-401d-a30e-d753bd06e87b"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="13" isBold="true"/>
				</textElement>
				<text><![CDATA[UBND TỈNH CÀ MAU
TRƯỜNG CAO ĐẲNG Y TẾ]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="30" width="185" height="30" uuid="5cfca718-616b-4287-8183-27c642bb7891"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="13" isBold="false"/>
				</textElement>
				<text><![CDATA[Địa chỉ: 146 Nguyễn Trung Trực 
 Phường 8 - Tp.Cà Mau]]></text>
			</staticText>
			<staticText>
				<reportElement x="226" y="0" width="285" height="50" uuid="0d599dfc-f646-4caa-bd38-8fb2a178cb19"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="13" isBold="false"/>
				</textElement>
				<text><![CDATA[Mẫu số 06-TT
(Ban hành theo Thông tư số 133/2016/TT-BTC ngày 26/08/2016 của BTC)]]></text>
			</staticText>
		</band>
	</title>
	<pageHeader>
		<band height="257" splitType="Stretch">
			<staticText>
				<reportElement x="160" y="10" width="190" height="20" uuid="f857e351-1b2b-4c62-a648-a053a491f659"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[BIÊN LAI THU TIỀN]]></text>
			</staticText>
			<textField>
				<reportElement x="0" y="110" width="511" height="22" uuid="4bc79f3c-89c8-45a5-8b90-71be76dbb2e6">
					<property name="com.jaspersoft.studio.unit.height" value="cm"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13"/>
				</textElement>
				<textFieldExpression><![CDATA["- Họ và tên người nộp: "+$P{hoTen}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="376" y="50" width="135" height="20" uuid="1be03721-b10e-4454-ae72-dc40be223f38"/>
				<textElement textAlignment="Right">
					<font fontName="Times New Roman" size="13" isBold="false"/>
				</textElement>
				<text><![CDATA[Quyển số:....................]]></text>
			</staticText>
			<textField>
				<reportElement x="160" y="30" width="190" height="20" uuid="94d54a3c-53f0-4b1e-8846-584cda4fb135"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="13" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Ngày "+DAY( TODAY( ))+" tháng "+MONTH(TODAY( ) )+" năm "+YEAR(TODAY( ) )]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="376" y="70" width="135" height="20" uuid="94cff684-f014-4f3e-a1fd-9e01f39346e5"/>
				<textElement textAlignment="Right">
					<font fontName="Times New Roman" size="13" isBold="false"/>
				</textElement>
				<text><![CDATA[Số:....................]]></text>
			</staticText>
			<textField>
				<reportElement x="0" y="132" width="511" height="22" uuid="82722f7b-f4fd-4cb0-89c7-09dcf98a1990">
					<property name="com.jaspersoft.studio.unit.height" value="cm"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13"/>
				</textElement>
				<textFieldExpression><![CDATA["- Địa chỉ: "+$P{diaChi}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="0" y="154" width="511" height="22" uuid="4f9fdcb8-be56-4099-b1c3-b4028c28a936"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13"/>
				</textElement>
				<textFieldExpression><![CDATA["- Nội dung thu: "+$P{noiDung}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="0" y="176" width="511" height="22" uuid="08e11ffb-3c1a-4291-b7a9-607549e38a3c">
					<property name="com.jaspersoft.studio.unit.height" value="cm"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13"/>
				</textElement>
				<textFieldExpression><![CDATA["- Số tiền thu: "+ $P{soTien}+" (Viết bằng chữ): "+$P{tienBangChu}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="210" width="221" height="20" uuid="3e47774e-aad7-4f46-a93d-9c8bdaa8faf2"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="13" isBold="true" isItalic="false"/>
				</textElement>
				<text><![CDATA[Người nộp tiền]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="290" y="210" width="221" height="20" uuid="60d3bb65-99f2-48e7-ba08-831c4d8a9687"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="13" isBold="true" isItalic="false"/>
				</textElement>
				<text><![CDATA[Người thu tiền]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="0" y="230" width="221" height="20" uuid="4d317c84-4f00-4e98-a699-9ed7a471be39"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="13" isBold="false" isItalic="true"/>
				</textElement>
				<text><![CDATA[(Ký, họ tên)]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="290" y="230" width="221" height="20" uuid="a222452e-59ef-46a9-a1db-1965ff1ea9c0"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="13" isBold="false" isItalic="true"/>
				</textElement>
				<text><![CDATA[(Ký, họ tên)]]></text>
			</staticText>
		</band>
	</pageHeader>
</jasperReport>
