<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.10.0.final using JasperReports Library version 6.10.0  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ThongKeNoPhi" pageWidth="842" pageHeight="595" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" isSummaryWithPageHeaderAndFooter="true" uuid="04d5015e-629e-446a-a6d4-c6a120881aad">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="Qldt.xml"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w1" value="396"/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w2" value="590"/>
	<property name="net.sf.jasperreports.export.xls.detect.cell.type" value="true"/>
	<property name="net.sf.jasperreports.export.xls.white.page.background" value="false"/>
	<property name="net.sf.jasperreports.export.xls.remove.empty.space.between.rows" value="true"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<parameter name="tenDonVi" class="java.lang.String"/>
	<parameter name="loai" class="java.lang.String"/>
	<parameter name="idHdt" class="java.lang.Integer"/>
	<parameter name="idLoai" class="java.lang.Integer"/>
	<parameter name="idNganh" class="java.lang.Integer"/>
	<parameter name="idKhoa" class="java.lang.Integer"/>
	<parameter name="idLop" class="java.lang.Integer"/>
	<parameter name="tuKhoa" class="java.lang.String"/>
	<queryString>
		<![CDATA[call TK_NO_PHI($P{idLoai},$P{idNganh},$P{idKhoa},$P{idLop},$P{idHdt},$P{tuKhoa})]]>
	</queryString>
	<field name="ID_SINH_VIEN" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.label" value="ID_SINH_VIEN"/>
	</field>
	<field name="MA_SINH_VIEN" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="MA_SINH_VIEN"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="SINH_VIEN"/>
	</field>
	<field name="HO_TEN" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="HO_TEN"/>
	</field>
	<field name="NU" class="java.lang.Boolean">
		<property name="com.jaspersoft.studio.field.label" value="NU"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="SINH_VIEN"/>
	</field>
	<field name="NGAY_SINH" class="java.sql.Date">
		<property name="com.jaspersoft.studio.field.label" value="NGAY_SINH"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="SINH_VIEN"/>
	</field>
	<field name="MA_LOP" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="MA_LOP"/>
	</field>
	<field name="TEN_LOP" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="TEN_LOP"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="LOP"/>
	</field>
	<field name="TONG_TIEN_NO" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.label" value="TONG_TIEN_NO"/>
	</field>
	<field name="PHI_NO" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="PHI_NO"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="SV_DONG_HP"/>
	</field>
	<field name="SDT_SINH_VIEN" class="java.lang.String"/>
	<field name="DIA_CHI" class="java.lang.String"/>
	<variable name="CUR_PAGE_NUM" class="java.lang.Integer" resetType="None" incrementType="Page">
		<variableExpression><![CDATA[$V{PAGE_NUMBER}]]></variableExpression>
	</variable>
	<variable name="TONG_TIEN_THU" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{TONG_TIEN_NO}]]></variableExpression>
	</variable>
	<title>
		<band height="140">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<staticText>
				<reportElement x="-37" y="0" width="362" height="20" uuid="9f891395-870a-4680-879e-80bdd73bf17d"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13" isBold="false"/>
				</textElement>
				<text><![CDATA[UBND TỈNH CÀ MAU]]></text>
			</staticText>
			<textField>
				<reportElement x="0" y="60" width="802" height="20" uuid="65ba45c1-4d30-45da-90a7-cf123485c578"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Loại phí: "+$P{loai}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="80" width="802" height="20" uuid="1fd25a29-f717-4319-915b-0830aa524e47"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Đơn vị tính: đồng]]></text>
			</staticText>
			<textField>
				<reportElement x="-37" y="20" width="362" height="20" uuid="de721211-3b88-456e-b7e3-c33e7261232c"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{tenDonVi}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="325" y="0" width="477" height="20" uuid="6bc9cd5f-1e2f-425e-ab6c-3b0791f2878b">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="325" y="20" width="477" height="20" uuid="6ac6101d-fa69-47c5-bbdf-a63a3f26b232">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="40" width="802" height="20" uuid="2dc4125c-89d9-446f-bfa3-15a6e845e609"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[THỐNG KÊ NỢ PHÍ SINH VIÊN]]></text>
			</staticText>
			<staticText>
				<reportElement x="610" y="100" width="192" height="40" uuid="7f26a45e-f106-4db1-b5d8-d132f264a5b5">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4cb2e071-b909-42a2-b8ed-cbee8d27600c"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Phí nợ]]></text>
			</staticText>
			<staticText>
				<reportElement x="406" y="100" width="122" height="40" uuid="8f74da20-3aa7-47aa-b92e-e8dea29c6533">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="bf708587-1549-4b72-b6ee-10b8d19dfc79"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Tên lớp]]></text>
			</staticText>
			<staticText>
				<reportElement x="528" y="100" width="82" height="40" uuid="7421c8e4-4ea2-4cd1-8b6c-69d6684765d6">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4cb2e071-b909-42a2-b8ed-cbee8d27600c"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Tiền nợ]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="100" width="30" height="40" uuid="9678232b-6426-4db5-be7c-1983e68eb04a">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="7e10bfe2-97d6-478d-b8b1-8c36b2bbefe0"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[STT]]></text>
			</staticText>
			<staticText>
				<reportElement x="75" y="100" width="80" height="40" uuid="3361c913-8a96-4379-8e47-4b079b9b35da">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="85d3e3e3-f3ce-4b47-aa3e-33784c3f31e7"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Họ tên]]></text>
			</staticText>
			<staticText>
				<reportElement x="210" y="120" width="55" height="20" uuid="de18fa67-20a7-49f5-b876-96b321b6abac">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="85d1125d-3d69-4439-ae93-544ae8a29068"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Nam]]></text>
			</staticText>
			<staticText>
				<reportElement x="155" y="100" width="110" height="20" uuid="37e9e817-c599-4d6d-b213-71219c1c6192">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="85d1125d-3d69-4439-ae93-544ae8a29068"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Ngày sinh]]></text>
			</staticText>
			<staticText>
				<reportElement x="155" y="120" width="55" height="20" uuid="95d0ff0c-3af6-4377-aee9-5a6769bb9fce">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="85d1125d-3d69-4439-ae93-544ae8a29068"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Nữ]]></text>
			</staticText>
			<staticText>
				<reportElement x="30" y="100" width="45" height="40" uuid="993899de-bfcc-4584-b7e9-e962c55fce2d">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="7e10bfe2-97d6-478d-b8b1-8c36b2bbefe0"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[MSSV]]></text>
			</staticText>
			<staticText>
				<reportElement x="265" y="100" width="60" height="40" uuid="28090a6c-9dca-4a2e-89e5-fcd62a84ec86">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="bf708587-1549-4b72-b6ee-10b8d19dfc79"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[SĐT]]></text>
			</staticText>
			<staticText>
				<reportElement x="325" y="100" width="81" height="40" uuid="7b1238c9-99a3-4b6a-a4fb-4d62c0453604">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="bf708587-1549-4b72-b6ee-10b8d19dfc79"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Địa chỉ]]></text>
			</staticText>
		</band>
	</title>
	<detail>
		<band height="20" splitType="Prevent">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="ElementGroupHeight" x="30" y="0" width="45" height="20" uuid="682787f9-a6e0-41c8-a9dc-f1340797bb14">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="7e10bfe2-97d6-478d-b8b1-8c36b2bbefe0"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MA_SINH_VIEN}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="ElementGroupHeight" x="0" y="0" width="30" height="20" uuid="5eb63047-4e24-4092-a1e2-b8337db26e00">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{REPORT_COUNT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="ElementGroupHeight" x="75" y="0" width="80" height="20" uuid="ab80e0d2-7adf-4a34-aaad-11d5d5b515b3">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="85d3e3e3-f3ce-4b47-aa3e-33784c3f31e7"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{HO_TEN}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="ElementGroupHeight" x="210" y="0" width="55" height="20" uuid="0de9a94d-0eea-4dbb-b79f-f692153e734e">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="85d1125d-3d69-4439-ae93-544ae8a29068"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NU}?"":DATEFORMAT($F{NGAY_SINH},"dd/MM/yyyy")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="ElementGroupHeight" x="406" y="0" width="122" height="20" uuid="4d5751fa-c43b-4302-8540-7115f254956a">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="bf708587-1549-4b72-b6ee-10b8d19dfc79"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TEN_LOP}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement stretchType="ElementGroupHeight" x="528" y="0" width="82" height="20" uuid="5ed1a182-145a-49cd-b98e-e10b8632cf38">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4cb2e071-b909-42a2-b8ed-cbee8d27600c"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TONG_TIEN_NO}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="ElementGroupHeight" x="155" y="0" width="55" height="20" uuid="f41e81bf-28dc-4617-a4b6-27b2a1ba5bca">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="85d1125d-3d69-4439-ae93-544ae8a29068"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NU}?DATEFORMAT($F{NGAY_SINH},"dd/MM/yyyy"):""]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement stretchType="ElementGroupHeight" x="610" y="0" width="192" height="20" uuid="0ecae170-d540-46fa-89dc-c8b143a69012">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4cb2e071-b909-42a2-b8ed-cbee8d27600c"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PHI_NO}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="ElementGroupHeight" x="265" y="0" width="60" height="20" uuid="0bfaa1a8-a0df-4650-bb36-f2c3b6dd1515">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="bf708587-1549-4b72-b6ee-10b8d19dfc79"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SDT_SINH_VIEN}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="ElementGroupHeight" x="325" y="0" width="81" height="20" uuid="da1b3fb4-**************-0ea5ca8ec68d">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="bf708587-1549-4b72-b6ee-10b8d19dfc79"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DIA_CHI}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="80" splitType="Stretch">
			<textField isStretchWithOverflow="true">
				<reportElement x="0" y="0" width="528" height="20" uuid="d5925c29-1d4d-4db1-812d-2eb31eee3686">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Tổng tiền nợ"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0" isBlankWhenNull="true">
				<reportElement x="528" y="0" width="274" height="20" uuid="33cdaff3-4589-4601-ad41-0378a859de7a">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{TONG_TIEN_THU}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="406" y="20" width="396" height="20" uuid="bdce6db8-8e49-4d0d-be2b-d5bca5b0b490">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Cà Mau, ngày "+DAY( TODAY( ))+" tháng "+MONTH(TODAY( ) )+" năm "+YEAR(TODAY( ) )]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="406" y="40" width="396" height="20" uuid="3cea9fdd-1a95-4e28-b707-44a7704d38c0"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13" isBold="false"/>
				</textElement>
				<text><![CDATA[NGƯỜI THỐNG KÊ]]></text>
			</staticText>
			<staticText>
				<reportElement x="406" y="60" width="396" height="20" uuid="1b2ebb83-08ce-4286-b8b8-cf7bb4ba19ab"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="40" width="406" height="20" uuid="*************-4cbb-9625-8f9e4e662526">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="20" width="406" height="20" uuid="fa333c71-6079-419b-864e-149dc2c1faf6"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="60" width="406" height="20" uuid="832c4640-**************-78e94e1903ba">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
		</band>
	</summary>
</jasperReport>
