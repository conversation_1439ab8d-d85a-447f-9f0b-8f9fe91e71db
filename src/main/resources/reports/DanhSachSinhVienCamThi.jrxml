<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.11.0.final using JasperReports Library version 6.10.0  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ctCtdtKehoach" pageWidth="842" pageHeight="595" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="758" leftMargin="42" rightMargin="42" topMargin="42" bottomMargin="42" uuid="17cc378a-d42d-40ba-b8b5-7b9e64456a0a">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<property name="net.sf.jasperreports.export.xls.detect.cell.type" value="true"/>
	<property name="net.sf.jasperreports.export.xls.white.page.background" value="false"/>
	<property name="net.sf.jasperreports.export.xls.remove.empty.space.between.rows" value="true"/>
	<subDataset name="MyDataset" uuid="150f38fc-c918-42fd-b541-37fd5eb1dccd">
		<queryString>
			<![CDATA[]]>
		</queryString>
		<field name="namHoc" class="java.lang.String"/>
		<field name="hocKy" class="java.lang.String"/>
		<field name="tenMonHoc" class="java.lang.String"/>
	</subDataset>
	<parameter name="tenHocky" class="java.lang.String"/>
	<parameter name="tenDv" class="java.lang.String"/>
	<parameter name="tenNamHoc" class="java.lang.String"/>
	<parameter name="tenMonHoc" class="java.lang.String"/>
	<parameter name="tenNhom" class="java.lang.String"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="hoTen" class="java.lang.String"/>
	<field name="maSinhVien" class="java.lang.String"/>
	<field name="maLop" class="java.lang.String"/>
	<field name="tenLop" class="java.lang.String"/>
	<field name="lyDo" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="190" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<staticText>
				<reportElement x="0" y="0" width="360" height="20" uuid="69298ada-b9ee-44c0-8ccf-16122442d97a">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="13"/>
				</textElement>
				<text><![CDATA[UBND TỈNH CÀ MAU]]></text>
			</staticText>
			<textField>
				<reportElement x="0" y="20" width="360" height="30" uuid="c0a7accf-a08d-4537-9a25-fbf69a903954">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Times New Roman" size="13" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{tenDv}.toUpperCase()]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="360" y="20" width="400" height="30" uuid="1d8423cf-95b2-4743-add1-d65e366adcd8">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="14" isBold="true" isUnderline="false"/>
				</textElement>
				<text><![CDATA[Độc lập - Tự do - Hạnh phúc]]></text>
			</staticText>
			<staticText>
				<reportElement x="360" y="0" width="400" height="20" uuid="b710e624-6f91-42b6-bf4c-147348b055ba">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="50" width="760" height="50" uuid="3752416f-b941-4231-8da3-5c2c1dd34324">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Times New Roman" size="15" isBold="true"/>
				</textElement>
				<text><![CDATA[DANH SÁCH SINH VIÊN CẤM THI]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="160" width="40" height="30" uuid="7a7045e2-b1a9-4dcd-87da-ffd955297ab4"/>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[STT]]></text>
			</staticText>
			<staticText>
				<reportElement x="130" y="160" width="160" height="30" uuid="4d113c3e-3929-4998-89ea-c5b94e4ec184"/>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Họ và tên]]></text>
			</staticText>
			<staticText>
				<reportElement x="290" y="160" width="70" height="30" uuid="d794b1aa-6bce-4690-9d04-be07c620d433"/>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Mã lớp]]></text>
			</staticText>
			<staticText>
				<reportElement x="360" y="160" width="230" height="30" uuid="69d5f75a-1d51-4237-9111-5a14c927c6d6"/>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Tên lớp]]></text>
			</staticText>
			<staticText>
				<reportElement x="40" y="160" width="90" height="30" uuid="a15d7b1b-7b94-4620-ad4f-dc7115b0568c"/>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[MSSV]]></text>
			</staticText>
			<textField>
				<reportElement x="0" y="100" width="760" height="30" uuid="262ba185-7340-4ecd-a5d5-4a6504c18a76"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["HỌC KỲ "+ $P{tenHocky} +" NĂM HỌC "+$P{tenNamHoc}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="130" width="760" height="30" uuid="d2b6cb4e-44e5-4c25-b0bf-4a21c4f6d9ae">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Môn học: "+$P{tenMonHoc}+".    Nhóm: "+$P{tenNhom}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="590" y="160" width="170" height="30" uuid="3a544d6e-2b9f-41c5-b7a4-68f77b77816f"/>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Lý do cấm thi]]></text>
			</staticText>
		</band>
	</title>
	<detail>
		<band height="20" splitType="Prevent">
			<textField isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="40" height="20" uuid="7c4b8be4-9c14-4682-9912-b59606679a9f">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{REPORT_COUNT}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="40" y="0" width="90" height="20" uuid="96d6db02-a73f-4583-9e4f-b5ef96a85222">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{maSinhVien}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="130" y="0" width="160" height="20" uuid="b0110028-77b7-41e5-a7e0-2e7b2b994628">
					<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="false"/>
					<paragraph leftIndent="0"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{hoTen}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="290" y="0" width="70" height="20" uuid="4da58dfa-9f53-49cc-b718-7485c1040ecd">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{maLop}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="360" y="0" width="230" height="20" uuid="4e283519-5bb9-4da0-9f58-fa93f672d382">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{tenLop}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="590" y="0" width="170" height="20" uuid="05f267e2-6ce0-4f8a-9a7f-b85840905ade">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{lyDo}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="85">
			<staticText>
				<reportElement x="490" y="20" width="270" height="65" uuid="60d3bb65-99f2-48e7-ba08-831c4d8a9687">
					<property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="13" isBold="true" isItalic="false"/>
					<paragraph spacingBefore="10"/>
				</textElement>
				<text><![CDATA[Người lập biểu]]></text>
			</staticText>
			<textField>
				<reportElement x="0" y="0" width="360" height="20" uuid="94d54a3c-53f0-4b1e-8846-584cda4fb135"/>
				<textElement textAlignment="Left" verticalAlignment="Bottom">
					<font fontName="Times New Roman" size="13" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["In ngày "+((DAY( TODAY( )))  >= 10 ? DAY( TODAY( )) :"0"+DAY( TODAY( )))+" tháng "+(MONTH(TODAY( ) ) >= 10? MONTH(TODAY( ) ): "0"+MONTH(TODAY( ) ))+" năm "+YEAR(TODAY( ) )]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="490" y="0" width="270" height="20" uuid="9c62fd4c-9d29-460e-b193-d3e17d5706b4"/>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Times New Roman" size="13" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Cà Mau, ngày "+((DAY( TODAY( )))  >= 10 ? DAY( TODAY( )) :"0"+DAY( TODAY( )))+" tháng "+(MONTH(TODAY( ) ) >= 10? MONTH(TODAY( ) ): "0"+MONTH(TODAY( ) ))+" năm "+YEAR(TODAY( ) )]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="360" y="0" width="130" height="20" uuid="024e56f4-8d07-49c6-a040-5dd2c3661d68"/>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="20" width="360" height="65" uuid="b90660fc-4c09-4d66-a4b4-36f6aa87db10"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="13" isBold="true" isItalic="false"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="360" y="20" width="130" height="65" uuid="eb6d75c8-8dc2-43a3-81da-e7ded9929bb6"/>
				<text><![CDATA[]]></text>
			</staticText>
		</band>
	</summary>
</jasperReport>
