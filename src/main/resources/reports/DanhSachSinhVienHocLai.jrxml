<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.10.0.final using JasperReports Library version 6.10.0  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="dsSvThiLai " pageWidth="842" pageHeight="595" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="758" leftMargin="42" rightMargin="42" topMargin="42" bottomMargin="42" uuid="17cc378a-d42d-40ba-b8b5-7b9e64456a0a">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="Qldt.xml"/>
	<property name="net.sf.jasperreports.export.xls.detect.cell.type" value="true"/>
	<property name="net.sf.jasperreports.export.xls.white.page.background" value="false"/>
	<property name="net.sf.jasperreports.export.xls.remove.empty.space.between.rows" value="true"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<subDataset name="MyDataset" uuid="150f38fc-c918-42fd-b541-37fd5eb1dccd">
		<queryString>
			<![CDATA[]]>
		</queryString>
		<field name="namHoc" class="java.lang.String"/>
		<field name="hocKy" class="java.lang.String"/>
		<field name="tenMonHoc" class="java.lang.String"/>
	</subDataset>
	<parameter name="tenHocKy" class="java.lang.String"/>
	<parameter name="tenDv" class="java.lang.String"/>
	<parameter name="tenNamHoc" class="java.lang.String"/>
	<parameter name="idNienKhoa" class="java.lang.Integer"/>
	<parameter name="idHocKy" class="java.lang.Integer"/>
	<parameter name="idMonHoc" class="java.lang.Integer"/>
	<parameter name="idLop" class="java.lang.Integer"/>
	<parameter name="keyword" class="java.lang.String"/>
	<parameter name="idHdt" class="java.lang.Integer"/>
	<parameter name="maMonHoc" class="java.lang.String"/>
	<queryString>
		<![CDATA[call LAY_DS_SV_HOC_LAI($P{idNienKhoa},$P{idHocKy},$P{idMonHoc},$P{idLop},$P{idHdt},$P{keyword},$P{maMonHoc})]]>
	</queryString>
	<field name="ID_SINH_VIEN" class="java.lang.Integer">
		<property name="com.jaspersoft.studio.field.label" value="ID_SINH_VIEN"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="SINH_VIEN"/>
	</field>
	<field name="MA_SINH_VIEN" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="MA_SINH_VIEN"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="SINH_VIEN"/>
	</field>
	<field name="HO_TEN" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="HO_TEN"/>
	</field>
	<field name="NU" class="java.lang.Boolean">
		<property name="com.jaspersoft.studio.field.label" value="NU"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="SINH_VIEN"/>
	</field>
	<field name="NGAY_SINH" class="java.sql.Date">
		<property name="com.jaspersoft.studio.field.label" value="NGAY_SINH"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="SINH_VIEN"/>
	</field>
	<field name="TEN_NIEN_KHOA" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="TEN_NIEN_KHOA"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="NIEN_KHOA"/>
	</field>
	<field name="TEN_HOC_KY" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="TEN_HOC_KY"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="HOC_KY"/>
	</field>
	<field name="MA_MON_HOC" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="MA_MON_HOC"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="MON_HOC"/>
	</field>
	<field name="TEN_MON_HOC" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="TEN_MON_HOC"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="MON_HOC"/>
	</field>
	<field name="TEN_NHOM" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="TEN_NHOM"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="NHOM"/>
	</field>
	<field name="TEN_LOP" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="TEN_LOP"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="LOP"/>
	</field>
	<field name="GHI_CHU" class="java.lang.String"/>
	<field name="NGAY_DONG_PHI" class="java.lang.String"/>
	<field name="MA_LOP" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="190" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<staticText>
				<reportElement x="-30" y="0" width="390" height="20" uuid="69298ada-b9ee-44c0-8ccf-16122442d97a">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="13"/>
				</textElement>
				<text><![CDATA[UBND TỈNH CÀ MAU]]></text>
			</staticText>
			<textField>
				<reportElement x="-30" y="20" width="390" height="30" uuid="c0a7accf-a08d-4537-9a25-fbf69a903954">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Times New Roman" size="13" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{tenDv}.toUpperCase()]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="360" y="20" width="430" height="30" uuid="1d8423cf-95b2-4743-add1-d65e366adcd8">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="14" isBold="true" isUnderline="false"/>
				</textElement>
				<text><![CDATA[Độc lập - Tự do - Hạnh phúc]]></text>
			</staticText>
			<staticText>
				<reportElement x="360" y="0" width="430" height="20" uuid="b710e624-6f91-42b6-bf4c-147348b055ba">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM]]></text>
			</staticText>
			<staticText>
				<reportElement x="-30" y="50" width="820" height="50" uuid="3752416f-b941-4231-8da3-5c2c1dd34324">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Times New Roman" size="15" isBold="true"/>
				</textElement>
				<text><![CDATA[DANH SÁCH SINH VIÊN HỌC LẠI]]></text>
			</staticText>
			<staticText>
				<reportElement x="-30" y="160" width="30" height="30" uuid="7a7045e2-b1a9-4dcd-87da-ffd955297ab4"/>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[STT]]></text>
			</staticText>
			<staticText>
				<reportElement x="70" y="160" width="90" height="30" uuid="4d113c3e-3929-4998-89ea-c5b94e4ec184"/>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Họ và tên]]></text>
			</staticText>
			<staticText>
				<reportElement x="160" y="160" width="60" height="30" uuid="d794b1aa-6bce-4690-9d04-be07c620d433"/>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Ngày sinh]]></text>
			</staticText>
			<staticText>
				<reportElement x="220" y="160" width="50" height="30" uuid="69d5f75a-1d51-4237-9111-5a14c927c6d6"/>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Giới tính]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="160" width="70" height="30" uuid="a15d7b1b-7b94-4620-ad4f-dc7115b0568c"/>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[MSSV]]></text>
			</staticText>
			<textField>
				<reportElement x="-30" y="100" width="820" height="30" uuid="262ba185-7340-4ecd-a5d5-4a6504c18a76"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["HỌC KỲ "+$P{tenHocKy} +" NĂM HỌC "+$P{tenNamHoc}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="400" y="160" width="60" height="30" uuid="3a544d6e-2b9f-41c5-b7a4-68f77b77816f"/>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Niên khóa]]></text>
			</staticText>
			<staticText>
				<reportElement x="460" y="160" width="40" height="30" uuid="556440a8-c5a7-4b06-bdcf-828fd5b2ec5f"/>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Học kỳ]]></text>
			</staticText>
			<staticText>
				<reportElement x="500" y="160" width="50" height="30" uuid="22f995fa-953c-4566-874f-67ff93b388b9"/>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Môn học]]></text>
			</staticText>
			<staticText>
				<reportElement x="550" y="160" width="80" height="30" uuid="b04f7bfa-5c7b-4877-ac42-786aed344d56"/>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Tên nhóm]]></text>
			</staticText>
			<staticText>
				<reportElement x="320" y="160" width="80" height="30" uuid="b88693ae-8db0-4494-97f0-6940c61f71d8"/>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Lớp]]></text>
			</staticText>
			<staticText>
				<reportElement x="630" y="160" width="80" height="30" uuid="49674f19-c57c-4b61-9be2-79a47cbbb84a"/>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Ghi chú]]></text>
			</staticText>
			<staticText>
				<reportElement x="710" y="160" width="80" height="30" uuid="ad016e04-0f54-449d-aeed-9564c07d1a75"/>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Ngày đóng phí]]></text>
			</staticText>
			<staticText>
				<reportElement x="270" y="160" width="50" height="30" uuid="03cd1bbb-7559-45be-b721-80c989fbfb11"/>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Mã lớp]]></text>
			</staticText>
		</band>
	</title>
	<detail>
		<band height="20" splitType="Prevent">
			<textField isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="-30" y="0" width="30" height="20" uuid="7c4b8be4-9c14-4682-9912-b59606679a9f">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{REPORT_COUNT}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="70" height="20" uuid="96d6db02-a73f-4583-9e4f-b5ef96a85222">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MA_SINH_VIEN}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="70" y="0" width="90" height="20" uuid="b0110028-77b7-41e5-a7e0-2e7b2b994628">
					<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false"/>
					<paragraph leftIndent="0"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{HO_TEN}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="160" y="0" width="60" height="20" uuid="c08bf787-2904-4acc-8f8f-a536c362a66b">
					<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false"/>
					<paragraph leftIndent="0"/>
				</textElement>
				<textFieldExpression><![CDATA[DATEFORMAT($F{NGAY_SINH},"dd/MM/yyyy")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="220" y="0" width="50" height="20" uuid="4dcd040a-dd60-4412-a88e-597d70a602ae">
					<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false"/>
					<paragraph leftIndent="0"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NU}?"Nữ":"Nam"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="400" y="0" width="60" height="20" uuid="a54b0626-8ec2-483e-8a5d-bb5c2348a229">
					<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false"/>
					<paragraph leftIndent="0"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TEN_NIEN_KHOA}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="460" y="0" width="40" height="20" uuid="9b1e691d-5927-4e5e-b58a-8519cd86358d">
					<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false"/>
					<paragraph leftIndent="0"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TEN_HOC_KY}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="500" y="0" width="50" height="20" uuid="bb55e40a-c0b1-4fd8-ae1c-00c42891dd65">
					<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false"/>
					<paragraph leftIndent="0"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TEN_MON_HOC}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="550" y="0" width="80" height="20" uuid="6d6d4ae0-59bf-44d6-93f4-9a170b55e457">
					<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false"/>
					<paragraph leftIndent="0"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TEN_NHOM}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="320" y="0" width="80" height="20" uuid="4b8b4f0e-000c-4bac-9b46-7da6b21d381b">
					<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false"/>
					<paragraph leftIndent="0"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TEN_LOP}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="630" y="0" width="80" height="20" uuid="bb43b9ac-616d-4505-b7a2-ea669d5e359d">
					<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false"/>
					<paragraph leftIndent="0"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{GHI_CHU}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="710" y="0" width="80" height="20" uuid="bb4a5baf-968e-418d-a698-fb7416f2fd44">
					<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false"/>
					<paragraph leftIndent="0"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY_DONG_PHI}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="270" y="0" width="50" height="20" uuid="74d4894a-5256-44f2-ad70-acb0f5a20ce8">
					<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false"/>
					<paragraph leftIndent="0"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MA_LOP}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="85">
			<staticText>
				<reportElement x="460" y="20" width="330" height="65" uuid="60d3bb65-99f2-48e7-ba08-831c4d8a9687">
					<property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="13" isBold="true" isItalic="false"/>
					<paragraph spacingBefore="10"/>
				</textElement>
				<text><![CDATA[Người lập biểu]]></text>
			</staticText>
			<textField>
				<reportElement x="-30" y="0" width="390" height="20" uuid="94d54a3c-53f0-4b1e-8846-584cda4fb135"/>
				<textElement textAlignment="Left" verticalAlignment="Bottom">
					<font fontName="Times New Roman" size="13" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["In ngày "+((DAY( TODAY( )))  >= 10 ? DAY( TODAY( )) :"0"+DAY( TODAY( )))+" tháng "+(MONTH(TODAY( ) ) >= 10? MONTH(TODAY( ) ): "0"+MONTH(TODAY( ) ))+" năm "+YEAR(TODAY( ) )]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="460" y="0" width="330" height="20" uuid="9c62fd4c-9d29-460e-b193-d3e17d5706b4"/>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Times New Roman" size="13" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Cà Mau, ngày "+((DAY( TODAY( )))  >= 10 ? DAY( TODAY( )) :"0"+DAY( TODAY( )))+" tháng "+(MONTH(TODAY( ) ) >= 10? MONTH(TODAY( ) ): "0"+MONTH(TODAY( ) ))+" năm "+YEAR(TODAY( ) )]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="360" y="0" width="99" height="20" uuid="024e56f4-8d07-49c6-a040-5dd2c3661d68"/>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="-30" y="20" width="390" height="65" uuid="b90660fc-4c09-4d66-a4b4-36f6aa87db10"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="13" isBold="true" isItalic="false"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="360" y="20" width="100" height="65" uuid="eb6d75c8-8dc2-43a3-81da-e7ded9929bb6"/>
				<text><![CDATA[]]></text>
			</staticText>
		</band>
	</summary>
</jasperReport>
