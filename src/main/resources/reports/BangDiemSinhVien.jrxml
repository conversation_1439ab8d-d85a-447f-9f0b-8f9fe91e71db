<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.10.0.final using JasperReports Library version 6.10.0-unknown  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="bangDiemSv" pageWidth="595" pageHeight="842" whenNoDataType="AllSectionsNoDetail" columnWidth="535" leftMargin="30" rightMargin="30" topMargin="30" bottomMargin="30" uuid="17cc378a-d42d-40ba-b8b5-7b9e64456a0a">
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<subDataset name="MyDataset" uuid="150f38fc-c918-42fd-b541-37fd5eb1dccd">
		<queryString>
			<![CDATA[]]>
		</queryString>
		<field name="namHoc" class="java.lang.String"/>
		<field name="hocKy" class="java.lang.String"/>
		<field name="tenMonHoc" class="java.lang.String"/>
	</subDataset>
	<parameter name="hoTen" class="java.lang.String"/>
	<parameter name="tenDv" class="java.lang.String"/>
	<parameter name="nienKhoa" class="java.lang.String"/>
	<parameter name="tenNganh" class="java.lang.String"/>
	<parameter name="maSinhVien" class="java.lang.String"/>
	<parameter name="ngaySinh" class="java.sql.Date"/>
	<parameter name="heDaoTao" class="java.lang.String"/>
	<parameter name="maLop" class="java.lang.String"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="namHoc" class="java.lang.String"/>
	<field name="tenMonHoc" class="java.lang.String"/>
	<field name="hocKy" class="java.lang.String"/>
	<field name="maMonHoc" class="java.lang.String"/>
	<field name="soDVHT" class="java.lang.Integer"/>
	<field name="diemTb1" class="java.lang.String"/>
	<field name="diemTb2" class="java.lang.String"/>
	<field name="diemTbHk" class="java.lang.String"/>
	<field name="diemTbTl" class="java.lang.String"/>
	<field name="soDvhtTl" class="java.lang.Integer"/>
	<field name="ghiChu" class="java.lang.String"/>
	<field name="diem4TbHk" class="java.lang.String"/>
	<field name="diem4TbTl" class="java.lang.String"/>
	<field name="monTtn" class="java.lang.Boolean"/>
	<field name="tenNamHocHocKy" class="java.lang.String"/>
	<field name="soDVHTHk" class="java.lang.Integer"/>
	<variable name="sum_dvht" class="java.lang.Integer" resetType="Group" resetGroup="namHoc_grp" calculation="Sum">
		<variableExpression><![CDATA[$F{soDVHTHk}]]></variableExpression>
	</variable>
	<variable name="CUR_PAGE_NUM" class="java.lang.Integer" resetType="None" incrementType="Page">
		<variableExpression><![CDATA[$V{PAGE_NUMBER}]]></variableExpression>
	</variable>
	<variable name="STT" class="java.lang.Integer" resetType="Group" resetGroup="namHoc_grp" calculation="Count">
		<variableExpression><![CDATA[$F{maMonHoc}]]></variableExpression>
	</variable>
	<group name="namHoc_grp">
		<groupExpression><![CDATA[$F{tenNamHocHocKy}]]></groupExpression>
		<groupHeader>
			<band height="54">
				<textField isBlankWhenNull="true">
					<reportElement x="0" y="36" width="535" height="18" uuid="c3bca2bd-c0d6-4023-87bf-e7dec3143c44">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box padding="3">
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{tenNamHocHocKy}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="280" y="0" width="40" height="36" uuid="c811146a-fc3b-4bc4-b8cb-376a9e62fe42"/>
					<box padding="3">
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[Tín chỉ]]></text>
				</staticText>
				<staticText>
					<reportElement x="0" y="0" width="25" height="36" uuid="6cad9724-ea5e-4f1a-89d2-66c5d3449ec2">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box padding="3">
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[STT]]></text>
				</staticText>
				<staticText>
					<reportElement x="25" y="0" width="55" height="36" uuid="40b881f4-f30e-41f9-80cd-c34dfcc70ecb">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
					</reportElement>
					<box padding="3">
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[Mã MH]]></text>
				</staticText>
				<staticText>
					<reportElement x="400" y="18" width="80" height="18" uuid="4614796b-147d-48fc-a90a-e481147d9f04">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box padding="3">
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[Lần 2]]></text>
				</staticText>
				<staticText>
					<reportElement x="480" y="0" width="55" height="36" uuid="37fa930d-a97d-472d-8472-198cc7cef7df">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box padding="3">
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[Ghi chú]]></text>
				</staticText>
				<staticText>
					<reportElement x="80" y="0" width="200" height="36" uuid="9afd19ba-3570-4aa2-a80c-07203cad614f"/>
					<box padding="3">
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[Tên môn học]]></text>
				</staticText>
				<staticText>
					<reportElement x="320" y="0" width="160" height="18" uuid="ab12989e-3f5f-4407-a3a6-5fc1ee6667f5">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box padding="3">
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[Điểm môn học]]></text>
				</staticText>
				<staticText>
					<reportElement x="320" y="18" width="80" height="18" uuid="cf70d9fb-e19a-47a8-bdd0-e2c0ced9fa34">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box padding="3">
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="9" isBold="true"/>
					</textElement>
					<text><![CDATA[Lần 1]]></text>
				</staticText>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="34">
				<textField isBlankWhenNull="true">
					<reportElement x="100" y="0" width="160" height="12" isRemoveLineWhenBlank="true" uuid="c1cb0b83-aa16-4f53-8a3f-e723635ab101">
						<printWhenExpression><![CDATA[!$F{monTtn}]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Top">
						<font fontName="Times New Roman" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{sum_dvht}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="0" y="0" width="100" height="12" isRemoveLineWhenBlank="true" uuid="cd42c39a-0e7b-49e2-8658-ddddf4963af3">
						<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<printWhenExpression><![CDATA[!$F{monTtn}]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Top">
						<font fontName="Times New Roman" size="9" isBold="true"/>
						<paragraph leftIndent="3"/>
					</textElement>
					<text><![CDATA[Tổng số tín chỉ học kỳ:]]></text>
				</staticText>
				<staticText>
					<reportElement x="260" y="12" width="110" height="12" isRemoveLineWhenBlank="true" uuid="40bc8513-94f2-45dd-83df-4f7fdfa23031">
						<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<printWhenExpression><![CDATA[!$F{monTtn} && ($F{hocKy}.equals( "1" ) || $F{hocKy}.equals( "2" ))]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="9" isBold="true"/>
						<paragraph leftIndent="3"/>
					</textElement>
					<text><![CDATA[Điểm trung bình tích lũy:]]></text>
				</staticText>
				<textField isBlankWhenNull="true">
					<reportElement x="370" y="0" width="165" height="12" isRemoveLineWhenBlank="true" uuid="9f269ca0-dbe4-4d87-962a-fdf25fddb7bf">
						<printWhenExpression><![CDATA[!$F{monTtn}]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Top">
						<font fontName="Times New Roman" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{diem4TbHk}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="260" y="0" width="110" height="12" isRemoveLineWhenBlank="true" uuid="aafad11e-ad53-4317-be32-e084557a5c87">
						<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
						<printWhenExpression><![CDATA[!$F{monTtn}]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Top">
						<font fontName="Times New Roman" size="9" isBold="true"/>
						<paragraph leftIndent="3"/>
					</textElement>
					<text><![CDATA[Điểm trung bình học kỳ:]]></text>
				</staticText>
				<textField isBlankWhenNull="true">
					<reportElement x="370" y="12" width="165" height="12" isRemoveLineWhenBlank="true" uuid="e0c7f27c-c9ab-46ec-9093-eca36d63daca">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<printWhenExpression><![CDATA[!$F{monTtn} && ($F{hocKy}.equals( "1" ) || $F{hocKy}.equals( "2" ))]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{diem4TbTl}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="100" y="12" width="160" height="12" isRemoveLineWhenBlank="true" uuid="4a8ca783-57c6-4f17-a05a-22fcb27e88d3">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<printWhenExpression><![CDATA[!$F{monTtn} && ($F{hocKy}.equals( "1" ) || $F{hocKy}.equals( "2" ))]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{soDvhtTl}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="0" y="12" width="100" height="12" isRemoveLineWhenBlank="true" uuid="4d6fa891-1fc1-45a4-afd4-e491e79d9aae">
						<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<printWhenExpression><![CDATA[!$F{monTtn} && ($F{hocKy}.equals( "1" ) || $F{hocKy}.equals( "2" ))]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="9" isBold="true"/>
						<paragraph leftIndent="3"/>
					</textElement>
					<text><![CDATA[Số tín chỉ tích lũy:]]></text>
				</staticText>
				<staticText>
					<reportElement x="0" y="24" width="535" height="10" uuid="959a83af-e742-4abd-88ee-43a21ecda656">
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<textElement>
						<font fontName="Times New Roman" size="9"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
			</band>
		</groupFooter>
	</group>
	<title>
		<band height="90" splitType="Stretch">
			<textField>
				<reportElement x="0" y="12" width="140" height="12" uuid="c0a7accf-a08d-4537-9a25-fbf69a903954">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{tenDv}.toUpperCase()]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="42" width="535" height="12" uuid="6719fcf0-a34b-4c5b-b69c-73828eb2eddc">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[KẾT QUẢ HỌC TẬP TOÀN KHÓA]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="0" width="140" height="12" uuid="cec58423-ccab-4f0f-aca3-c4343e2b985e">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="9"/>
				</textElement>
				<text><![CDATA[UBND TỈNH CÀ MAU]]></text>
			</staticText>
			<staticText>
				<reportElement x="335" y="12" width="200" height="12" uuid="4c654aed-6e4d-469a-a051-b6661b2f0f8a">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Độc lập - Tự do - Hạnh phúc]]></text>
			</staticText>
			<staticText>
				<reportElement x="335" y="0" width="200" height="12" uuid="b77b82c7-f8e9-4542-9c00-7fa2abbbf195">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM]]></text>
			</staticText>
			<line>
				<reportElement x="30" y="24" width="80" height="1" uuid="2391ca85-6365-4a7d-aa68-3caece6bb5e0">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</line>
			<textField>
				<reportElement x="335" y="30" width="200" height="12" uuid="d013b77d-6ac4-4b3e-98d6-d36e00160a6e">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Cà Mau, ngày "+((DAY( TODAY( )))  >= 10 ? DAY( TODAY( )) :"0"+DAY( TODAY( )))+" tháng "+(MONTH(TODAY( ) ) >= 10? MONTH(TODAY( ) ): "0"+MONTH(TODAY( ) ))+" năm "+YEAR(TODAY( ) )]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="396" y="24" width="80" height="1" uuid="80c1b187-edd5-489c-9619-8da49a9d6710">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</line>
			<textField>
				<reportElement x="52" y="66" width="222" height="12" uuid="0989b165-488f-482b-a221-2824e999d508">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[DATEFORMAT($P{ngaySinh},"dd/MM/yyyy")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="321" y="54" width="214" height="12" uuid="c21b4257-95df-4aee-8ed5-65cf234d6aa5">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{maLop}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement stretchType="RelativeToTallestObject" x="321" y="78" width="214" height="12" uuid="a5f5bc28-8060-484f-b90c-49115a9add16">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{heDaoTao}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="279" y="54" width="42" height="12" uuid="6441c99b-a62c-454d-b0a5-2e1edbbe940f">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<text><![CDATA[Lớp]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToTallestObject" x="279" y="66" width="42" height="12" uuid="ac64e426-b626-4cab-9fe9-19676168047f">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<text><![CDATA[Ngành]]></text>
			</staticText>
			<textField>
				<reportElement x="52" y="78" width="222" height="12" uuid="f0ec1430-2f5a-4b23-a44b-f5f8063e110d">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{maSinhVien}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement stretchType="RelativeToTallestObject" x="279" y="78" width="42" height="12" uuid="a5c212e9-0d94-4840-a40b-556cad0de18c">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<text><![CDATA[Hệ đào tạo]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="66" width="52" height="12" uuid="5cf97399-bb7b-4881-afc1-c283d1e01a8f">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<text><![CDATA[Ngày sinh]]></text>
			</staticText>
			<textField>
				<reportElement stretchType="RelativeToTallestObject" x="321" y="66" width="214" height="12" uuid="c506214d-d5aa-4d50-9ba7-4d2eded23a70">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{tenNganh}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="78" width="52" height="12" uuid="476d4e1e-df19-4e37-8d29-f7e959fa1ea4">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<text><![CDATA[Mã sinh viên]]></text>
			</staticText>
			<textField>
				<reportElement x="52" y="54" width="222" height="12" uuid="fa5a49c4-1dd5-4d86-b84e-a377a904b79b">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{hoTen}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="54" width="52" height="12" uuid="845d1b4a-add4-4cff-8461-12424bf9e5f3">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<text><![CDATA[Sinh viên]]></text>
			</staticText>
		</band>
	</title>
	<detail>
		<band height="18">
			<textField isBlankWhenNull="true">
				<reportElement x="80" y="0" width="200" height="18" uuid="7814467d-aa09-41d9-8486-b2b79a2a71db">
					<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="false"/>
					<paragraph leftIndent="0"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{tenMonHoc}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="0" width="25" height="18" uuid="3ca0df67-69c7-4bbf-9777-839551ea62d4">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{STT}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="25" y="0" width="55" height="18" uuid="109ccc6f-52fe-4039-9c58-0b2733b12b70">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{maMonHoc}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="280" y="0" width="40" height="18" uuid="ec2a1c9c-b904-4ac5-b7d9-d3da3433e1ad">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{soDVHT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="480" y="0" width="55" height="18" uuid="26f6cdfc-b0f2-41ba-a664-f766c51cec01">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ghiChu}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="320" y="0" width="80" height="18" uuid="9dcf9944-6d56-4358-8a79-1547ed2a422c">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{diemTb1}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="400" y="0" width="80" height="18" uuid="bb9b84b7-4637-4d5a-a052-670fa3eb0840">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="3">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{diemTb2}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<pageFooter>
		<band height="15">
			<textField evaluationTime="Auto">
				<reportElement x="0" y="0" width="535" height="15" uuid="5d3b7a15-d30b-4986-8640-ad81369ae893">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA["Trang "+$V{CUR_PAGE_NUM}+"/"+$V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
	<summary>
		<band height="103">
			<staticText>
				<reportElement x="335" y="12" width="200" height="15" uuid="5f2eaafc-e4d9-43f9-a6ec-ef06669b39cd">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[HIỆU TRƯỞNG]]></text>
			</staticText>
			<staticText>
				<reportElement x="335" y="27" width="200" height="18" uuid="44fb27d0-7620-4e85-8de7-6e08e3bf101f">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="27" width="200" height="18" uuid="25c68422-01f6-45b8-963a-1276b4e0fe1c">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[TRƯỞNG PHÒNG ĐÀO TẠO]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="0" width="535" height="12" uuid="43426238-83e2-4911-92af-2db48522d6a1">
					<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="9" isItalic="true"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[Ghi chú: (*) Môn học tích lũy; (**) Môn học thi lại.]]></text>
			</staticText>
		</band>
	</summary>
</jasperReport>
