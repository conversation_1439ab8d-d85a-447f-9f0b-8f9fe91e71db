/*!
 * Fancytree "Win8" 32x32 skin.
 *
 * DON'T EDIT THE CSS FILE DIRECTLY, since it is automatically generated from
 * the LESS templates.
 */

// Import common styles
@import "../skin-common.less";

// Import standard win8
@import "../skin-win8/ui.fancytree.less";


// Override the variable after the import. 
// NOTE: Variables are always resolved as the last definition, even if it is 
// after where it is used.
@fancy-use-sprites: true;      // false: suppress all background images (i.e. icons)

@fancy-level-indent: 32px;
@fancy-line-height: 32px;      // height of a nodes selection bar including borders
@fancy-node-v-spacing: 1px;    // gap between two node borders
@fancy-icon-width: 32px;
@fancy-icon-height: 32px;
@fancy-icon-spacing: 6px;      // margin between icon/icon or icon/title
@fancy-icon-ofs-top: 0px;      // extra vertical offset for expander, checkbox and icon
@fancy-title-ofs-top: 0px;     // extra vertical offset for title
@fancy-node-border-width: 1px;
@fancy-node-border-radius: 0px;
@fancy-node-outline-width: 1px;

ul.fancytree-container {
//	font-family: tahoma, arial, helvetica;
	font-size: 20pt;
	padding: 6px;
}