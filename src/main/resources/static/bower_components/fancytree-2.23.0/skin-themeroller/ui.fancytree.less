/*!
 * Fancytree "ThemeRoller" skin.
 * This file should be included after a jQuery Themeroller style sheet.
 * It is meant to be used together with the ext-themeroller extension.
 *
 * DON'T EDIT THE CSS FILE DIRECTLY, since it is automatically generated from
 * the LESS templates.
 */

// Import common styles
@import "../skin-common.less";


/*******************************************************************************
 * Styles specific to this skin.
 *
 * This section is automatically generated from the `ui-fancytree.less` template.
 ******************************************************************************/


// Override the variable after the import. 
// NOTE: Variables are always resolved as the last definition, even if it is 
// after where it is used.
@fancy-use-sprites: true;      // false: suppress all background images (i.e. icons)

@fancy-line-height: 20px;      // height of a nodes selection bar including borders
@fancy-node-v-spacing: 0px;    // gap between two node borders
@fancy-icon-width: 16px;
@fancy-icon-height: 16px;
@fancy-icon-spacing: 3px;      // margin between icon/icon or icon/title
@fancy-icon-ofs-top: 2px;      // extra vertical offset for expander, checkbox and icon
@fancy-title-ofs-top: 0px;     // extra vertical offset for title
@fancy-node-border-width: 1px;
@fancy-node-border-radius: 0px;
@fancy-node-outline-width: 1px;


// Use 'data-uri(...)' to embed the image into CSS instead of linking to 'loading.gif':
@fancy-loading-url: data-uri("@{fancy-image-dir}/loading.gif");
// Set to `true` to use `data-uri(...)` which will embed icons.gif into CSS 
// instead of linking to that file:
// @fancy-inline-sprites: true;

/*******************************************************************************
 * Node titles
 */
.fancytree-plain  {
	span.fancytree-node {
		border: @fancy-node-border-width solid transparent;  // avoid jumping, when a border is added on hover
	}
}

/*******************************************************************************
 * 'table' extension
 */
table.fancytree-ext-table tbody {
	tr td {
	  border: 1px solid transparent;
	}
}
