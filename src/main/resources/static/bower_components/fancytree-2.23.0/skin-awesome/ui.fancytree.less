/*!
 * Fancytree "awesome" skin.
 *
 * DON'T EDIT THE CSS FILE DIRECTLY, since it is automatically generated from
 * the LESS templates.
 */

// Import common styles
@import "../skin-common.less";


/*******************************************************************************
 * Styles specific to this skin.
 *
 * This section is automatically generated from the `ui-fancytree.less` template.
 ******************************************************************************/

// Borders have NO radius and NO gradients are used!

// both:
//    unselected background: white
//    hover bar (unselected, inactive): #E5F3FB (border: #70C0E7) 'very light blue'
//    active node: #CBE8F6 (border: #26A0DA)  'light blue'
//    active node with hover: wie active node

// Tree view:
//    active node, tree inactive: #F7F7F7 (border: #DEDEDE) 'light gray, selected, but tree not active'

// List view: 
//    selected bar: --> active bar
//    focus  bar: transparent(white) + border 1px solid #3399FF  ()

//    table left/right border: #EDEDED 'light gray'

// local vars
@fancy-my-icon-size: 16px;

// Override the variable after the import. 
// NOTE: Variables are always resolved as the last definition, even if it is 
// after where it is used.
@fancy-use-sprites: false;     // false: suppress all background images (i.e. icons)
@fancy-loading-url: none;

@fancy-icon-width: 1em;
@fancy-icon-height: 1em;
@fancy-line-height: 1em;
@fancy-icon-spacing: 0.5em;

ul.fancytree-container ul
{
	padding: 0.3em 0 0 1em;
	margin: 0;
}
