/*!
 * Fancytree "Win7" skin.
 *
 * DON'T EDIT THE CSS FILE DIRECTLY, since it is automatically generated from
 * the LESS templates.
 */
/*******************************************************************************
 * Common Styles for Fancytree Skins.
 *
 * This section is automatically generated from the `skin-common.less` template.
 ******************************************************************************/
/*------------------------------------------------------------------------------
 * Helpers
 *----------------------------------------------------------------------------*/
.ui-helper-hidden {
  display: none;
}
/*------------------------------------------------------------------------------
 * Container and UL / LI
 *----------------------------------------------------------------------------*/
ul.fancytree-container {
  font-family: tahoma, arial, helvetica;
  font-size: 10pt;
  white-space: nowrap;
  padding: 3px;
  margin: 0;
  background-color: white;
  border: 1px dotted gray;
  min-height: 0%;
  position: relative;
}
ul.fancytree-container ul {
  padding: 0 0 0 16px;
  margin: 0;
}
ul.fancytree-container ul > li:before {
  content: none;
}
ul.fancytree-container li {
  list-style-image: none;
  list-style-position: outside;
  list-style-type: none;
  -moz-background-clip: border;
  -moz-background-inline-policy: continuous;
  -moz-background-origin: padding;
  background-attachment: scroll;
  background-color: transparent;
  background-position: 0px 0px;
  background-repeat: repeat-y;
  background-image: none;
  margin: 0;
}
ul.fancytree-container li.fancytree-lastsib {
  background-image: none;
}
.ui-fancytree-disabled ul.fancytree-container {
  opacity: 0.5;
  background-color: silver;
}
ul.fancytree-connectors.fancytree-container li {
  background-image: url("data:image/gif;base64,R0lGODlhEAAQAPcAAAAAANPT0wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACH5BAEAAP8ALAAAAAAQABAAAAgxAP8JHPgvAMGDCA0iXFiQ4UKFDglCjChwIkWLETE61MiQ40OKEkEO9JhQZEWTDRcGBAA7");
  background-position: 0 0;
}
ul.fancytree-container li.fancytree-lastsib,
ul.fancytree-no-connector > li {
  background-image: none;
}
li.fancytree-animating {
  position: relative;
}
/*------------------------------------------------------------------------------
 * Common icon definitions
 *----------------------------------------------------------------------------*/
span.fancytree-empty,
span.fancytree-vline,
span.fancytree-expander,
span.fancytree-icon,
span.fancytree-checkbox,
span.fancytree-drag-helper-img,
#fancytree-drop-marker {
  width: 16px;
  height: 16px;
  display: inline-block;
  vertical-align: top;
  background-repeat: no-repeat;
  background-position: left;
  background-image: url("icons.gif");
  background-position: 0px 0px;
}
span.fancytree-icon,
span.fancytree-checkbox,
span.fancytree-expander,
span.fancytree-custom-icon {
  margin-top: 2px;
}
/* Used by icon option: */
span.fancytree-custom-icon {
  width: 16px;
  height: 16px;
  display: inline-block;
  margin-left: 3px;
  background-position: 0px 0px;
}
/* Used by 'icon' node option: */
img.fancytree-icon {
  width: 16px;
  height: 16px;
  margin-left: 3px;
  margin-top: 2px;
  vertical-align: top;
  border-style: none;
}
/*------------------------------------------------------------------------------
 * Expander icon
 *
 * Note: IE6 doesn't correctly evaluate multiples class names,
 *		 so we create combined class names that can be used in the CSS.
 *
 * Prefix: fancytree-exp-
 * 1st character: 'e': expanded, 'c': collapsed, 'n': no children
 * 2nd character (optional): 'd': lazy (Delayed)
 * 3rd character (optional): 'l': Last sibling
 *----------------------------------------------------------------------------*/
span.fancytree-expander {
  cursor: pointer;
}
.fancytree-exp-n span.fancytree-expander,
.fancytree-exp-nl span.fancytree-expander {
  background-image: none;
  cursor: default;
}
.fancytree-connectors .fancytree-exp-n span.fancytree-expander,
.fancytree-connectors .fancytree-exp-nl span.fancytree-expander {
  background-image: url("icons.gif");
  margin-top: 0;
}
.fancytree-connectors .fancytree-exp-n span.fancytree-expander,
.fancytree-connectors .fancytree-exp-n span.fancytree-expander:hover {
  background-position: 0px -64px;
}
.fancytree-connectors .fancytree-exp-nl span.fancytree-expander,
.fancytree-connectors .fancytree-exp-nl span.fancytree-expander:hover {
  background-position: -16px -64px;
}
.fancytree-exp-c span.fancytree-expander {
  background-position: 0px -80px;
}
.fancytree-exp-c span.fancytree-expander:hover {
  background-position: -16px -80px;
}
.fancytree-exp-cl span.fancytree-expander {
  background-position: 0px -96px;
}
.fancytree-exp-cl span.fancytree-expander:hover {
  background-position: -16px -96px;
}
.fancytree-exp-cd span.fancytree-expander {
  background-position: -64px -80px;
}
.fancytree-exp-cd span.fancytree-expander:hover {
  background-position: -80px -80px;
}
.fancytree-exp-cdl span.fancytree-expander {
  background-position: -64px -96px;
}
.fancytree-exp-cdl span.fancytree-expander:hover {
  background-position: -80px -96px;
}
.fancytree-exp-e span.fancytree-expander,
.fancytree-exp-ed span.fancytree-expander {
  background-position: -32px -80px;
}
.fancytree-exp-e span.fancytree-expander:hover,
.fancytree-exp-ed span.fancytree-expander:hover {
  background-position: -48px -80px;
}
.fancytree-exp-el span.fancytree-expander,
.fancytree-exp-edl span.fancytree-expander {
  background-position: -32px -96px;
}
.fancytree-exp-el span.fancytree-expander:hover,
.fancytree-exp-edl span.fancytree-expander:hover {
  background-position: -48px -96px;
}
/* Fade out expanders, when container is not hovered or active */
.fancytree-fade-expander span.fancytree-expander {
  transition: opacity 1.5s;
  opacity: 0;
}
.fancytree-fade-expander:hover span.fancytree-expander,
.fancytree-fade-expander.fancytree-treefocus span.fancytree-expander,
.fancytree-fade-expander .fancytree-treefocus span.fancytree-expander,
.fancytree-fade-expander [class*='fancytree-statusnode-'] span.fancytree-expander {
  transition: opacity 0.6s;
  opacity: 1;
}
/*------------------------------------------------------------------------------
 * Checkbox icon
 *----------------------------------------------------------------------------*/
span.fancytree-checkbox {
  margin-left: 3px;
  background-position: 0px -32px;
}
span.fancytree-checkbox:hover {
  background-position: -16px -32px;
}
span.fancytree-checkbox.fancytree-radio {
  background-position: 0px -48px;
}
span.fancytree-checkbox.fancytree-radio:hover {
  background-position: -16px -48px;
}
.fancytree-partsel span.fancytree-checkbox {
  background-position: -64px -32px;
}
.fancytree-partsel span.fancytree-checkbox:hover {
  background-position: -80px -32px;
}
.fancytree-partsel span.fancytree-checkbox.fancytree-radio {
  background-position: -64px -48px;
}
.fancytree-partsel span.fancytree-checkbox.fancytree-radio:hover {
  background-position: -80px -48px;
}
.fancytree-selected span.fancytree-checkbox {
  background-position: -32px -32px;
}
.fancytree-selected span.fancytree-checkbox:hover {
  background-position: -48px -32px;
}
.fancytree-selected span.fancytree-checkbox.fancytree-radio {
  background-position: -32px -48px;
}
.fancytree-selected span.fancytree-checkbox.fancytree-radio:hover {
  background-position: -48px -48px;
}
.fancytree-unselectable span.fancytree-checkbox {
  opacity: 0.4;
  filter: alpha(opacity=40);
}
.fancytree-unselectable span.fancytree-checkbox:hover {
  background-position: 0px -32px;
}
.fancytree-unselectable.fancytree-partsel span.fancytree-checkbox:hover {
  background-position: -64px -32px;
}
.fancytree-unselectable.fancytree-selected span.fancytree-checkbox:hover {
  background-position: -32px -32px;
}
/*------------------------------------------------------------------------------
 * Node type icon
 * Note: IE6 doesn't correctly evaluate multiples class names,
 *		 so we create combined class names that can be used in the CSS.
 *
 * Prefix: fancytree-ico-
 * 1st character: 'e': expanded, 'c': collapsed
 * 2nd character (optional): 'f': folder
 *----------------------------------------------------------------------------*/
span.fancytree-icon {
  margin-left: 3px;
  background-position: 0px 0px;
}
/* Documents */
.fancytree-ico-c span.fancytree-icon:hover {
  background-position: -16px 0px;
}
.fancytree-has-children.fancytree-ico-c span.fancytree-icon {
  background-position: -32px 0px;
}
.fancytree-has-children.fancytree-ico-c span.fancytree-icon:hover {
  background-position: -48px 0px;
}
.fancytree-ico-e span.fancytree-icon {
  background-position: -64px 0px;
}
.fancytree-ico-e span.fancytree-icon:hover {
  background-position: -80px 0px;
}
/* Folders */
.fancytree-ico-cf span.fancytree-icon {
  background-position: 0px -16px;
}
.fancytree-ico-cf span.fancytree-icon:hover {
  background-position: -16px -16px;
}
.fancytree-has-children.fancytree-ico-cf span.fancytree-icon {
  background-position: -32px -16px;
}
.fancytree-has-children.fancytree-ico-cf span.fancytree-icon:hover {
  background-position: -48px -16px;
}
.fancytree-ico-ef span.fancytree-icon {
  background-position: -64px -16px;
}
.fancytree-ico-ef span.fancytree-icon:hover {
  background-position: -80px -16px;
}
.fancytree-loading span.fancytree-expander,
.fancytree-loading span.fancytree-expander:hover,
.fancytree-statusnode-loading span.fancytree-icon,
.fancytree-statusnode-loading span.fancytree-icon:hover {
  background-image: url("data:image/gif;base64,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");
  background-position: 0px 0px;
}
/* Status node icons */
.fancytree-statusnode-error span.fancytree-icon,
.fancytree-statusnode-error span.fancytree-icon:hover {
  background-position: 0px -112px;
}
/*------------------------------------------------------------------------------
 * Node titles and highlighting
 *----------------------------------------------------------------------------*/
span.fancytree-node {
  /* See #117 */
  display: inherit;
  width: 100%;
  margin-top: 1px;
  min-height: 20px;
}
span.fancytree-title {
  color: black;
  cursor: pointer;
  display: inline-block;
  vertical-align: top;
  min-height: 20px;
  padding: 0 3px 0 3px;
  margin: 0px 0 0 3px;
  border: 1px solid transparent;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  -ms-border-radius: 3px;
  -o-border-radius: 3px;
  border-radius: 3px;
}
span.fancytree-node.fancytree-error span.fancytree-title {
  color: red;
}
/*------------------------------------------------------------------------------
 * Drag'n'drop support
 *----------------------------------------------------------------------------*/
div.fancytree-drag-helper span.fancytree-childcounter,
div.fancytree-drag-helper span.fancytree-dnd-modifier {
  display: inline-block;
  color: #fff;
  background: #337ab7;
  border: 1px solid gray;
  min-width: 10px;
  height: 10px;
  line-height: 1;
  vertical-align: baseline;
  border-radius: 10px;
  padding: 2px;
  text-align: center;
  font-size: 9px;
}
div.fancytree-drag-helper span.fancytree-childcounter {
  position: absolute;
  top: -6px;
  right: -6px;
}
div.fancytree-drag-helper span.fancytree-dnd-modifier {
  background: #5cb85c;
  border: none;
  font-weight: bolder;
}
div.fancytree-drag-helper.fancytree-drop-accept span.fancytree-drag-helper-img {
  background-position: -32px -112px;
}
div.fancytree-drag-helper.fancytree-drop-reject span.fancytree-drag-helper-img {
  background-position: -16px -112px;
}
/*** Drop marker icon *********************************************************/
#fancytree-drop-marker {
  width: 32px;
  position: absolute;
  background-position: 0px -128px;
  margin: 0;
}
#fancytree-drop-marker.fancytree-drop-after,
#fancytree-drop-marker.fancytree-drop-before {
  width: 64px;
  background-position: 0px -144px;
}
#fancytree-drop-marker.fancytree-drop-copy {
  background-position: -64px -128px;
}
#fancytree-drop-marker.fancytree-drop-move {
  background-position: -32px -128px;
}
/*** Source node while dragging ***********************************************/
span.fancytree-drag-source.fancytree-drag-remove {
  opacity: 0.15;
}
/*** Target node while dragging cursor is over it *****************************/
/*------------------------------------------------------------------------------
 * 'rtl' option
 *----------------------------------------------------------------------------*/
.fancytree-container.fancytree-rtl .fancytree-title {
  /*unicode-bidi: bidi-override;*/
  /* optional: reverse title letters */
}
.fancytree-container.fancytree-rtl span.fancytree-connector,
.fancytree-container.fancytree-rtl span.fancytree-expander,
.fancytree-container.fancytree-rtl span.fancytree-icon,
.fancytree-container.fancytree-rtl span.fancytree-drag-helper-img,
.fancytree-container.fancytree-rtl #fancytree-drop-marker {
  background-image: url("icons-rtl.gif");
}
.fancytree-container.fancytree-rtl .fancytree-exp-n span.fancytree-expander,
.fancytree-container.fancytree-rtl .fancytree-exp-nl span.fancytree-expander {
  background-image: none;
}
.fancytree-container.fancytree-rtl.fancytree-connectors .fancytree-exp-n span.fancytree-expander,
.fancytree-container.fancytree-rtl.fancytree-connectors .fancytree-exp-nl span.fancytree-expander {
  background-image: url("icons-rtl.gif");
}
ul.fancytree-container.fancytree-rtl ul {
  padding: 0 16px 0 0;
}
ul.fancytree-container.fancytree-rtl.fancytree-connectors li {
  background-position: right 0;
  background-image: url("vline-rtl.gif");
}
ul.fancytree-container.fancytree-rtl li.fancytree-lastsib,
ul.fancytree-container.fancytree-rtl.fancytree-no-connector > li {
  background-image: none;
}
/*------------------------------------------------------------------------------
 * 'table' extension
 *----------------------------------------------------------------------------*/
table.fancytree-ext-table {
  border-collapse: collapse;
}
table.fancytree-ext-table span.fancytree-node {
  display: inline-block;
  box-sizing: border-box;
}
/*------------------------------------------------------------------------------
 * 'columnview' extension
 *----------------------------------------------------------------------------*/
table.fancytree-ext-columnview tbody tr td {
  position: relative;
  border: 1px solid gray;
  vertical-align: top;
  overflow: auto;
}
table.fancytree-ext-columnview tbody tr td > ul {
  padding: 0;
}
table.fancytree-ext-columnview tbody tr td > ul li {
  list-style-image: none;
  list-style-position: outside;
  list-style-type: none;
  -moz-background-clip: border;
  -moz-background-inline-policy: continuous;
  -moz-background-origin: padding;
  background-attachment: scroll;
  background-color: transparent;
  background-position: 0px 0px;
  background-repeat: repeat-y;
  background-image: none;
  /* no v-lines */
  margin: 0;
}
table.fancytree-ext-columnview span.fancytree-node {
  position: relative;
  /* allow positioning of embedded spans */
  display: inline-block;
}
table.fancytree-ext-columnview span.fancytree-node.fancytree-expanded {
  background-color: #CBE8F6;
}
table.fancytree-ext-columnview .fancytree-has-children span.fancytree-cv-right {
  position: absolute;
  right: 3px;
  background-position: 0px -80px;
}
table.fancytree-ext-columnview .fancytree-has-children span.fancytree-cv-right:hover {
  background-position: -16px -80px;
}
/*------------------------------------------------------------------------------
 * 'filter' extension
 *----------------------------------------------------------------------------*/
.fancytree-ext-filter-dimm span.fancytree-node span.fancytree-title {
  color: silver;
  font-weight: lighter;
}
.fancytree-ext-filter-dimm tr.fancytree-submatch span.fancytree-title,
.fancytree-ext-filter-dimm span.fancytree-node.fancytree-submatch span.fancytree-title {
  color: black;
  font-weight: normal;
}
.fancytree-ext-filter-dimm tr.fancytree-match span.fancytree-title,
.fancytree-ext-filter-dimm span.fancytree-node.fancytree-match span.fancytree-title {
  color: black;
  font-weight: bold;
}
.fancytree-ext-filter-hide tr.fancytree-hide,
.fancytree-ext-filter-hide span.fancytree-node.fancytree-hide {
  display: none;
}
.fancytree-ext-filter-hide tr.fancytree-submatch span.fancytree-title,
.fancytree-ext-filter-hide span.fancytree-node.fancytree-submatch span.fancytree-title {
  color: silver;
  font-weight: lighter;
}
.fancytree-ext-filter-hide tr.fancytree-match span.fancytree-title,
.fancytree-ext-filter-hide span.fancytree-node.fancytree-match span.fancytree-title {
  color: black;
  font-weight: normal;
}
/* Hide expanders if all child nodes are hidden by filter */
.fancytree-ext-filter-hide-expanders tr.fancytree-match span.fancytree-expander,
.fancytree-ext-filter-hide-expanders span.fancytree-node.fancytree-match span.fancytree-expander {
  visibility: hidden;
}
.fancytree-ext-filter-hide-expanders tr.fancytree-submatch span.fancytree-expander,
.fancytree-ext-filter-hide-expanders span.fancytree-node.fancytree-submatch span.fancytree-expander {
  visibility: visible;
}
.fancytree-ext-childcounter span.fancytree-icon,
.fancytree-ext-filter span.fancytree-icon {
  position: relative;
}
.fancytree-ext-childcounter span.fancytree-childcounter,
.fancytree-ext-filter span.fancytree-childcounter {
  color: #fff;
  background: #777;
  border: 1px solid gray;
  position: absolute;
  top: -6px;
  right: -6px;
  min-width: 10px;
  height: 10px;
  line-height: 1;
  vertical-align: baseline;
  border-radius: 10px;
  padding: 2px;
  text-align: center;
  font-size: 9px;
}
/*------------------------------------------------------------------------------
 * 'wide' extension
 *----------------------------------------------------------------------------*/
ul.fancytree-ext-wide {
  position: relative;
  min-width: 100%;
  z-index: 2;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
ul.fancytree-ext-wide span.fancytree-node > span {
  position: relative;
  z-index: 2;
}
ul.fancytree-ext-wide span.fancytree-node span.fancytree-title {
  position: absolute;
  z-index: 1;
  left: 0px;
  min-width: 100%;
  margin-left: 0;
  margin-right: 0;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
/*------------------------------------------------------------------------------
 * 'fixed' extension
 *----------------------------------------------------------------------------*/
.fancytree-ext-fixed-wrapper .fancytree-fixed-hidden {
  display: none;
}
.fancytree-ext-fixed-wrapper div.scrollBorderBottom {
  border-bottom: 3px solid rgba(0, 0, 0, 0.75);
}
.fancytree-ext-fixed-wrapper div.scrollBorderRight {
  border-right: 3px solid rgba(0, 0, 0, 0.75);
}
.fancytree-ext-fixed-wrapper div.fancytree-fixed-wrapper-tl {
  position: absolute;
  overflow: hidden;
  z-index: 3;
  top: 0px;
  left: 0px;
}
.fancytree-ext-fixed-wrapper div.fancytree-fixed-wrapper-tr {
  position: absolute;
  overflow: hidden;
  z-index: 2;
  top: 0px;
}
.fancytree-ext-fixed-wrapper div.fancytree-fixed-wrapper-bl {
  position: absolute;
  overflow: hidden;
  z-index: 2;
  left: 0px;
}
.fancytree-ext-fixed-wrapper div.fancytree-fixed-wrapper-br {
  position: absolute;
  overflow: scroll;
  z-index: 1;
}
/*******************************************************************************
 * Styles specific to this skin.
 *
 * This section is automatically generated from the `ui-fancytree.less` template.
 ******************************************************************************/
/*******************************************************************************
 * Node titles
 */
span.fancytree-active .fancytree-title,
span.fancytree-selected .fancytree-title {
  border-color: #d9d9d9;
  background: #e5e5e5;
  color: inherit;
  background: -moz-linear-gradient(top, #fafafb 0%, #e5e5e5 100%);
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #fafafb), color-stop(100%, #e5e5e5));
  background: -webkit-linear-gradient(top, #fafafb 0%, #e5e5e5 100%);
  background: -o-linear-gradient(top, #fafafb 0%, #e5e5e5 100%);
  background: -ms-linear-gradient(top, #fafafb 0%, #e5e5e5 100%);
  background: linear-gradient(to bottom, #fafafb 0%, #e5e5e5 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fafafb', endColorstr='#e5e5e5', GradientType=0);
}
span.fancytree-selected .fancytree-title {
  font-style: italic;
}
.fancytree-treefocus span.fancytree-active .fancytree-title,
.fancytree-treefocus span.fancytree-selected .fancytree-title {
  border-color: #99defd;
  background: #f6fbfd;
  color: inherit;
  background: -moz-linear-gradient(top, #f6fbfd 0%, #d5effc 100%);
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #f6fbfd), color-stop(100%, #d5effc));
  background: -webkit-linear-gradient(top, #f6fbfd 0%, #d5effc 100%);
  background: -o-linear-gradient(top, #f6fbfd 0%, #d5effc 100%);
  background: -ms-linear-gradient(top, #f6fbfd 0%, #d5effc 100%);
  background: linear-gradient(to bottom, #f6fbfd 0%, #d5effc 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#f6fbfd', endColorstr='#d5effc', GradientType=0);
}
.fancytree-treefocus span.fancytree-focused span.fancytree-title {
  border: 1px solid #719acb;
}
span.fancytree-title:hover {
  border-color: #d8f0fa;
  background: #f8fcfe;
  color: inherit;
  background: -moz-linear-gradient(top, #f8fcfe 0%, #eff9fe 100%);
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #f8fcfe), color-stop(100%, #eff9fe));
  background: -webkit-linear-gradient(top, #f8fcfe 0%, #eff9fe 100%);
  background: -o-linear-gradient(top, #f8fcfe 0%, #eff9fe 100%);
  background: -ms-linear-gradient(top, #f8fcfe 0%, #eff9fe 100%);
  background: linear-gradient(to bottom, #f8fcfe 0%, #eff9fe 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#f8fcfe', endColorstr='#eff9fe', GradientType=0);
}
span.fancytree-active .fancytree-title:hover,
span.fancytree-selected .fancytree-title:hover {
  border-color: #719acb;
  background: #f2f9fd;
  color: inherit;
  background: -moz-linear-gradient(top, #f2f9fd 0%, #c4e8fa 100%);
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #f2f9fd), color-stop(100%, #c4e8fa));
  background: -webkit-linear-gradient(top, #f2f9fd 0%, #c4e8fa 100%);
  background: -o-linear-gradient(top, #f2f9fd 0%, #c4e8fa 100%);
  background: -ms-linear-gradient(top, #f2f9fd 0%, #c4e8fa 100%);
  background: linear-gradient(to bottom, #f2f9fd 0%, #c4e8fa 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#f2f9fd', endColorstr='#c4e8fa', GradientType=0);
}
/*******************************************************************************
 * 'table' extension
 */
table.fancytree-ext-table tbody tr td {
  border: 1px solid #ededed;
}
table.fancytree-ext-table tbody tr:hover {
  border-color: inherit;
  background: #f8fcfe;
  color: inherit;
  background: -moz-linear-gradient(top, #f8fcfe 0%, #eff9fe 100%);
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #f8fcfe), color-stop(100%, #eff9fe));
  background: -webkit-linear-gradient(top, #f8fcfe 0%, #eff9fe 100%);
  background: -o-linear-gradient(top, #f8fcfe 0%, #eff9fe 100%);
  background: -ms-linear-gradient(top, #f8fcfe 0%, #eff9fe 100%);
  background: linear-gradient(to bottom, #f8fcfe 0%, #eff9fe 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#f8fcfe', endColorstr='#eff9fe', GradientType=0);
  outline: 1px solid #d8f0fa;
}
table.fancytree-ext-table tbody tr.fancytree-focused {
  outline: 1px dotted #090402;
}
table.fancytree-ext-table tbody span.fancytree-focused span.fancytree-title {
  outline: solid dotted black;
}
table.fancytree-ext-table tbody span.fancytree-title:hover {
  border: 1px solid transparent;
  background: inherit;
  background: transparent;
  background: none;
  filter: none;
}
table.fancytree-ext-table tbody tr.fancytree-active:hover,
table.fancytree-ext-table tbody tr.fancytree-selected:hover {
  border-color: inherit;
  background: #f2f9fd;
  color: inherit;
  background: -moz-linear-gradient(top, #f2f9fd 0%, #c4e8fa 100%);
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #f2f9fd), color-stop(100%, #c4e8fa));
  background: -webkit-linear-gradient(top, #f2f9fd 0%, #c4e8fa 100%);
  background: -o-linear-gradient(top, #f2f9fd 0%, #c4e8fa 100%);
  background: -ms-linear-gradient(top, #f2f9fd 0%, #c4e8fa 100%);
  background: linear-gradient(to bottom, #f2f9fd 0%, #c4e8fa 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#f2f9fd', endColorstr='#c4e8fa', GradientType=0);
  outline: 1px solid #B6E6FB;
}
table.fancytree-ext-table tbody tr.fancytree-active,
table.fancytree-ext-table tbody tr.fancytree-selected {
  border-color: inherit;
  background: #f6fbfd;
  color: inherit;
  background: -moz-linear-gradient(top, #f6fbfd 0%, #d5effc 100%);
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #f6fbfd), color-stop(100%, #d5effc));
  background: -webkit-linear-gradient(top, #f6fbfd 0%, #d5effc 100%);
  background: -o-linear-gradient(top, #f6fbfd 0%, #d5effc 100%);
  background: -ms-linear-gradient(top, #f6fbfd 0%, #d5effc 100%);
  background: linear-gradient(to bottom, #f6fbfd 0%, #d5effc 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#f6fbfd', endColorstr='#d5effc', GradientType=0);
  outline: 1px solid #99DEFD;
}
