/*!
 * Fancytree "bootstrap" skin (highlighting the node span instead of title-only).
 *
 * DON'T EDIT THE CSS FILE DIRECTLY, since it is automatically generated from
 * the LESS templates.
 */

// Import common styles
@import "../skin-common.less";


/*******************************************************************************
 * Styles specific to this skin.
 *
 * This section is automatically generated from the `ui-fancytree.less` template.
 ******************************************************************************/


// local vars
@fancy-my-icon-size: 16px;

// Override the variable after the import. 
// NOTE: Variables are always resolved as the last definition, even if it is 
// after where it is used.
@fancy-use-sprites: false;  // false: suppress all background images (i.e. icons)
@fancy-loading-url: none;

@fancy-icon-width: 1em;
@fancy-icon-height: 1em;
@fancy-line-height: 1em;
@fancy-icon-spacing: 0.5em;

// Use 'data-uri(...)' to embed the image into CSS instead of linking to 'loading.gif':
// @fancy-loading-url: data-uri("@{fancy-image-dir}/loading.gif");
// Set to `true` to use `data-uri(...)` which will embed icons.gif into CSS 
// instead of linking to that file:
// @fancy-inline-sprites: true;


ul.fancytree-container ul {
	padding: 0 0 0 1.5em;
	margin: 0;
}
/* Prevent focus frame */
.fancytree-container:focus {
	outline: none; 
}

// Error status node
.fancytree-container span.fancytree-statusnode-error span.fancytree-expander  {
	color: @fancy-font-error-color;
}

/* Helper to allow spinning loader icon with bootstrap */
.glyphicon-spin {
    -webkit-animation: spin 1000ms infinite linear;
    animation: spin 1000ms infinite linear;
}
@-webkit-keyframes spin {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(359deg);
        transform: rotate(359deg);
    }
}
@keyframes spin {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(359deg);
        transform: rotate(359deg);
    }
}

/////
// Original bootstrap colors (http://getbootstrap.com/css/#responsive-utilities)
@gray-darker:  lighten(#000, 13.5%); // #222
@gray-dark:    lighten(#000, 20%);   // #333
@gray:         lighten(#000, 33.5%); // #555
@gray-light:   lighten(#000, 60%);   // #999
@gray-lighter: lighten(#000, 93.5%); // #eee

@brand-primary: #428bca;  // blue
@brand-success: #5cb85c;  // green
@brand-info:    #5bc0de;  // light blue
@brand-warning: #f0ad4e;  // orange
@brand-danger:  #d9534f;  // red

@border-radius-base:  4px;
@border-radius-large: 6px;
@border-radius-small: 3px;
/////////////

span.fancytree-node {
	border: @fancy-node-border-width solid transparent;  // avoid jumping, when a border is added on hover
	border-radius: @border-radius-small;
	padding-left: 8px;
}
span.fancytree-title {
	border-radius: @border-radius-small;
}
// Inactive tree:
span.fancytree-node.fancytree-selected { // selected nodes inside inactive tree
	background-color: lighten(@brand-success, 10%);
	border-color: lighten(@brand-success, 10%);
	span.fancytree-title {
		background-color: lighten(@brand-success, 10%); // green title, even when active
	}
}
span.fancytree-node.fancytree-active { // active nodes inside inactive tree
	background-color: lighten(@brand-primary, 10%);
}
// Active tree:
.fancytree-container.fancytree-treefocus {
	span.fancytree-node:hover {
		background-color: lighten(@brand-primary, 42%);
	}
	span.fancytree-node.fancytree-focused {
		border-color: @brand-primary;
	}
	span.fancytree-node.fancytree-selected {
		background-color: @brand-success;
		span.fancytree-title {
			background-color: @brand-success; // green title, even when active
		}
	}
	span.fancytree-node.fancytree-active {
		background-color: @brand-primary;
		border-color: @brand-primary;
	}
}

/*******************************************************************************
 * 'table' extension
 */
table.fancytree-ext-table tbody {
	tr td {
	  border: 1px solid @gray-lighter;
	}
	// span.fancytree-node,
	// span.fancytree-node:hover { // undo standard tree css
	// 	border: none;
	// 	background: none;
	// }
	// // Title get's a white background, when hovered. Undo standard node formatting
	// span.fancytree-title:hover {
	//   border: none;
	//   background: inherit;
	//   background: transparent;
	//   background: none;
	//   filter: none;
	// }
	// dimmed, if inside inactive tree
	tr.fancytree-selected {  
		background-color: lighten(@brand-success, 10%);
		span.fancytree-node {
			background-color: lighten(@brand-success, 10%);
		}
		span.fancytree-title {
			background-color: lighten(@brand-success, 10%); // green title, even when active
		}
	}
	tr.fancytree-active {  // dimmed, if inside inactive tree
		background-color: lighten(@brand-primary, 10%);
		span.fancytree-node {
			background-color: lighten(@brand-primary, 10%);
		}
	}
}

table.fancytree-ext-table.fancytree-treefocus tbody {
	tr:hover  {
		background-color: lighten(@brand-primary, 42%);
	    // outline: 1px solid @brand-primary;
	}
	tr.fancytree-focused span.fancytree-title {
		outline: 1px dotted @brand-primary;
	}
	tr.fancytree-active:hover,
	tr.fancytree-selected:hover {
		// background-color: #CBE8F6;
	 //    outline: 1px solid #26A0DA;
	}
	tr.fancytree-selected {
		background-color: @brand-success;
		span.fancytree-node {
			background-color: @brand-success;
		}
		span.fancytree-title {
			background-color: @brand-success; // green title, even when active
		}
	}
	tr.fancytree-active {
		background-color: @brand-primary;
		span.fancytree-node {
			background-color: @brand-primary;
		}
	}
}
