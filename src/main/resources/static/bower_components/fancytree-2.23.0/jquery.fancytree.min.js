/*!
 * jquery.fancytree.js
 * Tree view control with support for lazy loading and much more.
 * https://github.com/mar10/fancytree/
 *
 * Copyright (c) 2008-2017, <PERSON> (http://wwWendt.de)
 * Released under the MIT license
 * https://github.com/mar10/fancytree/wiki/LicenseInfo
 *
 * @version 2.23.0
 * @date 2017-05-27T20:09:38Z
 */
!function(a,b,c,d){"use strict";function e(b,c){b||(c=c?": "+c:"",a.error("Fancytree assertion failed"+c))}function f(a,c){var d,e,f=b.console?b.console[a]:null;if(f)try{f.apply(b.console,c)}catch(g){for(e="",d=0;d<c.length;d++)e+=c[d];f(e)}}function g(a){return!(!a.tree||a.statusNodeType===d)}function h(b,c,d,e){var f,g,h,i=a.map(a.trim(b).split("."),function(a){return parseInt(a,10)}),j=a.map(Array.prototype.slice.call(arguments,1),function(a){return parseInt(a,10)});for(f=0;f<j.length;f++)if(g=i[f]||0,h=j[f]||0,g!==h)return g>h;return!0}function i(a,b,c,d,e){var f=function(){var c=b[a],f=d[a],g=b.ext[e],h=function(){return c.apply(b,arguments)},i=function(a){return c.apply(b,a)};return function(){var a=b._local,c=b._super,d=b._superApply;try{return b._local=g,b._super=h,b._superApply=i,f.apply(b,arguments)}finally{b._local=a,b._super=c,b._superApply=d}}}();return f}function j(b,c,d,e){for(var f in d)"function"==typeof d[f]?"function"==typeof b[f]?b[f]=i(f,b,c,d,e):"_"===f.charAt(0)?b.ext[e][f]=i(f,b,c,d,e):a.error("Could not override tree."+f+". Use prefix '_' to create tree."+e+"._"+f):"options"!==f&&(b.ext[e][f]=d[f])}function k(b,c){return b===d?a.Deferred(function(){this.resolve()}).promise():a.Deferred(function(){this.resolveWith(b,c)}).promise()}function l(b,c){return b===d?a.Deferred(function(){this.reject()}).promise():a.Deferred(function(){this.rejectWith(b,c)}).promise()}function m(a,b){return function(){a.resolveWith(b)}}function n(b){var c=a.extend({},b.data()),d=c.json;return delete c.fancytree,delete c.uiFancytree,d&&(delete c.json,c=a.extend(c,d)),c}function o(a){return(""+a).replace(y,function(a){return B[a]})}function p(a){return(""+a).replace(z,function(a){return B[a]})}function q(a){return a=a.toLowerCase(),function(b){return b.title.toLowerCase().indexOf(a)>=0}}function r(a){var b=new RegExp("^"+a,"i");return function(a){return b.test(a.title)}}function s(b,c){var d,f,g,h;for(this.parent=b,this.tree=b.tree,this.ul=null,this.li=null,this.statusNodeType=null,this._isLoading=!1,this._error=null,this.data={},d=0,f=H.length;d<f;d++)g=H[d],this[g]=c[g];null==this.unselectableIgnore&&null==this.unselectableStatus||(this.unselectable=!0),c.hideCheckbox&&a.error("'hideCheckbox' node option was removed in v2.23.0: use 'checkbox: false'"),c.data&&a.extend(this.data,c.data);for(g in c)I[g]||a.isFunction(c[g])||K[g]||(this.data[g]=c[g]);null==this.key?this.tree.options.defaultKey?(this.key=this.tree.options.defaultKey(this),e(this.key,"defaultKey() must return a unique key")):this.key="_"+w._nextNodeKey++:this.key=""+this.key,c.active&&(e(null===this.tree.activeNode,"only one active node allowed"),this.tree.activeNode=this),c.selected&&(this.tree.lastSelectedNode=this),h=c.children,h?h.length?this._setChildren(h):this.children=this.lazy?[]:null:this.children=null,this.tree._callHook("treeRegisterNode",this.tree,!0,this)}function t(b){this.widget=b,this.$div=b.element,this.options=b.options,this.options&&(a.isFunction(this.options.lazyload)&&!a.isFunction(this.options.lazyLoad)&&(this.options.lazyLoad=function(){return w.warn("The 'lazyload' event is deprecated since 2014-02-25. Use 'lazyLoad' (with uppercase L) instead."),b.options.lazyload.apply(this,arguments)}),a.isFunction(this.options.loaderror)&&a.error("The 'loaderror' event was renamed since 2014-07-03. Use 'loadError' (with uppercase E) instead."),this.options.fx!==d&&w.warn("The 'fx' option was replaced by 'toggleEffect' since 2014-11-30."),this.options.removeNode!==d&&a.error("The 'removeNode' event was replaced by 'modifyChild' since 2.20 (2016-09-10).")),this.ext={},this.data=n(this.$div),this._id=a.ui.fancytree._nextId++,this._ns=".fancytree-"+this._id,this.activeNode=null,this.focusNode=null,this._hasFocus=null,this._enableUpdate=!0,this.lastSelectedNode=null,this.systemFocusElement=null,this.lastQuicksearchTerm="",this.lastQuicksearchTime=0,this.statusClassPropName="span",this.ariaPropName="li",this.nodeContainerAttrName="li",this.$div.find(">ul.fancytree-container").remove();var c,e={tree:this};this.rootNode=new s(e,{title:"root",key:"root_"+this._id,children:null,expanded:!0}),this.rootNode.parent=null,c=a("<ul>",{"class":"ui-fancytree fancytree-container fancytree-plain"}).appendTo(this.$div),this.$container=c,this.rootNode.ul=c[0],null==this.options.debugLevel&&(this.options.debugLevel=w.debugLevel)}if(a.ui&&a.ui.fancytree)return void a.ui.fancytree.warn("Fancytree: ignored duplicate include");var u,v,w=null,x=new RegExp(/\.|\//),y=/[&<>"'\/]/g,z=/[<>"'\/]/g,A="$recursive_request",B={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"},C={16:!0,17:!0,18:!0},D={8:"backspace",9:"tab",10:"return",13:"return",19:"pause",20:"capslock",27:"esc",32:"space",33:"pageup",34:"pagedown",35:"end",36:"home",37:"left",38:"up",39:"right",40:"down",45:"insert",46:"del",59:";",61:"=",96:"0",97:"1",98:"2",99:"3",100:"4",101:"5",102:"6",103:"7",104:"8",105:"9",106:"*",107:"+",109:"-",110:".",111:"/",112:"f1",113:"f2",114:"f3",115:"f4",116:"f5",117:"f6",118:"f7",119:"f8",120:"f9",121:"f10",122:"f11",123:"f12",144:"numlock",145:"scroll",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},E={0:"",1:"left",2:"middle",3:"right"},F="active expanded focus folder lazy radiogroup selected unselectable unselectableIgnore".split(" "),G={},H="checkbox expanded extraClasses folder icon key lazy radiogroup refKey selected statusNodeType title tooltip unselectable unselectableIgnore unselectableStatus".split(" "),I={},J={},K={active:!0,children:!0,data:!0,focus:!0};for(u=0;u<F.length;u++)G[F[u]]=!0;for(u=0;u<H.length;u++)v=H[u],I[v]=!0,v!==v.toLowerCase()&&(J[v.toLowerCase()]=v);e(a.ui,"Fancytree requires jQuery UI (http://jqueryui.com)"),s.prototype={_findDirectChild:function(a){var b,c,d=this.children;if(d)if("string"==typeof a){for(b=0,c=d.length;b<c;b++)if(d[b].key===a)return d[b]}else{if("number"==typeof a)return this.children[a];if(a.parent===this)return a}return null},_setChildren:function(a){e(a&&(!this.children||0===this.children.length),"only init supported"),this.children=[];for(var b=0,c=a.length;b<c;b++)this.children.push(new s(this,a[b]))},addChildren:function(b,c){var d,f,g,h=this.getFirstChild(),i=this.getLastChild(),j=null,k=[];for(a.isPlainObject(b)&&(b=[b]),this.children||(this.children=[]),d=0,f=b.length;d<f;d++)k.push(new s(this,b[d]));if(j=k[0],null==c?this.children=this.children.concat(k):(c=this._findDirectChild(c),g=a.inArray(c,this.children),e(g>=0,"insertBefore must be an existing child"),this.children.splice.apply(this.children,[g,0].concat(k))),h&&!c){for(d=0,f=k.length;d<f;d++)k[d].render();h!==this.getFirstChild()&&h.renderStatus(),i!==this.getLastChild()&&i.renderStatus()}else(!this.parent||this.parent.ul||this.tr)&&this.render();return 3===this.tree.options.selectMode&&this.fixSelection3FromEndNodes(),this.triggerModifyChild("add",1===k.length?k[0]:null),j},addClass:function(a){return this.toggleClass(a,!0)},addNode:function(a,b){switch(b!==d&&"over"!==b||(b="child"),b){case"after":return this.getParent().addChildren(a,this.getNextSibling());case"before":return this.getParent().addChildren(a,this);case"firstChild":var c=this.children?this.children[0]:null;return this.addChildren(a,c);case"child":case"over":return this.addChildren(a)}e(!1,"Invalid mode: "+b)},addPagingNode:function(b,c){var d,e;if(c=c||"child",b===!1){for(d=this.children.length-1;d>=0;d--)e=this.children[d],"paging"===e.statusNodeType&&this.removeChild(e);return void(this.partload=!1)}return b=a.extend({title:this.tree.options.strings.moreData,statusNodeType:"paging",icon:!1},b),this.partload=!0,this.addNode(b,c)},appendSibling:function(a){return this.addNode(a,"after")},applyPatch:function(b){if(null===b)return this.remove(),k(this);var c,d,e,f={children:!0,expanded:!0,parent:!0};for(c in b)e=b[c],f[c]||a.isFunction(e)||(I[c]?this[c]=e:this.data[c]=e);return b.hasOwnProperty("children")&&(this.removeChildren(),b.children&&this._setChildren(b.children)),this.isVisible()&&(this.renderTitle(),this.renderStatus()),d=b.hasOwnProperty("expanded")?this.setExpanded(b.expanded):k(this)},collapseSiblings:function(){return this.tree._callHook("nodeCollapseSiblings",this)},copyTo:function(a,b,c){return a.addNode(this.toDict(!0,c),b)},countChildren:function(a){var b,c,d,e=this.children;if(!e)return 0;if(d=e.length,a!==!1)for(b=0,c=d;b<c;b++)d+=e[b].countChildren();return d},debug:function(a){this.tree.options.debugLevel>=2&&(Array.prototype.unshift.call(arguments,this.toString()),f("log",arguments))},discard:function(){return this.warn("FancytreeNode.discard() is deprecated since 2014-02-16. Use .resetLazy() instead."),this.resetLazy()},discardMarkup:function(a){var b=a?"nodeRemoveMarkup":"nodeRemoveChildMarkup";this.tree._callHook(b,this)},findAll:function(b){b=a.isFunction(b)?b:q(b);var c=[];return this.visit(function(a){b(a)&&c.push(a)}),c},findFirst:function(b){b=a.isFunction(b)?b:q(b);var c=null;return this.visit(function(a){if(b(a))return c=a,!1}),c},_changeSelectStatusAttrs:function(a){var b=!1,c=this.tree.options,f=w.evalOption("unselectable",this,this,c,!1),g=w.evalOption("unselectableStatus",this,this,c,d);switch(f&&null!=g&&(a=g),a){case!1:b=this.selected||this.partsel,this.selected=!1,this.partsel=!1;break;case!0:b=!this.selected||!this.partsel,this.selected=!0,this.partsel=!0;break;case d:b=this.selected||!this.partsel,this.selected=!1,this.partsel=!0;break;default:e(!1,"invalid state: "+a)}return b&&this.renderStatus(),b},fixSelection3AfterClick:function(a){var b=this.isSelected();this.visit(function(a){a._changeSelectStatusAttrs(b)}),this.fixSelection3FromEndNodes(a)},fixSelection3FromEndNodes:function(a){function b(a){var e,f,g,h,i,j,k,l,m,n=a.children;if(n&&n.length){for(j=!0,k=!1,e=0,f=n.length;e<f;e++)g=n[e],h=b(g),l=w.evalOption("unselectableIgnore",g,g,c,!1),l||(h!==!1&&(k=!0),h!==!0&&(j=!1));i=!!j||!!k&&d}else m=w.evalOption("unselectableStatus",a,a,c,d),i=null==m?!!a.selected:!!m;return a._changeSelectStatusAttrs(i),i}var c=this.tree.options;e(3===c.selectMode,"expected selectMode 3"),b(this),this.visitParents(function(a){var b,e,f,g,h,i,j=a.children,k=!0,l=!1;for(b=0,e=j.length;b<e;b++)f=j[b],h=w.evalOption("unselectableIgnore",f,f,c,!1),h||(i=w.evalOption("unselectableStatus",f,f,c,d),g=null==i?!!f.selected:!!i,(g||f.partsel)&&(l=!0),g||(k=!1));g=!!k||!!l&&d,a._changeSelectStatusAttrs(g)})},fromDict:function(b){for(var c in b)I[c]?this[c]=b[c]:"data"===c?a.extend(this.data,b.data):a.isFunction(b[c])||K[c]||(this.data[c]=b[c]);b.children&&(this.removeChildren(),this.addChildren(b.children)),this.renderTitle()},getChildren:function(){return this.hasChildren()===d?d:this.children},getFirstChild:function(){return this.children?this.children[0]:null},getIndex:function(){return a.inArray(this,this.parent.children)},getIndexHier:function(b,c){b=b||".";var d,e=[];return a.each(this.getParentList(!1,!0),function(a,b){d=""+(b.getIndex()+1),c&&(d=("0000000"+d).substr(-c)),e.push(d)}),e.join(b)},getKeyPath:function(a){var b=[],c=this.tree.options.keyPathSeparator;return this.visitParents(function(a){a.parent&&b.unshift(a.key)},!a),c+b.join(c)},getLastChild:function(){return this.children?this.children[this.children.length-1]:null},getLevel:function(){for(var a=0,b=this.parent;b;)a++,b=b.parent;return a},getNextSibling:function(){if(this.parent){var a,b,c=this.parent.children;for(a=0,b=c.length-1;a<b;a++)if(c[a]===this)return c[a+1]}return null},getParent:function(){return this.parent},getParentList:function(a,b){for(var c=[],d=b?this:this.parent;d;)(a||d.parent)&&c.unshift(d),d=d.parent;return c},getPrevSibling:function(){if(this.parent){var a,b,c=this.parent.children;for(a=1,b=c.length;a<b;a++)if(c[a]===this)return c[a-1]}return null},getSelectedNodes:function(a){var b=[];return this.visit(function(c){if(c.selected&&(b.push(c),a===!0))return"skip"}),b},hasChildren:function(){return this.lazy?null==this.children?d:0!==this.children.length&&(1!==this.children.length||!this.children[0].isStatusNode()||d):!(!this.children||!this.children.length)},hasFocus:function(){return this.tree.hasFocus()&&this.tree.focusNode===this},info:function(a){this.tree.options.debugLevel>=1&&(Array.prototype.unshift.call(arguments,this.toString()),f("info",arguments))},isActive:function(){return this.tree.activeNode===this},isChildOf:function(a){return this.parent&&this.parent===a},isDescendantOf:function(a){if(!a||a.tree!==this.tree)return!1;for(var b=this.parent;b;){if(b===a)return!0;b=b.parent}return!1},isExpanded:function(){return!!this.expanded},isFirstSibling:function(){var a=this.parent;return!a||a.children[0]===this},isFolder:function(){return!!this.folder},isLastSibling:function(){var a=this.parent;return!a||a.children[a.children.length-1]===this},isLazy:function(){return!!this.lazy},isLoaded:function(){return!this.lazy||this.hasChildren()!==d},isLoading:function(){return!!this._isLoading},isRoot:function(){return this.isRootNode()},isPartsel:function(){return!this.selected&&!!this.partsel},isPartload:function(){return!!this.partload},isRootNode:function(){return this.tree.rootNode===this},isSelected:function(){return!!this.selected},isStatusNode:function(){return!!this.statusNodeType},isPagingNode:function(){return"paging"===this.statusNodeType},isTopLevel:function(){return this.tree.rootNode===this.parent},isUndefined:function(){return this.hasChildren()===d},isVisible:function(){var a,b,c=this.getParentList(!1,!1);for(a=0,b=c.length;a<b;a++)if(!c[a].expanded)return!1;return!0},lazyLoad:function(a){return this.warn("FancytreeNode.lazyLoad() is deprecated since 2014-02-16. Use .load() instead."),this.load(a)},load:function(a){var b,c,d=this,f=this.isExpanded();return e(this.isLazy(),"load() requires a lazy node"),a||this.isUndefined()?(this.isLoaded()&&this.resetLazy(),c=this.tree._triggerNodeEvent("lazyLoad",this),c===!1?k(this):(e("boolean"!=typeof c,"lazyLoad event must return source in data.result"),b=this.tree._callHook("nodeLoadChildren",this,c),f?(this.expanded=!0,b.always(function(){d.render()})):b.always(function(){d.renderStatus()}),b)):k(this)},makeVisible:function(b){var c,d=this,e=[],f=new a.Deferred,g=this.getParentList(!1,!1),h=g.length,i=!(b&&b.noAnimation===!0),j=!(b&&b.scrollIntoView===!1);for(c=h-1;c>=0;c--)e.push(g[c].setExpanded(!0,b));return a.when.apply(a,e).done(function(){j?d.scrollIntoView(i).done(function(){f.resolve()}):f.resolve()}),f.promise()},moveTo:function(b,c,f){c===d||"over"===c?c="child":"firstChild"===c&&(b.children&&b.children.length?(c="before",b=b.children[0]):c="child");var g,h=this.parent,i="child"===c?b:b.parent;if(this!==b){if(this.parent?i.isDescendantOf(this)&&a.error("Cannot move a node to its own descendant"):a.error("Cannot move system root"),i!==h&&h.triggerModifyChild("remove",this),1===this.parent.children.length){if(this.parent===i)return;this.parent.children=this.parent.lazy?[]:null,this.parent.expanded=!1}else g=a.inArray(this,this.parent.children),e(g>=0,"invalid source parent"),this.parent.children.splice(g,1);if(this.parent=i,i.hasChildren())switch(c){case"child":i.children.push(this);break;case"before":g=a.inArray(b,i.children),e(g>=0,"invalid target parent"),i.children.splice(g,0,this);break;case"after":g=a.inArray(b,i.children),e(g>=0,"invalid target parent"),i.children.splice(g+1,0,this);break;default:a.error("Invalid mode "+c)}else i.children=[this];f&&b.visit(f,!0),i===h?i.triggerModifyChild("move",this):i.triggerModifyChild("add",this),this.tree!==b.tree&&(this.warn("Cross-tree moveTo is experimantal!"),this.visit(function(a){a.tree=b.tree},!0)),h.isDescendantOf(i)||h.render(),i.isDescendantOf(h)||i===h||i.render()}},navigate:function(b,c){function d(d){if(d){try{d.makeVisible({scrollIntoView:!1})}catch(e){}return a(d.span).is(":visible")?c===!1?d.setFocus():d.setActive():(d.debug("Navigate: skipping hidden node"),void d.navigate(b,c))}}var e,f,g,h=!0,i=a.ui.keyCode,j=null;switch(b){case i.BACKSPACE:this.parent&&this.parent.parent&&(g=d(this.parent));break;case i.HOME:this.tree.visit(function(b){if(a(b.span).is(":visible"))return g=d(b),!1});break;case i.END:this.tree.visit(function(b){a(b.span).is(":visible")&&(g=b)}),g&&(g=d(g));break;case i.LEFT:this.expanded?(this.setExpanded(!1),g=d(this)):this.parent&&this.parent.parent&&(g=d(this.parent));break;case i.RIGHT:this.expanded||!this.children&&!this.lazy?this.children&&this.children.length&&(g=d(this.children[0])):(this.setExpanded(),g=d(this));break;case i.UP:for(j=this.getPrevSibling();j&&!a(j.span).is(":visible");)j=j.getPrevSibling();for(;j&&j.expanded&&j.children&&j.children.length;)j=j.children[j.children.length-1];!j&&this.parent&&this.parent.parent&&(j=this.parent),g=d(j);break;case i.DOWN:if(this.expanded&&this.children&&this.children.length)j=this.children[0];else for(f=this.getParentList(!1,!0),e=f.length-1;e>=0;e--){for(j=f[e].getNextSibling();j&&!a(j.span).is(":visible");)j=j.getNextSibling();if(j)break}g=d(j);break;default:h=!1}return g||k()},remove:function(){return this.parent.removeChild(this)},removeChild:function(a){return this.tree._callHook("nodeRemoveChild",this,a)},removeChildren:function(){return this.tree._callHook("nodeRemoveChildren",this)},removeClass:function(a){return this.toggleClass(a,!1)},render:function(a,b){return this.tree._callHook("nodeRender",this,a,b)},renderTitle:function(){return this.tree._callHook("nodeRenderTitle",this)},renderStatus:function(){return this.tree._callHook("nodeRenderStatus",this)},replaceWith:function(b){var c,d=this.parent,f=a.inArray(this,d.children),g=this;return e(this.isPagingNode(),"replaceWith() currently requires a paging status node"),c=this.tree._callHook("nodeLoadChildren",this,b),c.done(function(a){var b=g.children;for(u=0;u<b.length;u++)b[u].parent=d;d.children.splice.apply(d.children,[f+1,0].concat(b)),g.children=null,g.remove(),d.render()}).fail(function(){g.setExpanded()}),c},resetLazy:function(){this.removeChildren(),this.expanded=!1,this.lazy=!0,this.children=d,this.renderStatus()},scheduleAction:function(b,c){this.tree.timer&&clearTimeout(this.tree.timer),this.tree.timer=null;var d=this;switch(b){case"cancel":break;case"expand":this.tree.timer=setTimeout(function(){d.tree.debug("setTimeout: trigger expand"),d.setExpanded(!0)},c);break;case"activate":this.tree.timer=setTimeout(function(){d.tree.debug("setTimeout: trigger activate"),d.setActive(!0)},c);break;default:a.error("Invalid mode "+b)}},scrollIntoView:function(f,h){h!==d&&g(h)&&(this.warn("scrollIntoView() with 'topNode' option is deprecated since 2014-05-08. Use 'options.topNode' instead."),h={topNode:h});var i,j,l,m,n=a.extend({effects:f===!0?{duration:200,queue:!1}:f,scrollOfs:this.tree.options.scrollOfs,scrollParent:this.tree.options.scrollParent||this.tree.$container,topNode:null},h),o=new a.Deferred,p=this,q=a(this.span).height(),r=a(n.scrollParent),s=n.scrollOfs.top||0,t=n.scrollOfs.bottom||0,u=r.height(),v=r.scrollTop(),w=r,x=r[0]===b,y=n.topNode||null,z=null;return a(this.span).is(":visible")?(x?(j=a(this.span).offset().top,i=y&&y.span?a(y.span).offset().top:0,w=a("html,body")):(e(r[0]!==c&&r[0]!==c.body,"scrollParent should be a simple element or `window`, not document or body."),m=r.offset().top,j=a(this.span).offset().top-m+v,i=y?a(y.span).offset().top-m+v:0,l=Math.max(0,r.innerHeight()-r[0].clientHeight),u-=l),j<v+s?z=j-s:j+q>v+u-t&&(z=j+q-u+t,y&&(e(y.isRootNode()||a(y.span).is(":visible"),"topNode must be visible"),i<z&&(z=i-s))),null!==z?n.effects?(n.effects.complete=function(){o.resolveWith(p)},w.stop(!0).animate({scrollTop:z},n.effects)):(w[0].scrollTop=z,o.resolveWith(this)):o.resolveWith(this),o.promise()):(this.warn("scrollIntoView(): node is invisible."),k())},setActive:function(a,b){return this.tree._callHook("nodeSetActive",this,a,b)},setExpanded:function(a,b){return this.tree._callHook("nodeSetExpanded",this,a,b)},setFocus:function(a){return this.tree._callHook("nodeSetFocus",this,a)},setSelected:function(a,b){return this.tree._callHook("nodeSetSelected",this,a,b)},setStatus:function(a,b,c){return this.tree._callHook("nodeSetStatus",this,a,b,c)},setTitle:function(a){this.title=a,this.renderTitle(),this.triggerModify("rename")},sortChildren:function(a,b){var c,d,e=this.children;if(e){if(a=a||function(a,b){var c=a.title.toLowerCase(),d=b.title.toLowerCase();return c===d?0:c>d?1:-1},e.sort(a),b)for(c=0,d=e.length;c<d;c++)e[c].children&&e[c].sortChildren(a,"$norender$");"$norender$"!==b&&this.render(),this.triggerModifyChild("sort")}},toDict:function(b,c){var d,e,f,g={},h=this;if(a.each(H,function(a,b){(h[b]||h[b]===!1)&&(g[b]=h[b])}),a.isEmptyObject(this.data)||(g.data=a.extend({},this.data),a.isEmptyObject(g.data)&&delete g.data),c&&c(g,h),b&&this.hasChildren())for(g.children=[],d=0,e=this.children.length;d<e;d++)f=this.children[d],f.isStatusNode()||g.children.push(f.toDict(!0,c));return g},toggleClass:function(b,c){var e,f,g=/\S+/g,h=b.match(g)||[],i=0,j=!1,k=this[this.tree.statusClassPropName],l=" "+(this.extraClasses||"")+" ";for(k&&a(k).toggleClass(b,c);e=h[i++];)if(f=l.indexOf(" "+e+" ")>=0,c=c===d?!f:!!c)f||(l+=e+" ",j=!0);else for(;l.indexOf(" "+e+" ")>-1;)l=l.replace(" "+e+" "," ");return this.extraClasses=a.trim(l),j},toggleExpanded:function(){return this.tree._callHook("nodeToggleExpanded",this)},toggleSelected:function(){return this.tree._callHook("nodeToggleSelected",this)},toString:function(){return"<FancytreeNode(#"+this.key+", '"+this.title+"')>"},triggerModifyChild:function(b,c,d){var e,f=this.tree.options.modifyChild;f&&(c&&c.parent!==this&&a.error("childNode "+c+" is not a child of "+this),e={node:this,tree:this.tree,operation:b,childNode:c||null},d&&a.extend(e,d),f({type:"modifyChild"},e))},triggerModify:function(a,b){this.parent.triggerModifyChild(a,this,b)},visit:function(a,b){var c,d,e=!0,f=this.children;if(b===!0&&(e=a(this),e===!1||"skip"===e))return e;if(f)for(c=0,d=f.length;c<d&&(e=f[c].visit(a,!0),e!==!1);c++);return e},visitAndLoad:function(b,c,d){var e,f,g,h=this;return b&&c===!0&&(f=b(h),f===!1||"skip"===f)?d?f:k():h.children||h.lazy?(e=new a.Deferred,g=[],h.load().done(function(){for(var c=0,d=h.children.length;c<d;c++){if(f=h.children[c].visitAndLoad(b,!0,!0),f===!1){e.reject();break}"skip"!==f&&g.push(f)}a.when.apply(this,g).then(function(){e.resolve()})}),e.promise()):k()},visitParents:function(a,b){if(b&&a(this)===!1)return!1;for(var c=this.parent;c;){if(a(c)===!1)return!1;c=c.parent}return!0},visitSiblings:function(a,b){var c,d,e,f=this.parent.children;for(c=0,d=f.length;c<d;c++)if(e=f[c],(b||e!==this)&&a(e)===!1)return!1;return!0},warn:function(a){Array.prototype.unshift.call(arguments,this.toString()),f("warn",arguments)}},t.prototype={_makeHookContext:function(b,c,e){var f,g;return b.node!==d?(c&&b.originalEvent!==c&&a.error("invalid args"),f=b):b.tree?(g=b.tree,f={node:b,tree:g,widget:g.widget,options:g.widget.options,originalEvent:c}):b.widget?f={node:null,tree:b,widget:b.widget,options:b.widget.options,originalEvent:c}:a.error("invalid args"),e&&a.extend(f,e),f},_callHook:function(b,c,d){var e=this._makeHookContext(c),f=this[b],g=Array.prototype.slice.call(arguments,2);return a.isFunction(f)||a.error("_callHook('"+b+"') is not a function"),g.unshift(e),f.apply(this,g)},_requireExtension:function(b,c,d,f){d=!!d;var g=this._local.name,h=this.options.extensions,i=a.inArray(b,h)<a.inArray(g,h),j=c&&null==this.ext[b],k=!j&&null!=d&&d!==i;return e(g&&g!==b,"invalid or same name"),!j&&!k||(f||(j||c?(f="'"+g+"' extension requires '"+b+"'",k&&(f+=" to be registered "+(d?"before":"after")+" itself")):f="If used together, `"+b+"` must be registered "+(d?"before":"after")+" `"+g+"`"),a.error(f),!1)},activateKey:function(a){var b=this.getNodeByKey(a);return b?b.setActive():this.activeNode&&this.activeNode.setActive(!1),b},addPagingNode:function(a,b){return this.rootNode.addPagingNode(a,b)},applyPatch:function(b){var c,d,f,g,h,i,j=b.length,k=[];for(d=0;d<j;d++)f=b[d],e(2===f.length,"patchList must be an array of length-2-arrays"),g=f[0],h=f[1],i=null===g?this.rootNode:this.getNodeByKey(g),i?(c=new a.Deferred,k.push(c),i.applyPatch(h).always(m(c,i))):this.warn("could not find node with key '"+g+"'");return a.when.apply(a,k).promise()},clear:function(a){this._callHook("treeClear",this)},count:function(){return this.rootNode.countChildren()},debug:function(a){this.options.debugLevel>=2&&(Array.prototype.unshift.call(arguments,this.toString()),f("log",arguments))},enableUpdate:function(a){return a=a!==!1,!!this._enableUpdate==!!a?a:(this._enableUpdate=a,a?(this.debug("enableUpdate(true): redraw "),this.render()):this.debug("enableUpdate(false)..."),!a)},findAll:function(a){return this.rootNode.findAll(a)},findFirst:function(a){return this.rootNode.findFirst(a)},findNextNode:function(b,c,d){var e=null,f=c.parent.children,g=null,h=function(a,b,c){var d,e,f=a.children,g=f.length,i=f[b];if(i&&c(i)===!1)return!1;if(i&&i.children&&i.expanded&&h(i,0,c)===!1)return!1;for(d=b+1;d<g;d++)if(h(a,d,c)===!1)return!1;return e=a.parent,e?h(e,e.children.indexOf(a)+1,c):h(a,0,c)};return b="string"==typeof b?r(b):b,c=c||this.getFirstChild(),h(c.parent,f.indexOf(c),function(d){return d!==e&&(e=e||d,a(d.span).is(":visible")?(!b(d)||(g=d,g===c))&&void 0:void d.debug("quicksearch: skipping hidden node"))}),g},generateFormElements:function(b,c,d){function e(b){j.append(a("<input>",{type:"checkbox",name:g,value:b.key,checked:!0}))}d=d||{};var f,g="string"==typeof b?b:"ft_"+this._id+"[]",h="string"==typeof c?c:"ft_"+this._id+"_active",i="fancytree_result_"+this._id,j=a("#"+i),k=3===this.options.selectMode&&d.stopOnParents!==!1;j.length?j.empty():j=a("<div>",{id:i}).hide().insertAfter(this.$container),c!==!1&&this.activeNode&&j.append(a("<input>",{type:"radio",name:h,value:this.activeNode.key,checked:!0})),d.filter?this.visit(function(a){var b=d.filter(a);return"skip"===b?b:void(b!==!1&&e(a))}):b!==!1&&(f=this.getSelectedNodes(k),a.each(f,function(a,b){e(b)}))},getActiveNode:function(){return this.activeNode},getFirstChild:function(){return this.rootNode.getFirstChild()},getFocusNode:function(){return this.focusNode},getNodeByKey:function(a,b){var d,e;return!b&&(d=c.getElementById(this.options.idPrefix+a))?d.ftnode?d.ftnode:null:(b=b||this.rootNode,e=null,b.visit(function(b){if(b.key===a)return e=b,!1},!0),e)},getRootNode:function(){return this.rootNode},getSelectedNodes:function(a){return this.rootNode.getSelectedNodes(a)},hasFocus:function(){return!!this._hasFocus},info:function(a){this.options.debugLevel>=1&&(Array.prototype.unshift.call(arguments,this.toString()),f("info",arguments))},loadKeyPath:function(b,c,e){function f(a,b,d){c.call(r,b,"loading"),b.load().done(function(){r.loadKeyPath.call(r,l[a],c,b).always(m(d,r))}).fail(function(e){r.warn("loadKeyPath: error loading: "+a+" (parent: "+o+")"),c.call(r,b,"error"),d.reject()})}var g,h,i,j,k,l,n,o,p,q=this.options.keyPathSeparator,r=this;for(c=c||a.noop,a.isArray(b)||(b=[b]),l={},i=0;i<b.length;i++)for(o=e||this.rootNode,j=b[i],j.charAt(0)===q&&(j=j.substr(1)),p=j.split(q);p.length;){if(k=p.shift(),n=o._findDirectChild(k),!n){this.warn("loadKeyPath: key not found: "+k+" (parent: "+o+")"),c.call(this,k,"error");break}if(0===p.length){c.call(this,n,"ok");break}if(n.lazy&&n.hasChildren()===d){c.call(this,n,"loaded"),l[k]?l[k].push(p.join(q)):l[k]=[p.join(q)];break}c.call(this,n,"loaded"),o=n}g=[];for(k in l)n=o._findDirectChild(k),null==n&&(n=r.getNodeByKey(k)),h=new a.Deferred,g.push(h),f(k,n,h);return a.when.apply(a,g).promise()},reactivate:function(a){var b,c=this.activeNode;return c?(this.activeNode=null,b=c.setActive(!0,{noFocus:!0}),a&&c.setFocus(),b):k()},reload:function(a){return this._callHook("treeClear",this),this._callHook("treeLoad",this,a)},render:function(a,b){return this.rootNode.render(a,b)},setFocus:function(a){return this._callHook("treeSetFocus",this,a)},toDict:function(a,b){var c=this.rootNode.toDict(!0,b);return a?c:c.children},toString:function(){return"<Fancytree(#"+this._id+")>"},_triggerNodeEvent:function(a,b,c,e){var f=this._makeHookContext(b,c,e),g=this.widget._trigger(a,c,f);return g!==!1&&f.result!==d?f.result:g},_triggerTreeEvent:function(a,b,c){var e=this._makeHookContext(this,b,c),f=this.widget._trigger(a,b,e);return f!==!1&&e.result!==d?e.result:f},visit:function(a){return this.rootNode.visit(a,!1)},warn:function(a){Array.prototype.unshift.call(arguments,this.toString()),f("warn",arguments)}},a.extend(t.prototype,{nodeClick:function(a){var b,c,d=a.targetType,e=a.node;if("expander"===d){if(e.isLoading())return void e.debug("Got 2nd click while loading: ignored");this._callHook("nodeToggleExpanded",a)}else if("checkbox"===d)this._callHook("nodeToggleSelected",a),a.options.focusOnSelect&&this._callHook("nodeSetFocus",a,!0);else{if(c=!1,b=!0,e.folder)switch(a.options.clickFolderMode){case 2:c=!0,b=!1;break;case 3:b=!0,c=!0}b&&(this.nodeSetFocus(a),this._callHook("nodeSetActive",a,!0)),c&&this._callHook("nodeToggleExpanded",a)}},nodeCollapseSiblings:function(a,b){var c,d,e,f=a.node;if(f.parent)for(c=f.parent.children,d=0,e=c.length;d<e;d++)c[d]!==f&&c[d].expanded&&this._callHook("nodeSetExpanded",c[d],!1,b)},nodeDblclick:function(a){"title"===a.targetType&&4===a.options.clickFolderMode&&this._callHook("nodeToggleExpanded",a),"title"===a.targetType&&a.originalEvent.preventDefault()},nodeKeydown:function(b){var c,d,e,f,g=b.originalEvent,h=b.node,i=b.tree,j=b.options,k=g.which,l=String.fromCharCode(k),m=!(g.altKey||g.ctrlKey||g.metaKey||g.shiftKey),n=a(g.target),o=!0,p=!(g.ctrlKey||!j.autoActivate);if(h||(f=this.getActiveNode()||this.getFirstChild(),f&&(f.setFocus(),h=b.node=this.focusNode,h.debug("Keydown force focus on active node"))),j.quicksearch&&m&&/\w/.test(l)&&!D[k]&&!n.is(":input:enabled"))return d=(new Date).getTime(),d-i.lastQuicksearchTime>500&&(i.lastQuicksearchTerm=""),i.lastQuicksearchTime=d,i.lastQuicksearchTerm+=l,c=i.findNextNode(i.lastQuicksearchTerm,i.getActiveNode()),c&&c.setActive(),void g.preventDefault();switch(w.eventToString(g)){case"+":case"=":i.nodeSetExpanded(b,!0);break;case"-":i.nodeSetExpanded(b,!1);break;case"space":h.isPagingNode()?i._triggerNodeEvent("clickPaging",b,g):j.checkbox?i.nodeToggleSelected(b):i.nodeSetActive(b,!0);break;case"return":i.nodeSetActive(b,!0);break;case"home":case"end":case"backspace":case"left":case"right":case"up":case"down":e=h.navigate(g.which,p,!0);break;default:o=!1}o&&g.preventDefault()},nodeLoadChildren:function(b,c){var d,f,g,h=b.tree,i=b.node,j=(new Date).getTime();return a.isFunction(c)&&(c=c.call(h,{type:"source"},b),e(!a.isFunction(c),"source callback must not return another function")),c.url&&(i._requestId&&i.warn("Recursive load request #"+j+" while #"+i._requestId+" is pending."),d=a.extend({},b.options.ajax,c),i._requestId=j,d.debugDelay?(f=d.debugDelay,a.isArray(f)&&(f=f[0]+Math.random()*(f[1]-f[0])),i.warn("nodeLoadChildren waiting debugDelay "+Math.round(f)+" ms ..."),d.debugDelay=!1,g=a.Deferred(function(b){setTimeout(function(){a.ajax(d).done(function(){b.resolveWith(this,arguments)}).fail(function(){b.rejectWith(this,arguments)})},f)})):g=a.ajax(d),c=new a.Deferred,g.done(function(d,e,f){var g,k;if("json"!==this.dataType&&"jsonp"!==this.dataType||"string"!=typeof d||a.error("Ajax request returned a string (did you get the JSON dataType wrong?)."),i._requestId&&i._requestId>j)return void c.rejectWith(this,[A]);if(b.options.postProcess){try{k=h._triggerNodeEvent("postProcess",b,b.originalEvent,{response:d,error:null,dataType:this.dataType})}catch(l){k={error:l,message:""+l,details:"postProcess failed"}}if(k.error)return g=a.isPlainObject(k.error)?k.error:{message:k.error},g=h._makeHookContext(i,null,g),void c.rejectWith(this,[g]);d=a.isArray(k)?k:d}else d&&d.hasOwnProperty("d")&&b.options.enableAspx&&(d="string"==typeof d.d?a.parseJSON(d.d):d.d);c.resolveWith(this,[d])}).fail(function(a,b,d){var e=h._makeHookContext(i,null,{error:a,args:Array.prototype.slice.call(arguments),message:d,details:a.status+": "+d});c.rejectWith(this,[e])})),a.isFunction(c.then)&&a.isFunction(c["catch"])&&(g=c,
c=new a.Deferred,g.then(function(a){c.resolve(a)},function(a){c.reject(a)})),a.isFunction(c.promise)&&(h.nodeSetStatus(b,"loading"),c.done(function(a){h.nodeSetStatus(b,"ok"),i._requestId=null}).fail(function(a){var c;return a===A?void i.warn("Ignored response for obsolete load request #"+j+" (expected #"+i._requestId+")"):(a.node&&a.error&&a.message?c=a:(c=h._makeHookContext(i,null,{error:a,args:Array.prototype.slice.call(arguments),message:a?a.message||a.toString():""}),"[object Object]"===c.message&&(c.message="")),i.warn("Load children failed ("+c.message+")",c),void(h._triggerNodeEvent("loadError",c,null)!==!1&&h.nodeSetStatus(b,"error",c.message,c.details)))})),a.when(c).done(function(b){var c;a.isPlainObject(b)&&(e(i.isRootNode(),"source may only be an object for root nodes (expecting an array of child objects otherwise)"),e(a.isArray(b.children),"if an object is passed as source, it must contain a 'children' array (all other properties are added to 'tree.data')"),c=b,b=b.children,delete c.children,a.extend(h.data,c)),e(a.isArray(b),"expected array of children"),i._setChildren(b),h._triggerNodeEvent("loadChildren",i)})},nodeLoadKeyPath:function(a,b){},nodeRemoveChild:function(b,c){var d,f=b.node,g=a.extend({},b,{node:c}),h=f.children;return 1===h.length?(e(c===h[0],"invalid single child"),this.nodeRemoveChildren(b)):(this.activeNode&&(c===this.activeNode||this.activeNode.isDescendantOf(c))&&this.activeNode.setActive(!1),this.focusNode&&(c===this.focusNode||this.focusNode.isDescendantOf(c))&&(this.focusNode=null),this.nodeRemoveMarkup(g),this.nodeRemoveChildren(g),d=a.inArray(c,h),e(d>=0,"invalid child"),f.triggerModifyChild("remove",c),c.visit(function(a){a.parent=null},!0),this._callHook("treeRegisterNode",this,!1,c),void h.splice(d,1))},nodeRemoveChildMarkup:function(b){var c=b.node;c.ul&&(c.isRootNode()?a(c.ul).empty():(a(c.ul).remove(),c.ul=null),c.visit(function(a){a.li=a.ul=null}))},nodeRemoveChildren:function(b){var c,d=b.tree,e=b.node,f=e.children;f&&(this.activeNode&&this.activeNode.isDescendantOf(e)&&this.activeNode.setActive(!1),this.focusNode&&this.focusNode.isDescendantOf(e)&&(this.focusNode=null),this.nodeRemoveChildMarkup(b),c=a.extend({},b),e.triggerModifyChild("remove",null),e.visit(function(a){a.parent=null,d._callHook("treeRegisterNode",d,!1,a)}),e.lazy?e.children=[]:e.children=null,e.isRootNode()||(e.expanded=!1),this.nodeRenderStatus(b))},nodeRemoveMarkup:function(b){var c=b.node;c.li&&(a(c.li).remove(),c.li=null),this.nodeRemoveChildMarkup(b)},nodeRender:function(b,d,f,g,h){var i,j,k,l,m,n,o,p=b.node,q=b.tree,r=b.options,s=r.aria,t=!1,u=p.parent,v=!u,w=p.children,x=null;if(q._enableUpdate!==!1&&(v||u.ul)){if(e(v||u.ul,"parent UL must exist"),v||(p.li&&(d||p.li.parentNode!==p.parent.ul)&&(p.li.parentNode===p.parent.ul?x=p.li.nextSibling:this.debug("Unlinking "+p+" (must be child of "+p.parent+")"),this.nodeRemoveMarkup(b)),p.li?this.nodeRenderStatus(b):(t=!0,p.li=c.createElement("li"),p.li.ftnode=p,p.key&&r.generateIds&&(p.li.id=r.idPrefix+p.key),p.span=c.createElement("span"),p.span.className="fancytree-node",s&&!p.tr&&a(p.li).attr("role","treeitem"),p.li.appendChild(p.span),this.nodeRenderTitle(b),r.createNode&&r.createNode.call(q,{type:"createNode"},b)),r.renderNode&&r.renderNode.call(q,{type:"renderNode"},b)),w){if(v||p.expanded||f===!0){for(p.ul||(p.ul=c.createElement("ul"),(g!==!0||h)&&p.expanded||(p.ul.style.display="none"),s&&a(p.ul).attr("role","group"),p.li?p.li.appendChild(p.ul):p.tree.$div.append(p.ul)),l=0,m=w.length;l<m;l++)o=a.extend({},b,{node:w[l]}),this.nodeRender(o,d,f,!1,!0);for(i=p.ul.firstChild;i;)k=i.ftnode,k&&k.parent!==p?(p.debug("_fixParent: remove missing "+k,i),n=i.nextSibling,i.parentNode.removeChild(i),i=n):i=i.nextSibling;for(i=p.ul.firstChild,l=0,m=w.length-1;l<m;l++)j=w[l],k=i.ftnode,j!==k?p.ul.insertBefore(j.li,k.li):i=i.nextSibling}}else p.ul&&(this.warn("remove child markup for "+p),this.nodeRemoveChildMarkup(b));v||t&&u.ul.insertBefore(p.li,x)}},nodeRenderTitle:function(b,c){var e,f,g,h,i,j,k,l=b.node,m=b.tree,n=b.options,q=n.aria,r=l.getLevel(),s=[];c!==d&&(l.title=c),l.span&&m._enableUpdate!==!1&&(i=q&&l.hasChildren()!==!1?" role='button'":"",r<n.minExpandLevel?(l.lazy||(l.expanded=!0),r>1&&s.push("<span "+i+" class='fancytree-expander fancytree-expander-fixed'></span>")):s.push("<span "+i+" class='fancytree-expander'></span>"),e=w.evalOption("checkbox",l,l,n,!1),e&&!l.isStatusNode()&&(i=q?" role='checkbox'":"",f="fancytree-checkbox",("radio"===e||l.parent&&l.parent.radiogroup)&&(f+=" fancytree-radio"),s.push("<span "+i+" class='"+f+"'></span>")),l.data.iconClass!==d&&(l.icon?a.error("'iconClass' node option is deprecated since v2.14.0: use 'icon' only instead"):(l.warn("'iconClass' node option is deprecated since v2.14.0: use 'icon' instead"),l.icon=l.data.iconClass)),g=w.evalOption("icon",l,l,n,!0),"boolean"!=typeof g&&(g=""+g),g!==!1&&(i=q?" role='presentation'":"","string"==typeof g?x.test(g)?(g="/"===g.charAt(0)?g:(n.imagePath||"")+g,s.push("<img src='"+g+"' class='fancytree-icon' alt='' />")):s.push("<span "+i+" class='fancytree-custom-icon "+g+"'></span>"):s.push("<span "+i+" class='fancytree-icon'></span>")),h="",n.renderTitle&&(h=n.renderTitle.call(m,{type:"renderTitle"},b)||""),h||(k=w.evalOption("tooltip",l,l,n,null),k===!0&&(k=l.title),k=k?" title='"+p(k)+"'":"",j=n.titlesTabbable?" tabindex='0'":"",h="<span class='fancytree-title'"+k+j+">"+(n.escapeTitles?o(l.title):l.title)+"</span>"),s.push(h),l.span.innerHTML=s.join(""),this.nodeRenderStatus(b),n.enhanceTitle&&(b.$title=a(">span.fancytree-title",l.span),h=n.enhanceTitle.call(m,{type:"enhanceTitle"},b)||""))},nodeRenderStatus:function(b){var c,d=b.node,e=b.tree,f=b.options,g=d.hasChildren(),h=d.isLastSibling(),i=f.aria,j=f._classNames,k=[],l=d[e.statusClassPropName];l&&e._enableUpdate!==!1&&(i&&(c=a(d.tr||d.li)),k.push(j.node),e.activeNode===d&&k.push(j.active),e.focusNode===d&&k.push(j.focused),d.expanded&&k.push(j.expanded),i&&(g!==!1?c.attr("aria-expanded",Boolean(d.expanded)):c.removeAttr("aria-expanded")),d.folder&&k.push(j.folder),g!==!1&&k.push(j.hasChildren),h&&k.push(j.lastsib),d.lazy&&null==d.children&&k.push(j.lazy),d.partload&&k.push(j.partload),d.partsel&&k.push(j.partsel),w.evalOption("unselectable",d,d,f,!1)&&k.push(j.unselectable),d._isLoading&&k.push(j.loading),d._error&&k.push(j.error),d.statusNodeType&&k.push(j.statusNodePrefix+d.statusNodeType),d.selected?(k.push(j.selected),i&&c.attr("aria-selected",!0)):i&&c.attr("aria-selected",!1),d.extraClasses&&k.push(d.extraClasses),g===!1?k.push(j.combinedExpanderPrefix+"n"+(h?"l":"")):k.push(j.combinedExpanderPrefix+(d.expanded?"e":"c")+(d.lazy&&null==d.children?"d":"")+(h?"l":"")),k.push(j.combinedIconPrefix+(d.expanded?"e":"c")+(d.folder?"f":"")),l.className=k.join(" "),d.li&&a(d.li).toggleClass(j.lastsib,h))},nodeSetActive:function(b,c,d){d=d||{};var f,g=b.node,h=b.tree,i=b.options,j=d.noEvents===!0,m=d.noFocus===!0,n=g===h.activeNode;return c=c!==!1,n===c?k(g):c&&!j&&this._triggerNodeEvent("beforeActivate",g,b.originalEvent)===!1?l(g,["rejected"]):(c?(h.activeNode&&(e(h.activeNode!==g,"node was active (inconsistency)"),f=a.extend({},b,{node:h.activeNode}),h.nodeSetActive(f,!1),e(null===h.activeNode,"deactivate was out of sync?")),i.activeVisible&&g.makeVisible({scrollIntoView:m&&null==h.focusNode}),h.activeNode=g,h.nodeRenderStatus(b),m||h.nodeSetFocus(b),j||h._triggerNodeEvent("activate",g,b.originalEvent)):(e(h.activeNode===g,"node was not active (inconsistency)"),h.activeNode=null,this.nodeRenderStatus(b),j||b.tree._triggerNodeEvent("deactivate",g,b.originalEvent)),k(g))},nodeSetExpanded:function(b,c,e){e=e||{};var f,g,h,i,j,m,n=b.node,o=b.tree,p=b.options,q=e.noAnimation===!0,r=e.noEvents===!0;if(c=c!==!1,n.expanded&&c||!n.expanded&&!c)return k(n);if(c&&!n.lazy&&!n.hasChildren())return k(n);if(!c&&n.getLevel()<p.minExpandLevel)return l(n,["locked"]);if(!r&&this._triggerNodeEvent("beforeExpand",n,b.originalEvent)===!1)return l(n,["rejected"]);if(q||n.isVisible()||(q=e.noAnimation=!0),g=new a.Deferred,c&&!n.expanded&&p.autoCollapse){j=n.getParentList(!1,!0),m=p.autoCollapse;try{for(p.autoCollapse=!1,h=0,i=j.length;h<i;h++)this._callHook("nodeCollapseSiblings",j[h],e)}finally{p.autoCollapse=m}}return g.done(function(){var a=n.getLastChild();c&&p.autoScroll&&!q&&a?a.scrollIntoView(!0,{topNode:n}).always(function(){r||b.tree._triggerNodeEvent(c?"expand":"collapse",b)}):r||b.tree._triggerNodeEvent(c?"expand":"collapse",b)}),f=function(d){var e,f,g=p._classNames,h=p.toggleEffect;if(n.expanded=c,o._callHook("nodeRender",b,!1,!1,!0),n.ul)if(e="none"!==n.ul.style.display,f=!!n.expanded,e===f)n.warn("nodeSetExpanded: UL.style.display already set");else{if(h&&!q)return a(n.li).addClass(g.animating),void a(n.ul).addClass(g.animating).toggle(h.effect,h.options,h.duration,function(){a(this).removeClass(g.animating),a(n.li).removeClass(g.animating),d()});n.ul.style.display=n.expanded||!parent?"":"none"}d()},c&&n.lazy&&n.hasChildren()===d?n.load().done(function(){g.notifyWith&&g.notifyWith(n,["loaded"]),f(function(){g.resolveWith(n)})}).fail(function(a){f(function(){g.rejectWith(n,["load failed ("+a+")"])})}):f(function(){g.resolveWith(n)}),g.promise()},nodeSetFocus:function(b,d){var e,f=b.tree,g=b.node,h=f.options,i=!!b.originalEvent&&a(b.originalEvent.target).is(":input");if(d=d!==!1,f.focusNode){if(f.focusNode===g&&d)return;e=a.extend({},b,{node:f.focusNode}),f.focusNode=null,this._triggerNodeEvent("blur",e),this._callHook("nodeRenderStatus",e)}d&&(this.hasFocus()||(g.debug("nodeSetFocus: forcing container focus"),this._callHook("treeSetFocus",b,!0,{calledByNode:!0})),g.makeVisible({scrollIntoView:!1}),f.focusNode=g,h.titlesTabbable?i||a(g.span).find(".fancytree-title").focus():0===a(c.activeElement).closest(".fancytree-container").length&&a(f.$container).focus(),h.aria&&a(f.$container).attr("aria-activedescendant",a(g.tr||g.li).uniqueId().attr("id")),this._triggerNodeEvent("focus",b),h.autoScroll&&g.scrollIntoView(),this._callHook("nodeRenderStatus",b))},nodeSetSelected:function(a,b,c){c=c||{};var d=a.node,e=a.tree,f=a.options,g=c.noEvents===!0;if(b=b!==!1,!w.evalOption("unselectable",d,d,f,!1)){if(d._lastSelectIntent=b,!!d.selected===b&&(3!==f.selectMode||!d.partsel||b))return b;if(!g&&this._triggerNodeEvent("beforeSelect",d,a.originalEvent)===!1)return!!d.selected;b&&1===f.selectMode?(e.lastSelectedNode&&e.lastSelectedNode.setSelected(!1),d.selected=b):3!==f.selectMode||d.parent.radiogroup||d.radiogroup?d.parent.radiogroup?d.visitSiblings(function(a){a._changeSelectStatusAttrs(b&&a===d)},!0):d.selected=b:(d.selected=b,d.fixSelection3AfterClick(c)),this.nodeRenderStatus(a),e.lastSelectedNode=b?d:null,g||e._triggerNodeEvent("select",a)}},nodeSetStatus:function(b,c,d,e){function f(){var a=h.children?h.children[0]:null;if(a&&a.isStatusNode()){try{h.ul&&(h.ul.removeChild(a.li),a.li=null)}catch(b){}1===h.children.length?h.children=[]:h.children.shift()}}function g(b,c){var d=h.children?h.children[0]:null;return d&&d.isStatusNode()?(a.extend(d,b),d.statusNodeType=c,i._callHook("nodeRenderTitle",d)):(h._setChildren([b]),h.children[0].statusNodeType=c,i.render()),h.children[0]}var h=b.node,i=b.tree;switch(c){case"ok":f(),h._isLoading=!1,h._error=null,h.renderStatus();break;case"loading":h.parent||g({title:i.options.strings.loading+(d?" ("+d+")":""),checkbox:!1,tooltip:e},c),h._isLoading=!0,h._error=null,h.renderStatus();break;case"error":g({title:i.options.strings.loadError+(d?" ("+d+")":""),checkbox:!1,tooltip:e},c),h._isLoading=!1,h._error={message:d,details:e},h.renderStatus();break;case"nodata":g({title:i.options.strings.noData,checkbox:!1,tooltip:e},c),h._isLoading=!1,h._error=null,h.renderStatus();break;default:a.error("invalid node status "+c)}},nodeToggleExpanded:function(a){return this.nodeSetExpanded(a,!a.node.expanded)},nodeToggleSelected:function(a){var b=a.node,c=!b.selected;return b.partsel&&!b.selected&&b._lastSelectIntent===!0&&(c=!1,b.selected=!0),b._lastSelectIntent=c,this.nodeSetSelected(a,c)},treeClear:function(a){var b=a.tree;b.activeNode=null,b.focusNode=null,b.$div.find(">ul.fancytree-container").empty(),b.rootNode.children=null},treeCreate:function(a){},treeDestroy:function(a){this.$div.find(">ul.fancytree-container").remove(),this.$source&&this.$source.removeClass("ui-helper-hidden")},treeInit:function(a){var b=a.tree,c=b.options;b.$container.attr("tabindex",c.tabindex),c.rtl?b.$container.attr("DIR","RTL").addClass("fancytree-rtl"):b.$container.removeAttr("DIR").removeClass("fancytree-rtl"),c.aria&&(b.$container.attr("role","tree"),1!==c.selectMode&&b.$container.attr("aria-multiselectable",!0)),this.treeLoad(a)},treeLoad:function(b,c){var d,f,g,h,i=b.tree,j=b.widget.element,k=a.extend({},b,{node:this.rootNode});if(i.rootNode.children&&this.treeClear(b),c=c||this.options.source)"string"==typeof c&&a.error("Not implemented");else switch(f=j.data("type")||"html"){case"html":g=j.find(">ul:first"),g.addClass("ui-fancytree-source ui-helper-hidden"),c=a.ui.fancytree.parseHtml(g),this.data=a.extend(this.data,n(g));break;case"json":c=a.parseJSON(j.text()),j.contents().filter(function(){return 3===this.nodeType}).remove(),a.isPlainObject(c)&&(e(a.isArray(c.children),"if an object is passed as source, it must contain a 'children' array (all other properties are added to 'tree.data')"),d=c,c=c.children,delete d.children,a.extend(i.data,d));break;default:a.error("Invalid data-type: "+f)}return h=this.nodeLoadChildren(k,c).done(function(){i.render(),3===b.options.selectMode&&i.rootNode.fixSelection3FromEndNodes(),i.activeNode&&i.options.activeVisible&&i.activeNode.makeVisible(),i._triggerTreeEvent("init",null,{status:!0})}).fail(function(){i.render(),i._triggerTreeEvent("init",null,{status:!1})})},treeRegisterNode:function(a,b,c){},treeSetFocus:function(b,c,d){function e(a){!a.activeNode&&a.getFirstChild()&&a.getFirstChild().setFocus()}if(c=c!==!1,c!==this.hasFocus()&&(this._hasFocus=c,!c&&this.focusNode?this.focusNode.setFocus(!1):!c||d&&d.calledByNode||a(this.$container).focus(),this.$container.toggleClass("fancytree-treefocus",c),this._triggerTreeEvent(c?"focusTree":"blurTree"),c)){var f=this;setTimeout(function(){e(f)},0)}},treeSetOption:function(b,c,d){var e=b.tree,f=!0,g=!1;switch(c){case"aria":case"checkbox":case"icon":case"minExpandLevel":case"tabindex":e._callHook("treeCreate",e),g=!0;break;case"escapeTitles":case"tooltip":g=!0;break;case"rtl":d===!1?e.$container.removeAttr("DIR").removeClass("fancytree-rtl"):e.$container.attr("DIR","RTL").addClass("fancytree-rtl"),g=!0;break;case"source":f=!1,e._callHook("treeLoad",e,d),g=!0}e.debug("set option "+c+"="+d+" <"+typeof d+">"),f&&(this.widget._super?this.widget._super.call(this.widget,c,d):a.Widget.prototype._setOption.call(this.widget,c,d)),g&&e.render(!0,!1)}}),a.widget("ui.fancytree",{options:{activeVisible:!0,ajax:{type:"GET",cache:!1,dataType:"json"},aria:!0,autoActivate:!0,autoCollapse:!1,autoScroll:!1,checkbox:!1,clickFolderMode:4,debugLevel:null,disabled:!1,enableAspx:!0,escapeTitles:!1,extensions:[],toggleEffect:{effect:"blind",options:{direction:"vertical",scale:"box"},duration:200},generateIds:!1,icon:!0,idPrefix:"ft_",focusOnSelect:!1,keyboard:!0,keyPathSeparator:"/",minExpandLevel:1,quicksearch:!1,rtl:!1,scrollOfs:{top:0,bottom:0},scrollParent:null,selectMode:2,strings:{loading:"Loading...",loadError:"Load error!",moreData:"More...",noData:"No data."},tabindex:"0",titlesTabbable:!1,tooltip:!1,_classNames:{node:"fancytree-node",folder:"fancytree-folder",animating:"fancytree-animating",combinedExpanderPrefix:"fancytree-exp-",combinedIconPrefix:"fancytree-ico-",hasChildren:"fancytree-has-children",active:"fancytree-active",selected:"fancytree-selected",expanded:"fancytree-expanded",lazy:"fancytree-lazy",focused:"fancytree-focused",partload:"fancytree-partload",partsel:"fancytree-partsel",radio:"fancytree-radio",unselectable:"fancytree-unselectable",lastsib:"fancytree-lastsib",loading:"fancytree-loading",error:"fancytree-error",statusNodePrefix:"fancytree-statusnode-"},lazyLoad:null,postProcess:null},_create:function(){this.tree=new t(this),this.$source=this.source||"json"===this.element.data("type")?this.element:this.element.find(">ul:first");var b,c,f,g=this.options,h=g.extensions,i=this.tree;for(f=0;f<h.length;f++)c=h[f],b=a.ui.fancytree._extensions[c],b||a.error("Could not apply extension '"+c+"' (it is not registered, did you forget to include it?)"),this.tree.options[c]=a.extend(!0,{},b.options,this.tree.options[c]),e(this.tree.ext[c]===d,"Extension name must not exist as Fancytree.ext attribute: '"+c+"'"),this.tree.ext[c]={},j(this.tree,i,b,c),i=b;g.icons!==d&&(g.icon!==!0?a.error("'icons' tree option is deprecated since v2.14.0: use 'icon' only instead"):(this.tree.warn("'icons' tree option is deprecated since v2.14.0: use 'icon' instead"),g.icon=g.icons)),g.iconClass!==d&&(g.icon?a.error("'iconClass' tree option is deprecated since v2.14.0: use 'icon' only instead"):(this.tree.warn("'iconClass' tree option is deprecated since v2.14.0: use 'icon' instead"),g.icon=g.iconClass)),g.tabbable!==d&&(g.tabindex=g.tabbable?"0":"-1",this.tree.warn("'tabbable' tree option is deprecated since v2.17.0: use 'tabindex='"+g.tabindex+"' instead")),this.tree._callHook("treeCreate",this.tree)},_init:function(){this.tree._callHook("treeInit",this.tree),this._bind()},_setOption:function(a,b){return this.tree._callHook("treeSetOption",this.tree,a,b)},destroy:function(){this._unbind(),this.tree._callHook("treeDestroy",this.tree),a.Widget.prototype.destroy.call(this)},_unbind:function(){var b=this.tree._ns;this.element.off(b),this.tree.$container.off(b),a(c).off(b)},_bind:function(){var a=this,b=this.options,c=this.tree,d=c._ns;this._unbind(),c.$container.on("focusin"+d+" focusout"+d,function(a){var b=w.getNode(a),d="focusin"===a.type;b?c._callHook("nodeSetFocus",c._makeHookContext(b,a),d):c._callHook("treeSetFocus",c,d)}).on("selectstart"+d,"span.fancytree-title",function(a){a.preventDefault()}).on("keydown"+d,function(a){if(b.disabled||b.keyboard===!1)return!0;var d,e=c.focusNode,f=c._makeHookContext(e||c,a),g=c.phase;try{return c.phase="userEvent",d=e?c._triggerNodeEvent("keydown",e,a):c._triggerTreeEvent("keydown",a),"preventNav"===d?d=!0:d!==!1&&(d=c._callHook("nodeKeydown",f)),d}finally{c.phase=g}}).on("mousedown"+d+" dblclick"+d,function(c){if(b.disabled)return!0;var d,e=w.getEventTarget(c),f=e.node,g=a.tree,h=g.phase;if(!f)return!0;d=g._makeHookContext(f,c);try{switch(g.phase="userEvent",c.type){case"mousedown":return d.targetType=e.type,f.isPagingNode()?g._triggerNodeEvent("clickPaging",d,c)===!0:g._triggerNodeEvent("click",d,c)!==!1&&g._callHook("nodeClick",d);case"dblclick":return d.targetType=e.type,g._triggerNodeEvent("dblclick",d,c)!==!1&&g._callHook("nodeDblclick",d)}}finally{g.phase=h}})},getActiveNode:function(){return this.tree.activeNode},getNodeByKey:function(a){return this.tree.getNodeByKey(a)},getRootNode:function(){return this.tree.rootNode},getTree:function(){return this.tree}}),w=a.ui.fancytree,a.extend(a.ui.fancytree,{version:"2.23.0",buildType: "production",debugLevel: 1,_nextId:1,_nextNodeKey:1,_extensions:{},_FancytreeClass:t,_FancytreeNodeClass:s,jquerySupports:{positionMyOfs:h(a.ui.version,1,9)},assert:function(a,b){return e(a,b)},debounce:function(a,b,c,d){var e;return 3===arguments.length&&"boolean"!=typeof c&&(d=c,c=!1),function(){var f=arguments;d=d||this,c&&!e&&b.apply(d,f),clearTimeout(e),e=setTimeout(function(){c||b.apply(d,f),e=null},a)}},debug:function(b){a.ui.fancytree.debugLevel>=2&&f("log",arguments)},error:function(a){f("error",arguments)},escapeHtml:o,fixPositionOptions:function(b){if((b.offset||(""+b.my+b.at).indexOf("%")>=0)&&a.error("expected new position syntax (but '%' is not supported)"),!a.ui.fancytree.jquerySupports.positionMyOfs){var c=/(\w+)([+-]?\d+)?\s+(\w+)([+-]?\d+)?/.exec(b.my),d=/(\w+)([+-]?\d+)?\s+(\w+)([+-]?\d+)?/.exec(b.at),e=(c[2]?+c[2]:0)+(d[2]?+d[2]:0),f=(c[4]?+c[4]:0)+(d[4]?+d[4]:0);b=a.extend({},b,{my:c[1]+" "+c[3],at:d[1]+" "+d[3]}),(e||f)&&(b.offset=""+e+" "+f)}return b},getEventTargetType:function(a){return this.getEventTarget(a).type},getEventTarget:function(b){var c=b&&b.target?b.target.className:"",e={node:this.getNode(b.target),type:d};return/\bfancytree-title\b/.test(c)?e.type="title":/\bfancytree-expander\b/.test(c)?e.type=e.node.hasChildren()===!1?"prefix":"expander":/\bfancytree-checkbox\b/.test(c)?e.type="checkbox":/\bfancytree-icon\b/.test(c)?e.type="icon":/\bfancytree-node\b/.test(c)?e.type="title":b&&b.target&&a(b.target).closest(".fancytree-title").length&&(e.type="title"),e},getNode:function(a){if(a instanceof s)return a;for(a instanceof jQuery?a=a[0]:a.originalEvent!==d&&(a=a.target);a;){if(a.ftnode)return a.ftnode;a=a.parentNode}return null},getTree:function(b){var c;return b instanceof t?b:(b===d&&(b=0),"number"==typeof b?b=a(".fancytree-container").eq(b):"string"==typeof b?b=a(b).eq(0):b.selector!==d?b=b.eq(0):b.originalEvent!==d&&(b=a(b.target)),b=b.closest(":ui-fancytree"),c=b.data("ui-fancytree")||b.data("fancytree"),c?c.tree:null)},evalOption:function(b,c,d,e,f){var g,h,i=c.tree,j=e[b],k=d[b];return a.isFunction(j)?(g={node:c,tree:i,widget:i.widget,options:i.widget.options},h=j.call(i,{type:b},g),null==h&&(h=k)):h=null!=k?k:j,null==h&&(h=f),h},eventToString:function(a){var b=a.which,c=a.type,d=[];return a.altKey&&d.push("alt"),a.ctrlKey&&d.push("ctrl"),a.metaKey&&d.push("meta"),a.shiftKey&&d.push("shift"),"click"===c||"dblclick"===c?d.push(E[a.button]+c):C[b]||d.push(D[b]||String.fromCharCode(b).toLowerCase()),d.join("+")},info:function(b){a.ui.fancytree.debugLevel>=1&&f("info",arguments)},keyEventToString:function(a){return this.warn("keyEventToString() is deprecated: use eventToString()"),this.eventToString(a)},overrideMethod:function(b,c,d){var e,f=b[c]||a.noop;b[c]=function(){try{return e=this._super,this._super=f,d.apply(this,arguments)}finally{this._super=e}}},parseHtml:function(b){var c,e,f,g,h,i,j,k,l=b.find(">li"),m=[];return l.each(function(){var l,o,p=a(this),q=p.find(">span:first",this),r=q.length?null:p.find(">a:first"),s={tooltip:null,data:{}};for(q.length?s.title=q.html():r&&r.length?(s.title=r.html(),s.data.href=r.attr("href"),s.data.target=r.attr("target"),s.tooltip=r.attr("title")):(s.title=p.html(),h=s.title.search(/<ul/i),h>=0&&(s.title=s.title.substring(0,h))),s.title=a.trim(s.title),g=0,i=F.length;g<i;g++)s[F[g]]=d;for(c=this.className.split(" "),f=[],g=0,i=c.length;g<i;g++)e=c[g],G[e]?s[e]=!0:f.push(e);if(s.extraClasses=f.join(" "),j=p.attr("title"),j&&(s.tooltip=j),j=p.attr("id"),j&&(s.key=j),p.attr("hideCheckbox")&&(s.checkbox=!1),l=n(p),l&&!a.isEmptyObject(l)){for(o in J)l.hasOwnProperty(o)&&(l[J[o]]=l[o],delete l[o]);for(g=0,i=H.length;g<i;g++)j=H[g],k=l[j],null!=k&&(delete l[j],s[j]=k);a.extend(s.data,l)}b=p.find(">ul:first"),b.length?s.children=a.ui.fancytree.parseHtml(b):s.children=s.lazy?d:null,m.push(s)}),m},registerExtension:function(b){e(null!=b.name,"extensions must have a `name` property."),e(null!=b.version,"extensions must have a `version` property."),a.ui.fancytree._extensions[b.name]=b},unescapeHtml:function(a){var b=c.createElement("div");return b.innerHTML=a,0===b.childNodes.length?"":b.childNodes[0].nodeValue},warn:function(a){f("warn",arguments)}})}(jQuery,window,document);