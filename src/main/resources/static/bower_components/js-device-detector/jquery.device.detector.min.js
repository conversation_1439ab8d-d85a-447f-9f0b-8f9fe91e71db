/* !
 * js.device.detector 1.0.3
 * https://github.com/exiguus/js.device.detector.git
 *
 * <PERSON> <<EMAIL>>
 * Released under the MIT license
 *
 * Date: 2018-10-06 02:10:35
 */
!function(s){"use strict";function i(e){return s.map(e,function(e){return""===e?null:e})}function o(e){var r=!1,n=i(e);return s.each(n,function(e,n){if(n=(""+n).toLowerCase(),!0===(r=-1<p.indexOf(n)||-1<u.indexOf(n)))return!1}),r}function t(e,n){try{var r=!1;return s.isArray(e)||(e=s.makeArray(e)),s.isArray(n)||(n=s.makeArray(n)),!1===o(n)&&(r=o(e)),r}catch(e){console.info("deviceDetector: No match String || Array given in isDeviceType()",e)}}function d(){var i=0,o=p+u;return s.each(l.browsers.versions,function(e,n){var r=o.indexOf(n.index);if(-1<r)return i=parseFloat(o.substring(r+n.map).split(".")[0]),!1}),i}function a(r){var i="unknown";return s.each(l.browsers.names,function(e,n){if(t(n.pattern.include,n.pattern.exclude))return i=!0===r?n.id:n.name,!1}),i}function e(){var o={string:"0.0.0",categories:{major:0,minor:0,bugfix:0}},t=p+u;return s.each(l.oss.versions,function(e,n){var r=t.indexOf(n.index);if(-1<r){o.string=t.substring(r+n.map).split(n.cut)[0]||"0.0.0";var i=o.string.split(n.chop);return o.categories.major=parseInt(i[0])||0,o.categories.minor=parseInt(i[1])||0,o.categories.bugfix=parseInt(i[2])||0,!1}}),o}function n(r){var i="unknown";return s.each(l.oss.names,function(e,n){if(t(n.pattern.include,n.pattern.exclude))return i=!0===r?n.id:n.name,!1}),i}function r(){var r=!1;return s.each(l.supports,function(e,n){a(!0)===n.id&&d()>=parseFloat(n.version)&&(r=!0)}),r}s.fn.deviceDetector=function(e){return void 0!==typeof e&&s.extend(!0,l,e),!0},s.fn.deviceDetector.isMobile=function(){return t(l.vendors.apple.ios.pattern.include,l.vendors.apple.ios.pattern.exclude)||t(l.vendors.google.android.pattern.include,l.vendors.google.android.pattern.exclude)||t(l.vendors.microsoft.windowsPhone.pattern.include,l.vendors.microsoft.windowsPhone.exclude)||t(l.vendors.blackberry.blackberry.pattern.include,l.vendors.blackberry.blackberry.exclude)||t(l.mobile.pattern.include,l.mobile.pattern.exclude)},s.fn.deviceDetector.isDesktop=function(){return t(l.vendors.apple.macos.pattern.include,l.vendors.apple.macos.pattern.exclude)||t(l.vendors.lbu.unixlike.pattern.include,l.vendors.lbu.unixlike.pattern.exclude)||t(l.vendors.microsoft.windows.pattern.include,l.vendors.microsoft.windows.pattern.exclude)},s.fn.deviceDetector.isSafari=function(){return t(l.vendors.apple.safari.pattern.include,l.vendors.apple.safari.pattern.exclude)},s.fn.deviceDetector.isIpad=function(){return t(l.vendors.apple.ipad.pattern.include,l.vendors.apple.ipad.pattern.exclude)},s.fn.deviceDetector.isIphone=function(){return t(l.vendors.apple.iphone.pattern.include,l.vendors.apple.iphone.pattern.exclude)},s.fn.deviceDetector.isIos=function(){return t(l.vendors.apple.ios.pattern.include,l.vendors.apple.ios.pattern.exclude)},s.fn.deviceDetector.isMacos=function(){return t(l.vendors.apple.macos.pattern.include,l.vendors.apple.macos.pattern.exclude)},s.fn.deviceDetector.isChrome=function(){return t(l.vendors.google.chrome.pattern.include,l.vendors.google.chrome.pattern.exclude)},s.fn.deviceDetector.isAndroid=function(){return t(l.vendors.google.android.pattern.include,l.vendors.google.android.pattern.exclude)},s.fn.deviceDetector.isFirefox=function(){return t(l.vendors.mozilla.firefox.pattern.include,l.vendors.mozilla.firefox.pattern.exclude)},s.fn.deviceDetector.isMsie=function(){return t(l.vendors.microsoft.msie.pattern.include,l.vendors.microsoft.msie.pattern.exclude)},s.fn.deviceDetector.isEdge=function(){return t(l.vendors.microsoft.edge.pattern.include,l.vendors.microsoft.edge.pattern.exclude)},s.fn.deviceDetector.isIe=function(){return t(l.vendors.microsoft.ie.pattern.include,l.vendors.microsoft.ie.pattern.exclude)},s.fn.deviceDetector.isIeMobile=function(){return t(l.vendors.microsoft.ieMobile.pattern.include,l.vendors.microsoft.ieMobile.pattern.exclude)},s.fn.deviceDetector.isWindows=function(){return t(l.vendors.microsoft.windows.pattern.include,l.vendors.microsoft.windows.pattern.exclude)},s.fn.deviceDetector.isWindowsPhone=function(){return t(l.vendors.microsoft.windowsPhone.pattern.include,l.vendors.microsoft.windowsPhone.pattern.exclude)},s.fn.deviceDetector.isOpera=function(){return t(l.vendors.opera.opera.pattern.include,l.vendors.opera.opera.pattern.exclude)},s.fn.deviceDetector.isOperaMini=function(){return t(l.vendors.opera.operaMini.pattern.include,l.vendors.opera.operaMini.pattern.exclude)},s.fn.deviceDetector.isBlackberry=function(){return t(l.vendors.blackberry.blackberry.pattern.include,l.vendors.blackberry.blackberry.pattern.exclude)},s.fn.deviceDetector.isLinux=function(){return t(l.vendors.lbu.linux.pattern.include,l.vendors.lbu.linux.pattern.exclude)},s.fn.deviceDetector.isBsd=function(){return t(l.vendors.lbu.bsd.pattern.include,l.vendors.lbu.bsd.pattern.exclude)},s.fn.deviceDetector.getBrowserVersion=function(){return d()},s.fn.deviceDetector.getBrowserName=function(){return a()},s.fn.deviceDetector.getBrowserId=function(){return a(!0)},s.fn.deviceDetector.getOsVersion=function(){return e().string},s.fn.deviceDetector.getOsVersionString=function(){return e().string},s.fn.deviceDetector.getOsVersionCategories=function(){return e().categories},s.fn.deviceDetector.getOsVersionMajor=function(){return e().categories.major},s.fn.deviceDetector.getOsVersionMinor=function(){return e().categories.minor},s.fn.deviceDetector.getOsVersionBugfix=function(){return e().categories.bugfix},s.fn.deviceDetector.getOsName=function(){return n()},s.fn.deviceDetector.getOsId=function(){return n(!0)},s.fn.deviceDetector.isSupported=function(){return r()},s.fn.deviceDetector.getInfo=function(){return{browserVersion:this.getBrowserVersion(),browserName:this.getBrowserName(),browserId:this.getBrowserId(),osVersion:this.getOsVersion(),osVersionString:this.getOsVersionString(),osVersionCategories:this.getOsVersionCategories(),osVersionMajor:this.getOsVersionMajor(),osVersionMinor:this.getOsVersionMinor(),osVersionBugfix:this.getOsVersionBugfix(),osName:this.getOsName(),osId:this.getOsId(),supported:r(),mobile:this.isMobile(),desktop:this.isDesktop(),safari:this.isSafari(),iphone:this.isIphone(),ipad:this.isIpad(),ios:this.isIos(),macos:this.isMacos(),chrome:this.isChrome(),android:this.isAndroid(),firefox:this.isFirefox(),ie:this.isIe(),msie:this.isMsie(),edge:this.isEdge(),ieMobile:this.isIeMobile(),windowsPhone:this.isWindowsPhone(),windows:this.isWindows(),opera:this.isOpera(),operaMini:this.isOperaMini(),blackberry:this.isBlackberry(),linux:this.isLinux(),bsd:this.isBsd()}},s.fn.deviceDetector.__removeEmptyValuesFromArray=i,s.fn.deviceDetector.__isMatching=o,s.fn.deviceDetector.__isDeviceType=t,s.fn.deviceDetector.__getBrowserVersion=d,s.fn.deviceDetector.__getBrowserName=a,s.fn.deviceDetector.__getOsVersion=e,s.fn.deviceDetector.__getOsName=n,s.fn.deviceDetector.__isSupported=r;var c=navigator,p=(""+c.userAgent).toLowerCase(),u=(""+c.appVersion).toLowerCase();p=(""+p).toLowerCase(),u=p||u;var l={};s.extend(l,{vendors:{apple:{safari:{pattern:{include:"safari",exclude:["chrome","iemobile","opr/","opera"]}},iphone:{pattern:{include:"iphone",exclude:"iemobile"}},ipad:{pattern:{include:"ipad",exclude:"iemobile"}},ios:{pattern:{include:["ipad","iphone","ipod"],exclude:"iemobile"}},macos:{pattern:{include:"mac os",exclude:["iphone","ipad","ipod"]}}},google:{chrome:{pattern:{include:"chrome",exclude:["edge","msie","firefox","opr/","opera"]}},android:{pattern:{include:"android",exclude:"windows phone"}}},mozilla:{firefox:{pattern:{include:"firefox",exclude:""}}},microsoft:{msie:{pattern:{include:["trident","msie"],exclude:"iemobile"}},edge:{pattern:{include:"edge",exclude:"iemobile"}},ie:{pattern:{include:["trident","msie","edge"],exclude:"iemobile"}},ieMobile:{pattern:{include:"iemobile",exclude:""}},windows:{pattern:{include:"windows nt",exclude:""}},windowsMobile:{pattern:{include:"windows phone",exclude:""}},windowsXp:{pattern:{include:"windows nt 5",exclude:""}},windowsVista:{pattern:{include:"windows nt 6.0",exclude:""}},windows7:{pattern:{include:"windows nt 6.1",exclude:""}},windows8:{pattern:{include:"windows nt 6.2",exclude:""}},windows81:{pattern:{include:"windows nt 6.3",exclude:""}},windows10:{pattern:{include:"windows nt 10.0",exclude:""}},windowsPhone:{pattern:{include:"windows phone",exclude:""}}},opera:{opera:{pattern:{include:["opera","opr","presto"],exclude:"opera mini"}},operaMini:{pattern:{include:"opera mini",exclude:""}}},blackberry:{blackberry:{pattern:{include:"blackberry",exclude:""}}},lbu:{linux:{pattern:{include:"linux",exclude:""}},bsd:{pattern:{include:["bsd","unix","sunos"],exclude:""}},unixlike:{pattern:{include:["linux","bsd","unix","sunos","X11"],exclude:["mobile","tablet","phone","droid"]}}}}}),s.extend(l,{browsers:{names:{edge:{id:"edge",name:"Microsoft Edge",pattern:l.vendors.microsoft.edge.pattern},ie:{id:"msie",name:"Microsoft Internet Explorer",pattern:l.vendors.microsoft.ie.pattern},ieMobile:{id:"ieMobile",name:"Microsoft Internet Explorer Mobile",pattern:l.vendors.microsoft.ieMobile.pattern},chrome:{id:"chrome",name:"Google Chrome",pattern:l.vendors.google.chrome.pattern},safari:{id:"safari",name:"Apple Safari",pattern:l.vendors.apple.safari.pattern},firefox:{id:"firefox",name:"Mozilla Firefox",pattern:l.vendors.mozilla.firefox.pattern},opera:{id:"opera",name:"Opera",pattern:l.vendors.opera.opera.pattern},operaMini:{id:"operaMini",name:"Opera Mini",pattern:l.vendors.opera.operaMini.pattern},blackberry:{id:"blackberry",name:"BlackBerry",pattern:l.vendors.blackberry.blackberry.pattern}},versions:{default:{index:"rv:",map:3},edge:{index:"edge/",map:5},msie:{index:"msie ",map:5},ieMobile:{index:"iemobile/",map:9},chrome:{index:"chrome/",map:7},firefox:{index:"firefox/",map:8},opr:{index:"opr/",map:4},operaMini:{index:"opera/",map:6},opera:{index:"opera ",map:5},safari:{index:"version/",map:8}}}}),s.extend(l,{oss:{names:{windowsPhone:{id:"windowsPhone",name:"Microsoft Windows Phone",pattern:l.vendors.microsoft.windowsPhone.pattern},windowsXp:{id:"windowsXp",name:"Microsoft Windows XP",pattern:l.vendors.microsoft.windowsXp.pattern},windowsVista:{id:"windowsVista",name:"Microsoft Windows Vista",pattern:l.vendors.microsoft.windowsVista.pattern},windows7:{id:"windows7",name:"Microsoft Windows 7",pattern:l.vendors.microsoft.windows7.pattern},window8:{id:"windows8",name:"Microsoft Windows 8",pattern:l.vendors.microsoft.windows8.pattern},window81:{id:"windows81",name:"Microsoft Windows 8.1",pattern:l.vendors.microsoft.windows81.pattern},windows10:{id:"windows10",name:"Microsoft Windows 10",pattern:l.vendors.microsoft.windows10.pattern},macos:{id:"macos",name:"Apple Mac OS X",pattern:l.vendors.apple.macos.pattern},ios:{id:"ios",name:"Apple iOS",pattern:l.vendors.apple.ios.pattern},android:{id:"android",name:"Android Linux",pattern:l.vendors.google.android.pattern},linux:{id:"linux",name:"GNU/Linux OS",pattern:l.vendors.lbu.linux.pattern},bsd:{id:"bsd",name:"BSD OS",pattern:l.vendors.lbu.bsd.pattern},blackberry:{id:"blackberry",name:"BlackBerry OS",pattern:l.vendors.blackberry.blackberry.pattern}},versions:{windowsPhone:{index:l.vendors.microsoft.windowsPhone.pattern.include,map:14,cut:/;|\)/,chop:"."},windowsXp:{index:l.vendors.microsoft.windowsXp.pattern.include,map:11,cut:/;|\)/,chop:"."},windowsVista:{index:l.vendors.microsoft.windowsVista.pattern.include,map:11,cut:/;|\)/,chop:"."},windows7:{index:l.vendors.microsoft.windows7.pattern.include,map:11,cut:/;|\)/,chop:"."},windows8:{index:l.vendors.microsoft.windows8.pattern.include,map:11,cut:/;|\)/,chop:"."},windows81:{index:l.vendors.microsoft.windows81.pattern.include,map:11,cut:/;|\)/,chop:"."},windows10:{index:l.vendors.microsoft.windows10.pattern.include,map:11,cut:/;|\)/,chop:"."},android:{index:l.vendors.google.android.pattern.include,map:8,cut:/;|\)/,chop:"."},ios:{index:"cpu os ",map:7,cut:/ |\)/,chop:"_"},iphone:{index:"iphone os ",map:10,cut:/ |\)/,chop:"_"},ipad:{index:"ipad os ",map:8,cut:/ |\)/,chop:"_"},macos:{index:"mac os x",map:9,cut:/ |\)|;/,chop:/_|\./},bsd:{index:l.vendors.lbu.bsd.pattern.include,map:5,cut:/ |\)/,chop:"."},linux:{index:l.vendors.lbu.linux.pattern.include,map:5,cut:/;| |\)/,chop:"."},blackberry:{index:l.vendors.blackberry.blackberry.pattern.include,map:10,cut:/;|\)/,chop:"."}}}}),s.extend(l,{mobile:{pattern:{include:["mobile","tablet","phone","droid"],exclude:""}}}),s.extend(l,{supports:{msie:{id:"msie",version:"11"},edge:{id:"edge",version:"12"},chrome:{id:"chrome",version:"66"},firefox:{id:"firefox",version:"60"},safari:{id:"safari",version:"11"}}})}(jQuery);