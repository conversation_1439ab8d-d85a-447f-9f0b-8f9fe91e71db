{"resources": {"head": [{"type": "script", "src": "./pace.js", "moduleType": "global", "exports": ["Pace"]}, {"type": "style", "src": "./themes/{{ options.color }}/pace-theme-{{ options.theme }}.css"}]}, "options": {"properties": {"color": {"title": "Color", "type": "string", "enum": ["black", "white", "silver", "red", "orange", "yellow", "green", "blue", "pink", "purple"], "enumNames": {"black": "Black", "white": "White", "silver": "Silver", "red": "Red", "orange": "Orange", "yellow": "Yellow", "green": "Green", "blue": "Blue", "pink": "Pink", "purple": "Purple"}, "default": "blue"}, "theme": {"title": "Theme", "type": "string", "enum": ["barber-shop", "big-counter", "bounce", "center-atom", "center-circle", "center-radar", "center-simple", "corner-indicator", "fill-left", "flash", "flat-top", "loading-bar", "mac-osx", "minimal"], "enumNames": {"barber-shop": "Barber Shop", "big-counter": "Big Counter", "bounce": "<PERSON><PERSON><PERSON>", "center-atom": "Center Atom", "center-circle": "Center Circle", "center-radar": "Center Radar", "center-simple": "Center Simple", "corner-indicator": "Corner Indicator", "fill-left": "Fill Left", "flash": "Flash", "flat-top": "Flat Top", "loading-bar": "Loading Bar", "mac-osx": "Mac OS X", "minimal": "Minimal"}, "default": "barber-shop"}}}}