{"errors": [], "warnings": [], "version": "1.15.0", "hash": "6177522c2e157dd44156", "publicPath": "", "assetsByChunkName": {"textMaskAddons": "textMaskAddons.js", "emailMask": "emailMask.js", "createNumberMask": "createNumberMask.js", "createAutoCorrectedDatePipe": "createAutoCorrectedDatePipe.js"}, "assets": [{"name": "textMaskAddons.js", "size": 5790, "chunks": [0], "chunkNames": ["textMaskAddons"], "emitted": true}, {"name": "emailMask.js", "size": 2171, "chunks": [1], "chunkNames": ["emailMask"], "emitted": true}, {"name": "createNumberMask.js", "size": 2528, "chunks": [2], "chunkNames": ["createNumberMask"], "emitted": true}, {"name": "createAutoCorrectedDatePipe.js", "size": 1625, "chunks": [3], "chunkNames": ["createAutoCorrectedDatePipe"], "emitted": true}, {"name": "stats.json", "size": 0, "chunks": [], "chunkNames": []}], "chunks": [{"id": 0, "rendered": true, "initial": true, "entry": true, "extraAsync": false, "size": 15129, "names": ["textMaskAddons"], "files": ["textMaskAddons.js"], "hash": "d85c8397637b5fe4d3f3", "parents": [], "modules": [{"id": 0, "identifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/addons/src/index.js", "name": "./addons/src/index.js", "index": 7, "index2": 7, "size": 864, "cacheable": true, "built": true, "optional": false, "prefetched": false, "chunks": [0], "assets": [], "issuer": null, "failed": false, "errors": 0, "warnings": 0, "reasons": [], "source": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _createAutoCorrectedDatePipe = require('./createAutoCorrectedDatePipe');\n\nObject.defineProperty(exports, 'createAutoCorrectedDatePipe', {\n  enumerable: true,\n  get: function get() {\n    return _interopRequireDefault(_createAutoCorrectedDatePipe).default;\n  }\n});\n\nvar _createNumberMask = require('./createNumberMask');\n\nObject.defineProperty(exports, 'createNumberMask', {\n  enumerable: true,\n  get: function get() {\n    return _interopRequireDefault(_createNumberMask).default;\n  }\n});\n\nvar _emailMask = require('./emailMask');\n\nObject.defineProperty(exports, 'emailMask', {\n  enumerable: true,\n  get: function get() {\n    return _interopRequireDefault(_emailMask).default;\n  }\n});\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }"}, {"id": 1, "identifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/addons/src/createAutoCorrectedDatePipe.js", "name": "./addons/src/createAutoCorrectedDatePipe.js", "index": 1, "index2": 0, "size": 2697, "cacheable": true, "built": true, "optional": false, "prefetched": false, "chunks": [0, 3], "assets": [], "issuer": "multi createAutoCorrectedDatePipe", "failed": false, "errors": 0, "warnings": 0, "reasons": [{"moduleId": 0, "moduleIdentifier": "multi createAutoCorrectedDatePipe", "module": "multi createAutoCorrectedDatePipe", "moduleName": "multi createAutoCorrectedDatePipe", "type": "single entry", "userRequest": "/Users/<USER>/Projects/text-mask/addons/src/createAutoCorrectedDatePipe.js"}, {"moduleId": 0, "moduleIdentifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/addons/src/index.js", "module": "./addons/src/index.js", "moduleName": "./addons/src/index.js", "type": "cjs require", "userRequest": "./createAutoCorrectedDatePipe", "loc": "7:35-75"}], "source": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = createAutoCorrectedDatePipe;\nvar maxValueMonth = [31, 31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];\nvar formatOrder = ['yyyy', 'yy', 'mm', 'dd', 'HH', 'MM', 'SS'];\nfunction createAutoCorrectedDatePipe() {\n  var dateFormat = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'mm dd yyyy';\n\n  var _ref = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},\n      _ref$minYear = _ref.minYear,\n      minYear = _ref$minYear === undefined ? 1 : _ref$minYear,\n      _ref$maxYear = _ref.maxYear,\n      maxYear = _ref$maxYear === undefined ? 9999 : _ref$maxYear;\n\n  var dateFormatArray = dateFormat.split(/[^dmyHMS]+/).sort(function (a, b) {\n    return formatOrder.indexOf(a) - formatOrder.indexOf(b);\n  });\n  return function (conformedValue) {\n    var indexesOfPipedChars = [];\n    var maxValue = { 'dd': 31, 'mm': 12, 'yy': 99, 'yyyy': maxYear, 'HH': 23, 'MM': 59, 'SS': 59 };\n    var minValue = { 'dd': 1, 'mm': 1, 'yy': 0, 'yyyy': minYear, 'HH': 0, 'MM': 0, 'SS': 0 };\n    var conformedValueArr = conformedValue.split('');\n\n    // Check first digit\n    dateFormatArray.forEach(function (format) {\n      var position = dateFormat.indexOf(format);\n      var maxFirstDigit = parseInt(maxValue[format].toString().substr(0, 1), 10);\n\n      if (parseInt(conformedValueArr[position], 10) > maxFirstDigit) {\n        conformedValueArr[position + 1] = conformedValueArr[position];\n        conformedValueArr[position] = 0;\n        indexesOfPipedChars.push(position);\n      }\n    });\n\n    // Check for invalid date\n    var month = 0;\n    var isInvalid = dateFormatArray.some(function (format) {\n      var position = dateFormat.indexOf(format);\n      var length = format.length;\n      var textValue = conformedValue.substr(position, length).replace(/\\D/g, '');\n      var value = parseInt(textValue, 10);\n      if (format === 'mm') {\n        month = value || 0;\n      }\n      var maxValueForFormat = format === 'dd' ? maxValueMonth[month] : maxValue[format];\n      if (format === 'yyyy' && (minYear !== 1 || maxYear !== 9999)) {\n        var scopedMaxValue = parseInt(maxValue[format].toString().substring(0, textValue.length), 10);\n        var scopedMinValue = parseInt(minValue[format].toString().substring(0, textValue.length), 10);\n        return value < scopedMinValue || value > scopedMaxValue;\n      }\n      return value > maxValueForFormat || textValue.length === length && value < minValue[format];\n    });\n\n    if (isInvalid) {\n      return false;\n    }\n\n    return {\n      value: conformedValueArr.join(''),\n      indexesOfPipedChars: indexesOfPipedChars\n    };\n  };\n}"}, {"id": 2, "identifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/addons/src/createNumberMask.js", "name": "./addons/src/createNumberMask.js", "index": 3, "index2": 2, "size": 6149, "cacheable": true, "built": true, "optional": false, "prefetched": false, "chunks": [0, 2], "assets": [], "issuer": "multi createNumberMask", "failed": false, "errors": 0, "warnings": 0, "reasons": [{"moduleId": 0, "moduleIdentifier": "multi createNumberMask", "module": "multi createNumberMask", "moduleName": "multi createNumberMask", "type": "single entry", "userRequest": "/Users/<USER>/Projects/text-mask/addons/src/createNumberMask.js"}, {"moduleId": 0, "moduleIdentifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/addons/src/index.js", "module": "./addons/src/index.js", "moduleName": "./addons/src/index.js", "type": "cjs require", "userRequest": "./createNumberMask", "loc": "16:24-53"}], "source": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; };\n\nexports.default = createNumberMask;\nvar dollarSign = '$';\nvar emptyString = '';\nvar comma = ',';\nvar period = '.';\nvar minus = '-';\nvar minusRegExp = /-/;\nvar nonDigitsRegExp = /\\D+/g;\nvar number = 'number';\nvar digitRegExp = /\\d/;\nvar caretTrap = '[]';\n\nfunction createNumberMask() {\n  var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n      _ref$prefix = _ref.prefix,\n      prefix = _ref$prefix === undefined ? dollarSign : _ref$prefix,\n      _ref$suffix = _ref.suffix,\n      suffix = _ref$suffix === undefined ? emptyString : _ref$suffix,\n      _ref$includeThousands = _ref.includeThousandsSeparator,\n      includeThousandsSeparator = _ref$includeThousands === undefined ? true : _ref$includeThousands,\n      _ref$thousandsSeparat = _ref.thousandsSeparatorSymbol,\n      thousandsSeparatorSymbol = _ref$thousandsSeparat === undefined ? comma : _ref$thousandsSeparat,\n      _ref$allowDecimal = _ref.allowDecimal,\n      allowDecimal = _ref$allowDecimal === undefined ? false : _ref$allowDecimal,\n      _ref$decimalSymbol = _ref.decimalSymbol,\n      decimalSymbol = _ref$decimalSymbol === undefined ? period : _ref$decimalSymbol,\n      _ref$decimalLimit = _ref.decimalLimit,\n      decimalLimit = _ref$decimalLimit === undefined ? 2 : _ref$decimalLimit,\n      _ref$requireDecimal = _ref.requireDecimal,\n      requireDecimal = _ref$requireDecimal === undefined ? false : _ref$requireDecimal,\n      _ref$allowNegative = _ref.allowNegative,\n      allowNegative = _ref$allowNegative === undefined ? false : _ref$allowNegative,\n      _ref$allowLeadingZero = _ref.allowLeadingZeroes,\n      allowLeadingZeroes = _ref$allowLeadingZero === undefined ? false : _ref$allowLeadingZero,\n      _ref$integerLimit = _ref.integerLimit,\n      integerLimit = _ref$integerLimit === undefined ? null : _ref$integerLimit;\n\n  var prefixLength = prefix && prefix.length || 0;\n  var suffixLength = suffix && suffix.length || 0;\n  var thousandsSeparatorSymbolLength = thousandsSeparatorSymbol && thousandsSeparatorSymbol.length || 0;\n\n  function numberMask() {\n    var rawValue = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : emptyString;\n\n    var rawValueLength = rawValue.length;\n\n    if (rawValue === emptyString || rawValue[0] === prefix[0] && rawValueLength === 1) {\n      return prefix.split(emptyString).concat([digitRegExp]).concat(suffix.split(emptyString));\n    } else if (rawValue === decimalSymbol && allowDecimal) {\n      return prefix.split(emptyString).concat(['0', decimalSymbol, digitRegExp]).concat(suffix.split(emptyString));\n    }\n\n    var isNegative = rawValue[0] === minus && allowNegative;\n    //If negative remove \"-\" sign\n    if (isNegative) {\n      rawValue = rawValue.toString().substr(1);\n    }\n\n    var indexOfLastDecimal = rawValue.lastIndexOf(decimalSymbol);\n    var hasDecimal = indexOfLastDecimal !== -1;\n\n    var integer = void 0;\n    var fraction = void 0;\n    var mask = void 0;\n\n    // remove the suffix\n    if (rawValue.slice(suffixLength * -1) === suffix) {\n      rawValue = rawValue.slice(0, suffixLength * -1);\n    }\n\n    if (hasDecimal && (allowDecimal || requireDecimal)) {\n      integer = rawValue.slice(rawValue.slice(0, prefixLength) === prefix ? prefixLength : 0, indexOfLastDecimal);\n\n      fraction = rawValue.slice(indexOfLastDecimal + 1, rawValueLength);\n      fraction = convertToMask(fraction.replace(nonDigitsRegExp, emptyString));\n    } else {\n      if (rawValue.slice(0, prefixLength) === prefix) {\n        integer = rawValue.slice(prefixLength);\n      } else {\n        integer = rawValue;\n      }\n    }\n\n    if (integerLimit && (typeof integerLimit === 'undefined' ? 'undefined' : _typeof(integerLimit)) === number) {\n      var thousandsSeparatorRegex = thousandsSeparatorSymbol === '.' ? '[.]' : '' + thousandsSeparatorSymbol;\n      var numberOfThousandSeparators = (integer.match(new RegExp(thousandsSeparatorRegex, 'g')) || []).length;\n\n      integer = integer.slice(0, integerLimit + numberOfThousandSeparators * thousandsSeparatorSymbolLength);\n    }\n\n    integer = integer.replace(nonDigitsRegExp, emptyString);\n\n    if (!allowLeadingZeroes) {\n      integer = integer.replace(/^0+(0$|[^0])/, '$1');\n    }\n\n    integer = includeThousandsSeparator ? addThousandsSeparator(integer, thousandsSeparatorSymbol) : integer;\n\n    mask = convertToMask(integer);\n\n    if (hasDecimal && allowDecimal || requireDecimal === true) {\n      if (rawValue[indexOfLastDecimal - 1] !== decimalSymbol) {\n        mask.push(caretTrap);\n      }\n\n      mask.push(decimalSymbol, caretTrap);\n\n      if (fraction) {\n        if ((typeof decimalLimit === 'undefined' ? 'undefined' : _typeof(decimalLimit)) === number) {\n          fraction = fraction.slice(0, decimalLimit);\n        }\n\n        mask = mask.concat(fraction);\n      }\n\n      if (requireDecimal === true && rawValue[indexOfLastDecimal - 1] === decimalSymbol) {\n        mask.push(digitRegExp);\n      }\n    }\n\n    if (prefixLength > 0) {\n      mask = prefix.split(emptyString).concat(mask);\n    }\n\n    if (isNegative) {\n      // If user is entering a negative number, add a mask placeholder spot to attract the caret to it.\n      if (mask.length === prefixLength) {\n        mask.push(digitRegExp);\n      }\n\n      mask = [minusRegExp].concat(mask);\n    }\n\n    if (suffix.length > 0) {\n      mask = mask.concat(suffix.split(emptyString));\n    }\n\n    return mask;\n  }\n\n  numberMask.instanceOf = 'createNumberMask';\n\n  return numberMask;\n}\n\nfunction convertToMask(strNumber) {\n  return strNumber.split(emptyString).map(function (char) {\n    return digitRegExp.test(char) ? digitRegExp : char;\n  });\n}\n\n// http://stackoverflow.com/a/10899795/604296\nfunction addThousandsSeparator(n, thousandsSeparatorSymbol) {\n  return n.replace(/\\B(?=(\\d{3})+(?!\\d))/g, thousandsSeparatorSymbol);\n}"}, {"id": 3, "identifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/addons/src/emailMask.js", "name": "./addons/src/emailMask.js", "index": 5, "index2": 5, "size": 3848, "cacheable": true, "built": true, "optional": false, "prefetched": false, "chunks": [0, 1], "assets": [], "issuer": "multi emailMask", "failed": false, "errors": 0, "warnings": 0, "reasons": [{"moduleId": 0, "moduleIdentifier": "multi emailMask", "module": "multi emailMask", "moduleName": "multi emailMask", "type": "single entry", "userRequest": "/Users/<USER>/Projects/text-mask/addons/src/emailMask.js"}, {"moduleId": 0, "moduleIdentifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/addons/src/index.js", "module": "./addons/src/index.js", "moduleName": "./addons/src/index.js", "type": "cjs require", "userRequest": "./emailMask", "loc": "25:17-39"}], "source": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _emailPipe = require('./emailPipe');\n\nvar _emailPipe2 = _interopRequireDefault(_emailPipe);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar asterisk = '*';\nvar dot = '.';\nvar emptyString = '';\nvar atSymbol = '@';\nvar caretTrap = '[]';\nvar space = ' ';\nvar g = 'g';\nvar anyNonWhitespaceRegExp = /[^\\s]/;\nvar anyNonDotOrWhitespaceRegExp = /[^.\\s]/;\nvar allWhitespaceRegExp = /\\s/g;\n\nfunction emailMask(rawValue, config) {\n  rawValue = rawValue.replace(allWhitespaceRegExp, emptyString);\n\n  var placeholderChar = config.placeholderChar,\n      currentCaretPosition = config.currentCaretPosition;\n\n  var indexOfFirstAtSymbol = rawValue.indexOf(atSymbol);\n  var indexOfLastDot = rawValue.lastIndexOf(dot);\n  var indexOfTopLevelDomainDot = indexOfLastDot < indexOfFirstAtSymbol ? -1 : indexOfLastDot;\n\n  var localPartToDomainConnector = getConnector(rawValue, indexOfFirstAtSymbol + 1, atSymbol);\n  var domainNameToTopLevelDomainConnector = getConnector(rawValue, indexOfTopLevelDomainDot - 1, dot);\n\n  var localPart = getLocalPart(rawValue, indexOfFirstAtSymbol, placeholderChar);\n  var domainName = getDomainName(rawValue, indexOfFirstAtSymbol, indexOfTopLevelDomainDot, placeholderChar);\n  var topLevelDomain = getTopLevelDomain(rawValue, indexOfTopLevelDomainDot, placeholderChar, currentCaretPosition);\n\n  localPart = convertToMask(localPart);\n  domainName = convertToMask(domainName);\n  topLevelDomain = convertToMask(topLevelDomain, true);\n\n  var mask = localPart.concat(localPartToDomainConnector).concat(domainName).concat(domainNameToTopLevelDomainConnector).concat(topLevelDomain);\n\n  return mask;\n}\n\nfunction getConnector(rawValue, indexOfConnection, connectionSymbol) {\n  var connector = [];\n\n  if (rawValue[indexOfConnection] === connectionSymbol) {\n    connector.push(connectionSymbol);\n  } else {\n    connector.push(caretTrap, connectionSymbol);\n  }\n\n  connector.push(caretTrap);\n\n  return connector;\n}\n\nfunction getLocalPart(rawValue, indexOfFirstAtSymbol) {\n  if (indexOfFirstAtSymbol === -1) {\n    return rawValue;\n  } else {\n    return rawValue.slice(0, indexOfFirstAtSymbol);\n  }\n}\n\nfunction getDomainName(rawValue, indexOfFirstAtSymbol, indexOfTopLevelDomainDot, placeholderChar) {\n  var domainName = emptyString;\n\n  if (indexOfFirstAtSymbol !== -1) {\n    if (indexOfTopLevelDomainDot === -1) {\n      domainName = rawValue.slice(indexOfFirstAtSymbol + 1, rawValue.length);\n    } else {\n      domainName = rawValue.slice(indexOfFirstAtSymbol + 1, indexOfTopLevelDomainDot);\n    }\n  }\n\n  domainName = domainName.replace(new RegExp('[\\\\s' + placeholderChar + ']', g), emptyString);\n\n  if (domainName === atSymbol) {\n    return asterisk;\n  } else if (domainName.length < 1) {\n    return space;\n  } else if (domainName[domainName.length - 1] === dot) {\n    return domainName.slice(0, domainName.length - 1);\n  } else {\n    return domainName;\n  }\n}\n\nfunction getTopLevelDomain(rawValue, indexOfTopLevelDomainDot, placeholderChar, currentCaretPosition) {\n  var topLevelDomain = emptyString;\n\n  if (indexOfTopLevelDomainDot !== -1) {\n    topLevelDomain = rawValue.slice(indexOfTopLevelDomainDot + 1, rawValue.length);\n  }\n\n  topLevelDomain = topLevelDomain.replace(new RegExp('[\\\\s' + placeholderChar + '.]', g), emptyString);\n\n  if (topLevelDomain.length === 0) {\n    return rawValue[indexOfTopLevelDomainDot - 1] === dot && currentCaretPosition !== rawValue.length ? asterisk : emptyString;\n  } else {\n    return topLevelDomain;\n  }\n}\n\nfunction convertToMask(str, noDots) {\n  return str.split(emptyString).map(function (char) {\n    return char === space ? char : noDots ? anyNonDotOrWhitespaceRegExp : anyNonWhitespaceRegExp;\n  });\n}\n\nexports.default = { mask: emailMask, pipe: _emailPipe2.default };"}, {"id": 4, "identifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/addons/src/emailPipe.js", "name": "./addons/src/emailPipe.js", "index": 6, "index2": 4, "size": 1571, "cacheable": true, "built": true, "optional": false, "prefetched": false, "chunks": [0, 1], "assets": [], "issuer": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/addons/src/emailMask.js", "failed": false, "errors": 0, "warnings": 0, "reasons": [{"moduleId": 3, "moduleIdentifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/addons/src/emailMask.js", "module": "./addons/src/emailMask.js", "moduleName": "./addons/src/emailMask.js", "type": "cjs require", "userRequest": "./emailPipe", "loc": "7:17-39"}], "source": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = emailPipe;\nvar atSymbol = '@';\nvar allAtSymbolsRegExp = /@/g;\nvar emptyString = '';\nvar atDot = '@.';\nvar dot = '.';\nvar dotDot = '..';\nvar emptyArray = [];\nvar allDotsRegExp = /\\./g;\n\nfunction emailPipe(conformedValue, config) {\n  var currentCaretPosition = config.currentCaretPosition,\n      rawValue = config.rawValue,\n      previousConformedValue = config.previousConformedValue,\n      placeholderChar = config.placeholderChar;\n\n\n  var value = conformedValue;\n\n  value = removeAllAtSymbolsButFirst(value);\n\n  var indexOfAtDot = value.indexOf(atDot);\n\n  var emptyEmail = rawValue.match(new RegExp('[^@\\\\s.' + placeholderChar + ']')) === null;\n\n  if (emptyEmail) {\n    return emptyString;\n  }\n\n  if (value.indexOf(dotDot) !== -1 || indexOfAtDot !== -1 && currentCaretPosition !== indexOfAtDot + 1 || rawValue.indexOf(atSymbol) === -1 && previousConformedValue !== emptyString && rawValue.indexOf(dot) !== -1) {\n    return false;\n  }\n\n  var indexOfAtSymbol = value.indexOf(atSymbol);\n  var domainPart = value.slice(indexOfAtSymbol + 1, value.length);\n\n  if ((domainPart.match(allDotsRegExp) || emptyArray).length > 1 && value.substr(-1) === dot && currentCaretPosition !== rawValue.length) {\n    value = value.slice(0, value.length - 1);\n  }\n\n  return value;\n}\n\nfunction removeAllAtSymbolsButFirst(str) {\n  var atSymbolCount = 0;\n\n  return str.replace(allAtSymbolsRegExp, function () {\n    atSymbolCount++;\n\n    return atSymbolCount === 1 ? atSymbol : emptyString;\n  });\n}"}], "filteredModules": 0, "origins": [{"moduleId": 0, "module": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/addons/src/index.js", "moduleIdentifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/addons/src/index.js", "moduleName": "./addons/src/index.js", "loc": "", "name": "textMaskAddons", "reasons": []}]}, {"id": 1, "rendered": true, "initial": true, "entry": true, "extraAsync": false, "size": 5447, "names": ["emailMask"], "files": ["emailMask.js"], "hash": "58c5a19613807ab3e3c6", "parents": [], "modules": [{"id": 0, "identifier": "multi emailMask", "name": "multi emailMask", "index": 4, "index2": 6, "size": 28, "cacheable": true, "built": true, "optional": false, "prefetched": false, "chunks": [1], "assets": [], "issuer": null, "failed": false, "errors": 0, "warnings": 0, "reasons": []}, {"id": 3, "identifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/addons/src/emailMask.js", "name": "./addons/src/emailMask.js", "index": 5, "index2": 5, "size": 3848, "cacheable": true, "built": true, "optional": false, "prefetched": false, "chunks": [0, 1], "assets": [], "issuer": "multi emailMask", "failed": false, "errors": 0, "warnings": 0, "reasons": [{"moduleId": 0, "moduleIdentifier": "multi emailMask", "module": "multi emailMask", "moduleName": "multi emailMask", "type": "single entry", "userRequest": "/Users/<USER>/Projects/text-mask/addons/src/emailMask.js"}, {"moduleId": 0, "moduleIdentifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/addons/src/index.js", "module": "./addons/src/index.js", "moduleName": "./addons/src/index.js", "type": "cjs require", "userRequest": "./emailMask", "loc": "25:17-39"}], "source": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _emailPipe = require('./emailPipe');\n\nvar _emailPipe2 = _interopRequireDefault(_emailPipe);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar asterisk = '*';\nvar dot = '.';\nvar emptyString = '';\nvar atSymbol = '@';\nvar caretTrap = '[]';\nvar space = ' ';\nvar g = 'g';\nvar anyNonWhitespaceRegExp = /[^\\s]/;\nvar anyNonDotOrWhitespaceRegExp = /[^.\\s]/;\nvar allWhitespaceRegExp = /\\s/g;\n\nfunction emailMask(rawValue, config) {\n  rawValue = rawValue.replace(allWhitespaceRegExp, emptyString);\n\n  var placeholderChar = config.placeholderChar,\n      currentCaretPosition = config.currentCaretPosition;\n\n  var indexOfFirstAtSymbol = rawValue.indexOf(atSymbol);\n  var indexOfLastDot = rawValue.lastIndexOf(dot);\n  var indexOfTopLevelDomainDot = indexOfLastDot < indexOfFirstAtSymbol ? -1 : indexOfLastDot;\n\n  var localPartToDomainConnector = getConnector(rawValue, indexOfFirstAtSymbol + 1, atSymbol);\n  var domainNameToTopLevelDomainConnector = getConnector(rawValue, indexOfTopLevelDomainDot - 1, dot);\n\n  var localPart = getLocalPart(rawValue, indexOfFirstAtSymbol, placeholderChar);\n  var domainName = getDomainName(rawValue, indexOfFirstAtSymbol, indexOfTopLevelDomainDot, placeholderChar);\n  var topLevelDomain = getTopLevelDomain(rawValue, indexOfTopLevelDomainDot, placeholderChar, currentCaretPosition);\n\n  localPart = convertToMask(localPart);\n  domainName = convertToMask(domainName);\n  topLevelDomain = convertToMask(topLevelDomain, true);\n\n  var mask = localPart.concat(localPartToDomainConnector).concat(domainName).concat(domainNameToTopLevelDomainConnector).concat(topLevelDomain);\n\n  return mask;\n}\n\nfunction getConnector(rawValue, indexOfConnection, connectionSymbol) {\n  var connector = [];\n\n  if (rawValue[indexOfConnection] === connectionSymbol) {\n    connector.push(connectionSymbol);\n  } else {\n    connector.push(caretTrap, connectionSymbol);\n  }\n\n  connector.push(caretTrap);\n\n  return connector;\n}\n\nfunction getLocalPart(rawValue, indexOfFirstAtSymbol) {\n  if (indexOfFirstAtSymbol === -1) {\n    return rawValue;\n  } else {\n    return rawValue.slice(0, indexOfFirstAtSymbol);\n  }\n}\n\nfunction getDomainName(rawValue, indexOfFirstAtSymbol, indexOfTopLevelDomainDot, placeholderChar) {\n  var domainName = emptyString;\n\n  if (indexOfFirstAtSymbol !== -1) {\n    if (indexOfTopLevelDomainDot === -1) {\n      domainName = rawValue.slice(indexOfFirstAtSymbol + 1, rawValue.length);\n    } else {\n      domainName = rawValue.slice(indexOfFirstAtSymbol + 1, indexOfTopLevelDomainDot);\n    }\n  }\n\n  domainName = domainName.replace(new RegExp('[\\\\s' + placeholderChar + ']', g), emptyString);\n\n  if (domainName === atSymbol) {\n    return asterisk;\n  } else if (domainName.length < 1) {\n    return space;\n  } else if (domainName[domainName.length - 1] === dot) {\n    return domainName.slice(0, domainName.length - 1);\n  } else {\n    return domainName;\n  }\n}\n\nfunction getTopLevelDomain(rawValue, indexOfTopLevelDomainDot, placeholderChar, currentCaretPosition) {\n  var topLevelDomain = emptyString;\n\n  if (indexOfTopLevelDomainDot !== -1) {\n    topLevelDomain = rawValue.slice(indexOfTopLevelDomainDot + 1, rawValue.length);\n  }\n\n  topLevelDomain = topLevelDomain.replace(new RegExp('[\\\\s' + placeholderChar + '.]', g), emptyString);\n\n  if (topLevelDomain.length === 0) {\n    return rawValue[indexOfTopLevelDomainDot - 1] === dot && currentCaretPosition !== rawValue.length ? asterisk : emptyString;\n  } else {\n    return topLevelDomain;\n  }\n}\n\nfunction convertToMask(str, noDots) {\n  return str.split(emptyString).map(function (char) {\n    return char === space ? char : noDots ? anyNonDotOrWhitespaceRegExp : anyNonWhitespaceRegExp;\n  });\n}\n\nexports.default = { mask: emailMask, pipe: _emailPipe2.default };"}, {"id": 4, "identifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/addons/src/emailPipe.js", "name": "./addons/src/emailPipe.js", "index": 6, "index2": 4, "size": 1571, "cacheable": true, "built": true, "optional": false, "prefetched": false, "chunks": [0, 1], "assets": [], "issuer": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/addons/src/emailMask.js", "failed": false, "errors": 0, "warnings": 0, "reasons": [{"moduleId": 3, "moduleIdentifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/addons/src/emailMask.js", "module": "./addons/src/emailMask.js", "moduleName": "./addons/src/emailMask.js", "type": "cjs require", "userRequest": "./emailPipe", "loc": "7:17-39"}], "source": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = emailPipe;\nvar atSymbol = '@';\nvar allAtSymbolsRegExp = /@/g;\nvar emptyString = '';\nvar atDot = '@.';\nvar dot = '.';\nvar dotDot = '..';\nvar emptyArray = [];\nvar allDotsRegExp = /\\./g;\n\nfunction emailPipe(conformedValue, config) {\n  var currentCaretPosition = config.currentCaretPosition,\n      rawValue = config.rawValue,\n      previousConformedValue = config.previousConformedValue,\n      placeholderChar = config.placeholderChar;\n\n\n  var value = conformedValue;\n\n  value = removeAllAtSymbolsButFirst(value);\n\n  var indexOfAtDot = value.indexOf(atDot);\n\n  var emptyEmail = rawValue.match(new RegExp('[^@\\\\s.' + placeholderChar + ']')) === null;\n\n  if (emptyEmail) {\n    return emptyString;\n  }\n\n  if (value.indexOf(dotDot) !== -1 || indexOfAtDot !== -1 && currentCaretPosition !== indexOfAtDot + 1 || rawValue.indexOf(atSymbol) === -1 && previousConformedValue !== emptyString && rawValue.indexOf(dot) !== -1) {\n    return false;\n  }\n\n  var indexOfAtSymbol = value.indexOf(atSymbol);\n  var domainPart = value.slice(indexOfAtSymbol + 1, value.length);\n\n  if ((domainPart.match(allDotsRegExp) || emptyArray).length > 1 && value.substr(-1) === dot && currentCaretPosition !== rawValue.length) {\n    value = value.slice(0, value.length - 1);\n  }\n\n  return value;\n}\n\nfunction removeAllAtSymbolsButFirst(str) {\n  var atSymbolCount = 0;\n\n  return str.replace(allAtSymbolsRegExp, function () {\n    atSymbolCount++;\n\n    return atSymbolCount === 1 ? atSymbol : emptyString;\n  });\n}"}], "filteredModules": 0, "origins": [{"moduleId": 0, "module": "multi emailMask", "moduleIdentifier": "multi emailMask", "moduleName": "multi emailMask", "loc": "", "name": "emailMask", "reasons": []}]}, {"id": 2, "rendered": true, "initial": true, "entry": true, "extraAsync": false, "size": 6177, "names": ["createNumberMask"], "files": ["createNumberMask.js"], "hash": "dca1d206eb329515c9cd", "parents": [], "modules": [{"id": 0, "identifier": "multi createNumberMask", "name": "multi createNumberMask", "index": 2, "index2": 3, "size": 28, "cacheable": true, "built": true, "optional": false, "prefetched": false, "chunks": [2], "assets": [], "issuer": null, "failed": false, "errors": 0, "warnings": 0, "reasons": []}, {"id": 2, "identifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/addons/src/createNumberMask.js", "name": "./addons/src/createNumberMask.js", "index": 3, "index2": 2, "size": 6149, "cacheable": true, "built": true, "optional": false, "prefetched": false, "chunks": [0, 2], "assets": [], "issuer": "multi createNumberMask", "failed": false, "errors": 0, "warnings": 0, "reasons": [{"moduleId": 0, "moduleIdentifier": "multi createNumberMask", "module": "multi createNumberMask", "moduleName": "multi createNumberMask", "type": "single entry", "userRequest": "/Users/<USER>/Projects/text-mask/addons/src/createNumberMask.js"}, {"moduleId": 0, "moduleIdentifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/addons/src/index.js", "module": "./addons/src/index.js", "moduleName": "./addons/src/index.js", "type": "cjs require", "userRequest": "./createNumberMask", "loc": "16:24-53"}], "source": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; };\n\nexports.default = createNumberMask;\nvar dollarSign = '$';\nvar emptyString = '';\nvar comma = ',';\nvar period = '.';\nvar minus = '-';\nvar minusRegExp = /-/;\nvar nonDigitsRegExp = /\\D+/g;\nvar number = 'number';\nvar digitRegExp = /\\d/;\nvar caretTrap = '[]';\n\nfunction createNumberMask() {\n  var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n      _ref$prefix = _ref.prefix,\n      prefix = _ref$prefix === undefined ? dollarSign : _ref$prefix,\n      _ref$suffix = _ref.suffix,\n      suffix = _ref$suffix === undefined ? emptyString : _ref$suffix,\n      _ref$includeThousands = _ref.includeThousandsSeparator,\n      includeThousandsSeparator = _ref$includeThousands === undefined ? true : _ref$includeThousands,\n      _ref$thousandsSeparat = _ref.thousandsSeparatorSymbol,\n      thousandsSeparatorSymbol = _ref$thousandsSeparat === undefined ? comma : _ref$thousandsSeparat,\n      _ref$allowDecimal = _ref.allowDecimal,\n      allowDecimal = _ref$allowDecimal === undefined ? false : _ref$allowDecimal,\n      _ref$decimalSymbol = _ref.decimalSymbol,\n      decimalSymbol = _ref$decimalSymbol === undefined ? period : _ref$decimalSymbol,\n      _ref$decimalLimit = _ref.decimalLimit,\n      decimalLimit = _ref$decimalLimit === undefined ? 2 : _ref$decimalLimit,\n      _ref$requireDecimal = _ref.requireDecimal,\n      requireDecimal = _ref$requireDecimal === undefined ? false : _ref$requireDecimal,\n      _ref$allowNegative = _ref.allowNegative,\n      allowNegative = _ref$allowNegative === undefined ? false : _ref$allowNegative,\n      _ref$allowLeadingZero = _ref.allowLeadingZeroes,\n      allowLeadingZeroes = _ref$allowLeadingZero === undefined ? false : _ref$allowLeadingZero,\n      _ref$integerLimit = _ref.integerLimit,\n      integerLimit = _ref$integerLimit === undefined ? null : _ref$integerLimit;\n\n  var prefixLength = prefix && prefix.length || 0;\n  var suffixLength = suffix && suffix.length || 0;\n  var thousandsSeparatorSymbolLength = thousandsSeparatorSymbol && thousandsSeparatorSymbol.length || 0;\n\n  function numberMask() {\n    var rawValue = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : emptyString;\n\n    var rawValueLength = rawValue.length;\n\n    if (rawValue === emptyString || rawValue[0] === prefix[0] && rawValueLength === 1) {\n      return prefix.split(emptyString).concat([digitRegExp]).concat(suffix.split(emptyString));\n    } else if (rawValue === decimalSymbol && allowDecimal) {\n      return prefix.split(emptyString).concat(['0', decimalSymbol, digitRegExp]).concat(suffix.split(emptyString));\n    }\n\n    var isNegative = rawValue[0] === minus && allowNegative;\n    //If negative remove \"-\" sign\n    if (isNegative) {\n      rawValue = rawValue.toString().substr(1);\n    }\n\n    var indexOfLastDecimal = rawValue.lastIndexOf(decimalSymbol);\n    var hasDecimal = indexOfLastDecimal !== -1;\n\n    var integer = void 0;\n    var fraction = void 0;\n    var mask = void 0;\n\n    // remove the suffix\n    if (rawValue.slice(suffixLength * -1) === suffix) {\n      rawValue = rawValue.slice(0, suffixLength * -1);\n    }\n\n    if (hasDecimal && (allowDecimal || requireDecimal)) {\n      integer = rawValue.slice(rawValue.slice(0, prefixLength) === prefix ? prefixLength : 0, indexOfLastDecimal);\n\n      fraction = rawValue.slice(indexOfLastDecimal + 1, rawValueLength);\n      fraction = convertToMask(fraction.replace(nonDigitsRegExp, emptyString));\n    } else {\n      if (rawValue.slice(0, prefixLength) === prefix) {\n        integer = rawValue.slice(prefixLength);\n      } else {\n        integer = rawValue;\n      }\n    }\n\n    if (integerLimit && (typeof integerLimit === 'undefined' ? 'undefined' : _typeof(integerLimit)) === number) {\n      var thousandsSeparatorRegex = thousandsSeparatorSymbol === '.' ? '[.]' : '' + thousandsSeparatorSymbol;\n      var numberOfThousandSeparators = (integer.match(new RegExp(thousandsSeparatorRegex, 'g')) || []).length;\n\n      integer = integer.slice(0, integerLimit + numberOfThousandSeparators * thousandsSeparatorSymbolLength);\n    }\n\n    integer = integer.replace(nonDigitsRegExp, emptyString);\n\n    if (!allowLeadingZeroes) {\n      integer = integer.replace(/^0+(0$|[^0])/, '$1');\n    }\n\n    integer = includeThousandsSeparator ? addThousandsSeparator(integer, thousandsSeparatorSymbol) : integer;\n\n    mask = convertToMask(integer);\n\n    if (hasDecimal && allowDecimal || requireDecimal === true) {\n      if (rawValue[indexOfLastDecimal - 1] !== decimalSymbol) {\n        mask.push(caretTrap);\n      }\n\n      mask.push(decimalSymbol, caretTrap);\n\n      if (fraction) {\n        if ((typeof decimalLimit === 'undefined' ? 'undefined' : _typeof(decimalLimit)) === number) {\n          fraction = fraction.slice(0, decimalLimit);\n        }\n\n        mask = mask.concat(fraction);\n      }\n\n      if (requireDecimal === true && rawValue[indexOfLastDecimal - 1] === decimalSymbol) {\n        mask.push(digitRegExp);\n      }\n    }\n\n    if (prefixLength > 0) {\n      mask = prefix.split(emptyString).concat(mask);\n    }\n\n    if (isNegative) {\n      // If user is entering a negative number, add a mask placeholder spot to attract the caret to it.\n      if (mask.length === prefixLength) {\n        mask.push(digitRegExp);\n      }\n\n      mask = [minusRegExp].concat(mask);\n    }\n\n    if (suffix.length > 0) {\n      mask = mask.concat(suffix.split(emptyString));\n    }\n\n    return mask;\n  }\n\n  numberMask.instanceOf = 'createNumberMask';\n\n  return numberMask;\n}\n\nfunction convertToMask(strNumber) {\n  return strNumber.split(emptyString).map(function (char) {\n    return digitRegExp.test(char) ? digitRegExp : char;\n  });\n}\n\n// http://stackoverflow.com/a/10899795/604296\nfunction addThousandsSeparator(n, thousandsSeparatorSymbol) {\n  return n.replace(/\\B(?=(\\d{3})+(?!\\d))/g, thousandsSeparatorSymbol);\n}"}], "filteredModules": 0, "origins": [{"moduleId": 0, "module": "multi createNumberMask", "moduleIdentifier": "multi createNumberMask", "moduleName": "multi createNumberMask", "loc": "", "name": "createNumberMask", "reasons": []}]}, {"id": 3, "rendered": true, "initial": true, "entry": true, "extraAsync": false, "size": 2725, "names": ["createAutoCorrectedDatePipe"], "files": ["createAutoCorrectedDatePipe.js"], "hash": "0b771778f08c8056164f", "parents": [], "modules": [{"id": 0, "identifier": "multi createAutoCorrectedDatePipe", "name": "multi createAutoCorrectedDatePipe", "index": 0, "index2": 1, "size": 28, "cacheable": true, "built": true, "optional": false, "prefetched": false, "chunks": [3], "assets": [], "issuer": null, "failed": false, "errors": 0, "warnings": 0, "reasons": []}, {"id": 1, "identifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/addons/src/createAutoCorrectedDatePipe.js", "name": "./addons/src/createAutoCorrectedDatePipe.js", "index": 1, "index2": 0, "size": 2697, "cacheable": true, "built": true, "optional": false, "prefetched": false, "chunks": [0, 3], "assets": [], "issuer": "multi createAutoCorrectedDatePipe", "failed": false, "errors": 0, "warnings": 0, "reasons": [{"moduleId": 0, "moduleIdentifier": "multi createAutoCorrectedDatePipe", "module": "multi createAutoCorrectedDatePipe", "moduleName": "multi createAutoCorrectedDatePipe", "type": "single entry", "userRequest": "/Users/<USER>/Projects/text-mask/addons/src/createAutoCorrectedDatePipe.js"}, {"moduleId": 0, "moduleIdentifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/addons/src/index.js", "module": "./addons/src/index.js", "moduleName": "./addons/src/index.js", "type": "cjs require", "userRequest": "./createAutoCorrectedDatePipe", "loc": "7:35-75"}], "source": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = createAutoCorrectedDatePipe;\nvar maxValueMonth = [31, 31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];\nvar formatOrder = ['yyyy', 'yy', 'mm', 'dd', 'HH', 'MM', 'SS'];\nfunction createAutoCorrectedDatePipe() {\n  var dateFormat = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'mm dd yyyy';\n\n  var _ref = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},\n      _ref$minYear = _ref.minYear,\n      minYear = _ref$minYear === undefined ? 1 : _ref$minYear,\n      _ref$maxYear = _ref.maxYear,\n      maxYear = _ref$maxYear === undefined ? 9999 : _ref$maxYear;\n\n  var dateFormatArray = dateFormat.split(/[^dmyHMS]+/).sort(function (a, b) {\n    return formatOrder.indexOf(a) - formatOrder.indexOf(b);\n  });\n  return function (conformedValue) {\n    var indexesOfPipedChars = [];\n    var maxValue = { 'dd': 31, 'mm': 12, 'yy': 99, 'yyyy': maxYear, 'HH': 23, 'MM': 59, 'SS': 59 };\n    var minValue = { 'dd': 1, 'mm': 1, 'yy': 0, 'yyyy': minYear, 'HH': 0, 'MM': 0, 'SS': 0 };\n    var conformedValueArr = conformedValue.split('');\n\n    // Check first digit\n    dateFormatArray.forEach(function (format) {\n      var position = dateFormat.indexOf(format);\n      var maxFirstDigit = parseInt(maxValue[format].toString().substr(0, 1), 10);\n\n      if (parseInt(conformedValueArr[position], 10) > maxFirstDigit) {\n        conformedValueArr[position + 1] = conformedValueArr[position];\n        conformedValueArr[position] = 0;\n        indexesOfPipedChars.push(position);\n      }\n    });\n\n    // Check for invalid date\n    var month = 0;\n    var isInvalid = dateFormatArray.some(function (format) {\n      var position = dateFormat.indexOf(format);\n      var length = format.length;\n      var textValue = conformedValue.substr(position, length).replace(/\\D/g, '');\n      var value = parseInt(textValue, 10);\n      if (format === 'mm') {\n        month = value || 0;\n      }\n      var maxValueForFormat = format === 'dd' ? maxValueMonth[month] : maxValue[format];\n      if (format === 'yyyy' && (minYear !== 1 || maxYear !== 9999)) {\n        var scopedMaxValue = parseInt(maxValue[format].toString().substring(0, textValue.length), 10);\n        var scopedMinValue = parseInt(minValue[format].toString().substring(0, textValue.length), 10);\n        return value < scopedMinValue || value > scopedMaxValue;\n      }\n      return value > maxValueForFormat || textValue.length === length && value < minValue[format];\n    });\n\n    if (isInvalid) {\n      return false;\n    }\n\n    return {\n      value: conformedValueArr.join(''),\n      indexesOfPipedChars: indexesOfPipedChars\n    };\n  };\n}"}], "filteredModules": 0, "origins": [{"moduleId": 0, "module": "multi createAutoCorrectedDatePipe", "moduleIdentifier": "multi createAutoCorrectedDatePipe", "moduleName": "multi createAutoCorrectedDatePipe", "loc": "", "name": "createAutoCorrectedDatePipe", "reasons": []}]}], "modules": [{"id": 0, "identifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/addons/src/index.js", "name": "./addons/src/index.js", "index": 7, "index2": 7, "size": 864, "cacheable": true, "built": true, "optional": false, "prefetched": false, "chunks": [0], "assets": [], "issuer": null, "failed": false, "errors": 0, "warnings": 0, "reasons": [], "source": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _createAutoCorrectedDatePipe = require('./createAutoCorrectedDatePipe');\n\nObject.defineProperty(exports, 'createAutoCorrectedDatePipe', {\n  enumerable: true,\n  get: function get() {\n    return _interopRequireDefault(_createAutoCorrectedDatePipe).default;\n  }\n});\n\nvar _createNumberMask = require('./createNumberMask');\n\nObject.defineProperty(exports, 'createNumberMask', {\n  enumerable: true,\n  get: function get() {\n    return _interopRequireDefault(_createNumberMask).default;\n  }\n});\n\nvar _emailMask = require('./emailMask');\n\nObject.defineProperty(exports, 'emailMask', {\n  enumerable: true,\n  get: function get() {\n    return _interopRequireDefault(_emailMask).default;\n  }\n});\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }"}, {"id": 0, "identifier": "multi createAutoCorrectedDatePipe", "name": "multi createAutoCorrectedDatePipe", "index": 0, "index2": 1, "size": 28, "cacheable": true, "built": true, "optional": false, "prefetched": false, "chunks": [3], "assets": [], "issuer": null, "failed": false, "errors": 0, "warnings": 0, "reasons": []}, {"id": 0, "identifier": "multi createNumberMask", "name": "multi createNumberMask", "index": 2, "index2": 3, "size": 28, "cacheable": true, "built": true, "optional": false, "prefetched": false, "chunks": [2], "assets": [], "issuer": null, "failed": false, "errors": 0, "warnings": 0, "reasons": []}, {"id": 0, "identifier": "multi emailMask", "name": "multi emailMask", "index": 4, "index2": 6, "size": 28, "cacheable": true, "built": true, "optional": false, "prefetched": false, "chunks": [1], "assets": [], "issuer": null, "failed": false, "errors": 0, "warnings": 0, "reasons": []}, {"id": 1, "identifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/addons/src/createAutoCorrectedDatePipe.js", "name": "./addons/src/createAutoCorrectedDatePipe.js", "index": 1, "index2": 0, "size": 2697, "cacheable": true, "built": true, "optional": false, "prefetched": false, "chunks": [0, 3], "assets": [], "issuer": "multi createAutoCorrectedDatePipe", "failed": false, "errors": 0, "warnings": 0, "reasons": [{"moduleId": 0, "moduleIdentifier": "multi createAutoCorrectedDatePipe", "module": "multi createAutoCorrectedDatePipe", "moduleName": "multi createAutoCorrectedDatePipe", "type": "single entry", "userRequest": "/Users/<USER>/Projects/text-mask/addons/src/createAutoCorrectedDatePipe.js"}, {"moduleId": 0, "moduleIdentifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/addons/src/index.js", "module": "./addons/src/index.js", "moduleName": "./addons/src/index.js", "type": "cjs require", "userRequest": "./createAutoCorrectedDatePipe", "loc": "7:35-75"}], "source": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = createAutoCorrectedDatePipe;\nvar maxValueMonth = [31, 31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];\nvar formatOrder = ['yyyy', 'yy', 'mm', 'dd', 'HH', 'MM', 'SS'];\nfunction createAutoCorrectedDatePipe() {\n  var dateFormat = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'mm dd yyyy';\n\n  var _ref = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},\n      _ref$minYear = _ref.minYear,\n      minYear = _ref$minYear === undefined ? 1 : _ref$minYear,\n      _ref$maxYear = _ref.maxYear,\n      maxYear = _ref$maxYear === undefined ? 9999 : _ref$maxYear;\n\n  var dateFormatArray = dateFormat.split(/[^dmyHMS]+/).sort(function (a, b) {\n    return formatOrder.indexOf(a) - formatOrder.indexOf(b);\n  });\n  return function (conformedValue) {\n    var indexesOfPipedChars = [];\n    var maxValue = { 'dd': 31, 'mm': 12, 'yy': 99, 'yyyy': maxYear, 'HH': 23, 'MM': 59, 'SS': 59 };\n    var minValue = { 'dd': 1, 'mm': 1, 'yy': 0, 'yyyy': minYear, 'HH': 0, 'MM': 0, 'SS': 0 };\n    var conformedValueArr = conformedValue.split('');\n\n    // Check first digit\n    dateFormatArray.forEach(function (format) {\n      var position = dateFormat.indexOf(format);\n      var maxFirstDigit = parseInt(maxValue[format].toString().substr(0, 1), 10);\n\n      if (parseInt(conformedValueArr[position], 10) > maxFirstDigit) {\n        conformedValueArr[position + 1] = conformedValueArr[position];\n        conformedValueArr[position] = 0;\n        indexesOfPipedChars.push(position);\n      }\n    });\n\n    // Check for invalid date\n    var month = 0;\n    var isInvalid = dateFormatArray.some(function (format) {\n      var position = dateFormat.indexOf(format);\n      var length = format.length;\n      var textValue = conformedValue.substr(position, length).replace(/\\D/g, '');\n      var value = parseInt(textValue, 10);\n      if (format === 'mm') {\n        month = value || 0;\n      }\n      var maxValueForFormat = format === 'dd' ? maxValueMonth[month] : maxValue[format];\n      if (format === 'yyyy' && (minYear !== 1 || maxYear !== 9999)) {\n        var scopedMaxValue = parseInt(maxValue[format].toString().substring(0, textValue.length), 10);\n        var scopedMinValue = parseInt(minValue[format].toString().substring(0, textValue.length), 10);\n        return value < scopedMinValue || value > scopedMaxValue;\n      }\n      return value > maxValueForFormat || textValue.length === length && value < minValue[format];\n    });\n\n    if (isInvalid) {\n      return false;\n    }\n\n    return {\n      value: conformedValueArr.join(''),\n      indexesOfPipedChars: indexesOfPipedChars\n    };\n  };\n}"}, {"id": 2, "identifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/addons/src/createNumberMask.js", "name": "./addons/src/createNumberMask.js", "index": 3, "index2": 2, "size": 6149, "cacheable": true, "built": true, "optional": false, "prefetched": false, "chunks": [0, 2], "assets": [], "issuer": "multi createNumberMask", "failed": false, "errors": 0, "warnings": 0, "reasons": [{"moduleId": 0, "moduleIdentifier": "multi createNumberMask", "module": "multi createNumberMask", "moduleName": "multi createNumberMask", "type": "single entry", "userRequest": "/Users/<USER>/Projects/text-mask/addons/src/createNumberMask.js"}, {"moduleId": 0, "moduleIdentifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/addons/src/index.js", "module": "./addons/src/index.js", "moduleName": "./addons/src/index.js", "type": "cjs require", "userRequest": "./createNumberMask", "loc": "16:24-53"}], "source": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; };\n\nexports.default = createNumberMask;\nvar dollarSign = '$';\nvar emptyString = '';\nvar comma = ',';\nvar period = '.';\nvar minus = '-';\nvar minusRegExp = /-/;\nvar nonDigitsRegExp = /\\D+/g;\nvar number = 'number';\nvar digitRegExp = /\\d/;\nvar caretTrap = '[]';\n\nfunction createNumberMask() {\n  var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n      _ref$prefix = _ref.prefix,\n      prefix = _ref$prefix === undefined ? dollarSign : _ref$prefix,\n      _ref$suffix = _ref.suffix,\n      suffix = _ref$suffix === undefined ? emptyString : _ref$suffix,\n      _ref$includeThousands = _ref.includeThousandsSeparator,\n      includeThousandsSeparator = _ref$includeThousands === undefined ? true : _ref$includeThousands,\n      _ref$thousandsSeparat = _ref.thousandsSeparatorSymbol,\n      thousandsSeparatorSymbol = _ref$thousandsSeparat === undefined ? comma : _ref$thousandsSeparat,\n      _ref$allowDecimal = _ref.allowDecimal,\n      allowDecimal = _ref$allowDecimal === undefined ? false : _ref$allowDecimal,\n      _ref$decimalSymbol = _ref.decimalSymbol,\n      decimalSymbol = _ref$decimalSymbol === undefined ? period : _ref$decimalSymbol,\n      _ref$decimalLimit = _ref.decimalLimit,\n      decimalLimit = _ref$decimalLimit === undefined ? 2 : _ref$decimalLimit,\n      _ref$requireDecimal = _ref.requireDecimal,\n      requireDecimal = _ref$requireDecimal === undefined ? false : _ref$requireDecimal,\n      _ref$allowNegative = _ref.allowNegative,\n      allowNegative = _ref$allowNegative === undefined ? false : _ref$allowNegative,\n      _ref$allowLeadingZero = _ref.allowLeadingZeroes,\n      allowLeadingZeroes = _ref$allowLeadingZero === undefined ? false : _ref$allowLeadingZero,\n      _ref$integerLimit = _ref.integerLimit,\n      integerLimit = _ref$integerLimit === undefined ? null : _ref$integerLimit;\n\n  var prefixLength = prefix && prefix.length || 0;\n  var suffixLength = suffix && suffix.length || 0;\n  var thousandsSeparatorSymbolLength = thousandsSeparatorSymbol && thousandsSeparatorSymbol.length || 0;\n\n  function numberMask() {\n    var rawValue = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : emptyString;\n\n    var rawValueLength = rawValue.length;\n\n    if (rawValue === emptyString || rawValue[0] === prefix[0] && rawValueLength === 1) {\n      return prefix.split(emptyString).concat([digitRegExp]).concat(suffix.split(emptyString));\n    } else if (rawValue === decimalSymbol && allowDecimal) {\n      return prefix.split(emptyString).concat(['0', decimalSymbol, digitRegExp]).concat(suffix.split(emptyString));\n    }\n\n    var isNegative = rawValue[0] === minus && allowNegative;\n    //If negative remove \"-\" sign\n    if (isNegative) {\n      rawValue = rawValue.toString().substr(1);\n    }\n\n    var indexOfLastDecimal = rawValue.lastIndexOf(decimalSymbol);\n    var hasDecimal = indexOfLastDecimal !== -1;\n\n    var integer = void 0;\n    var fraction = void 0;\n    var mask = void 0;\n\n    // remove the suffix\n    if (rawValue.slice(suffixLength * -1) === suffix) {\n      rawValue = rawValue.slice(0, suffixLength * -1);\n    }\n\n    if (hasDecimal && (allowDecimal || requireDecimal)) {\n      integer = rawValue.slice(rawValue.slice(0, prefixLength) === prefix ? prefixLength : 0, indexOfLastDecimal);\n\n      fraction = rawValue.slice(indexOfLastDecimal + 1, rawValueLength);\n      fraction = convertToMask(fraction.replace(nonDigitsRegExp, emptyString));\n    } else {\n      if (rawValue.slice(0, prefixLength) === prefix) {\n        integer = rawValue.slice(prefixLength);\n      } else {\n        integer = rawValue;\n      }\n    }\n\n    if (integerLimit && (typeof integerLimit === 'undefined' ? 'undefined' : _typeof(integerLimit)) === number) {\n      var thousandsSeparatorRegex = thousandsSeparatorSymbol === '.' ? '[.]' : '' + thousandsSeparatorSymbol;\n      var numberOfThousandSeparators = (integer.match(new RegExp(thousandsSeparatorRegex, 'g')) || []).length;\n\n      integer = integer.slice(0, integerLimit + numberOfThousandSeparators * thousandsSeparatorSymbolLength);\n    }\n\n    integer = integer.replace(nonDigitsRegExp, emptyString);\n\n    if (!allowLeadingZeroes) {\n      integer = integer.replace(/^0+(0$|[^0])/, '$1');\n    }\n\n    integer = includeThousandsSeparator ? addThousandsSeparator(integer, thousandsSeparatorSymbol) : integer;\n\n    mask = convertToMask(integer);\n\n    if (hasDecimal && allowDecimal || requireDecimal === true) {\n      if (rawValue[indexOfLastDecimal - 1] !== decimalSymbol) {\n        mask.push(caretTrap);\n      }\n\n      mask.push(decimalSymbol, caretTrap);\n\n      if (fraction) {\n        if ((typeof decimalLimit === 'undefined' ? 'undefined' : _typeof(decimalLimit)) === number) {\n          fraction = fraction.slice(0, decimalLimit);\n        }\n\n        mask = mask.concat(fraction);\n      }\n\n      if (requireDecimal === true && rawValue[indexOfLastDecimal - 1] === decimalSymbol) {\n        mask.push(digitRegExp);\n      }\n    }\n\n    if (prefixLength > 0) {\n      mask = prefix.split(emptyString).concat(mask);\n    }\n\n    if (isNegative) {\n      // If user is entering a negative number, add a mask placeholder spot to attract the caret to it.\n      if (mask.length === prefixLength) {\n        mask.push(digitRegExp);\n      }\n\n      mask = [minusRegExp].concat(mask);\n    }\n\n    if (suffix.length > 0) {\n      mask = mask.concat(suffix.split(emptyString));\n    }\n\n    return mask;\n  }\n\n  numberMask.instanceOf = 'createNumberMask';\n\n  return numberMask;\n}\n\nfunction convertToMask(strNumber) {\n  return strNumber.split(emptyString).map(function (char) {\n    return digitRegExp.test(char) ? digitRegExp : char;\n  });\n}\n\n// http://stackoverflow.com/a/10899795/604296\nfunction addThousandsSeparator(n, thousandsSeparatorSymbol) {\n  return n.replace(/\\B(?=(\\d{3})+(?!\\d))/g, thousandsSeparatorSymbol);\n}"}, {"id": 3, "identifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/addons/src/emailMask.js", "name": "./addons/src/emailMask.js", "index": 5, "index2": 5, "size": 3848, "cacheable": true, "built": true, "optional": false, "prefetched": false, "chunks": [0, 1], "assets": [], "issuer": "multi emailMask", "failed": false, "errors": 0, "warnings": 0, "reasons": [{"moduleId": 0, "moduleIdentifier": "multi emailMask", "module": "multi emailMask", "moduleName": "multi emailMask", "type": "single entry", "userRequest": "/Users/<USER>/Projects/text-mask/addons/src/emailMask.js"}, {"moduleId": 0, "moduleIdentifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/addons/src/index.js", "module": "./addons/src/index.js", "moduleName": "./addons/src/index.js", "type": "cjs require", "userRequest": "./emailMask", "loc": "25:17-39"}], "source": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _emailPipe = require('./emailPipe');\n\nvar _emailPipe2 = _interopRequireDefault(_emailPipe);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar asterisk = '*';\nvar dot = '.';\nvar emptyString = '';\nvar atSymbol = '@';\nvar caretTrap = '[]';\nvar space = ' ';\nvar g = 'g';\nvar anyNonWhitespaceRegExp = /[^\\s]/;\nvar anyNonDotOrWhitespaceRegExp = /[^.\\s]/;\nvar allWhitespaceRegExp = /\\s/g;\n\nfunction emailMask(rawValue, config) {\n  rawValue = rawValue.replace(allWhitespaceRegExp, emptyString);\n\n  var placeholderChar = config.placeholderChar,\n      currentCaretPosition = config.currentCaretPosition;\n\n  var indexOfFirstAtSymbol = rawValue.indexOf(atSymbol);\n  var indexOfLastDot = rawValue.lastIndexOf(dot);\n  var indexOfTopLevelDomainDot = indexOfLastDot < indexOfFirstAtSymbol ? -1 : indexOfLastDot;\n\n  var localPartToDomainConnector = getConnector(rawValue, indexOfFirstAtSymbol + 1, atSymbol);\n  var domainNameToTopLevelDomainConnector = getConnector(rawValue, indexOfTopLevelDomainDot - 1, dot);\n\n  var localPart = getLocalPart(rawValue, indexOfFirstAtSymbol, placeholderChar);\n  var domainName = getDomainName(rawValue, indexOfFirstAtSymbol, indexOfTopLevelDomainDot, placeholderChar);\n  var topLevelDomain = getTopLevelDomain(rawValue, indexOfTopLevelDomainDot, placeholderChar, currentCaretPosition);\n\n  localPart = convertToMask(localPart);\n  domainName = convertToMask(domainName);\n  topLevelDomain = convertToMask(topLevelDomain, true);\n\n  var mask = localPart.concat(localPartToDomainConnector).concat(domainName).concat(domainNameToTopLevelDomainConnector).concat(topLevelDomain);\n\n  return mask;\n}\n\nfunction getConnector(rawValue, indexOfConnection, connectionSymbol) {\n  var connector = [];\n\n  if (rawValue[indexOfConnection] === connectionSymbol) {\n    connector.push(connectionSymbol);\n  } else {\n    connector.push(caretTrap, connectionSymbol);\n  }\n\n  connector.push(caretTrap);\n\n  return connector;\n}\n\nfunction getLocalPart(rawValue, indexOfFirstAtSymbol) {\n  if (indexOfFirstAtSymbol === -1) {\n    return rawValue;\n  } else {\n    return rawValue.slice(0, indexOfFirstAtSymbol);\n  }\n}\n\nfunction getDomainName(rawValue, indexOfFirstAtSymbol, indexOfTopLevelDomainDot, placeholderChar) {\n  var domainName = emptyString;\n\n  if (indexOfFirstAtSymbol !== -1) {\n    if (indexOfTopLevelDomainDot === -1) {\n      domainName = rawValue.slice(indexOfFirstAtSymbol + 1, rawValue.length);\n    } else {\n      domainName = rawValue.slice(indexOfFirstAtSymbol + 1, indexOfTopLevelDomainDot);\n    }\n  }\n\n  domainName = domainName.replace(new RegExp('[\\\\s' + placeholderChar + ']', g), emptyString);\n\n  if (domainName === atSymbol) {\n    return asterisk;\n  } else if (domainName.length < 1) {\n    return space;\n  } else if (domainName[domainName.length - 1] === dot) {\n    return domainName.slice(0, domainName.length - 1);\n  } else {\n    return domainName;\n  }\n}\n\nfunction getTopLevelDomain(rawValue, indexOfTopLevelDomainDot, placeholderChar, currentCaretPosition) {\n  var topLevelDomain = emptyString;\n\n  if (indexOfTopLevelDomainDot !== -1) {\n    topLevelDomain = rawValue.slice(indexOfTopLevelDomainDot + 1, rawValue.length);\n  }\n\n  topLevelDomain = topLevelDomain.replace(new RegExp('[\\\\s' + placeholderChar + '.]', g), emptyString);\n\n  if (topLevelDomain.length === 0) {\n    return rawValue[indexOfTopLevelDomainDot - 1] === dot && currentCaretPosition !== rawValue.length ? asterisk : emptyString;\n  } else {\n    return topLevelDomain;\n  }\n}\n\nfunction convertToMask(str, noDots) {\n  return str.split(emptyString).map(function (char) {\n    return char === space ? char : noDots ? anyNonDotOrWhitespaceRegExp : anyNonWhitespaceRegExp;\n  });\n}\n\nexports.default = { mask: emailMask, pipe: _emailPipe2.default };"}, {"id": 4, "identifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/addons/src/emailPipe.js", "name": "./addons/src/emailPipe.js", "index": 6, "index2": 4, "size": 1571, "cacheable": true, "built": true, "optional": false, "prefetched": false, "chunks": [0, 1], "assets": [], "issuer": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/addons/src/emailMask.js", "failed": false, "errors": 0, "warnings": 0, "reasons": [{"moduleId": 3, "moduleIdentifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/addons/src/emailMask.js", "module": "./addons/src/emailMask.js", "moduleName": "./addons/src/emailMask.js", "type": "cjs require", "userRequest": "./emailPipe", "loc": "7:17-39"}], "source": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = emailPipe;\nvar atSymbol = '@';\nvar allAtSymbolsRegExp = /@/g;\nvar emptyString = '';\nvar atDot = '@.';\nvar dot = '.';\nvar dotDot = '..';\nvar emptyArray = [];\nvar allDotsRegExp = /\\./g;\n\nfunction emailPipe(conformedValue, config) {\n  var currentCaretPosition = config.currentCaretPosition,\n      rawValue = config.rawValue,\n      previousConformedValue = config.previousConformedValue,\n      placeholderChar = config.placeholderChar;\n\n\n  var value = conformedValue;\n\n  value = removeAllAtSymbolsButFirst(value);\n\n  var indexOfAtDot = value.indexOf(atDot);\n\n  var emptyEmail = rawValue.match(new RegExp('[^@\\\\s.' + placeholderChar + ']')) === null;\n\n  if (emptyEmail) {\n    return emptyString;\n  }\n\n  if (value.indexOf(dotDot) !== -1 || indexOfAtDot !== -1 && currentCaretPosition !== indexOfAtDot + 1 || rawValue.indexOf(atSymbol) === -1 && previousConformedValue !== emptyString && rawValue.indexOf(dot) !== -1) {\n    return false;\n  }\n\n  var indexOfAtSymbol = value.indexOf(atSymbol);\n  var domainPart = value.slice(indexOfAtSymbol + 1, value.length);\n\n  if ((domainPart.match(allDotsRegExp) || emptyArray).length > 1 && value.substr(-1) === dot && currentCaretPosition !== rawValue.length) {\n    value = value.slice(0, value.length - 1);\n  }\n\n  return value;\n}\n\nfunction removeAllAtSymbolsButFirst(str) {\n  var atSymbolCount = 0;\n\n  return str.replace(allAtSymbolsRegExp, function () {\n    atSymbolCount++;\n\n    return atSymbolCount === 1 ? atSymbol : emptyString;\n  });\n}"}], "filteredModules": 0, "children": []}