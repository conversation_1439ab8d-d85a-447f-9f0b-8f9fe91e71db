!function(t){var e={};function n(o){if(e[o])return e[o].exports;var r=e[o]={i:o,l:!1,exports:{}};return t[o].call(r.exports,r,r.exports,n),r.l=!0,r.exports}n.m=t,n.c=e,n.d=function(t,e,o){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:o})},n.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var o=Object.create(null);if(n.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var r in t)n.d(o,r,function(e){return t[e]}.bind(null,r));return o},n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="/dist",n(n.s=1)}([function(t,e){t.exports=jQuery},function(t,e,n){"use strict";n.r(e);var o=n(0),r=n.n(o),i="nested-form";function a(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}var s=function(){function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.options=e,this.pkRegexps=this.options.assocs.map((function(t){return new RegExp("".concat(t,"_\\d+_id$"))}))}var e,n,o;return e=t,(n=[{key:"build",value:function(t){var e=t.clone(this.options.cloneEvents,this.options.cloneEvents);return this.removePk(e),this.initFields(e),this.checkRadio(e),e}},{key:"removePk",value:function(t){var e=this;t.find('input[id][type="hidden"]').each((function(t,n){var o=r()(n);e.pkRegexps.forEach((function(t){o.attr("id").match(t)&&o.remove()}))}))}},{key:"initFields",value:function(t){t.find('textarea, input[type="text"]').val(""),t.find('input[type="radio"], input[type="checkbox"]').prop("checked",!1),t.find("option").removeAttr("selected").prop("selected",!1),t.find('input[id$="__destroy"]').removeAttr("value")}},{key:"checkRadio",value:function(t){t.find('input[name][type="radio"]').map((function(t,e){return r()(e).attr("name")})).get().filter((function(t,e,n){return n.indexOf(t)==e})).forEach((function(e){var n=t.find('input[type="radio"][name="'.concat(e,'"]'));0===n.filter(":checked").length&&n.first().prop("checked",!0)}))}}])&&a(e.prototype,n),o&&a(e,o),t}();function u(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}var c=function(){function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.options=e,this.assocRegexps=this.options.assocs.map((function(t,e){return new RegExp("(".concat(t,"(\\[|\\]\\[|_)?)\\d+"))})),this.destroyRegexps=this.options.assocs.map((function(t,e){return new RegExp("".concat(t,"_\\d+__destroy"))}))}var e,n,o;return e=t,(n=[{key:"build",value:function(t){var e=new s(this.options).build(t.last());e.show();var n=t.length+this.options.startIndex;return this.renewIndex(e,n),[e,n]}},{key:"renewIndex",value:function(t,e){var n=this,o=this.options.tags.join(", ");t.find(o).each((function(t,o){var i=r()(o);n.options.attributes.forEach((function(t){var o=i.attr(t);o&&(n.assocRegexps.forEach((function(t){o=o.replace(t,"$1"+e)})),i.attr(t,o))}))}))}},{key:"destroy",value:function(t){var e=this;t.hide(),t.find("input[id][type=hidden]").each((function(t,n){var o=r()(n);e.destroyRegexps.forEach((function(t){o.attr("id").match(t)&&o.val("1")}))}))}}])&&u(e.prototype,n),o&&u(e,o),t}();function f(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){if("undefined"==typeof Symbol||!(Symbol.iterator in Object(t)))return;var n=[],o=!0,r=!1,i=void 0;try{for(var a,s=t[Symbol.iterator]();!(o=(a=s.next()).done)&&(n.push(a.value),!e||n.length!==e);o=!0);}catch(t){r=!0,i=t}finally{try{o||null==s.return||s.return()}finally{if(r)throw i}}return n}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return l(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return l(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function l(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,o=new Array(e);n<e;n++)o[n]=t[n];return o}function d(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function h(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}var p={forms:"",adder:"",remover:null,associations:"",postfixes:"_attributes",increment:1,max:null,startIndex:0,tags:["input","textarea","select","label"],attributes:["id","name","for"],cloneEvents:!0,addTo:"last",nestedForm:null,afterInitialize:null,onBuildForm:null,beforeAddForm:null,afterAddForm:null,beforeRemoveForm:null,afterRemoveForm:null},v=function(){function t(e){var n=this,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};d(this,t),this.options=r.a.extend({},p,o),this.options.assocs=this.makeAssocs(),this.$container=r()(e),this.$adder=this.adder(),this.builder=new c(this.options),this.bind(),this.options.afterInitialize&&this.options.afterInitialize(this),this.options.nestedForm&&this.forms().each((function(t,e){r()(e).nestedForm(n.options.nestedForm)}))}var e,n,o;return e=t,o=[{key:"getDefaults",value:function(){return p}},{key:"setDefaults",value:function(t){return r.a.extend(p,t)}}],(n=[{key:"destroy",value:function(){this.unbind()}},{key:"bind",value:function(){var t=this;this.$adder.on("click.".concat(i),(function(e){e.preventDefault(),t.addForms()})),this.options.remover&&this.$container.on("click.".concat(i),this.options.remover,(function(e){e.preventDefault(),t.removeWith(r()(e.currentTarget))}))}},{key:"unbind",value:function(){this.$adder.off(".".concat(i)),this.$container.off(".".concat(i))}},{key:"adder",value:function(){return this.$container.find(this.options.adder).length?this.$container.find(this.options.adder):r()(this.options.adder)}},{key:"forms",value:function(){return this.$container.find(this.options.forms)}},{key:"addForms",value:function(){for(var t=0;t<this.options.increment&&!1!==this.add();t++);}},{key:"add",value:function(){var t=f(this.builder.build(this.forms()),2),e=t[0],n=t[1];return this.options.onBuildForm&&this.options.onBuildForm(e,n),(!this.options.beforeAddForm||!1!==this.options.beforeAddForm(this.$container,e,n))&&("first"==this.options.addTo?this.forms().first().before(e):this.forms().last().after(e),this.options.afterAddForm&&this.options.afterAddForm(this.$container,e,n),this.options.nestedForm&&e.nestedForm(this.options.nestedForm),this.refresh())}},{key:"removeWith",value:function(t){var e=this;this.forms().each((function(n,o){var i=r()(o);r.a.contains(i.get(0),t.get(0))&&e.remove(i)}))}},{key:"remove",value:function(t){this.options.beforeRemoveForm&&!1===this.options.beforeRemoveForm(t)||(this.builder.destroy(t),this.options.afterRemoveForm&&this.options.afterRemoveForm(t),this.refresh())}},{key:"refresh",value:function(){return this.options.max&&this.forms().filter(":visible").length>=this.options.max?(this.disable(),!1):(this.enable(),!0)}},{key:"enable",value:function(){this.$adder.prop("disabled",!1)}},{key:"disable",value:function(){this.$adder.prop("disabled",!0)}},{key:"makeAssocs",value:function(){var t=[].concat(this.options.associations),e=[].concat(this.options.postfixes);return t.map((function(t,n){var o=e[n]||e[0];return"".concat(t).concat(o)}))}}])&&h(e.prototype,n),o&&h(e,o),t}();r.a.fn.nestedForm=function(t){return this.each((function(e,n){var o=r()(n);o.data(i)&&o.data(i).destroy(),o.data(i,new v(o,t))}))},r.a.NestedForm=v}]);