{"version": 3, "file": "perfect-scrollbar.min.js", "sources": ["../src/handlers/mouse-wheel.js", "../src/update-geometry.js", "../src/lib/css.js", "../src/lib/dom.js", "../src/lib/class-names.js", "../src/process-scroll-diff.js", "../src/lib/util.js", "../src/handlers/drag-thumb.js", "../src/lib/event-manager.js", "../src/index.js", "../src/handlers/click-rail.js", "../src/handlers/keyboard.js", "../src/handlers/touch.js"], "sourcesContent": ["import * as CSS from '../lib/css';\nimport cls from '../lib/class-names';\nimport updateGeometry from '../update-geometry';\nimport { env } from '../lib/util';\n\nexport default function(i) {\n  const element = i.element;\n\n  let shouldPrevent = false;\n\n  function shouldPreventDefault(deltaX, deltaY) {\n    const roundedScrollTop = Math.floor(element.scrollTop);\n    const isTop = element.scrollTop === 0;\n    const isBottom =\n      roundedScrollTop + element.offsetHeight === element.scrollHeight;\n    const isLeft = element.scrollLeft === 0;\n    const isRight =\n      element.scrollLeft + element.offsetWidth === element.scrollWidth;\n\n    let hitsBound;\n\n    // pick axis with primary direction\n    if (Math.abs(deltaY) > Math.abs(deltaX)) {\n      hitsBound = isTop || isBottom;\n    } else {\n      hitsBound = isLeft || isRight;\n    }\n\n    return hitsBound ? !i.settings.wheelPropagation : true;\n  }\n\n  function getDeltaFromEvent(e) {\n    let deltaX = e.deltaX;\n    let deltaY = -1 * e.deltaY;\n\n    if (typeof deltaX === 'undefined' || typeof deltaY === 'undefined') {\n      // OS X Safari\n      deltaX = (-1 * e.wheelDeltaX) / 6;\n      deltaY = e.wheelDeltaY / 6;\n    }\n\n    if (e.deltaMode && e.deltaMode === 1) {\n      // Firefox in deltaMode 1: Line scrolling\n      deltaX *= 10;\n      deltaY *= 10;\n    }\n\n    if (deltaX !== deltaX && deltaY !== deltaY /* NaN checks */) {\n      // IE in some mouse drivers\n      deltaX = 0;\n      deltaY = e.wheelDelta;\n    }\n\n    if (e.shiftKey) {\n      // reverse axis with shift key\n      return [-deltaY, -deltaX];\n    }\n    return [deltaX, deltaY];\n  }\n\n  function shouldBeConsumedByChild(target, deltaX, deltaY) {\n    // FIXME: this is a workaround for <select> issue in FF and IE #571\n    if (!env.isWebKit && element.querySelector('select:focus')) {\n      return true;\n    }\n\n    if (!element.contains(target)) {\n      return false;\n    }\n\n    let cursor = target;\n\n    while (cursor && cursor !== element) {\n      if (cursor.classList.contains(cls.element.consuming)) {\n        return true;\n      }\n\n      const style = CSS.get(cursor);\n\n      // if deltaY && vertical scrollable\n      if (deltaY && style.overflowY.match(/(scroll|auto)/)) {\n        const maxScrollTop = cursor.scrollHeight - cursor.clientHeight;\n        if (maxScrollTop > 0) {\n          if (\n            (cursor.scrollTop > 0 && deltaY < 0) ||\n            (cursor.scrollTop < maxScrollTop && deltaY > 0)\n          ) {\n            return true;\n          }\n        }\n      }\n      // if deltaX && horizontal scrollable\n      if (deltaX && style.overflowX.match(/(scroll|auto)/)) {\n        const maxScrollLeft = cursor.scrollWidth - cursor.clientWidth;\n        if (maxScrollLeft > 0) {\n          if (\n            (cursor.scrollLeft > 0 && deltaX < 0) ||\n            (cursor.scrollLeft < maxScrollLeft && deltaX > 0)\n          ) {\n            return true;\n          }\n        }\n      }\n\n      cursor = cursor.parentNode;\n    }\n\n    return false;\n  }\n\n  function mousewheelHandler(e) {\n    const [deltaX, deltaY] = getDeltaFromEvent(e);\n\n    if (shouldBeConsumedByChild(e.target, deltaX, deltaY)) {\n      return;\n    }\n\n    let shouldPrevent = false;\n    if (!i.settings.useBothWheelAxes) {\n      // deltaX will only be used for horizontal scrolling and deltaY will\n      // only be used for vertical scrolling - this is the default\n      element.scrollTop -= deltaY * i.settings.wheelSpeed;\n      element.scrollLeft += deltaX * i.settings.wheelSpeed;\n    } else if (i.scrollbarYActive && !i.scrollbarXActive) {\n      // only vertical scrollbar is active and useBothWheelAxes option is\n      // active, so let's scroll vertical bar using both mouse wheel axes\n      if (deltaY) {\n        element.scrollTop -= deltaY * i.settings.wheelSpeed;\n      } else {\n        element.scrollTop += deltaX * i.settings.wheelSpeed;\n      }\n      shouldPrevent = true;\n    } else if (i.scrollbarXActive && !i.scrollbarYActive) {\n      // useBothWheelAxes and only horizontal bar is active, so use both\n      // wheel axes for horizontal bar\n      if (deltaX) {\n        element.scrollLeft += deltaX * i.settings.wheelSpeed;\n      } else {\n        element.scrollLeft -= deltaY * i.settings.wheelSpeed;\n      }\n      shouldPrevent = true;\n    }\n\n    updateGeometry(i);\n\n    shouldPrevent = shouldPrevent || shouldPreventDefault(deltaX, deltaY);\n    if (shouldPrevent && !e.ctrlKey) {\n      e.stopPropagation();\n      e.preventDefault();\n    }\n  }\n\n  if (typeof window.onwheel !== 'undefined') {\n    i.event.bind(element, 'wheel', mousewheelHandler);\n  } else if (typeof window.onmousewheel !== 'undefined') {\n    i.event.bind(element, 'mousewheel', mousewheelHandler);\n  }\n}\n", "import * as CSS from './lib/css';\nimport * as DOM from './lib/dom';\nimport cls from './lib/class-names';\nimport { toInt } from './lib/util';\n\nexport default function(i) {\n  const element = i.element;\n  const roundedScrollTop = Math.floor(element.scrollTop);\n  const rect = element.getBoundingClientRect();\n\n  i.containerWidth = Math.ceil(rect.width);\n  i.containerHeight = Math.ceil(rect.height);\n  i.contentWidth = element.scrollWidth;\n  i.contentHeight = element.scrollHeight;\n\n  if (!element.contains(i.scrollbarXRail)) {\n    // clean up and append\n    DOM.queryChildren(element, cls.element.rail('x')).forEach(el =>\n      DOM.remove(el)\n    );\n    element.appendChild(i.scrollbarXRail);\n  }\n  if (!element.contains(i.scrollbarYRail)) {\n    // clean up and append\n    DOM.queryChildren(element, cls.element.rail('y')).forEach(el =>\n      DOM.remove(el)\n    );\n    element.appendChild(i.scrollbarYRail);\n  }\n\n  if (\n    !i.settings.suppressScrollX &&\n    i.containerWidth + i.settings.scrollXMarginOffset < i.contentWidth\n  ) {\n    i.scrollbarXActive = true;\n    i.railXWidth = i.containerWidth - i.railXMarginWidth;\n    i.railXRatio = i.containerWidth / i.railXWidth;\n    i.scrollbarXWidth = getThumbSize(\n      i,\n      toInt((i.railXWidth * i.containerWidth) / i.contentWidth)\n    );\n    i.scrollbarXLeft = toInt(\n      ((i.negativeScrollAdjustment + element.scrollLeft) *\n        (i.railXWidth - i.scrollbarXWidth)) /\n        (i.contentWidth - i.containerWidth)\n    );\n  } else {\n    i.scrollbarXActive = false;\n  }\n\n  if (\n    !i.settings.suppressScrollY &&\n    i.containerHeight + i.settings.scrollYMarginOffset < i.contentHeight\n  ) {\n    i.scrollbarYActive = true;\n    i.railYHeight = i.containerHeight - i.railYMarginHeight;\n    i.railYRatio = i.containerHeight / i.railYHeight;\n    i.scrollbarYHeight = getThumbSize(\n      i,\n      toInt((i.railYHeight * i.containerHeight) / i.contentHeight)\n    );\n    i.scrollbarYTop = toInt(\n      (roundedScrollTop * (i.railYHeight - i.scrollbarYHeight)) /\n        (i.contentHeight - i.containerHeight)\n    );\n  } else {\n    i.scrollbarYActive = false;\n  }\n\n  if (i.scrollbarXLeft >= i.railXWidth - i.scrollbarXWidth) {\n    i.scrollbarXLeft = i.railXWidth - i.scrollbarXWidth;\n  }\n  if (i.scrollbarYTop >= i.railYHeight - i.scrollbarYHeight) {\n    i.scrollbarYTop = i.railYHeight - i.scrollbarYHeight;\n  }\n\n  updateCss(element, i);\n\n  if (i.scrollbarXActive) {\n    element.classList.add(cls.state.active('x'));\n  } else {\n    element.classList.remove(cls.state.active('x'));\n    i.scrollbarXWidth = 0;\n    i.scrollbarXLeft = 0;\n    element.scrollLeft = i.isRtl === true ? i.contentWidth : 0;\n  }\n  if (i.scrollbarYActive) {\n    element.classList.add(cls.state.active('y'));\n  } else {\n    element.classList.remove(cls.state.active('y'));\n    i.scrollbarYHeight = 0;\n    i.scrollbarYTop = 0;\n    element.scrollTop = 0;\n  }\n}\n\nfunction getThumbSize(i, thumbSize) {\n  if (i.settings.minScrollbarLength) {\n    thumbSize = Math.max(thumbSize, i.settings.minScrollbarLength);\n  }\n  if (i.settings.maxScrollbarLength) {\n    thumbSize = Math.min(thumbSize, i.settings.maxScrollbarLength);\n  }\n  return thumbSize;\n}\n\nfunction updateCss(element, i) {\n  const xRailOffset = { width: i.railXWidth };\n  const roundedScrollTop = Math.floor(element.scrollTop);\n\n  if (i.isRtl) {\n    xRailOffset.left =\n      i.negativeScrollAdjustment +\n      element.scrollLeft +\n      i.containerWidth -\n      i.contentWidth;\n  } else {\n    xRailOffset.left = element.scrollLeft;\n  }\n  if (i.isScrollbarXUsingBottom) {\n    xRailOffset.bottom = i.scrollbarXBottom - roundedScrollTop;\n  } else {\n    xRailOffset.top = i.scrollbarXTop + roundedScrollTop;\n  }\n  CSS.set(i.scrollbarXRail, xRailOffset);\n\n  const yRailOffset = { top: roundedScrollTop, height: i.railYHeight };\n  if (i.isScrollbarYUsingRight) {\n    if (i.isRtl) {\n      yRailOffset.right =\n        i.contentWidth -\n        (i.negativeScrollAdjustment + element.scrollLeft) -\n        i.scrollbarYRight -\n        i.scrollbarYOuterWidth -\n        9;\n    } else {\n      yRailOffset.right = i.scrollbarYRight - element.scrollLeft;\n    }\n  } else {\n    if (i.isRtl) {\n      yRailOffset.left =\n        i.negativeScrollAdjustment +\n        element.scrollLeft +\n        i.containerWidth * 2 -\n        i.contentWidth -\n        i.scrollbarYLeft -\n        i.scrollbarYOuterWidth;\n    } else {\n      yRailOffset.left = i.scrollbarYLeft + element.scrollLeft;\n    }\n  }\n  CSS.set(i.scrollbarYRail, yRailOffset);\n\n  CSS.set(i.scrollbarX, {\n    left: i.scrollbarXLeft,\n    width: i.scrollbarXWidth - i.railBorderXWidth,\n  });\n  CSS.set(i.scrollbarY, {\n    top: i.scrollbarYTop,\n    height: i.scrollbarYHeight - i.railBorderYWidth,\n  });\n}\n", "export function get(element) {\n  return getComputedStyle(element);\n}\n\nexport function set(element, obj) {\n  for (const key in obj) {\n    let val = obj[key];\n    if (typeof val === 'number') {\n      val = `${val}px`;\n    }\n    element.style[key] = val;\n  }\n  return element;\n}\n", "export function div(className) {\n  const div = document.createElement('div');\n  div.className = className;\n  return div;\n}\n\nconst elMatches =\n  typeof Element !== 'undefined' &&\n  (Element.prototype.matches ||\n    Element.prototype.webkitMatchesSelector ||\n    Element.prototype.mozMatchesSelector ||\n    Element.prototype.msMatchesSelector);\n\nexport function matches(element, query) {\n  if (!elMatches) {\n    throw new Error('No element matching method supported');\n  }\n\n  return elMatches.call(element, query);\n}\n\nexport function remove(element) {\n  if (element.remove) {\n    element.remove();\n  } else {\n    if (element.parentNode) {\n      element.parentNode.removeChild(element);\n    }\n  }\n}\n\nexport function queryChildren(element, selector) {\n  return Array.prototype.filter.call(element.children, child =>\n    matches(child, selector)\n  );\n}\n", "const cls = {\n  main: 'ps',\n  rtl: 'ps__rtl',\n  element: {\n    thumb: x => `ps__thumb-${x}`,\n    rail: x => `ps__rail-${x}`,\n    consuming: 'ps__child--consume',\n  },\n  state: {\n    focus: 'ps--focus',\n    clicking: 'ps--clicking',\n    active: x => `ps--active-${x}`,\n    scrolling: x => `ps--scrolling-${x}`,\n  },\n};\n\nexport default cls;\n\n/*\n * Helper methods\n */\nconst scrollingClassTimeout = { x: null, y: null };\n\nexport function addScrollingClass(i, x) {\n  const classList = i.element.classList;\n  const className = cls.state.scrolling(x);\n\n  if (classList.contains(className)) {\n    clearTimeout(scrollingClassTimeout[x]);\n  } else {\n    classList.add(className);\n  }\n}\n\nexport function removeScrollingClass(i, x) {\n  scrollingClassTimeout[x] = setTimeout(\n    () => i.isAlive && i.element.classList.remove(cls.state.scrolling(x)),\n    i.settings.scrollingThreshold\n  );\n}\n\nexport function setScrollingClassInstantly(i, x) {\n  addScrollingClass(i, x);\n  removeScrollingClass(i, x);\n}\n", "import { setScrollingClassInstantly } from './lib/class-names';\n\nfunction createEvent(name) {\n  if (typeof window.CustomEvent === 'function') {\n    return new CustomEvent(name);\n  } else {\n    const evt = document.createEvent('CustomEvent');\n    evt.initCustomEvent(name, false, false, undefined);\n    return evt;\n  }\n}\n\nexport default function(\n  i,\n  axis,\n  diff,\n  useScrollingClass = true,\n  forceFireReachEvent = false\n) {\n  let fields;\n  if (axis === 'top') {\n    fields = [\n      'contentHeight',\n      'containerHeight',\n      'scrollTop',\n      'y',\n      'up',\n      'down',\n    ];\n  } else if (axis === 'left') {\n    fields = [\n      'contentWidth',\n      'containerWidth',\n      'scrollLeft',\n      'x',\n      'left',\n      'right',\n    ];\n  } else {\n    throw new Error('A proper axis should be provided');\n  }\n\n  processScrollDiff(i, diff, fields, useScrollingClass, forceFireReachEvent);\n}\n\nfunction processScrollDiff(\n  i,\n  diff,\n  [contentHeight, containerHeight, scrollTop, y, up, down],\n  useScrollingClass = true,\n  forceFireReachEvent = false\n) {\n  const element = i.element;\n\n  // reset reach\n  i.reach[y] = null;\n\n  // 1 for subpixel rounding\n  if (element[scrollTop] < 1) {\n    i.reach[y] = 'start';\n  }\n\n  // 1 for subpixel rounding\n  if (element[scrollTop] > i[contentHeight] - i[containerHeight] - 1) {\n    i.reach[y] = 'end';\n  }\n\n  if (diff) {\n    element.dispatchEvent(createEvent(`ps-scroll-${y}`));\n\n    if (diff < 0) {\n      element.dispatchEvent(createEvent(`ps-scroll-${up}`));\n    } else if (diff > 0) {\n      element.dispatchEvent(createEvent(`ps-scroll-${down}`));\n    }\n\n    if (useScrollingClass) {\n      setScrollingClassInstantly(i, y);\n    }\n  }\n\n  if (i.reach[y] && (diff || forceFireReachEvent)) {\n    element.dispatchEvent(createEvent(`ps-${y}-reach-${i.reach[y]}`));\n  }\n}\n", "import * as CSS from './css';\nimport * as DOM from './dom';\n\nexport function toInt(x) {\n  return parseInt(x, 10) || 0;\n}\n\nexport function isEditable(el) {\n  return (\n    DOM.matches(el, 'input,[contenteditable]') ||\n    DOM.matches(el, 'select,[contenteditable]') ||\n    DOM.matches(el, 'textarea,[contenteditable]') ||\n    DOM.matches(el, 'button,[contenteditable]')\n  );\n}\n\nexport function outerWidth(element) {\n  const styles = CSS.get(element);\n  return (\n    toInt(styles.width) +\n    toInt(styles.paddingLeft) +\n    toInt(styles.paddingRight) +\n    toInt(styles.borderLeftWidth) +\n    toInt(styles.borderRightWidth)\n  );\n}\n\nexport const env = {\n  isWebKit:\n    typeof document !== 'undefined' &&\n    'WebkitAppearance' in document.documentElement.style,\n  supportsTouch:\n    typeof window !== 'undefined' &&\n    ('ontouchstart' in window ||\n      ('maxTouchPoints' in window.navigator &&\n        window.navigator.maxTouchPoints > 0) ||\n      (window.DocumentTouch && document instanceof window.DocumentTouch)),\n  supportsIePointer:\n    typeof navigator !== 'undefined' && navigator.msMaxTouchPoints,\n  isChrome:\n    typeof navigator !== 'undefined' &&\n    /Chrome/i.test(navigator && navigator.userAgent),\n};\n", "import * as CSS from '../lib/css';\nimport * as DOM from '../lib/dom';\nimport cls, {\n  addScrollingClass,\n  removeScrollingClass,\n} from '../lib/class-names';\nimport updateGeometry from '../update-geometry';\nimport { toInt } from '../lib/util';\n\nexport default function(i) {\n  bindMouseScrollHandler(i, [\n    'containerWidth',\n    'contentWidth',\n    'pageX',\n    'railXWidth',\n    'scrollbarX',\n    'scrollbarXWidth',\n    'scrollLeft',\n    'x',\n    'scrollbarXRail',\n  ]);\n  bindMouseScrollHandler(i, [\n    'containerHeight',\n    'contentHeight',\n    'pageY',\n    'railYHeight',\n    'scrollbarY',\n    'scrollbarYHeight',\n    'scrollTop',\n    'y',\n    'scrollbarYRail',\n  ]);\n}\n\nfunction bindMouseScrollHandler(\n  i,\n  [\n    containerHeight,\n    contentHeight,\n    pageY,\n    railYHeight,\n    scrollbarY,\n    scrollbarYHeight,\n    scrollTop,\n    y,\n    scrollbarYRail,\n  ]\n) {\n  const element = i.element;\n\n  let startingScrollTop = null;\n  let startingMousePageY = null;\n  let scrollBy = null;\n\n  function mouseMoveHandler(e) {\n    if (e.touches && e.touches[0]) {\n      e[pageY] = e.touches[0].pageY;\n    }\n    element[scrollTop] =\n      startingScrollTop + scrollBy * (e[pageY] - startingMousePageY);\n    addScrollingClass(i, y);\n    updateGeometry(i);\n\n    e.stopPropagation();\n    e.preventDefault();\n  }\n\n  function mouseUpHandler() {\n    removeScrollingClass(i, y);\n    i[scrollbarYRail].classList.remove(cls.state.clicking);\n    i.event.unbind(i.ownerDocument, 'mousemove', mouseMoveHandler);\n  }\n\n  function bindMoves(e, touchMode) {\n    startingScrollTop = element[scrollTop];\n    if (touchMode && e.touches) {\n      e[pageY] = e.touches[0].pageY;\n    }\n    startingMousePageY = e[pageY];\n    scrollBy =\n      (i[contentHeight] - i[containerHeight]) /\n      (i[railYHeight] - i[scrollbarYHeight]);\n    if (!touchMode) {\n      i.event.bind(i.ownerDocument, 'mousemove', mouseMoveHandler);\n      i.event.once(i.ownerDocument, 'mouseup', mouseUpHandler);\n      e.preventDefault();\n    } else {\n      i.event.bind(i.ownerDocument, 'touchmove', mouseMoveHandler);\n    }\n\n    i[scrollbarYRail].classList.add(cls.state.clicking);\n\n    e.stopPropagation();\n  }\n\n  i.event.bind(i[scrollbarY], 'mousedown', e => {\n    bindMoves(e);\n  });\n  i.event.bind(i[scrollbarY], 'touchstart', e => {\n    bindMoves(e, true);\n  });\n}\n", "class EventElement {\n  constructor(element) {\n    this.element = element;\n    this.handlers = {};\n  }\n\n  bind(eventName, handler) {\n    if (typeof this.handlers[eventName] === 'undefined') {\n      this.handlers[eventName] = [];\n    }\n    this.handlers[eventName].push(handler);\n    this.element.addEventListener(eventName, handler, false);\n  }\n\n  unbind(eventName, target) {\n    this.handlers[eventName] = this.handlers[eventName].filter(handler => {\n      if (target && handler !== target) {\n        return true;\n      }\n      this.element.removeEventListener(eventName, handler, false);\n      return false;\n    });\n  }\n\n  unbindAll() {\n    for (const name in this.handlers) {\n      this.unbind(name);\n    }\n  }\n\n  get isEmpty() {\n    return Object.keys(this.handlers).every(\n      key => this.handlers[key].length === 0\n    );\n  }\n}\n\nexport default class EventManager {\n  constructor() {\n    this.eventElements = [];\n  }\n\n  eventElement(element) {\n    let ee = this.eventElements.filter(ee => ee.element === element)[0];\n    if (!ee) {\n      ee = new EventElement(element);\n      this.eventElements.push(ee);\n    }\n    return ee;\n  }\n\n  bind(element, eventName, handler) {\n    this.eventElement(element).bind(eventName, handler);\n  }\n\n  unbind(element, eventName, handler) {\n    const ee = this.eventElement(element);\n    ee.unbind(eventName, handler);\n\n    if (ee.isEmpty) {\n      // remove\n      this.eventElements.splice(this.eventElements.indexOf(ee), 1);\n    }\n  }\n\n  unbindAll() {\n    this.eventElements.forEach(e => e.unbindAll());\n    this.eventElements = [];\n  }\n\n  once(element, eventName, handler) {\n    const ee = this.eventElement(element);\n    const onceHandler = evt => {\n      ee.unbind(eventName, onceHandler);\n      handler(evt);\n    };\n    ee.bind(eventName, onceHandler);\n  }\n}\n", "import * as CSS from './lib/css';\nimport * as DOM from './lib/dom';\nimport cls from './lib/class-names';\nimport EventManager from './lib/event-manager';\nimport processScrollDiff from './process-scroll-diff';\nimport updateGeometry from './update-geometry';\nimport { toInt, outerWidth } from './lib/util';\n\nimport clickRail from './handlers/click-rail';\nimport dragThumb from './handlers/drag-thumb';\nimport keyboard from './handlers/keyboard';\nimport wheel from './handlers/mouse-wheel';\nimport touch from './handlers/touch';\n\nconst defaultSettings = () => ({\n  handlers: ['click-rail', 'drag-thumb', 'keyboard', 'wheel', 'touch'],\n  maxScrollbarLength: null,\n  minScrollbarLength: null,\n  scrollingThreshold: 1000,\n  scrollXMarginOffset: 0,\n  scrollYMarginOffset: 0,\n  suppressScrollX: false,\n  suppressScrollY: false,\n  swipeEasing: true,\n  useBothWheelAxes: false,\n  wheelPropagation: true,\n  wheelSpeed: 1,\n});\n\nconst handlers = {\n  'click-rail': clickRail,\n  'drag-thumb': dragThumb,\n  keyboard,\n  wheel,\n  touch,\n};\n\nexport default class PerfectScrollbar {\n  constructor(element, userSettings = {}) {\n    if (typeof element === 'string') {\n      element = document.querySelector(element);\n    }\n\n    if (!element || !element.nodeName) {\n      throw new Error('no element is specified to initialize PerfectScrollbar');\n    }\n\n    this.element = element;\n\n    element.classList.add(cls.main);\n\n    this.settings = defaultSettings();\n    for (const key in userSettings) {\n      this.settings[key] = userSettings[key];\n    }\n\n    this.containerWidth = null;\n    this.containerHeight = null;\n    this.contentWidth = null;\n    this.contentHeight = null;\n\n    const focus = () => element.classList.add(cls.state.focus);\n    const blur = () => element.classList.remove(cls.state.focus);\n\n    this.isRtl = CSS.get(element).direction === 'rtl';\n    if (this.isRtl === true) {\n      element.classList.add(cls.rtl);\n    }\n    this.isNegativeScroll = (() => {\n      const originalScrollLeft = element.scrollLeft;\n      let result = null;\n      element.scrollLeft = -1;\n      result = element.scrollLeft < 0;\n      element.scrollLeft = originalScrollLeft;\n      return result;\n    })();\n    this.negativeScrollAdjustment = this.isNegativeScroll\n      ? element.scrollWidth - element.clientWidth\n      : 0;\n    this.event = new EventManager();\n    this.ownerDocument = element.ownerDocument || document;\n\n    this.scrollbarXRail = DOM.div(cls.element.rail('x'));\n    element.appendChild(this.scrollbarXRail);\n    this.scrollbarX = DOM.div(cls.element.thumb('x'));\n    this.scrollbarXRail.appendChild(this.scrollbarX);\n    this.scrollbarX.setAttribute('tabindex', 0);\n    this.event.bind(this.scrollbarX, 'focus', focus);\n    this.event.bind(this.scrollbarX, 'blur', blur);\n    this.scrollbarXActive = null;\n    this.scrollbarXWidth = null;\n    this.scrollbarXLeft = null;\n    const railXStyle = CSS.get(this.scrollbarXRail);\n    this.scrollbarXBottom = parseInt(railXStyle.bottom, 10);\n    if (isNaN(this.scrollbarXBottom)) {\n      this.isScrollbarXUsingBottom = false;\n      this.scrollbarXTop = toInt(railXStyle.top);\n    } else {\n      this.isScrollbarXUsingBottom = true;\n    }\n    this.railBorderXWidth =\n      toInt(railXStyle.borderLeftWidth) + toInt(railXStyle.borderRightWidth);\n    // Set rail to display:block to calculate margins\n    CSS.set(this.scrollbarXRail, { display: 'block' });\n    this.railXMarginWidth =\n      toInt(railXStyle.marginLeft) + toInt(railXStyle.marginRight);\n    CSS.set(this.scrollbarXRail, { display: '' });\n    this.railXWidth = null;\n    this.railXRatio = null;\n\n    this.scrollbarYRail = DOM.div(cls.element.rail('y'));\n    element.appendChild(this.scrollbarYRail);\n    this.scrollbarY = DOM.div(cls.element.thumb('y'));\n    this.scrollbarYRail.appendChild(this.scrollbarY);\n    this.scrollbarY.setAttribute('tabindex', 0);\n    this.event.bind(this.scrollbarY, 'focus', focus);\n    this.event.bind(this.scrollbarY, 'blur', blur);\n    this.scrollbarYActive = null;\n    this.scrollbarYHeight = null;\n    this.scrollbarYTop = null;\n    const railYStyle = CSS.get(this.scrollbarYRail);\n    this.scrollbarYRight = parseInt(railYStyle.right, 10);\n    if (isNaN(this.scrollbarYRight)) {\n      this.isScrollbarYUsingRight = false;\n      this.scrollbarYLeft = toInt(railYStyle.left);\n    } else {\n      this.isScrollbarYUsingRight = true;\n    }\n    this.scrollbarYOuterWidth = this.isRtl ? outerWidth(this.scrollbarY) : null;\n    this.railBorderYWidth =\n      toInt(railYStyle.borderTopWidth) + toInt(railYStyle.borderBottomWidth);\n    CSS.set(this.scrollbarYRail, { display: 'block' });\n    this.railYMarginHeight =\n      toInt(railYStyle.marginTop) + toInt(railYStyle.marginBottom);\n    CSS.set(this.scrollbarYRail, { display: '' });\n    this.railYHeight = null;\n    this.railYRatio = null;\n\n    this.reach = {\n      x:\n        element.scrollLeft <= 0\n          ? 'start'\n          : element.scrollLeft >= this.contentWidth - this.containerWidth\n          ? 'end'\n          : null,\n      y:\n        element.scrollTop <= 0\n          ? 'start'\n          : element.scrollTop >= this.contentHeight - this.containerHeight\n          ? 'end'\n          : null,\n    };\n\n    this.isAlive = true;\n\n    this.settings.handlers.forEach(handlerName => handlers[handlerName](this));\n\n    this.lastScrollTop = Math.floor(element.scrollTop); // for onScroll only\n    this.lastScrollLeft = element.scrollLeft; // for onScroll only\n    this.event.bind(this.element, 'scroll', e => this.onScroll(e));\n    updateGeometry(this);\n  }\n\n  update() {\n    if (!this.isAlive) {\n      return;\n    }\n\n    // Recalcuate negative scrollLeft adjustment\n    this.negativeScrollAdjustment = this.isNegativeScroll\n      ? this.element.scrollWidth - this.element.clientWidth\n      : 0;\n\n    // Recalculate rail margins\n    CSS.set(this.scrollbarXRail, { display: 'block' });\n    CSS.set(this.scrollbarYRail, { display: 'block' });\n    this.railXMarginWidth =\n      toInt(CSS.get(this.scrollbarXRail).marginLeft) +\n      toInt(CSS.get(this.scrollbarXRail).marginRight);\n    this.railYMarginHeight =\n      toInt(CSS.get(this.scrollbarYRail).marginTop) +\n      toInt(CSS.get(this.scrollbarYRail).marginBottom);\n\n    // Hide scrollbars not to affect scrollWidth and scrollHeight\n    CSS.set(this.scrollbarXRail, { display: 'none' });\n    CSS.set(this.scrollbarYRail, { display: 'none' });\n\n    updateGeometry(this);\n\n    processScrollDiff(this, 'top', 0, false, true);\n    processScrollDiff(this, 'left', 0, false, true);\n\n    CSS.set(this.scrollbarXRail, { display: '' });\n    CSS.set(this.scrollbarYRail, { display: '' });\n  }\n\n  onScroll(e) {\n    if (!this.isAlive) {\n      return;\n    }\n\n    updateGeometry(this);\n    processScrollDiff(this, 'top', this.element.scrollTop - this.lastScrollTop);\n    processScrollDiff(\n      this,\n      'left',\n      this.element.scrollLeft - this.lastScrollLeft\n    );\n\n    this.lastScrollTop = Math.floor(this.element.scrollTop);\n    this.lastScrollLeft = this.element.scrollLeft;\n  }\n\n  destroy() {\n    if (!this.isAlive) {\n      return;\n    }\n\n    this.event.unbindAll();\n    DOM.remove(this.scrollbarX);\n    DOM.remove(this.scrollbarY);\n    DOM.remove(this.scrollbarXRail);\n    DOM.remove(this.scrollbarYRail);\n    this.removePsClasses();\n\n    // unset elements\n    this.element = null;\n    this.scrollbarX = null;\n    this.scrollbarY = null;\n    this.scrollbarXRail = null;\n    this.scrollbarYRail = null;\n\n    this.isAlive = false;\n  }\n\n  removePsClasses() {\n    this.element.className = this.element.className\n      .split(' ')\n      .filter(name => !name.match(/^ps([-_].+|)$/))\n      .join(' ');\n  }\n}\n", "import updateGeometry from '../update-geometry';\n\nexport default function(i) {\n  const element = i.element;\n\n  i.event.bind(i.scrollbarY, 'mousedown', e => e.stopPropagation());\n  i.event.bind(i.scrollbarYRail, 'mousedown', e => {\n    const positionTop =\n      e.pageY -\n      window.pageYOffset -\n      i.scrollbarYRail.getBoundingClientRect().top;\n    const direction = positionTop > i.scrollbarYTop ? 1 : -1;\n\n    i.element.scrollTop += direction * i.containerHeight;\n    updateGeometry(i);\n\n    e.stopPropagation();\n  });\n\n  i.event.bind(i.scrollbarX, 'mousedown', e => e.stopPropagation());\n  i.event.bind(i.scrollbarXRail, 'mousedown', e => {\n    const positionLeft =\n      e.pageX -\n      window.pageXOffset -\n      i.scrollbarXRail.getBoundingClientRect().left;\n    const direction = positionLeft > i.scrollbarXLeft ? 1 : -1;\n\n    i.element.scrollLeft += direction * i.containerWidth;\n    updateGeometry(i);\n\n    e.stopPropagation();\n  });\n}\n", "import * as DOM from '../lib/dom';\nimport updateGeometry from '../update-geometry';\nimport { isEditable } from '../lib/util';\n\nexport default function(i) {\n  const element = i.element;\n\n  const elementHovered = () => DOM.matches(element, ':hover');\n  const scrollbarFocused = () =>\n    DOM.matches(i.scrollbarX, ':focus') || DOM.matches(i.scrollbarY, ':focus');\n\n  function shouldPreventDefault(deltaX, deltaY) {\n    const scrollTop = Math.floor(element.scrollTop);\n    if (deltaX === 0) {\n      if (!i.scrollbarYActive) {\n        return false;\n      }\n      if (\n        (scrollTop === 0 && deltaY > 0) ||\n        (scrollTop >= i.contentHeight - i.containerHeight && deltaY < 0)\n      ) {\n        return !i.settings.wheelPropagation;\n      }\n    }\n\n    const scrollLeft = element.scrollLeft;\n    if (deltaY === 0) {\n      if (!i.scrollbarXActive) {\n        return false;\n      }\n      if (\n        (scrollLeft === 0 && deltaX < 0) ||\n        (scrollLeft >= i.contentWidth - i.containerWidth && deltaX > 0)\n      ) {\n        return !i.settings.wheelPropagation;\n      }\n    }\n    return true;\n  }\n\n  i.event.bind(i.ownerDocument, 'keydown', e => {\n    if (\n      (e.isDefaultPrevented && e.isDefaultPrevented()) ||\n      e.defaultPrevented\n    ) {\n      return;\n    }\n\n    if (!elementHovered() && !scrollbarFocused()) {\n      return;\n    }\n\n    let activeElement = document.activeElement\n      ? document.activeElement\n      : i.ownerDocument.activeElement;\n    if (activeElement) {\n      if (activeElement.tagName === 'IFRAME') {\n        activeElement = activeElement.contentDocument.activeElement;\n      } else {\n        // go deeper if element is a webcomponent\n        while (activeElement.shadowRoot) {\n          activeElement = activeElement.shadowRoot.activeElement;\n        }\n      }\n      if (isEditable(activeElement)) {\n        return;\n      }\n    }\n\n    let deltaX = 0;\n    let deltaY = 0;\n\n    switch (e.which) {\n      case 37: // left\n        if (e.metaKey) {\n          deltaX = -i.contentWidth;\n        } else if (e.altKey) {\n          deltaX = -i.containerWidth;\n        } else {\n          deltaX = -30;\n        }\n        break;\n      case 38: // up\n        if (e.metaKey) {\n          deltaY = i.contentHeight;\n        } else if (e.altKey) {\n          deltaY = i.containerHeight;\n        } else {\n          deltaY = 30;\n        }\n        break;\n      case 39: // right\n        if (e.metaKey) {\n          deltaX = i.contentWidth;\n        } else if (e.altKey) {\n          deltaX = i.containerWidth;\n        } else {\n          deltaX = 30;\n        }\n        break;\n      case 40: // down\n        if (e.metaKey) {\n          deltaY = -i.contentHeight;\n        } else if (e.altKey) {\n          deltaY = -i.containerHeight;\n        } else {\n          deltaY = -30;\n        }\n        break;\n      case 32: // space bar\n        if (e.shiftKey) {\n          deltaY = i.containerHeight;\n        } else {\n          deltaY = -i.containerHeight;\n        }\n        break;\n      case 33: // page up\n        deltaY = i.containerHeight;\n        break;\n      case 34: // page down\n        deltaY = -i.containerHeight;\n        break;\n      case 36: // home\n        deltaY = i.contentHeight;\n        break;\n      case 35: // end\n        deltaY = -i.contentHeight;\n        break;\n      default:\n        return;\n    }\n\n    if (i.settings.suppressScrollX && deltaX !== 0) {\n      return;\n    }\n    if (i.settings.suppressScrollY && deltaY !== 0) {\n      return;\n    }\n\n    element.scrollTop -= deltaY;\n    element.scrollLeft += deltaX;\n    updateGeometry(i);\n\n    if (shouldPreventDefault(deltaX, deltaY)) {\n      e.preventDefault();\n    }\n  });\n}\n", "import updateGeometry from '../update-geometry';\nimport cls from '../lib/class-names';\nimport * as CSS from '../lib/css';\nimport { env } from '../lib/util';\n\nexport default function(i) {\n  if (!env.supportsTouch && !env.supportsIePointer) {\n    return;\n  }\n\n  const element = i.element;\n\n  function shouldPrevent(deltaX, deltaY) {\n    const scrollTop = Math.floor(element.scrollTop);\n    const scrollLeft = element.scrollLeft;\n    const magnitudeX = Math.abs(deltaX);\n    const magnitudeY = Math.abs(deltaY);\n\n    if (magnitudeY > magnitudeX) {\n      // user is perhaps trying to swipe up/down the page\n\n      if (\n        (deltaY < 0 && scrollTop === i.contentHeight - i.containerHeight) ||\n        (deltaY > 0 && scrollTop === 0)\n      ) {\n        // set prevent for mobile Chrome refresh\n        return window.scrollY === 0 && deltaY > 0 && env.isChrome;\n      }\n    } else if (magnitudeX > magnitudeY) {\n      // user is perhaps trying to swipe left/right across the page\n\n      if (\n        (deltaX < 0 && scrollLeft === i.contentWidth - i.containerWidth) ||\n        (deltaX > 0 && scrollLeft === 0)\n      ) {\n        return true;\n      }\n    }\n\n    return true;\n  }\n\n  function applyTouchMove(differenceX, differenceY) {\n    element.scrollTop -= differenceY;\n    element.scrollLeft -= differenceX;\n\n    updateGeometry(i);\n  }\n\n  let startOffset = {};\n  let startTime = 0;\n  let speed = {};\n  let easingLoop = null;\n\n  function getTouch(e) {\n    if (e.targetTouches) {\n      return e.targetTouches[0];\n    } else {\n      // Maybe IE pointer\n      return e;\n    }\n  }\n\n  function shouldHandle(e) {\n    if (e.pointerType && e.pointerType === 'pen' && e.buttons === 0) {\n      return false;\n    }\n    if (e.targetTouches && e.targetTouches.length === 1) {\n      return true;\n    }\n    if (\n      e.pointerType &&\n      e.pointerType !== 'mouse' &&\n      e.pointerType !== e.MSPOINTER_TYPE_MOUSE\n    ) {\n      return true;\n    }\n    return false;\n  }\n\n  function touchStart(e) {\n    if (!shouldHandle(e)) {\n      return;\n    }\n\n    const touch = getTouch(e);\n\n    startOffset.pageX = touch.pageX;\n    startOffset.pageY = touch.pageY;\n\n    startTime = new Date().getTime();\n\n    if (easingLoop !== null) {\n      clearInterval(easingLoop);\n    }\n  }\n\n  function shouldBeConsumedByChild(target, deltaX, deltaY) {\n    if (!element.contains(target)) {\n      return false;\n    }\n\n    let cursor = target;\n\n    while (cursor && cursor !== element) {\n      if (cursor.classList.contains(cls.element.consuming)) {\n        return true;\n      }\n\n      const style = CSS.get(cursor);\n\n      // if deltaY && vertical scrollable\n      if (deltaY && style.overflowY.match(/(scroll|auto)/)) {\n        const maxScrollTop = cursor.scrollHeight - cursor.clientHeight;\n        if (maxScrollTop > 0) {\n          if (\n            (cursor.scrollTop > 0 && deltaY < 0) ||\n            (cursor.scrollTop < maxScrollTop && deltaY > 0)\n          ) {\n            return true;\n          }\n        }\n      }\n      // if deltaX && horizontal scrollable\n      if (deltaX && style.overflowX.match(/(scroll|auto)/)) {\n        const maxScrollLeft = cursor.scrollWidth - cursor.clientWidth;\n        if (maxScrollLeft > 0) {\n          if (\n            (cursor.scrollLeft > 0 && deltaX < 0) ||\n            (cursor.scrollLeft < maxScrollLeft && deltaX > 0)\n          ) {\n            return true;\n          }\n        }\n      }\n\n      cursor = cursor.parentNode;\n    }\n\n    return false;\n  }\n\n  function touchMove(e) {\n    if (shouldHandle(e)) {\n      const touch = getTouch(e);\n\n      const currentOffset = { pageX: touch.pageX, pageY: touch.pageY };\n\n      const differenceX = currentOffset.pageX - startOffset.pageX;\n      const differenceY = currentOffset.pageY - startOffset.pageY;\n\n      if (shouldBeConsumedByChild(e.target, differenceX, differenceY)) {\n        return;\n      }\n\n      applyTouchMove(differenceX, differenceY);\n      startOffset = currentOffset;\n\n      const currentTime = new Date().getTime();\n\n      const timeGap = currentTime - startTime;\n      if (timeGap > 0) {\n        speed.x = differenceX / timeGap;\n        speed.y = differenceY / timeGap;\n        startTime = currentTime;\n      }\n\n      if (shouldPrevent(differenceX, differenceY)) {\n        e.preventDefault();\n      }\n    }\n  }\n  function touchEnd() {\n    if (i.settings.swipeEasing) {\n      clearInterval(easingLoop);\n      easingLoop = setInterval(function() {\n        if (i.isInitialized) {\n          clearInterval(easingLoop);\n          return;\n        }\n\n        if (!speed.x && !speed.y) {\n          clearInterval(easingLoop);\n          return;\n        }\n\n        if (Math.abs(speed.x) < 0.01 && Math.abs(speed.y) < 0.01) {\n          clearInterval(easingLoop);\n          return;\n        }\n\n        applyTouchMove(speed.x * 30, speed.y * 30);\n\n        speed.x *= 0.8;\n        speed.y *= 0.8;\n      }, 10);\n    }\n  }\n\n  if (env.supportsTouch) {\n    i.event.bind(element, 'touchstart', touchStart);\n    i.event.bind(element, 'touchmove', touchMove);\n    i.event.bind(element, 'touchend', touchEnd);\n  } else if (env.supportsIePointer) {\n    if (window.PointerEvent) {\n      i.event.bind(element, 'pointerdown', touchStart);\n      i.event.bind(element, 'pointermove', touchMove);\n      i.event.bind(element, 'pointerup', touchEnd);\n    } else if (window.MSPointerEvent) {\n      i.event.bind(element, 'MSPointerDown', touchStart);\n      i.event.bind(element, 'MSPointerMove', touchMove);\n      i.event.bind(element, 'MSPointerUp', touchEnd);\n    }\n  }\n}\n"], "names": ["Math", "abs", "floor", "get", "element", "getComputedStyle", "set", "obj", "const", "key", "let", "val", "style", "div", "className", "document", "createElement", "matches", "query", "elMatches", "Error", "call", "remove", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "query<PERSON><PERSON><PERSON><PERSON>", "selector", "Array", "prototype", "filter", "children", "child", "addScrollingClass", "i", "x", "classList", "cls", "state", "scrolling", "contains", "clearTimeout", "scrollingClassTimeout", "add", "removeScrollingClass", "setTimeout", "isAlive", "settings", "scrollingT<PERSON>eshold", "setScrollingClassInstantly", "createEvent", "name", "window", "CustomEvent", "evt", "initCustomEvent", "axis", "diff", "useScrollingClass", "forceFireReachEvent", "fields", "processScrollDiff", "ref", "reach", "y", "scrollTop", "contentHeight", "containerHeight", "dispatchEvent", "up", "down", "toInt", "parseInt", "isEditable", "el", "DOM.matches", "outerWidth", "styles", "CSS.get", "width", "paddingLeft", "paddingRight", "borderLeftWidth", "borderRightWidth", "ceil", "roundedScrollTop", "rect", "getBoundingClientRect", "containerWidth", "height", "contentWidth", "scrollWidth", "scrollHeight", "scrollbarXRail", "DOM.query<PERSON><PERSON><PERSON>n", "rail", "for<PERSON>ach", "DOM.remove", "append<PERSON><PERSON><PERSON>", "scrollbarYRail", "suppressScrollX", "scrollXMarginOffset", "scrollbarXActive", "railXWidth", "railXMarginWidth", "railXRatio", "scrollbarXWidth", "getThumbSize", "scrollbarXLeft", "negativeScrollAdjustment", "scrollLeft", "suppressScrollY", "scrollYMarginOffset", "scrollbarYActive", "railYHeight", "railYMarginHeight", "railYRatio", "scrollbarYHeight", "scrollbarYTop", "updateCss", "active", "isRtl", "thumbSize", "min", "max", "minScrollbar<PERSON><PERSON>th", "maxS<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "xRailOffset", "left", "isScrollbarXUsingBottom", "bottom", "scrollbarXBottom", "top", "scrollbarXTop", "CSS.set", "yRailOffset", "isScrollbarYUsingRight", "right", "scrollbarYRight", "scrollbarYOuterWidth", "scrollbarYLeft", "scrollbarX", "railBorderXWidth", "scrollbarY", "railBorderYWidth", "bindMouseScrollHandler", "mouseMoveHandler", "e", "touches", "pageY", "startingScrollTop", "scrollBy", "startingMousePageY", "updateGeometry", "stopPropagation", "preventDefault", "mouseUpHandler", "clicking", "event", "unbind", "ownerDocument", "bindMoves", "touchMode", "bind", "once", "Element", "webkitMatchesSelector", "mozMatchesSelector", "msMatchesSelector", "main", "rtl", "thumb", "consuming", "focus", "EventElement", "handlers", "eventName", "handler", "push", "addEventListener", "target", "this", "removeEventListener", "unbindAll", "prototypeAccessors", "isEmpty", "Object", "keys", "every", "length", "EventManager", "eventElements", "eventElement", "ee", "splice", "indexOf", "once<PERSON><PERSON><PERSON>", "env", "isWebKit", "documentElement", "supportsTouch", "navigator", "maxTouchPoints", "DocumentTouch", "supportsIePointer", "msMaxTouchPoints", "isChrome", "test", "userAgent", "defaultSettings", "swipeEasing", "useBothWheelAxes", "wheelPropagation", "wheelSpeed", "positionTop", "pageYOffset", "direction", "positionLeft", "pageX", "pageXOffset", "shouldPreventDefault", "deltaX", "deltaY", "elementHovered", "scrollbarFocused", "isDefaultPrevented", "defaultPrevented", "activeElement", "tagName", "contentDocument", "shadowRoot", "which", "metaKey", "altKey", "shift<PERSON>ey", "hitsBound", "isTop", "isBottom", "offsetHeight", "isLeft", "isRight", "offsetWidth", "getDeltaFromEvent", "wheelDeltaX", "wheelDeltaY", "deltaMode", "wheelDelta", "shouldBeConsumedByChild", "querySelector", "cursor", "overflowY", "match", "maxScrollTop", "clientHeight", "overflowX", "maxScrollLeft", "clientWidth", "mousewheelHandler", "shouldPrevent", "ctrl<PERSON>ey", "onwheel", "onmousew<PERSON><PERSON>", "magnitudeX", "magnitudeY", "scrollY", "applyTouchMove", "differenceX", "differenceY", "getTouch", "targetTouches", "<PERSON><PERSON><PERSON><PERSON>", "pointerType", "buttons", "MSPOINTER_TYPE_MOUSE", "touchStart", "touch", "startOffset", "startTime", "Date", "getTime", "easingLoop", "clearInterval", "touchMove", "currentOffset", "currentTime", "timeGap", "speed", "touchEnd", "setInterval", "isInitialized", "PointerEvent", "MSPointerEvent", "PerfectScrollbar", "userSettings", "nodeName", "blur", "isNegativeScroll", "originalScrollLeft", "result", "DOM.div", "setAttribute", "railXStyle", "isNaN", "display", "marginLeft", "marginRight", "railYStyle", "borderTopWidth", "borderBottomWidth", "marginTop", "marginBottom", "handler<PERSON>ame", "lastScrollTop", "lastScrollLeft", "onScroll", "update", "destroy", "removePsClasses", "split", "join"], "mappings": ";;;;kNAsBQA,IAAI,CAACC,MCfcD,IAAI,CAACE,MCPzB,QAASC,CAAAA,CAAT,CAAaC,CAAb,CAAsB,CAC3B,MAAOC,CAAAA,gBAAgB,CAACD,CAAD,CACxB,CAEM,QAASE,CAAAA,CAAT,CAAaF,CAAb,CAAsBG,CAAtB,CAA2B,CAChC,IAAKC,GAAMC,CAAAA,CAAX,GAAkBF,CAAAA,CAAlB,CAAuB,CACrBG,GAAIC,CAAAA,CAAG,CAAGJ,CAAG,CAACE,CAAD,CAAbC,CACmB,QAAf,QAAOC,CAAAA,CAFU,GAGnBA,CAHmB,MAAA,EAKrBP,CAAO,CAACQ,KAAR,CAAcH,CAAd,EAAqBE,CACtB,CACD,MAAOP,CAAAA,ECZF,QAASS,CAAAA,CAAT,CAAaC,CAAb,CAAwB,CAC7BN,GAAMK,CAAAA,CAAG,CAAGE,QAAQ,CAACC,aAAT,CAAuB,KAAvB,CAAZR,CAEA,MADAK,CAAAA,CAAG,CAACC,SAAJ,CAAgBA,CAChB,CAAOD,CACR,CASM,QAASI,CAAAA,CAAT,CAAiBb,CAAjB,CAA0Bc,CAA1B,CAAiC,CACtC,GAAI,CAACC,CAAL,CACE,KAAM,IAAIC,CAAAA,KAAJ,CAAU,sCAAV,CAAN,CAGF,MAAOD,CAAAA,CAAS,CAACE,IAAV,CAAejB,CAAf,CAAwBc,CAAxB,CACR,CAEM,QAASI,CAAAA,CAAT,CAAgBlB,CAAhB,CAAyB,CAC1BA,CAAO,CAACkB,MADkB,CAE5BlB,CAAO,CAACkB,MAAR,EAF4B,CAIxBlB,CAAO,CAACmB,UAJgB,EAK1BnB,CAAO,CAACmB,UAAR,CAAmBC,WAAnB,CAA+BpB,CAA/B,CAGL,CAEM,QAASqB,CAAAA,CAAT,CAAuBrB,CAAvB,CAAgCsB,CAAhC,CAA0C,CAC/C,MAAOC,CAAAA,KAAK,CAACC,SAAN,CAAgBC,MAAhB,CAAuBR,IAAvB,CAA4BjB,CAAO,CAAC0B,QAApC,UAA8CC,EAAM,OACzDd,CAAAA,CAAO,CAACc,CAAD,CAAQL,CAAR,CAAiB,CADnB,CAGR,CCZM,QAASM,CAAAA,CAAT,CAA2BC,CAA3B,CAA8BC,CAA9B,CAAiC,IAChCC,CAAAA,CAAS,CAAGF,CAAC,CAAC7B,OAAF,CAAU+B,SADU,CAEhCrB,CAAS,CAAGsB,CAAG,CAACC,KAAJ,CAAUC,SAAV,CAAoBJ,CAApB,CAFoB,CAIlCC,CAAS,CAACI,QAAV,CAAmBzB,CAAnB,CAJkC,CAKpC0B,YAAY,CAACC,CAAqB,CAACP,CAAD,CAAtB,CALwB,CAOpCC,CAAS,CAACO,GAAV,CAAc5B,CAAd,CAEH,CAEM,QAAS6B,CAAAA,CAAT,CAA8BV,CAA9B,CAAiCC,CAAjC,CAAoC,CACzCO,CAAqB,CAACP,CAAD,CAArB,CAA2BU,UAAU,WAChC,OAAGX,CAAAA,CAAC,CAACY,OAAF,EAAaZ,CAAC,CAAC7B,OAAF,CAAU+B,SAAV,CAAoBb,MAApB,CAA2Bc,CAAG,CAACC,KAAJ,CAAUC,SAAV,CAAoBJ,CAApB,CAA3B,CAAkD,CADlC,CAEnCD,CAAC,CAACa,QAAF,CAAWC,kBAFwB,CAItC,CAEM,QAASC,CAAAA,CAAT,CAAoCf,CAApC,CAAuCC,CAAvC,CAA0C,CAC/CF,CAAiB,CAACC,CAAD,CAAIC,CAAJ,CAD8B,CAE/CS,CAAoB,CAACV,CAAD,CAAIC,CAAJ,CACrB,CC1CD,QAASe,CAAAA,CAAT,CAAqBC,CAArB,CAA2B,CACzB,GAAkC,UAA9B,QAAOC,CAAAA,MAAM,CAACC,WAAlB,CACE,MAAO,IAAIA,CAAAA,WAAJ,CAAgBF,CAAhB,CAAP,CAEA1C,GAAM6C,CAAAA,CAAG,CAAGtC,QAAQ,CAACkC,WAAT,CAAqB,aAArB,CAAZzC,CAEA,MADA6C,CAAAA,CAAG,CAACC,eAAJ,CAAoBJ,CAApB,cACA,CAAOG,CAEV,CAEc,UAAA,CACbpB,CADa,CAEbsB,CAFa,CAGbC,CAHa,CAIbC,CAJa,CAKbC,CALa,CAMb,WAAA,IAFiB,GAEjB,YAAA,IADmB,GACnB,EACAhD,GAAIiD,CAAAA,CAAJjD,CACA,GAAa,KAAT,GAAA6C,CAAJ,CACEI,CAAM,CAAG,CACP,eADO,CAEP,iBAFO,CAGP,WAHO,CAIP,GAJO,CAKP,IALO,CAMP,MANO,CADX,KASO,IAAa,MAAT,GAAAJ,CAAJ,CACLI,CAAM,CAAG,CACP,cADO,CAEP,gBAFO,CAGP,YAHO,CAIP,GAJO,CAKP,MALO,CAMP,OANO,CADJ,KAUL,MAAM,IAAIvC,CAAAA,KAAJ,CAAU,kCAAV,CAAN,CAGFwC,CAAiB,CAAC3B,CAAD,CAAIuB,CAAJ,CAAUG,CAAV,CAAkBF,CAAlB,CAAqCC,CAArC,CAClB,CAED,QAASE,CAAAA,CAAT,CACE3B,CADF,CAEEuB,CAFF,CAGEK,CAHF,CAIEJ,CAJF,CAKEC,CALF,CAME,WAAA,OAAA,OAAA,OAAA,OAAA,OAAA,WAAA,IAFiB,GAEjB,YAAA,IADmB,GACnB,EACAlD,GAAMJ,CAAAA,CAAO,CAAG6B,CAAC,CAAC7B,OAAlBI;AAGAyB,CAAC,CAAC6B,KAAF,CAAQC,CAAR,EAAa,IAJb,CAOyB,CAArB,CAAA3D,CAAO,CAAC4D,CAAD,CAPX,GAQE/B,CAAC,CAAC6B,KAAF,CAAQC,CAAR,EAAa,OARf,EAYI3D,CAAO,CAAC4D,CAAD,CAAP,CAAqB/B,CAAC,CAACgC,CAAD,CAAD,CAAmBhC,CAAC,CAACiC,CAAD,CAApB,CAAwC,CAZjE,GAaEjC,CAAC,CAAC6B,KAAF,CAAQC,CAAR,EAAa,KAbf,EAgBIP,CAhBJ,GAiBEpD,CAAO,CAAC+D,aAAR,CAAsBlB,CAAW,cAAcc,CAAd,CAAjC,CAjBF,CAmBa,CAAP,CAAAP,CAnBN,CAoBIpD,CAAO,CAAC+D,aAAR,CAAsBlB,CAAW,cAAcmB,CAAd,CAAjC,CApBJ,CAqBoB,CAAP,CAAAZ,CArBb,EAsBIpD,CAAO,CAAC+D,aAAR,CAAsBlB,CAAW,cAAcoB,CAAd,CAAjC,CAtBJ,CAyBMZ,CAzBN,EA0BIT,CAA0B,CAACf,CAAD,CAAI8B,CAAJ,CA1B9B,EA8BI9B,CAAC,CAAC6B,KAAF,CAAQC,CAAR,IAAeP,CAAI,EAAIE,CAAvB,CA9BJ,EA+BEtD,CAAO,CAAC+D,aAAR,CAAsBlB,CAAW,OAAOc,YAAW9B,CAAC,CAAC6B,KAAF,CAAQC,CAAR,CAAlB,CAAjC,CAEH,CCjFM,QAASO,CAAAA,CAAT,CAAepC,CAAf,CAAkB,CACvB,MAAOqC,CAAAA,QAAQ,CAACrC,CAAD,CAAI,EAAJ,CAAR,EAAmB,CAC3B,CAEM,QAASsC,CAAAA,CAAT,CAAoBC,CAApB,CAAwB,CAC7B,MACEC,CAAAA,CAAW,CAACD,CAAD,CAAK,yBAAL,CAAXC,EACAA,CAAW,CAACD,CAAD,CAAK,0BAAL,CADXC,EAEAA,CAAW,CAACD,CAAD,CAAK,4BAAL,CAFXC,EAGAA,CAAW,CAACD,CAAD,CAAK,0BAAL,CAEd,CAEM,QAASE,CAAAA,CAAT,CAAoBvE,CAApB,CAA6B,CAClCI,GAAMoE,CAAAA,CAAM,CAAGC,CAAO,CAACzE,CAAD,CAAtBI,CACA,MACE8D,CAAAA,CAAK,CAACM,CAAM,CAACE,KAAR,CAAL,CACAR,CAAK,CAACM,CAAM,CAACG,WAAR,CADL,CAEAT,CAAK,CAACM,CAAM,CAACI,YAAR,CAFL,CAGAV,CAAK,CAACM,CAAM,CAACK,eAAR,CAHL,CAIAX,CAAK,CAACM,CAAM,CAACM,gBAAR,CAER,CLpBc,UAAA,CAASjD,CAAT,CAAY,OAKNjC,IAAI,CAACmF,IALC,CACnB/E,CAAO,CAAG6B,CAAC,CAAC7B,OADO,CAEnBgF,CAAgB,CAAG,EAAWhF,CAAO,CAAC4D,SAAnB,CAFA,CAGnBqB,CAAI,CAAGjF,CAAO,CAACkF,qBAAR,EAHY,CAKzBrD,CAAC,CAACsD,cAAF,CAAmB,EAAUF,CAAI,CAACP,KAAf,CALM,CAMzB7C,CAAC,CAACiC,eAAF,CAAoB,EAAUmB,CAAI,CAACG,MAAf,CANK,CAOzBvD,CAAC,CAACwD,YAAF,CAAiBrF,CAAO,CAACsF,WAPA,CAQzBzD,CAAC,CAACgC,aAAF,CAAkB7D,CAAO,CAACuF,YARD,CAUpBvF,CAAO,CAACmC,QAAR,CAAiBN,CAAC,CAAC2D,cAAnB,CAVoB,GAYvBC,CAAiB,CAACzF,CAAD,CAAUgC,CAAG,CAAChC,OAAJ,CAAY0F,IAAZ,CAAiB,GAAjB,CAAV,CAAjBD,CAAkDE,OAAlDF,UAA0DpB,EAAG,OAC3DuB,CAAAA,CAAU,CAACvB,CAAD,CAAI,CADhBoB,CAZuB,CAevBzF,CAAO,CAAC6F,WAAR,CAAoBhE,CAAC,CAAC2D,cAAtB,CAfuB,EAiBpBxF,CAAO,CAACmC,QAAR,CAAiBN,CAAC,CAACiE,cAAnB,CAjBoB,GAmBvBL,CAAiB,CAACzF,CAAD,CAAUgC,CAAG,CAAChC,OAAJ,CAAY0F,IAAZ,CAAiB,GAAjB,CAAV,CAAjBD,CAAkDE,OAAlDF,UAA0DpB,EAAG,OAC3DuB,CAAAA,CAAU,CAACvB,CAAD,CAAI,CADhBoB,CAnBuB,CAsBvBzF,CAAO,CAAC6F,WAAR,CAAoBhE,CAAC,CAACiE,cAAtB,CAtBuB,EA0BvB,CAACjE,CAAC,CAACa,QAAF,CAAWqD,eAAZ,EACAlE,CAAC,CAACsD,cAAF,CAAmBtD,CAAC,CAACa,QAAF,CAAWsD,mBAA9B,CAAoDnE,CAAC,CAACwD,YA3B/B,EA6BvBxD,CAAC,CAACoE,gBAAF,GA7BuB,CA8BvBpE,CAAC,CAACqE,UAAF,CAAerE,CAAC,CAACsD,cAAF,CAAmBtD,CAAC,CAACsE,gBA9Bb,CA+BvBtE,CAAC,CAACuE,UAAF,CAAevE,CAAC,CAACsD,cAAF,CAAmBtD,CAAC,CAACqE,UA/Bb,CAgCvBrE,CAAC,CAACwE,eAAF,CAAoBC,CAAY,CAC9BzE,CAD8B,CAE9BqC,CAAK,CAAErC,CAAC,CAACqE,UAAF,CAAerE,CAAC,CAACsD,cAAlB,CAAoCtD,CAAC,CAACwD,YAAvC,CAFyB,CAhCT,CAoCvBxD,CAAC,CAAC0E,cAAF,CAAmBrC,CAAK,CACrB,CAACrC,CAAC,CAAC2E,wBAAF,CAA6BxG,CAAO,CAACyG,UAAtC,GACE5E,CAAC,CAACqE,UAAF,CAAerE,CAAC,CAACwE,eADnB,CAAD,EAEGxE,CAAC,CAACwD,YAAF,CAAiBxD,CAAC,CAACsD,cAFtB,CADsB,CApCD,EA0CvBtD,CAAC,CAACoE,gBAAF,GA1CuB,CA8CvB,CAACpE,CAAC,CAACa,QAAF,CAAWgE,eAAZ,EACA7E,CAAC,CAACiC,eAAF,CAAoBjC,CAAC,CAACa,QAAF,CAAWiE,mBAA/B,CAAqD9E,CAAC,CAACgC,aA/ChC,EAiDvBhC,CAAC,CAAC+E,gBAAF,GAjDuB,CAkDvB/E,CAAC,CAACgF,WAAF,CAAgBhF,CAAC,CAACiC,eAAF,CAAoBjC,CAAC,CAACiF,iBAlDf,CAmDvBjF,CAAC,CAACkF,UAAF,CAAelF,CAAC,CAACiC,eAAF,CAAoBjC,CAAC,CAACgF,WAnDd,CAoDvBhF,CAAC,CAACmF,gBAAF,CAAqBV,CAAY,CAC/BzE,CAD+B,CAE/BqC,CAAK,CAAErC,CAAC,CAACgF,WAAF,CAAgBhF,CAAC,CAACiC,eAAnB,CAAsCjC,CAAC,CAACgC,aAAzC,CAF0B,CApDV,CAwDvBhC,CAAC,CAACoF,aAAF,CAAkB/C,CAAK,CACpBc,CAAgB,EAAInD,CAAC,CAACgF,WAAF,CAAgBhF,CAAC,CAACmF,gBAAtB,CAAjB,EACGnF,CAAC,CAACgC,aAAF,CAAkBhC,CAAC,CAACiC,eADvB,CADqB,CAxDA,EA6DvBjC,CAAC,CAAC+E,gBAAF,GA7DuB,CAgErB/E,CAAC,CAAC0E,cAAF,EAAoB1E,CAAC,CAACqE,UAAF,CAAerE,CAAC,CAACwE,eAhEhB,GAiEvBxE,CAAC,CAAC0E,cAAF,CAAmB1E,CAAC,CAACqE,UAAF,CAAerE,CAAC,CAACwE,eAjEb,EAmErBxE,CAAC,CAACoF,aAAF,EAAmBpF,CAAC,CAACgF,WAAF,CAAgBhF,CAAC,CAACmF,gBAnEhB,GAoEvBnF,CAAC,CAACoF,aAAF,CAAkBpF,CAAC,CAACgF,WAAF,CAAgBhF,CAAC,CAACmF,gBApEb,EAuEzBE,CAAS,CAAClH,CAAD,CAAU6B,CAAV,CAvEgB,CAyErBA,CAAC,CAACoE,gBAzEmB,CA0EvBjG,CAAO,CAAC+B,SAAR,CAAkBO,GAAlB,CAAsBN,CAAG,CAACC,KAAJ,CAAUkF,MAAV,CAAiB,GAAjB,CAAtB,CA1EuB,EA4EvBnH,CAAO,CAAC+B,SAAR,CAAkBb,MAAlB,CAAyBc,CAAG,CAACC,KAAJ,CAAUkF,MAAV,CAAiB,GAAjB,CAAzB,CA5EuB,CA6EvBtF,CAAC,CAACwE,eAAF,CAAoB,CA7EG,CA8EvBxE,CAAC,CAAC0E,cAAF,CAAmB,CA9EI,CA+EvBvG,CAAO,CAACyG,UAAR,CAAqB,KAAA5E,CAAC,CAACuF,KAAF,CAAmBvF,CAAC,CAACwD,YAArB,CAAoC,CA/ElC,EAiFrBxD,CAAC,CAAC+E,gBAjFmB,CAkFvB5G,CAAO,CAAC+B,SAAR,CAAkBO,GAAlB,CAAsBN,CAAG,CAACC,KAAJ,CAAUkF,MAAV,CAAiB,GAAjB,CAAtB,CAlFuB,EAoFvBnH,CAAO,CAAC+B,SAAR,CAAkBb,MAAlB,CAAyBc,CAAG,CAACC,KAAJ,CAAUkF,MAAV,CAAiB,GAAjB,CAAzB,CApFuB,CAqFvBtF,CAAC,CAACmF,gBAAF,CAAqB,CArFE,CAsFvBnF,CAAC,CAACoF,aAAF,CAAkB,CAtFK,CAuFvBjH,CAAO,CAAC4D,SAAR,CAAoB,CAvFG,CAyF1B,CAED,QAAS0C,CAAAA,CAAT,CAAsBzE,CAAtB,CAAyBwF,CAAzB,CAAoC,OAKpBzH,IAAI,CAAC0H,GALe,GAEpB1H,IAAI,CAAC2H,GAFe,CAOlC,MANI1F,CAAAA,CAAC,CAACa,QAAF,CAAW8E,kBAMf,GALEH,CAAS,CAAG,EAASA,CAAT,CAAoBxF,CAAC,CAACa,QAAF,CAAW8E,kBAA/B,CAKd,EAHI3F,CAAC,CAACa,QAAF,CAAW+E,kBAGf,GAFEJ,CAAS,CAAG,EAASA,CAAT,CAAoBxF,CAAC,CAACa,QAAF,CAAW+E,kBAA/B,CAEd,EAAOJ,CACR,CAED,QAASH,CAAAA,CAAT,CAAmBlH,CAAnB,CAA4B6B,CAA5B,CAA+B,IACvB6F,CAAAA,CAAW,CAAG,CAAEhD,KAAK,CAAE7C,CAAC,CAACqE,UAAX,CADS,CAEvBlB,CAAgB,CAAG,EAAWhF,CAAO,CAAC4D,SAAnB,CAFI,CAK3B8D,CAAW,CAACC,IALe,CAIzB9F,CAAC,CAACuF,KAJuB,CAMzBvF,CAAC,CAAC2E,wBAAF,CACAxG,CAAO,CAACyG,UADR,CAEA5E,CAAC,CAACsD,cAFF,CAGAtD,CAAC,CAACwD,YATuB,CAWRrF,CAAO,CAACyG,UAXA,CAazB5E,CAAC,CAAC+F,uBAbuB,CAc3BF,CAAW,CAACG,MAAZ,CAAqBhG,CAAC,CAACiG,gBAAF,CAAqB9C,CAdf,CAgB3B0C,CAAW,CAACK,GAAZ,CAAkBlG,CAAC,CAACmG,aAAF,CAAkBhD,CAhBT,CAkB7BiD,CAAO,CAACpG,CAAC,CAAC2D,cAAH,CAAmBkC,CAAnB,CAlBsB,CAoB7BtH,GAAM8H,CAAAA,CAAW,CAAG,CAAEH,GAAG,CAAE/C,CAAP,CAAyBI,MAAM,CAAEvD,CAAC,CAACgF,WAAnC,CAApBzG,CACIyB,CAAC,CAACsG,sBArBuB,CAsBvBtG,CAAC,CAACuF,KAtBqB,CAuBzBc,CAAW,CAACE,KAAZ,CACEvG,CAAC,CAACwD,YAAF,EACCxD,CAAC,CAAC2E,wBAAF,CAA6BxG,CAAO,CAACyG,UADtC,EAEA5E,CAAC,CAACwG,eAFF,CAGAxG,CAAC,CAACyG,oBAHF,CAIA,CA5BuB,CA8BzBJ,CAAW,CAACE,KAAZ,CAAoBvG,CAAC,CAACwG,eAAF,CAAoBrI,CAAO,CAACyG,UA9BvB,CAiCvB5E,CAAC,CAACuF,KAjCqB,CAkCzBc,CAAW,CAACP,IAAZ,CACE9F,CAAC,CAAC2E,wBAAF,CACAxG,CAAO,CAACyG,UADR,CAEmB,CAAnB,CAAA5E,CAAC,CAACsD,cAFF,CAGAtD,CAAC,CAACwD,YAHF,CAIAxD,CAAC,CAAC0G,cAJF,CAKA1G,CAAC,CAACyG,oBAxCqB,CA0CzBJ,CAAW,CAACP,IAAZ,CAAmB9F,CAAC,CAAC0G,cAAF,CAAmBvI,CAAO,CAACyG,UA1CrB,CA6C7BwB,CAAO,CAACpG,CAAC,CAACiE,cAAH,CAAmBoC,CAAnB,CA7CsB,CA+C7BD,CAAO,CAACpG,CAAC,CAAC2G,UAAH,CAAe,CACpBb,IAAI,CAAE9F,CAAC,CAAC0E,cADY,CAEpB7B,KAAK,CAAE7C,CAAC,CAACwE,eAAF,CAAoBxE,CAAC,CAAC4G,gBAFT,CAAf,CA/CsB,CAmD7BR,CAAO,CAACpG,CAAC,CAAC6G,UAAH,CAAe,CACpBX,GAAG,CAAElG,CAAC,CAACoF,aADa,CAEpB7B,MAAM,CAAEvD,CAAC,CAACmF,gBAAF,CAAqBnF,CAAC,CAAC8G,gBAFX,CAAf,CAIR,CM/HD,QAASC,CAAAA,CAAT,CACE/G,CADF,CAEE4B,CAFF,CAaE,CAOA,QAASoF,CAAAA,CAAT,CAA0BC,CAA1B,CAA6B,CACvBA,CAAC,CAACC,OAAF,EAAaD,CAAC,CAACC,OAAF,CAAU,CAAV,CADU,GAEzBD,CAAC,CAACE,CAAD,CAAD,CAAWF,CAAC,CAACC,OAAF,CAAU,CAAV,EAAaC,KAFC,EAI3BhJ,CAAO,CAAC4D,CAAD,CAAP,CACEqF,CAAiB,CAAGC,CAAQ,EAAIJ,CAAC,CAACE,CAAD,CAAD,CAAWG,CAAf,CALH,CAM3BvH,CAAiB,CAACC,CAAD,CAAI8B,CAAJ,CANU,CAO3ByF,CAAc,CAACvH,CAAD,CAPa,CAS3BiH,CAAC,CAACO,eAAF,EAT2B,CAU3BP,CAAC,CAACQ,cAAF,EACD,CAED,QAASC,CAAAA,CAAT,EAA0B,CACxBhH,CAAoB,CAACV,CAAD,CAAI8B,CAAJ,CADI,CAExB9B,CAAC,CAACiE,CAAD,CAAD,CAAkB/D,SAAlB,CAA4Bb,MAA5B,CAAmCc,CAAG,CAACC,KAAJ,CAAUuH,QAA7C,CAFwB,CAGxB3H,CAAC,CAAC4H,KAAF,CAAQC,MAAR,CAAe7H,CAAC,CAAC8H,aAAjB,CAAgC,WAAhC,CAA6Cd,CAA7C,CACD,CAED,QAASe,CAAAA,CAAT,CAAmBd,CAAnB,CAAsBe,CAAtB,CAAiC,CAC/BZ,CAAiB,CAAGjJ,CAAO,CAAC4D,CAAD,CADI,CAE3BiG,CAAS,EAAIf,CAAC,CAACC,OAFY,GAG7BD,CAAC,CAACE,CAAD,CAAD,CAAWF,CAAC,CAACC,OAAF,CAAU,CAAV,EAAaC,KAHK,EAK/BG,CAAkB,CAAGL,CAAC,CAACE,CAAD,CALS,CAM/BE,CAAQ,CACN,CAACrH,CAAC,CAACgC,CAAD,CAAD,CAAmBhC,CAAC,CAACiC,CAAD,CAArB,GACCjC,CAAC,CAACgF,CAAD,CAAD,CAAiBhF,CAAC,CAACmF,CAAD,CADnB,CAP6B,CAS1B6C,CAT0B,CAc7BhI,CAAC,CAAC4H,KAAF,CAAQK,IAAR,CAAajI,CAAC,CAAC8H,aAAf,CAA8B,WAA9B,CAA2Cd,CAA3C,CAd6B,EAU7BhH,CAAC,CAAC4H,KAAF,CAAQK,IAAR,CAAajI,CAAC,CAAC8H,aAAf,CAA8B,WAA9B,CAA2Cd,CAA3C,CAV6B,CAW7BhH,CAAC,CAAC4H,KAAF,CAAQM,IAAR,CAAalI,CAAC,CAAC8H,aAAf,CAA8B,SAA9B,CAAyCJ,CAAzC,CAX6B,CAY7BT,CAAC,CAACQ,cAAF,EAZ6B,EAiB/BzH,CAAC,CAACiE,CAAD,CAAD,CAAkB/D,SAAlB,CAA4BO,GAA5B,CAAgCN,CAAG,CAACC,KAAJ,CAAUuH,QAA1C,CAjB+B,CAmB/BV,CAAC,CAACO,eAAF,EACD,CA9CD,UAAA,OAAA,OAAA,OAAA,OAAA,OAAA,OAAA,OAAA,OAAA,CACMrJ,CAAO,CAAG6B,CAAC,CAAC7B,OADlB,CAGIiJ,CAAiB,CAAG,IAHxB,CAIIE,CAAkB,CAAG,IAJzB,CAKID,CAAQ,CAAG,IALf,CAgDArH,CAAC,CAAC4H,KAAF,CAAQK,IAAR,CAAajI,CAAC,CAAC6G,CAAD,CAAd,CAA4B,WAA5B,UAAyCI,EAAE,CACzCc,CAAS,CAACd,CAAD,CACV,CAFD,CAhDA,CAmDAjH,CAAC,CAAC4H,KAAF,CAAQK,IAAR,CAAajI,CAAC,CAAC6G,CAAD,CAAd,CAA4B,YAA5B,UAA0CI,EAAE,CAC1Cc,CAAS,CAACd,CAAD,IACV,CAFD,CAGD,IJ/FK/H,CAAAA,CAAS,CACM,WAAnB,QAAOiJ,CAAAA,OAAP,GACCA,OAAO,CAACxI,SAAR,CAAkBX,OAAlB,EACCmJ,OAAO,CAACxI,SAAR,CAAkByI,qBADnB,EAECD,OAAO,CAACxI,SAAR,CAAkB0I,kBAFnB,EAGCF,OAAO,CAACxI,SAAR,CAAkB2I,iBAJpB,ECPInI,CAAG,CAAG,CACVoI,IAAI,CAAE,IADI,CAEVC,GAAG,CAAE,SAFK,CAGVrK,OAAO,CAAE,CACPsK,KAAK,UAAExI,EAAE,oBAAgBA,CAAG,CADrB,CAEP4D,IAAI,UAAE5D,EAAE,mBAAeA,CAAG,CAFnB,CAGPyI,SAAS,CAAE,oBAHJ,CAHC,CAQVtI,KAAK,CAAE,CACLuI,KAAK,CAAE,WADF,CAELhB,QAAQ,CAAE,cAFL,CAGLrC,MAAM,UAAErF,EAAE,qBAAiBA,CAAG,CAHzB,CAILI,SAAS,UAAEJ,EAAE,wBAAoBA,CAAG,CAJ/B,CARG,EAqBNO,CAAqB,CAAG,CAAEP,CAAC,CAAE,IAAL,CAAW6B,CAAC,CAAE,IAAd,EIrBxB8G,CAAY,CAChB,SAAYzK,CAAZ,CAAqB,CACnB,KAAKA,OAAL,CAAeA,CADI,CAEnB,KAAK0K,QAAL,CAAgB,kCAGpBD,WAAA,CAAEX,IAAF,UAAOa,EAAWC,EAAS,CACiB,WAApC,QAAO,MAAKF,QAAL,CAAcC,CAAd,CADY,GAEvB,KAAOD,QAAP,CAAgBC,CAAhB,EAA6B,EAFN,EAIzB,KAAOD,QAAP,CAAgBC,CAAhB,EAA2BE,IAA3B,CAAgCD,CAAhC,CAJyB,CAKvB,KAAK5K,OAAL,CAAa8K,gBAAb,CAA8BH,CAA9B,CAAyCC,CAAzC,MAGJH,WAAA,CAAEf,MAAF,UAASiB,EAAWI,EAAQ,YACxB,KAAKL,QAAL,CAAcC,CAAd,EAA2B,KAAKD,QAAL,CAAcC,CAAd,EAAyBlJ,MAAzB,UAAgCmJ,EAAQ,UAC7DG,CAAM,EAAIH,CAAO,GAAKG,CADuC,IAIjEC,CAAI,CAAChL,OAALgL,CAAaC,mBAAbD,CAAiCL,CAAjCK,CAA4CJ,CAA5CI,IAJiE,IAMlE,CAN0B,GAS/BP,WAAA,CAAES,SAAF,WAAc,CACZ,IAAO9K,GAAM0C,CAAAA,CAAb,GAAqB,MAAK4H,QAA1B,CACI,KAAKhB,MAAL,CAAY5G,CAAZ,GAINqI,EAAMC,OAAN,IAAA,WAAgB,YACd,MAASC,CAAAA,MAAM,CAACC,IAAP,CAAY,KAAKZ,QAAjB,EAA2Ba,KAA3B,CACP,SAAElL,CAAF,CAAM,OAAiC,EAA9B2K,GAAAA,CAAI,CAACN,QAALM,CAAc3K,CAAd2K,EAAmBQ,MAAY,CADjC,CAGR,yCAGY,GAAMC,CAAAA,CAAY,CAC/B,UAAc,CACZ,KAAKC,aAAL,CAAqB,GAFV,CAKfD,WAAA,CAAEE,YAAF,UAAe3L,EAAS,CACtB,GAAM4L,CAAAA,CAAE,CAAG,KAAKF,aAAL,CAAmBjK,MAAnB,UAA0BmK,EAAG,OAAGA,CAAAA,CAAE,CAAC5L,OAAH,GAAeA,CAAO,CAAtD,EAAwD,CAAxD,CAAX,CAKA,MAJO4L,CAAAA,CAIP,GAHIA,CAAE,CAAG,GAAInB,CAAAA,CAAJ,CAAiBzK,CAAjB,CAGT,CAFE,KAAO0L,aAAP,CAAqBb,IAArB,CAA0Be,CAA1B,CAEF,EAASA,GAGXH,WAAA,CAAE3B,IAAF,UAAO9J,EAAS2K,EAAWC,EAAS,CAChC,KAAKe,YAAL,CAAkB3L,CAAlB,EAA2B8J,IAA3B,CAAgCa,CAAhC,CAA2CC,CAA3C,GAGJa,WAAA,CAAE/B,MAAF,UAAS1J,EAAS2K,EAAWC,EAAS,CACpC,GAAQgB,CAAAA,CAAE,CAAG,KAAKD,YAAL,CAAkB3L,CAAlB,CAAb,CACA4L,CAAI,CAAClC,MAAL,CAAYiB,CAAZ,CAAuBC,CAAvB,CAFoC,CAI9BgB,CAAE,CAACR,OAJ2B,EAMhC,KAAKM,aAAL,CAAmBG,MAAnB,CAA0B,KAAKH,aAAL,CAAmBI,OAAnB,CAA2BF,CAA3B,CAA1B,CAA0D,CAA1D,GAINH,WAAA,CAAEP,SAAF,WAAc,CACV,KAAKQ,aAAL,CAAmB/F,OAAnB,UAA2BmD,EAAE,OAAGA,CAAAA,CAAC,CAACoC,SAAF,EAAa,CAA7C,CADU,CAEV,KAAKQ,aAAL,CAAqB,IAGzBD,WAAA,CAAE1B,IAAF,UAAO/J,EAAS2K,EAAWC,EAAS,IAC1BgB,CAAAA,CAAE,CAAG,KAAKD,YAAL,CAAkB3L,CAAlB,CADqB,CAE1B+L,CAAW,UAAG9I,EAAI,CACxB2I,CAAI,CAAClC,MAAL,CAAYiB,CAAZ,CAAuBoB,CAAvB,CADwB,CAEtBnB,CAAO,CAAC3H,CAAD,CACR,CAL+B,CAMlC2I,CAAI,CAAC9B,IAAL,CAAUa,CAAV,CAAqBoB,CAArB,CACC,KFlDUC,CAAAA,CAAG,CAAG,CACjBC,QAAQ,CACc,WAApB,QAAOtL,CAAAA,QAAP,EACA,oBAAsBA,CAAAA,QAAQ,CAACuL,eAAT,CAAyB1L,KAHhC,CAIjB2L,aAAa,CACO,WAAlB,QAAOpJ,CAAAA,MAAP,GACC,gBAAkBA,CAAAA,MAAlB,EACE,kBAAoBA,CAAAA,MAAM,CAACqJ,SAA3B,EACmC,CAAlC,CAAArJ,MAAM,CAACqJ,SAAP,CAAiBC,cAFpB,EAGEtJ,MAAM,CAACuJ,aAAP,EAAwB3L,QAAQ,WAAYoC,CAAAA,MAAM,CAACuJ,aAJtD,CALe,CAUjBC,iBAAiB,CACM,WAArB,QAAOH,CAAAA,SAAP,EAAoCA,SAAS,CAACI,gBAX/B,CAYjBC,QAAQ,CACe,WAArB,QAAOL,CAAAA,SAAP,EACA,UAAUM,IAAV,CAAeN,SAAS,EAAIA,SAAS,CAACO,SAAtC,CAde,EGbbC,CAAe,WAAM,OAAI,CAC7BlC,QAAQ,CAAE,CAAC,YAAD,CAAe,YAAf,CAA6B,UAA7B,CAAyC,OAAzC,CAAkD,OAAlD,CADmB,CAE7BjD,kBAAkB,CAAE,IAFS,CAG7BD,kBAAkB,CAAE,IAHS,CAI7B7E,kBAAkB,CAAE,GAJS,CAK7BqD,mBAAmB,CAAE,CALQ,CAM7BW,mBAAmB,CAAE,CANQ,CAO7BZ,eAAe,GAPc,CAQ7BW,eAAe,GARc,CAS7BmG,WAAW,GATkB,CAU7BC,gBAAgB,GAVa,CAW7BC,gBAAgB,GAXa,CAY7BC,UAAU,CAAE,CAZiB,CAa7B,EAEItC,CAAQ,CAAG,CACf,aC5Ba,SAAS7I,CAAT,CAAY,CACTA,CAAC,CAAC7B,OADO,CAGzB6B,CAAC,CAAC4H,KAAF,CAAQK,IAAR,CAAajI,CAAC,CAAC6G,UAAf,CAA2B,WAA3B,UAAwCI,EAAE,OAAGA,CAAAA,CAAC,CAACO,eAAF,EAAmB,CAAhE,CAHyB,CAIzBxH,CAAC,CAAC4H,KAAF,CAAQK,IAAR,CAAajI,CAAC,CAACiE,cAAf,CAA+B,WAA/B,UAA4CgD,EAAE,IACtCmE,CAAAA,CAAW,CACfnE,CAAC,CAACE,KAAF,CACAjG,MAAM,CAACmK,WADP,CAEArL,CAAC,CAACiE,cAAF,CAAiBZ,qBAAjB,GAAyC6C,GAJC,CAKtCoF,CAAS,CAAGF,CAAW,CAAGpL,CAAC,CAACoF,aAAhB,CAAgC,CAAhC,CAAoC,CAAC,CALX,CAO5CpF,CAAC,CAAC7B,OAAF,CAAU4D,SAAV,EAAuBuJ,CAAS,CAAGtL,CAAC,CAACiC,eAPO,CAQ5CsF,CAAc,CAACvH,CAAD,CAR8B,CAU5CiH,CAAC,CAACO,eAAF,EACD,CAXD,CAJyB,CAiBzBxH,CAAC,CAAC4H,KAAF,CAAQK,IAAR,CAAajI,CAAC,CAAC2G,UAAf,CAA2B,WAA3B,UAAwCM,EAAE,OAAGA,CAAAA,CAAC,CAACO,eAAF,EAAmB,CAAhE,CAjByB,CAkBzBxH,CAAC,CAAC4H,KAAF,CAAQK,IAAR,CAAajI,CAAC,CAAC2D,cAAf,CAA+B,WAA/B,UAA4CsD,EAAE,IACtCsE,CAAAA,CAAY,CAChBtE,CAAC,CAACuE,KAAF,CACAtK,MAAM,CAACuK,WADP,CAEAzL,CAAC,CAAC2D,cAAF,CAAiBN,qBAAjB,GAAyCyC,IAJC,CAKtCwF,CAAS,CAAGC,CAAY,CAAGvL,CAAC,CAAC0E,cAAjB,CAAkC,CAAlC,CAAsC,CAAC,CALb,CAO5C1E,CAAC,CAAC7B,OAAF,CAAUyG,UAAV,EAAwB0G,CAAS,CAAGtL,CAAC,CAACsD,cAPM,CAQ5CiE,CAAc,CAACvH,CAAD,CAR8B,CAU5CiH,CAAC,CAACO,eAAF,EACD,CAXD,CAYD,CDHgB,CAEf,aFtBa,SAASxH,CAAT,CAAY,CACzB+G,CAAsB,CAAC/G,CAAD,CAAI,CACxB,gBADwB,CAExB,cAFwB,CAGxB,OAHwB,CAIxB,YAJwB,CAKxB,YALwB,CAMxB,iBANwB,CAOxB,YAPwB,CAQxB,GARwB,CASxB,gBATwB,CAAJ,CADG,CAYzB+G,CAAsB,CAAC/G,CAAD,CAAI,CACxB,iBADwB,CAExB,eAFwB,CAGxB,OAHwB,CAIxB,aAJwB,CAKxB,YALwB,CAMxB,kBANwB,CAOxB,WAPwB,CAQxB,GARwB,CASxB,gBATwB,CAAJ,CAWvB,CEHgB,UEzBF,SAASA,CAAT,CAAY,CAOzB,QAAS0L,CAAAA,CAAT,CAA8BC,CAA9B,CAAsCC,CAAtC,CAA8C,CAC5CrN,GAAMwD,CAAAA,CAAS,CAAG,EAAW5D,CAAO,CAAC4D,SAAnB,CAAlBxD,CACA,GAAe,CAAX,GAAAoN,CAAJ,CAAkB,CAChB,GAAI,CAAC3L,CAAC,CAAC+E,gBAAP,CACE,SAEF,GACiB,CAAd,GAAAhD,CAAS,EAAmB,CAAT,CAAA6J,CAApB,EACC7J,CAAS,EAAI/B,CAAC,CAACgC,aAAF,CAAkBhC,CAAC,CAACiC,eAAjC,EAA6D,CAAT,CAAA2J,CAFvD,CAIE,MAAO,CAAC5L,CAAC,CAACa,QAAF,CAAWqK,gBAEtB,CAED3M,GAAMqG,CAAAA,CAAU,CAAGzG,CAAO,CAACyG,UAA3BrG,CACA,GAAe,CAAX,GAAAqN,CAAJ,CAAkB,CAChB,GAAI,CAAC5L,CAAC,CAACoE,gBAAP,CACE,SAEF,GACkB,CAAf,GAAAQ,CAAU,EAAmB,CAAT,CAAA+G,CAArB,EACC/G,CAAU,EAAI5E,CAAC,CAACwD,YAAF,CAAiBxD,CAAC,CAACsD,cAAjC,EAA4D,CAAT,CAAAqI,CAFtD,CAIE,MAAO,CAAC3L,CAAC,CAACa,QAAF,CAAWqK,gBAEtB,CACD,QACD,CAlCwB,GACnB/M,CAAAA,CAAO,CAAG6B,CAAC,CAAC7B,OADO,CAGnB0N,CAAc,WAAM,OAAGpJ,CAAAA,CAAW,CAACtE,CAAD,CAAU,QAAV,CAAmB,CAHlC,CAInB2N,CAAgB,WAAM,OAC1BrJ,CAAAA,CAAW,CAACzC,CAAC,CAAC2G,UAAH,CAAe,QAAf,CAAXlE,EAAuCA,CAAW,CAACzC,CAAC,CAAC6G,UAAH,CAAe,QAAf,CAAwB,CALnD,CAoCzB7G,CAAC,CAAC4H,KAAF,CAAQK,IAAR,CAAajI,CAAC,CAAC8H,aAAf,CAA8B,SAA9B,UAAyCb,EAAE,CACzC,KACGA,CAAC,CAAC8E,kBAAF,EAAwB9E,CAAC,CAAC8E,kBAAF,EAAzB,EACA9E,CAAC,CAAC+E,gBAFJ,IAOKH,CAAc,EAAf,EAAsBC,CAAgB,EAP1C,GAWArN,GAAIwN,CAAAA,CAAa,CAAGnN,QAAQ,CAACmN,aAAT,CAChBnN,QAAQ,CAACmN,aADO,CAEhBjM,CAAC,CAAC8H,aAAF,CAAgBmE,aAFpBxN,CAGA,GAAIwN,CAAJ,CAAmB,CACjB,GAA8B,QAA1B,GAAAA,CAAa,CAACC,OAAlB,CACED,CAAa,CAAGA,CAAa,CAACE,eAAd,CAA8BF,aADhD;AAAA,KAISA,CAAa,CAACG,UAJvB,EAKIH,CAAa,CAAGA,CAAa,CAACG,UAAd,CAAyBH,aAAzC,CAGJ,GAAI1J,CAAU,CAAC0J,CAAD,CAAd,CACE,MAEH,CA1BD,GA4BIN,CAAAA,CAAM,CAAG,CA5Bb,CA6BIC,CAAM,CAAG,CA7Bb,CA+BA,OAAQ3E,CAAC,CAACoF,KAAV,EACE,IAAK,GAAL,CAEIV,CAFJ,CACM1E,CAAC,CAACqF,OADR,CAEa,CAACtM,CAAC,CAACwD,YAFhB,CAGayD,CAAC,CAACsF,MAHf,CAIa,CAACvM,CAAC,CAACsD,cAJhB,CAMa,CAAC,EANd,CAQE,MACF,IAAK,GAAL,CAEIsI,CAFJ,CACM3E,CAAC,CAACqF,OADR,CAEatM,CAAC,CAACgC,aAFf,CAGaiF,CAAC,CAACsF,MAHf,CAIavM,CAAC,CAACiC,eAJf,CAMa,EANb,CAQE,MACF,IAAK,GAAL,CAEI0J,CAFJ,CACM1E,CAAC,CAACqF,OADR,CAEatM,CAAC,CAACwD,YAFf,CAGayD,CAAC,CAACsF,MAHf,CAIavM,CAAC,CAACsD,cAJf,CAMa,EANb,CAQE,MACF,IAAK,GAAL,CAEIsI,CAFJ,CACM3E,CAAC,CAACqF,OADR,CAEa,CAACtM,CAAC,CAACgC,aAFhB,CAGaiF,CAAC,CAACsF,MAHf,CAIa,CAACvM,CAAC,CAACiC,eAJhB,CAMa,CAAC,EANd,CAQE,MACF,IAAK,GAAL,CAEI2J,CAFJ,CACM3E,CAAC,CAACuF,QADR,CAEaxM,CAAC,CAACiC,eAFf,CAIa,CAACjC,CAAC,CAACiC,eAJhB,CAME,MACF,IAAK,GAAL,CACE2J,CAAM,CAAG5L,CAAC,CAACiC,eADb,CAEE,MACF,IAAK,GAAL,CACE2J,CAAM,CAAG,CAAC5L,CAAC,CAACiC,eADd,CAEE,MACF,IAAK,GAAL,CACE2J,CAAM,CAAG5L,CAAC,CAACgC,aADb,CAEE,MACF,IAAK,GAAL,CACE4J,CAAM,CAAG,CAAC5L,CAAC,CAACgC,aADd,CAEE,MACF,QACE,OAzDJ,CA4DIhC,CAAC,CAACa,QAAF,CAAWqD,eAAX,EAAyC,CAAX,GAAAyH,CA3FlC,EA8FI3L,CAAC,CAACa,QAAF,CAAWgE,eAAX,EAAyC,CAAX,GAAA+G,CA9FlC,GAkGAzN,CAAO,CAAC4D,SAAR,EAAqB6J,CAlGrB,CAmGAzN,CAAO,CAACyG,UAAR,EAAsB+G,CAnGtB,CAoGApE,CAAc,CAACvH,CAAD,CApGd,CAsGI0L,CAAoB,CAACC,CAAD,CAASC,CAAT,CAtGxB,EAuGE3E,CAAC,CAACQ,cAAF,EAvGF,EAyGD,CA1GD,CA2GD,CFtHgB,OTxBF,SAASzH,CAAT,CAAY,CAKzB,QAAS0L,CAAAA,CAAT,CAA8BC,CAA9B,CAAsCC,CAAtC,CAA8C,IASxCa,CAAAA,CATwC,CACtCtJ,CAAgB,CAAG,EAAWhF,CAAO,CAAC4D,SAAnB,CADmB,CAEtC2K,CAAK,CAAyB,CAAtB,GAAAvO,CAAO,CAAC4D,SAFsB,CAGtC4K,CAAQ,CACZxJ,CAAgB,CAAGhF,CAAO,CAACyO,YAA3B,GAA4CzO,CAAO,CAACuF,YAJV,CAKtCmJ,CAAM,CAA0B,CAAvB,GAAA1O,CAAO,CAACyG,UALqB,CAMtCkI,CAAO,CACX3O,CAAO,CAACyG,UAAR,CAAqBzG,CAAO,CAAC4O,WAA7B,GAA6C5O,CAAO,CAACsF,WAPX,CAkB5C,MALEgJ,CAAAA,CAKF,CANI,EAASb,CAAT,EAAmB,EAASD,CAAT,CAMvB,CALce,CAAK,EAAIC,CAKvB,CAHcE,CAAM,EAAIC,CAGxB,EAAOL,CAAP,EAAmB,CAACzM,CAAC,CAACa,QAAF,CAAWqK,gBAChC,CAED,QAAS8B,CAAAA,CAAT,CAA2B/F,CAA3B,CAA8B,IACxB0E,CAAAA,CAAM,CAAG1E,CAAC,CAAC0E,MADa,CAExBC,CAAM,CAAG,CAAC,CAAD,CAAK3E,CAAC,CAAC2E,MAFQ,QAIN,WAAlB,QAAOD,CAAAA,CAAP,EAAmD,WAAlB,QAAOC,CAAAA,CAJhB,IAM1BD,CAAM,CAAI,CAAC,CAAD,CAAK1E,CAAC,CAACgG,WAAR,CAAuB,CANN,CAO1BrB,CAAM,CAAG3E,CAAC,CAACiG,WAAF,CAAgB,CAPC,EAUxBjG,CAAC,CAACkG,SAAF,EAA+B,CAAhB,GAAAlG,CAAC,CAACkG,SAVO,GAY1BxB,CAAM,EAAI,EAZgB,CAa1BC,CAAM,EAAI,EAbgB,EAgBxBD,CAAM,GAAKA,CAAX,EAAqBC,CAAM,GAAKA,iBAhBR,GAkB1BD,CAAM,CAAG,CAlBiB,CAmB1BC,CAAM,CAAG3E,CAAC,CAACmG,UAnBe,EAsBxBnG,CAAC,CAACuF,QAtBsB,CAwBnB,CAAC,CAACZ,CAAF,CAAU,CAACD,CAAX,CAxBmB,CA0BrB,CAACA,CAAD,CAASC,CAAT,CACR,CAED,QAASyB,CAAAA,CAAT,CAAiCnE,CAAjC,CAAyCyC,CAAzC,CAAiDC,CAAjD,CAAyD;AAEvD,GAAI,CAACzB,CAAG,CAACC,QAAL,EAAiBjM,CAAO,CAACmP,aAAR,CAAsB,cAAtB,CAArB,CACE,SAGF,GAAI,CAACnP,CAAO,CAACmC,QAAR,CAAiB4I,CAAjB,CAAL,CACE,SAPqD,IAUvDzK,GAAI8O,CAAAA,CAAM,CAAGrE,CAV0C,CAYhDqE,CAAM,EAAIA,CAAM,GAAKpP,CAZ2B,EAYlB,CACnC,GAAIoP,CAAM,CAACrN,SAAP,CAAiBI,QAAjB,CAA0BH,CAAG,CAAChC,OAAJ,CAAYuK,SAAtC,CAAJ,CACE,SAGFnK,GAAMI,CAAAA,CAAK,CAAGiE,CAAO,CAAC2K,CAAD,CAArBhP;AAGA,GAAIqN,CAAM,EAAIjN,CAAK,CAAC6O,SAAN,CAAgBC,KAAhB,CAAsB,eAAtB,CAAd,CAAsD,CACpDlP,GAAMmP,CAAAA,CAAY,CAAGH,CAAM,CAAC7J,YAAP,CAAsB6J,CAAM,CAACI,YAAlDpP,CACA,GAAmB,CAAf,CAAAmP,CAAJ,GAEwB,CAAnB,CAAAH,CAAM,CAACxL,SAAP,EAAiC,CAAT,CAAA6J,CAAzB,EACC2B,CAAM,CAACxL,SAAP,CAAmB2L,CAAnB,EAA4C,CAAT,CAAA9B,CAHxC,EAKI,QAGL;AAED,GAAID,CAAM,EAAIhN,CAAK,CAACiP,SAAN,CAAgBH,KAAhB,CAAsB,eAAtB,CAAd,CAAsD,CACpDlP,GAAMsP,CAAAA,CAAa,CAAGN,CAAM,CAAC9J,WAAP,CAAqB8J,CAAM,CAACO,WAAlDvP,CACA,GAAoB,CAAhB,CAAAsP,CAAJ,GAEyB,CAApB,CAAAN,CAAM,CAAC3I,UAAP,EAAkC,CAAT,CAAA+G,CAA1B,EACC4B,CAAM,CAAC3I,UAAP,CAAoBiJ,CAApB,EAA8C,CAAT,CAAAlC,CAH1C,EAKI,QAGL,CAED4B,CAAM,CAAGA,CAAM,CAACjO,UACjB,CAED,QACD,CAED,QAASyO,CAAAA,CAAT,CAA2B9G,CAA3B,CAA8B,MACN,CAAG+F,CAAiB,CAAC/F,CAAD,CADd,OAAA,OAAA,CAG5B,IAAIoG,CAAuB,CAACpG,CAAC,CAACiC,MAAH,CAAWyC,CAAX,CAAmBC,CAAnB,CAA3B,EAIAnN,GAAIuP,CAAAA,CAAa,GAAjBvP,CACKuB,CAAC,CAACa,QAAF,CAAWoK,gBALhB,CAUWjL,CAAC,CAAC+E,gBAAF,EAAsB,CAAC/E,CAAC,CAACoE,gBAVpC,EAaMwH,CAbN,CAcIzN,CAAO,CAAC4D,SAAR,EAAqB6J,CAAM,CAAG5L,CAAC,CAACa,QAAF,CAAWsK,UAd7C,CAgBIhN,CAAO,CAAC4D,SAAR,EAAqB4J,CAAM,CAAG3L,CAAC,CAACa,QAAF,CAAWsK,UAhB7C,CAkBE6C,CAAa,GAlBf,EAmBWhO,CAAC,CAACoE,gBAAF,EAAsB,CAACpE,CAAC,CAAC+E,gBAnBpC,GAsBM4G,CAtBN,CAuBIxN,CAAO,CAACyG,UAAR,EAAsB+G,CAAM,CAAG3L,CAAC,CAACa,QAAF,CAAWsK,UAvB9C,CAyBIhN,CAAO,CAACyG,UAAR,EAAsBgH,CAAM,CAAG5L,CAAC,CAACa,QAAF,CAAWsK,UAzB9C,CA2BE6C,CAAa,GA3Bf,GAQE7P,CAAO,CAAC4D,SAAR,EAAqB6J,CAAM,CAAG5L,CAAC,CAACa,QAAF,CAAWsK,UAR3C,CASEhN,CAAO,CAACyG,UAAR,EAAsB+G,CAAM,CAAG3L,CAAC,CAACa,QAAF,CAAWsK,UAT5C,EA8BA5D,CAAc,CAACvH,CAAD,CA9Bd,CAgCAgO,CAAa,CAAGA,CAAa,EAAItC,CAAoB,CAACC,CAAD,CAASC,CAAT,CAhCrD,CAiCIoC,CAAa,EAAI,CAAC/G,CAAC,CAACgH,OAjCxB,GAkCEhH,CAAC,CAACO,eAAF,EAlCF,CAmCEP,CAAC,CAACQ,cAAF,EAnCF,EAqCD,CAhJDlJ,GAAMJ,CAAAA,CAAO,CAAG6B,CAAC,CAAC7B,OAAlBI,CAkJ8B,WAA1B,QAAO2C,CAAAA,MAAM,CAACgN,OAnJO,CAqJiB,WAA/B,QAAOhN,CAAAA,MAAM,CAACiN,YArJA,EAsJvBnO,CAAC,CAAC4H,KAAF,CAAQK,IAAR,CAAa9J,CAAb,CAAsB,YAAtB,CAAoC4P,CAApC,CAtJuB,CAoJvB/N,CAAC,CAAC4H,KAAF,CAAQK,IAAR,CAAa9J,CAAb,CAAsB,OAAtB,CAA+B4P,CAA/B,CAIH,CShIgB,OGxBF,SAAS/N,CAAT,CAAY,CAOzB,QAASgO,CAAAA,CAAT,CAAuBrC,CAAvB,CAA+BC,CAA/B,CAAuC,IAC/B7J,CAAAA,CAAS,CAAG,EAAW5D,CAAO,CAAC4D,SAAnB,CADmB,CAE/B6C,CAAU,CAAGzG,CAAO,CAACyG,UAFU,CAG/BwJ,CAAU,CAAG,EAASzC,CAAT,CAHkB,CAI/B0C,CAAU,CAAG,EAASzC,CAAT,CAJkB,CAMrC,GAAIyC,CAAU,CAAGD,CAAjB;AAGE,GACY,CAAT,CAAAxC,CAAM,EAAQ7J,CAAS,GAAK/B,CAAC,CAACgC,aAAF,CAAkBhC,CAAC,CAACiC,eAAjD,EACU,CAAT,CAAA2J,CAAM,EAAsB,CAAd,GAAA7J,CAFjB;AAKE,MAA0B,EAAnB,GAAAb,MAAM,CAACoN,OAAP,EAAiC,CAAT,CAAA1C,CAAxB,EAAsCzB,CAAG,CAACS,QAAjD,CARJ,KAUO,IAAIwD,CAAU,CAAGC,CAAjB,GAIO,CAAT,CAAA1C,CAAM,EAAQ/G,CAAU,GAAK5E,CAAC,CAACwD,YAAF,CAAiBxD,CAAC,CAACsD,cAAjD,EACU,CAAT,CAAAqI,CAAM,EAAuB,CAAf,GAAA/G,CALZ;AAOH,SAIJ,QACD,CAED,QAAS2J,CAAAA,CAAT,CAAwBC,CAAxB,CAAqCC,CAArC,CAAkD,CAChDtQ,CAAO,CAAC4D,SAAR,EAAqB0M,CAD2B,CAEhDtQ,CAAO,CAACyG,UAAR,EAAsB4J,CAF0B,CAIhDjH,CAAc,CAACvH,CAAD,CACf,CAOD,QAAS0O,CAAAA,CAAT,CAAkBzH,CAAlB,CAAqB,OACfA,CAAAA,CAAC,CAAC0H,aADa,CAEV1H,CAAC,CAAC0H,aAAF,CAAgB,CAAhB,CAFU,CAKV1H,CAEV,CAED,QAAS2H,CAAAA,CAAT,CAAsB3H,CAAtB,CAAyB,SACnBA,CAAC,CAAC4H,WAAF,EAAmC,KAAlB,GAAA5H,CAAC,CAAC4H,WAAnB,EAA0D,CAAd,GAAA5H,CAAC,CAAC6H,OAD3B,OAInB7H,CAAC,CAAC0H,aAAF,EAA8C,CAA3B,GAAA1H,CAAC,CAAC0H,aAAF,CAAgBhF,MAJhB,MAQrB1C,CAAC,CAAC4H,WAAF,EACkB,OAAlB,GAAA5H,CAAC,CAAC4H,WADF,EAEA5H,CAAC,CAAC4H,WAAF,GAAkB5H,CAAC,CAAC8H,oBAVC,EAexB,CAED,QAASC,CAAAA,CAAT,CAAoB/H,CAApB,CAAuB,CACrB,GAAK2H,CAAY,CAAC3H,CAAD,CAAjB,EAIA1I,GAAM0Q,CAAAA,CAAK,CAAGP,CAAQ,CAACzH,CAAD,CAAtB1I,CAEA2Q,CAAW,CAAC1D,KAAZ,CAAoByD,CAAK,CAACzD,KAN1B,CAOA0D,CAAW,CAAC/H,KAAZ,CAAoB8H,CAAK,CAAC9H,KAP1B,CASAgI,CAAS,CAAG,GAAIC,CAAAA,IAAJ,GAAWC,OAAX,EATZ,CAWmB,IAAf,GAAAC,CAXJ,EAYEC,aAAa,CAACD,CAAD,CAZf,CAcD,CAED,QAASjC,CAAAA,CAAT,CAAiCnE,CAAjC,CAAyCyC,CAAzC,CAAiDC,CAAjD,CAAyD,CACvD,GAAI,CAACzN,CAAO,CAACmC,QAAR,CAAiB4I,CAAjB,CAAL,CACE,SAFqD,IAKvDzK,GAAI8O,CAAAA,CAAM,CAAGrE,CAL0C,CAOhDqE,CAAM,EAAIA,CAAM,GAAKpP,CAP2B,EAOlB,CACnC,GAAIoP,CAAM,CAACrN,SAAP,CAAiBI,QAAjB,CAA0BH,CAAG,CAAChC,OAAJ,CAAYuK,SAAtC,CAAJ,CACE,SAGFnK,GAAMI,CAAAA,CAAK,CAAGiE,CAAO,CAAC2K,CAAD,CAArBhP;AAGA,GAAIqN,CAAM,EAAIjN,CAAK,CAAC6O,SAAN,CAAgBC,KAAhB,CAAsB,eAAtB,CAAd,CAAsD,CACpDlP,GAAMmP,CAAAA,CAAY,CAAGH,CAAM,CAAC7J,YAAP,CAAsB6J,CAAM,CAACI,YAAlDpP,CACA,GAAmB,CAAf,CAAAmP,CAAJ,GAEwB,CAAnB,CAAAH,CAAM,CAACxL,SAAP,EAAiC,CAAT,CAAA6J,CAAzB,EACC2B,CAAM,CAACxL,SAAP,CAAmB2L,CAAnB,EAA4C,CAAT,CAAA9B,CAHxC,EAKI,QAGL;AAED,GAAID,CAAM,EAAIhN,CAAK,CAACiP,SAAN,CAAgBH,KAAhB,CAAsB,eAAtB,CAAd,CAAsD,CACpDlP,GAAMsP,CAAAA,CAAa,CAAGN,CAAM,CAAC9J,WAAP,CAAqB8J,CAAM,CAACO,WAAlDvP,CACA,GAAoB,CAAhB,CAAAsP,CAAJ,GAEyB,CAApB,CAAAN,CAAM,CAAC3I,UAAP,EAAkC,CAAT,CAAA+G,CAA1B,EACC4B,CAAM,CAAC3I,UAAP,CAAoBiJ,CAApB,EAA8C,CAAT,CAAAlC,CAH1C,EAKI,QAGL,CAED4B,CAAM,CAAGA,CAAM,CAACjO,UACjB,CAED,QACD,CAED,QAASkQ,CAAAA,CAAT,CAAmBvI,CAAnB,CAAsB,CACpB,GAAI2H,CAAY,CAAC3H,CAAD,CAAhB,CAAqB,IACbgI,CAAAA,CAAK,CAAGP,CAAQ,CAACzH,CAAD,CADH,CAGbwI,CAAa,CAAG,CAAEjE,KAAK,CAAEyD,CAAK,CAACzD,KAAf,CAAsBrE,KAAK,CAAE8H,CAAK,CAAC9H,KAAnC,CAHH,CAKbqH,CAAW,CAAGiB,CAAa,CAACjE,KAAd,CAAsB0D,CAAW,CAAC1D,KALnC,CAMbiD,CAAW,CAAGgB,CAAa,CAACtI,KAAd,CAAsB+H,CAAW,CAAC/H,KANnC,CAQnB,GAAIkG,CAAuB,CAACpG,CAAC,CAACiC,MAAH,CAAWsF,CAAX,CAAwBC,CAAxB,CAA3B,CACE,OAGFF,CAAc,CAACC,CAAD,CAAcC,CAAd,CAZK,CAanBS,CAAW,CAAGO,CAbK,IAebC,CAAAA,CAAW,CAAG,GAAIN,CAAAA,IAAJ,GAAWC,OAAX,EAfD,CAiBbM,CAAO,CAAGD,CAAW,CAAGP,CAjBX,CAkBL,CAAV,CAAAQ,CAlBe,GAmBjBC,CAAK,CAAC3P,CAAN,CAAUuO,CAAW,CAAGmB,CAnBP,CAoBjBC,CAAK,CAAC9N,CAAN,CAAU2M,CAAW,CAAGkB,CApBP,CAqBjBR,CAAS,CAAGO,CArBK,EAwBf1B,CAAa,CAACQ,CAAD,CAAcC,CAAd,CAxBE,EAyBjBxH,CAAC,CAACQ,cAAF,EAEH,CACF,CACD,QAASoI,CAAAA,CAAT,EAAoB,CACd7P,CAAC,CAACa,QAAF,CAAWmK,WADG,GAEhBuE,aAAa,CAACD,CAAD,CAFG,CAGhBA,CAAU,CAAGQ,WAAW,CAAC,UAAW,OAC9B9P,CAAAA,CAAC,CAAC+P,aAD4B,KAEhCR,CAAAA,aAAa,CAACD,CAAD,CAFmB,CAM7BM,CAAK,CAAC3P,CAAP,EAAa2P,CAAK,CAAC9N,CANW,CAWV,GAApB,GAAS8N,CAAK,CAAC3P,CAAf,GAAgD,GAApB,GAAS2P,CAAK,CAAC9N,CAAf,CAXE,KAYhCyN,CAAAA,aAAa,CAACD,CAAD,CAZmB,MAgBlCf,CAAc,CAAW,EAAV,CAAAqB,CAAK,CAAC3P,CAAP,CAAyB,EAAV,CAAA2P,CAAK,CAAC9N,CAArB,CAhBoB,CAkBlC8N,CAAK,CAAC3P,CAAN,EAAW,EAlBuB,CAmBlC2P,CAAK,CAAC9N,CAAN,EAAW,EAnBuB,MAOhCyN,CAAAA,aAAa,CAACD,CAAD,CAahB,CApBuB,CAoBrB,EApBqB,CAHR,CAyBnB,CA/LD,GAAKnF,CAAG,CAACG,aAAL,EAAuBH,CAAG,CAACO,iBAA/B,KAIMvM,CAAAA,CAAO,CAAG6B,CAAC,CAAC7B,OAJlB,CA2CI+Q,CAAW,CAAG,EA3ClB,CA4CIC,CAAS,CAAG,CA5ChB,CA6CIS,CAAK,CAAG,EA7CZ,CA8CIN,CAAU,CAAG,IA9CjB,CAiMInF,CAAG,CAACG,aAjMR,EAkMEtK,CAAC,CAAC4H,KAAF,CAAQK,IAAR,CAAa9J,CAAb,CAAsB,YAAtB,CAAoC6Q,CAApC,CAlMF,CAmMEhP,CAAC,CAAC4H,KAAF,CAAQK,IAAR,CAAa9J,CAAb,CAAsB,WAAtB,CAAmCqR,CAAnC,CAnMF,CAoMExP,CAAC,CAAC4H,KAAF,CAAQK,IAAR,CAAa9J,CAAb,CAAsB,UAAtB,CAAkC0R,CAAlC,CApMF,EAqMW1F,CAAG,CAACO,iBArMf,GAsMMxJ,MAAM,CAAC8O,YAtMb,EAuMIhQ,CAAC,CAAC4H,KAAF,CAAQK,IAAR,CAAa9J,CAAb,CAAsB,aAAtB,CAAqC6Q,CAArC,CAvMJ,CAwMIhP,CAAC,CAAC4H,KAAF,CAAQK,IAAR,CAAa9J,CAAb,CAAsB,aAAtB,CAAqCqR,CAArC,CAxMJ,CAyMIxP,CAAC,CAAC4H,KAAF,CAAQK,IAAR,CAAa9J,CAAb,CAAsB,WAAtB,CAAmC0R,CAAnC,CAzMJ,EA0Ma3O,MAAM,CAAC+O,cA1MpB,GA2MIjQ,CAAC,CAAC4H,KAAF,CAAQK,IAAR,CAAa9J,CAAb,CAAsB,eAAtB,CAAuC6Q,CAAvC,CA3MJ,CA4MIhP,CAAC,CAAC4H,KAAF,CAAQK,IAAR,CAAa9J,CAAb,CAAsB,eAAtB,CAAuCqR,CAAvC,CA5MJ,CA6MIxP,CAAC,CAAC4H,KAAF,CAAQK,IAAR,CAAa9J,CAAb,CAAsB,aAAtB,CAAqC0R,CAArC,CA7MJ,GAgND,CHzLgB,EAQIK,CAAgB,CACnC,SAAY/R,CAAZ,CAAqBgS,CAArB,CAAwC,YAKxC,aAAA,IALiC,CAAG,EAKpC,EAJyB,QAAnB,QAAOhS,CAAAA,CAIb,GAHEA,CAAS,CAAGW,QAAQ,CAACwO,aAAT,CAAuBnP,CAAvB,CAGd,EAAM,CAACA,CAAD,EAAY,CAACA,CAAO,CAACiS,QAA3B,CACI,KAAM,IAAIjR,CAAAA,KAAJ,CAAU,wDAAV,CAAN,CAQF,IAAKZ,GAAMC,CAAAA,CAAX,GALA,MAAKL,OAAL,CAAeA,CAKf,CAHFA,CAAS,CAAC+B,SAAV,CAAoBO,GAApB,CAAwBN,CAAG,CAACoI,IAA5B,CAGE,CADA,KAAK1H,QAAL,CAAgBkK,CAAe,EAC/B,CAAkBoF,CAAlB,CACA,KAAOtP,QAAP,CAAgBrC,CAAhB,EAAuB2R,CAAY,CAAC3R,CAAD,CAAnC,CAGA,KAAK8E,cAAL,CAAsB,IAlBgB,CAmBtC,KAAKrB,eAAL,CAAuB,IAnBe,CAoBtC,KAAKuB,YAAL,CAAoB,IApBkB,CAqBtC,KAAKxB,aAAL,CAAqB,IArBiB,IAuBhC2G,CAAAA,CAAK,WAAM,OAAGxK,CAAAA,CAAO,CAAC+B,SAAR,CAAkBO,GAAlB,CAAsBN,CAAG,CAACC,KAAJ,CAAUuI,KAAhC,CAAsC,CAvBpB,CAwBhC0H,CAAI,WAAM,OAAGlS,CAAAA,CAAO,CAAC+B,SAAR,CAAkBb,MAAlB,CAAyBc,CAAG,CAACC,KAAJ,CAAUuI,KAAnC,CAAyC,CAxBtB,CA0BtC,KAAKpD,KAAL,CAA4C,KAA/B3C,GAAAA,CAAO,CAACzE,CAAD,CAAPyE,CAAiB0I,SA1BQ,CA2BlC,UAAK/F,KA3B6B,EA4BtCpH,CAAS,CAAC+B,SAAV,CAAoBO,GAApB,CAAwBN,CAAG,CAACqI,GAA5B,CA5BsC,CA8BtC,KAAK8H,gBAAL,WAA4B,IACpBC,CAAAA,CAAkB,CAAGpS,CAAO,CAACyG,UADT,CAEtB4L,CAAM,CAAG,IAFa,CAM5B,MAHErS,CAAAA,CAAO,CAACyG,UAAR,CAAqB,CAAC,CAGxB,CAFE4L,CAAM,CAAwB,CAArB,CAAArS,CAAO,CAACyG,UAEnB,CADEzG,CAAO,CAACyG,UAAR,CAAqB2L,CACvB,CAASC,CACR,CAPuB,EA9Bc,CAsCtC,KAAK7L,wBAAL,CAAgC,KAAK2L,gBAAL,CAC5BnS,CAAO,CAACsF,WAAR,CAAsBtF,CAAO,CAAC2P,WADF,CAE5B,CAxCkC,CAyCtC,KAAKlG,KAAL,CAAa,GAAIgC,CAAAA,CAzCqB,CA0CxC,KAAO9B,aAAP,CAAuB3J,CAAO,CAAC2J,aAAR,EAAyBhJ,QA1CR,CA4CtC,KAAK6E,cAAL,CAAsB8M,CAAO,CAACtQ,CAAG,CAAChC,OAAJ,CAAY0F,IAAZ,CAAiB,GAAjB,CAAD,CA5CS,CA6CxC1F,CAAS,CAAC6F,WAAV,CAAsB,KAAKL,cAA3B,CA7CwC,CA8CtC,KAAKgD,UAAL,CAAkB8J,CAAO,CAACtQ,CAAG,CAAChC,OAAJ,CAAYsK,KAAZ,CAAkB,GAAlB,CAAD,CA9Ca,CA+CxC,KAAO9E,cAAP,CAAsBK,WAAtB,CAAkC,KAAK2C,UAAvC,CA/CwC,CAgDxC,KAAOA,UAAP,CAAkB+J,YAAlB,CAA+B,UAA/B,CAA2C,CAA3C,CAhDwC,CAiDtC,KAAK9I,KAAL,CAAWK,IAAX,CAAgB,KAAKtB,UAArB,CAAiC,OAAjC,CAA0CgC,CAA1C,CAjDsC,CAkDtC,KAAKf,KAAL,CAAWK,IAAX,CAAgB,KAAKtB,UAArB,CAAiC,MAAjC,CAAyC0J,CAAzC,CAlDsC,CAmDtC,KAAKjM,gBAAL,CAAwB,IAnDc,CAoDtC,KAAKI,eAAL,CAAuB,IApDe,CAqDtC,KAAKE,cAAL,CAAsB,IArDgB,CAsDtCnG,GAAMoS,CAAAA,CAAU,CAAG/N,CAAO,CAAC,KAAKe,cAAN,CAA1BpF,CACA,KAAK0H,gBAAL,CAAwB3D,QAAQ,CAACqO,CAAU,CAAC3K,MAAZ,CAAoB,EAApB,CAvDM,CAwDlC4K,KAAK,CAAC,KAAK3K,gBAAN,CAxD6B,EAyDpC,KAAKF,uBAAL,GAzDoC,CA0DtC,KAAOI,aAAP,CAAuB9D,CAAK,CAACsO,CAAU,CAACzK,GAAZ,CA1DU,EA4DpC,KAAKH,uBAAL,GA5DoC,CA8DxC,KAAOa,gBAAP,CACIvE,CAAK,CAACsO,CAAU,CAAC3N,eAAZ,CAAL,CAAoCX,CAAK,CAACsO,CAAU,CAAC1N,gBAAZ,CA/DL,CAiEtCmD,CAAO,CAAC,KAAKzC,cAAN,CAAsB,CAAEkN,OAAO,CAAE,OAAX,CAAtB,CAjE+B,CAkExC,KAAOvM,gBAAP,CACIjC,CAAK,CAACsO,CAAU,CAACG,UAAZ,CAAL,CAA+BzO,CAAK,CAACsO,CAAU,CAACI,WAAZ,CAnEA,CAoEtC3K,CAAO,CAAC,KAAKzC,cAAN,CAAsB,CAAEkN,OAAO,CAAE,EAAX,CAAtB,CApE+B,CAqEtC,KAAKxM,UAAL,CAAkB,IArEoB,CAsEtC,KAAKE,UAAL,CAAkB,IAtEoB,CAwEtC,KAAKN,cAAL,CAAsBwM,CAAO,CAACtQ,CAAG,CAAChC,OAAJ,CAAY0F,IAAZ,CAAiB,GAAjB,CAAD,CAxES,CAyExC1F,CAAS,CAAC6F,WAAV,CAAsB,KAAKC,cAA3B,CAzEwC,CA0EtC,KAAK4C,UAAL,CAAkB4J,CAAO,CAACtQ,CAAG,CAAChC,OAAJ,CAAYsK,KAAZ,CAAkB,GAAlB,CAAD,CA1Ea,CA2ExC,KAAOxE,cAAP,CAAsBD,WAAtB,CAAkC,KAAK6C,UAAvC,CA3EwC,CA4ExC,KAAOA,UAAP,CAAkB6J,YAAlB,CAA+B,UAA/B,CAA2C,CAA3C,CA5EwC,CA6EtC,KAAK9I,KAAL,CAAWK,IAAX,CAAgB,KAAKpB,UAArB,CAAiC,OAAjC,CAA0C8B,CAA1C,CA7EsC,CA8EtC,KAAKf,KAAL,CAAWK,IAAX,CAAgB,KAAKpB,UAArB,CAAiC,MAAjC,CAAyCwJ,CAAzC,CA9EsC,CA+EtC,KAAKtL,gBAAL,CAAwB,IA/Ec,CAgFtC,KAAKI,gBAAL,CAAwB,IAhFc,CAiFtC,KAAKC,aAAL,CAAqB,IAjFiB,CAkFtC7G,GAAMyS,CAAAA,CAAU,CAAGpO,CAAO,CAAC,KAAKqB,cAAN,CAA1B1F,CACA,KAAKiI,eAAL,CAAuBlE,QAAQ,CAAC0O,CAAU,CAACzK,KAAZ,CAAmB,EAAnB,CAnFO,CAoFlCqK,KAAK,CAAC,KAAKpK,eAAN,CApF6B,EAqFpC,KAAKF,sBAAL,GArFoC,CAsFtC,KAAOI,cAAP,CAAwBrE,CAAK,CAAC2O,CAAU,CAAClL,IAAZ,CAtFS,EAwFpC,KAAKQ,sBAAL,GAxFoC,CA0FtC,KAAKG,oBAAL,CAA4B,KAAKlB,KAAL,CAAa7C,CAAU,CAAC,KAAKmE,UAAN,CAAvB,CAA2C,IA1FjC,CA2FxC,KAAOC,gBAAP,CACIzE,CAAK,CAAC2O,CAAU,CAACC,cAAZ,CAAL,CAAmC5O,CAAK,CAAC2O,CAAU,CAACE,iBAAZ,CA5FJ,CA6FtC9K,CAAO,CAAC,KAAKnC,cAAN,CAAsB,CAAE4M,OAAO,CAAE,OAAX,CAAtB,CA7F+B,CA8FxC,KAAO5L,iBAAP,CACI5C,CAAK,CAAC2O,CAAU,CAACG,SAAZ,CAAL,CAA8B9O,CAAK,CAAC2O,CAAU,CAACI,YAAZ,CA/FC,CAgGtChL,CAAO,CAAC,KAAKnC,cAAN,CAAsB,CAAE4M,OAAO,CAAE,EAAX,CAAtB,CAhG+B,CAiGtC,KAAK7L,WAAL,CAAmB,IAjGmB,CAkGtC,KAAKE,UAAL,CAAkB,IAlGoB,CAoGxC,KAAOrD,KAAP,CAAe,CACX5B,CAAC,CACuB,CAAtB,EAAA9B,CAAO,CAACyG,UAAR,CACI,OADJ,CAEIzG,CAAO,CAACyG,UAAR,EAAsB,KAAKpB,YAAL,CAAoB,KAAKF,cAA/C,CACA,KADA,CAEA,IANK,CAOXxB,CAAC,CACsB,CAArB,EAAA3D,CAAO,CAAC4D,SAAR,CACI,OADJ,CAEI5D,CAAO,CAAC4D,SAAR,EAAqB,KAAKC,aAAL,CAAqB,KAAKC,eAA/C,CACA,KADA,CAEA,IAZK,CApGyB,CAmHtC,KAAKrB,OAAL,GAnHsC,CAqHtC,KAAKC,QAAL,CAAcgI,QAAd,CAAuB/E,OAAvB,UAA+BuN,EAAY,OAAGxI,CAAAA,CAAQ,CAACwI,CAAD,CAAR,CAAsBlI,CAAtB,CAA2B,CAAzE,CArHsC,CAuHtC,KAAKmI,aAAL,CAAqB,EAAWnT,CAAO,CAAC4D,SAAnB,CAvHiB,CAwHtC,KAAKwP,cAAL,CAAsBpT,CAAO,CAACyG,UAxHQ,CAyHxC,KAAOgD,KAAP,CAAaK,IAAb,CAAkB,KAAK9J,OAAvB,CAAgC,QAAhC,UAA0C8I,EAAE,OAAGkC,CAAAA,CAAI,CAACqI,QAALrI,CAAclC,CAAdkC,CAAgB,CAA/D,CAzHwC,CA0HtC5B,CAAc,CAAC,IAAD,SAGlB2I,CAAAA,WAAA,CAAEuB,MAAF,WAAW,CACF,KAAK7Q,OADH;;;AAMP,KAAK+D,wBAAL,CAAgC,KAAK2L,gBAAL,CAC5B,KAAKnS,OAAL,CAAasF,WAAb,CAA2B,KAAKtF,OAAL,CAAa2P,WADZ,CAE5B,CARG,CAWP1H,CAAO,CAAC,KAAKzC,cAAN,CAAsB,CAAEkN,OAAO,CAAE,OAAX,CAAtB,CAXA,CAYPzK,CAAO,CAAC,KAAKnC,cAAN,CAAsB,CAAE4M,OAAO,CAAE,OAAX,CAAtB,CAZA,CAaT,KAAOvM,gBAAP,CACIjC,CAAK,CAACO,CAAO,CAAC,KAAKe,cAAN,CAAPf,CAA6BkO,UAA9B,CAAL,CACAzO,CAAK,CAACO,CAAO,CAAC,KAAKe,cAAN,CAAPf,CAA6BmO,WAA9B,CAfA,CAgBT,KAAO9L,iBAAP,CACI5C,CAAK,CAACO,CAAO,CAAC,KAAKqB,cAAN,CAAPrB,CAA6BuO,SAA9B,CAAL,CACA9O,CAAK,CAACO,CAAO,CAAC,KAAKqB,cAAN,CAAPrB,CAA6BwO,YAA9B,CAlBA,CAqBPhL,CAAO,CAAC,KAAKzC,cAAN,CAAsB,CAAEkN,OAAO,CAAE,MAAX,CAAtB,CArBA,CAsBPzK,CAAO,CAAC,KAAKnC,cAAN,CAAsB,CAAE4M,OAAO,CAAE,MAAX,CAAtB,CAtBA,CAwBPtJ,CAAc,CAAC,IAAD,CAxBP,CA0BP5F,CAAiB,CAAC,IAAD,CAAO,KAAP,CAAc,CAAd,OA1BV,CA2BPA,CAAiB,CAAC,IAAD,CAAO,MAAP,CAAe,CAAf,OA3BV,CA6BPyE,CAAO,CAAC,KAAKzC,cAAN,CAAsB,CAAEkN,OAAO,CAAE,EAAX,CAAtB,CA7BA,CA8BPzK,CAAO,CAAC,KAAKnC,cAAN,CAAsB,CAAE4M,OAAO,CAAE,EAAX,CAAtB,CA9BA,GAiCXX,WAAA,CAAEsB,QAAF,WAAc,CACL,KAAK5Q,OADA,GAKV2G,CAAc,CAAC,IAAD,CALJ,CAMV5F,CAAiB,CAAC,IAAD,CAAO,KAAP,CAAc,KAAKxD,OAAL,CAAa4D,SAAb,CAAyB,KAAKuP,aAA5C,CANP,CAOV3P,CAAiB,CACf,IADe,CAEf,MAFe,CAGjB,KAAOxD,OAAP,CAAeyG,UAAf,CAA4B,KAAK2M,cAHhB,CAPP,CAaV,KAAKD,aAAL,CAAqB,EAAW,KAAKnT,OAAL,CAAa4D,SAAxB,CAbX,CAcZ,KAAOwP,cAAP,CAAwB,KAAKpT,OAAL,CAAayG,UAdzB,GAiBdsL,WAAA,CAAEwB,OAAF,WAAY,CACH,KAAK9Q,OADF;AAKR,KAAKgH,KAAL,CAAWyB,SAAX,EALQ,CAMVtF,CAAY,CAAC,KAAK4C,UAAN,CANF,CAOV5C,CAAY,CAAC,KAAK8C,UAAN,CAPF,CAQV9C,CAAY,CAAC,KAAKJ,cAAN,CARF,CASVI,CAAY,CAAC,KAAKE,cAAN,CATF,CAUR,KAAK0N,eAAL,EAVQ,CAaR,KAAKxT,OAAL,CAAe,IAbP,CAcR,KAAKwI,UAAL,CAAkB,IAdV,CAeR,KAAKE,UAAL,CAAkB,IAfV,CAgBR,KAAKlD,cAAL,CAAsB,IAhBd,CAiBR,KAAKM,cAAL,CAAsB,IAjBd,CAmBR,KAAKrD,OAAL,GAnBQ,GAsBZsP,WAAA,CAAEyB,eAAF,WAAoB,CAClB,KAAOxT,OAAP,CAAeU,SAAf,CAA2B,KAAKV,OAAL,CAAaU,SAAb,CACtB+S,KADsB,CAChB,GADgB,EAEtBhS,MAFsB,UAEfqB,EAAK,OAAG,CAACA,CAAI,CAACwM,KAAL,CAAW,eAAX,CAA2B,CAFrB,EAGtBoE,IAHsB,CAGjB,GAHiB,CAI1B"}