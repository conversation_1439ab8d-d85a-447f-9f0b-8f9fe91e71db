{"version": 3, "file": "perfect-scrollbar.esm.js", "sources": ["../src/lib/css.js", "../src/lib/dom.js", "../src/lib/class-names.js", "../src/lib/event-manager.js", "../src/process-scroll-diff.js", "../src/lib/util.js", "../src/update-geometry.js", "../src/handlers/click-rail.js", "../src/handlers/drag-thumb.js", "../src/handlers/keyboard.js", "../src/handlers/mouse-wheel.js", "../src/handlers/touch.js", "../src/index.js"], "sourcesContent": ["export function get(element) {\n  return getComputedStyle(element);\n}\n\nexport function set(element, obj) {\n  for (const key in obj) {\n    let val = obj[key];\n    if (typeof val === 'number') {\n      val = `${val}px`;\n    }\n    element.style[key] = val;\n  }\n  return element;\n}\n", "export function div(className) {\n  const div = document.createElement('div');\n  div.className = className;\n  return div;\n}\n\nconst elMatches =\n  typeof Element !== 'undefined' &&\n  (Element.prototype.matches ||\n    Element.prototype.webkitMatchesSelector ||\n    Element.prototype.mozMatchesSelector ||\n    Element.prototype.msMatchesSelector);\n\nexport function matches(element, query) {\n  if (!elMatches) {\n    throw new Error('No element matching method supported');\n  }\n\n  return elMatches.call(element, query);\n}\n\nexport function remove(element) {\n  if (element.remove) {\n    element.remove();\n  } else {\n    if (element.parentNode) {\n      element.parentNode.removeChild(element);\n    }\n  }\n}\n\nexport function queryChildren(element, selector) {\n  return Array.prototype.filter.call(element.children, child =>\n    matches(child, selector)\n  );\n}\n", "const cls = {\n  main: 'ps',\n  rtl: 'ps__rtl',\n  element: {\n    thumb: x => `ps__thumb-${x}`,\n    rail: x => `ps__rail-${x}`,\n    consuming: 'ps__child--consume',\n  },\n  state: {\n    focus: 'ps--focus',\n    clicking: 'ps--clicking',\n    active: x => `ps--active-${x}`,\n    scrolling: x => `ps--scrolling-${x}`,\n  },\n};\n\nexport default cls;\n\n/*\n * Helper methods\n */\nconst scrollingClassTimeout = { x: null, y: null };\n\nexport function addScrollingClass(i, x) {\n  const classList = i.element.classList;\n  const className = cls.state.scrolling(x);\n\n  if (classList.contains(className)) {\n    clearTimeout(scrollingClassTimeout[x]);\n  } else {\n    classList.add(className);\n  }\n}\n\nexport function removeScrollingClass(i, x) {\n  scrollingClassTimeout[x] = setTimeout(\n    () => i.isAlive && i.element.classList.remove(cls.state.scrolling(x)),\n    i.settings.scrollingThreshold\n  );\n}\n\nexport function setScrollingClassInstantly(i, x) {\n  addScrollingClass(i, x);\n  removeScrollingClass(i, x);\n}\n", "class EventElement {\n  constructor(element) {\n    this.element = element;\n    this.handlers = {};\n  }\n\n  bind(eventName, handler) {\n    if (typeof this.handlers[eventName] === 'undefined') {\n      this.handlers[eventName] = [];\n    }\n    this.handlers[eventName].push(handler);\n    this.element.addEventListener(eventName, handler, false);\n  }\n\n  unbind(eventName, target) {\n    this.handlers[eventName] = this.handlers[eventName].filter(handler => {\n      if (target && handler !== target) {\n        return true;\n      }\n      this.element.removeEventListener(eventName, handler, false);\n      return false;\n    });\n  }\n\n  unbindAll() {\n    for (const name in this.handlers) {\n      this.unbind(name);\n    }\n  }\n\n  get isEmpty() {\n    return Object.keys(this.handlers).every(\n      key => this.handlers[key].length === 0\n    );\n  }\n}\n\nexport default class EventManager {\n  constructor() {\n    this.eventElements = [];\n  }\n\n  eventElement(element) {\n    let ee = this.eventElements.filter(ee => ee.element === element)[0];\n    if (!ee) {\n      ee = new EventElement(element);\n      this.eventElements.push(ee);\n    }\n    return ee;\n  }\n\n  bind(element, eventName, handler) {\n    this.eventElement(element).bind(eventName, handler);\n  }\n\n  unbind(element, eventName, handler) {\n    const ee = this.eventElement(element);\n    ee.unbind(eventName, handler);\n\n    if (ee.isEmpty) {\n      // remove\n      this.eventElements.splice(this.eventElements.indexOf(ee), 1);\n    }\n  }\n\n  unbindAll() {\n    this.eventElements.forEach(e => e.unbindAll());\n    this.eventElements = [];\n  }\n\n  once(element, eventName, handler) {\n    const ee = this.eventElement(element);\n    const onceHandler = evt => {\n      ee.unbind(eventName, onceHandler);\n      handler(evt);\n    };\n    ee.bind(eventName, onceHandler);\n  }\n}\n", "import { setScrollingClassInstantly } from './lib/class-names';\n\nfunction createEvent(name) {\n  if (typeof window.CustomEvent === 'function') {\n    return new CustomEvent(name);\n  } else {\n    const evt = document.createEvent('CustomEvent');\n    evt.initCustomEvent(name, false, false, undefined);\n    return evt;\n  }\n}\n\nexport default function(\n  i,\n  axis,\n  diff,\n  useScrollingClass = true,\n  forceFireReachEvent = false\n) {\n  let fields;\n  if (axis === 'top') {\n    fields = [\n      'contentHeight',\n      'containerHeight',\n      'scrollTop',\n      'y',\n      'up',\n      'down',\n    ];\n  } else if (axis === 'left') {\n    fields = [\n      'contentWidth',\n      'containerWidth',\n      'scrollLeft',\n      'x',\n      'left',\n      'right',\n    ];\n  } else {\n    throw new Error('A proper axis should be provided');\n  }\n\n  processScrollDiff(i, diff, fields, useScrollingClass, forceFireReachEvent);\n}\n\nfunction processScrollDiff(\n  i,\n  diff,\n  [contentHeight, containerHeight, scrollTop, y, up, down],\n  useScrollingClass = true,\n  forceFireReachEvent = false\n) {\n  const element = i.element;\n\n  // reset reach\n  i.reach[y] = null;\n\n  // 1 for subpixel rounding\n  if (element[scrollTop] < 1) {\n    i.reach[y] = 'start';\n  }\n\n  // 1 for subpixel rounding\n  if (element[scrollTop] > i[contentHeight] - i[containerHeight] - 1) {\n    i.reach[y] = 'end';\n  }\n\n  if (diff) {\n    element.dispatchEvent(createEvent(`ps-scroll-${y}`));\n\n    if (diff < 0) {\n      element.dispatchEvent(createEvent(`ps-scroll-${up}`));\n    } else if (diff > 0) {\n      element.dispatchEvent(createEvent(`ps-scroll-${down}`));\n    }\n\n    if (useScrollingClass) {\n      setScrollingClassInstantly(i, y);\n    }\n  }\n\n  if (i.reach[y] && (diff || forceFireReachEvent)) {\n    element.dispatchEvent(createEvent(`ps-${y}-reach-${i.reach[y]}`));\n  }\n}\n", "import * as CSS from './css';\nimport * as DOM from './dom';\n\nexport function toInt(x) {\n  return parseInt(x, 10) || 0;\n}\n\nexport function isEditable(el) {\n  return (\n    DOM.matches(el, 'input,[contenteditable]') ||\n    DOM.matches(el, 'select,[contenteditable]') ||\n    DOM.matches(el, 'textarea,[contenteditable]') ||\n    DOM.matches(el, 'button,[contenteditable]')\n  );\n}\n\nexport function outerWidth(element) {\n  const styles = CSS.get(element);\n  return (\n    toInt(styles.width) +\n    toInt(styles.paddingLeft) +\n    toInt(styles.paddingRight) +\n    toInt(styles.borderLeftWidth) +\n    toInt(styles.borderRightWidth)\n  );\n}\n\nexport const env = {\n  isWebKit:\n    typeof document !== 'undefined' &&\n    'WebkitAppearance' in document.documentElement.style,\n  supportsTouch:\n    typeof window !== 'undefined' &&\n    ('ontouchstart' in window ||\n      ('maxTouchPoints' in window.navigator &&\n        window.navigator.maxTouchPoints > 0) ||\n      (window.DocumentTouch && document instanceof window.DocumentTouch)),\n  supportsIePointer:\n    typeof navigator !== 'undefined' && navigator.msMaxTouchPoints,\n  isChrome:\n    typeof navigator !== 'undefined' &&\n    /Chrome/i.test(navigator && navigator.userAgent),\n};\n", "import * as CSS from './lib/css';\nimport * as DOM from './lib/dom';\nimport cls from './lib/class-names';\nimport { toInt } from './lib/util';\n\nexport default function(i) {\n  const element = i.element;\n  const roundedScrollTop = Math.floor(element.scrollTop);\n  const rect = element.getBoundingClientRect();\n\n  i.containerWidth = Math.ceil(rect.width);\n  i.containerHeight = Math.ceil(rect.height);\n  i.contentWidth = element.scrollWidth;\n  i.contentHeight = element.scrollHeight;\n\n  if (!element.contains(i.scrollbarXRail)) {\n    // clean up and append\n    DOM.queryChildren(element, cls.element.rail('x')).forEach(el =>\n      DOM.remove(el)\n    );\n    element.appendChild(i.scrollbarXRail);\n  }\n  if (!element.contains(i.scrollbarYRail)) {\n    // clean up and append\n    DOM.queryChildren(element, cls.element.rail('y')).forEach(el =>\n      DOM.remove(el)\n    );\n    element.appendChild(i.scrollbarYRail);\n  }\n\n  if (\n    !i.settings.suppressScrollX &&\n    i.containerWidth + i.settings.scrollXMarginOffset < i.contentWidth\n  ) {\n    i.scrollbarXActive = true;\n    i.railXWidth = i.containerWidth - i.railXMarginWidth;\n    i.railXRatio = i.containerWidth / i.railXWidth;\n    i.scrollbarXWidth = getThumbSize(\n      i,\n      toInt((i.railXWidth * i.containerWidth) / i.contentWidth)\n    );\n    i.scrollbarXLeft = toInt(\n      ((i.negativeScrollAdjustment + element.scrollLeft) *\n        (i.railXWidth - i.scrollbarXWidth)) /\n        (i.contentWidth - i.containerWidth)\n    );\n  } else {\n    i.scrollbarXActive = false;\n  }\n\n  if (\n    !i.settings.suppressScrollY &&\n    i.containerHeight + i.settings.scrollYMarginOffset < i.contentHeight\n  ) {\n    i.scrollbarYActive = true;\n    i.railYHeight = i.containerHeight - i.railYMarginHeight;\n    i.railYRatio = i.containerHeight / i.railYHeight;\n    i.scrollbarYHeight = getThumbSize(\n      i,\n      toInt((i.railYHeight * i.containerHeight) / i.contentHeight)\n    );\n    i.scrollbarYTop = toInt(\n      (roundedScrollTop * (i.railYHeight - i.scrollbarYHeight)) /\n        (i.contentHeight - i.containerHeight)\n    );\n  } else {\n    i.scrollbarYActive = false;\n  }\n\n  if (i.scrollbarXLeft >= i.railXWidth - i.scrollbarXWidth) {\n    i.scrollbarXLeft = i.railXWidth - i.scrollbarXWidth;\n  }\n  if (i.scrollbarYTop >= i.railYHeight - i.scrollbarYHeight) {\n    i.scrollbarYTop = i.railYHeight - i.scrollbarYHeight;\n  }\n\n  updateCss(element, i);\n\n  if (i.scrollbarXActive) {\n    element.classList.add(cls.state.active('x'));\n  } else {\n    element.classList.remove(cls.state.active('x'));\n    i.scrollbarXWidth = 0;\n    i.scrollbarXLeft = 0;\n    element.scrollLeft = i.isRtl === true ? i.contentWidth : 0;\n  }\n  if (i.scrollbarYActive) {\n    element.classList.add(cls.state.active('y'));\n  } else {\n    element.classList.remove(cls.state.active('y'));\n    i.scrollbarYHeight = 0;\n    i.scrollbarYTop = 0;\n    element.scrollTop = 0;\n  }\n}\n\nfunction getThumbSize(i, thumbSize) {\n  if (i.settings.minScrollbarLength) {\n    thumbSize = Math.max(thumbSize, i.settings.minScrollbarLength);\n  }\n  if (i.settings.maxScrollbarLength) {\n    thumbSize = Math.min(thumbSize, i.settings.maxScrollbarLength);\n  }\n  return thumbSize;\n}\n\nfunction updateCss(element, i) {\n  const xRailOffset = { width: i.railXWidth };\n  const roundedScrollTop = Math.floor(element.scrollTop);\n\n  if (i.isRtl) {\n    xRailOffset.left =\n      i.negativeScrollAdjustment +\n      element.scrollLeft +\n      i.containerWidth -\n      i.contentWidth;\n  } else {\n    xRailOffset.left = element.scrollLeft;\n  }\n  if (i.isScrollbarXUsingBottom) {\n    xRailOffset.bottom = i.scrollbarXBottom - roundedScrollTop;\n  } else {\n    xRailOffset.top = i.scrollbarXTop + roundedScrollTop;\n  }\n  CSS.set(i.scrollbarXRail, xRailOffset);\n\n  const yRailOffset = { top: roundedScrollTop, height: i.railYHeight };\n  if (i.isScrollbarYUsingRight) {\n    if (i.isRtl) {\n      yRailOffset.right =\n        i.contentWidth -\n        (i.negativeScrollAdjustment + element.scrollLeft) -\n        i.scrollbarYRight -\n        i.scrollbarYOuterWidth -\n        9;\n    } else {\n      yRailOffset.right = i.scrollbarYRight - element.scrollLeft;\n    }\n  } else {\n    if (i.isRtl) {\n      yRailOffset.left =\n        i.negativeScrollAdjustment +\n        element.scrollLeft +\n        i.containerWidth * 2 -\n        i.contentWidth -\n        i.scrollbarYLeft -\n        i.scrollbarYOuterWidth;\n    } else {\n      yRailOffset.left = i.scrollbarYLeft + element.scrollLeft;\n    }\n  }\n  CSS.set(i.scrollbarYRail, yRailOffset);\n\n  CSS.set(i.scrollbarX, {\n    left: i.scrollbarXLeft,\n    width: i.scrollbarXWidth - i.railBorderXWidth,\n  });\n  CSS.set(i.scrollbarY, {\n    top: i.scrollbarYTop,\n    height: i.scrollbarYHeight - i.railBorderYWidth,\n  });\n}\n", "import updateGeometry from '../update-geometry';\n\nexport default function(i) {\n  const element = i.element;\n\n  i.event.bind(i.scrollbarY, 'mousedown', e => e.stopPropagation());\n  i.event.bind(i.scrollbarYRail, 'mousedown', e => {\n    const positionTop =\n      e.pageY -\n      window.pageYOffset -\n      i.scrollbarYRail.getBoundingClientRect().top;\n    const direction = positionTop > i.scrollbarYTop ? 1 : -1;\n\n    i.element.scrollTop += direction * i.containerHeight;\n    updateGeometry(i);\n\n    e.stopPropagation();\n  });\n\n  i.event.bind(i.scrollbarX, 'mousedown', e => e.stopPropagation());\n  i.event.bind(i.scrollbarXRail, 'mousedown', e => {\n    const positionLeft =\n      e.pageX -\n      window.pageXOffset -\n      i.scrollbarXRail.getBoundingClientRect().left;\n    const direction = positionLeft > i.scrollbarXLeft ? 1 : -1;\n\n    i.element.scrollLeft += direction * i.containerWidth;\n    updateGeometry(i);\n\n    e.stopPropagation();\n  });\n}\n", "import * as CSS from '../lib/css';\nimport * as DOM from '../lib/dom';\nimport cls, {\n  addScrollingClass,\n  removeScrollingClass,\n} from '../lib/class-names';\nimport updateGeometry from '../update-geometry';\nimport { toInt } from '../lib/util';\n\nexport default function(i) {\n  bindMouseScrollHandler(i, [\n    'containerWidth',\n    'contentWidth',\n    'pageX',\n    'railXWidth',\n    'scrollbarX',\n    'scrollbarXWidth',\n    'scrollLeft',\n    'x',\n    'scrollbarXRail',\n  ]);\n  bindMouseScrollHandler(i, [\n    'containerHeight',\n    'contentHeight',\n    'pageY',\n    'railYHeight',\n    'scrollbarY',\n    'scrollbarYHeight',\n    'scrollTop',\n    'y',\n    'scrollbarYRail',\n  ]);\n}\n\nfunction bindMouseScrollHandler(\n  i,\n  [\n    containerHeight,\n    contentHeight,\n    pageY,\n    railYHeight,\n    scrollbarY,\n    scrollbarYHeight,\n    scrollTop,\n    y,\n    scrollbarYRail,\n  ]\n) {\n  const element = i.element;\n\n  let startingScrollTop = null;\n  let startingMousePageY = null;\n  let scrollBy = null;\n\n  function mouseMoveHandler(e) {\n    if (e.touches && e.touches[0]) {\n      e[pageY] = e.touches[0].pageY;\n    }\n    element[scrollTop] =\n      startingScrollTop + scrollBy * (e[pageY] - startingMousePageY);\n    addScrollingClass(i, y);\n    updateGeometry(i);\n\n    e.stopPropagation();\n    e.preventDefault();\n  }\n\n  function mouseUpHandler() {\n    removeScrollingClass(i, y);\n    i[scrollbarYRail].classList.remove(cls.state.clicking);\n    i.event.unbind(i.ownerDocument, 'mousemove', mouseMoveHandler);\n  }\n\n  function bindMoves(e, touchMode) {\n    startingScrollTop = element[scrollTop];\n    if (touchMode && e.touches) {\n      e[pageY] = e.touches[0].pageY;\n    }\n    startingMousePageY = e[pageY];\n    scrollBy =\n      (i[contentHeight] - i[containerHeight]) /\n      (i[railYHeight] - i[scrollbarYHeight]);\n    if (!touchMode) {\n      i.event.bind(i.ownerDocument, 'mousemove', mouseMoveHandler);\n      i.event.once(i.ownerDocument, 'mouseup', mouseUpHandler);\n      e.preventDefault();\n    } else {\n      i.event.bind(i.ownerDocument, 'touchmove', mouseMoveHandler);\n    }\n\n    i[scrollbarYRail].classList.add(cls.state.clicking);\n\n    e.stopPropagation();\n  }\n\n  i.event.bind(i[scrollbarY], 'mousedown', e => {\n    bindMoves(e);\n  });\n  i.event.bind(i[scrollbarY], 'touchstart', e => {\n    bindMoves(e, true);\n  });\n}\n", "import * as DOM from '../lib/dom';\nimport updateGeometry from '../update-geometry';\nimport { isEditable } from '../lib/util';\n\nexport default function(i) {\n  const element = i.element;\n\n  const elementHovered = () => DOM.matches(element, ':hover');\n  const scrollbarFocused = () =>\n    DOM.matches(i.scrollbarX, ':focus') || DOM.matches(i.scrollbarY, ':focus');\n\n  function shouldPreventDefault(deltaX, deltaY) {\n    const scrollTop = Math.floor(element.scrollTop);\n    if (deltaX === 0) {\n      if (!i.scrollbarYActive) {\n        return false;\n      }\n      if (\n        (scrollTop === 0 && deltaY > 0) ||\n        (scrollTop >= i.contentHeight - i.containerHeight && deltaY < 0)\n      ) {\n        return !i.settings.wheelPropagation;\n      }\n    }\n\n    const scrollLeft = element.scrollLeft;\n    if (deltaY === 0) {\n      if (!i.scrollbarXActive) {\n        return false;\n      }\n      if (\n        (scrollLeft === 0 && deltaX < 0) ||\n        (scrollLeft >= i.contentWidth - i.containerWidth && deltaX > 0)\n      ) {\n        return !i.settings.wheelPropagation;\n      }\n    }\n    return true;\n  }\n\n  i.event.bind(i.ownerDocument, 'keydown', e => {\n    if (\n      (e.isDefaultPrevented && e.isDefaultPrevented()) ||\n      e.defaultPrevented\n    ) {\n      return;\n    }\n\n    if (!elementHovered() && !scrollbarFocused()) {\n      return;\n    }\n\n    let activeElement = document.activeElement\n      ? document.activeElement\n      : i.ownerDocument.activeElement;\n    if (activeElement) {\n      if (activeElement.tagName === 'IFRAME') {\n        activeElement = activeElement.contentDocument.activeElement;\n      } else {\n        // go deeper if element is a webcomponent\n        while (activeElement.shadowRoot) {\n          activeElement = activeElement.shadowRoot.activeElement;\n        }\n      }\n      if (isEditable(activeElement)) {\n        return;\n      }\n    }\n\n    let deltaX = 0;\n    let deltaY = 0;\n\n    switch (e.which) {\n      case 37: // left\n        if (e.metaKey) {\n          deltaX = -i.contentWidth;\n        } else if (e.altKey) {\n          deltaX = -i.containerWidth;\n        } else {\n          deltaX = -30;\n        }\n        break;\n      case 38: // up\n        if (e.metaKey) {\n          deltaY = i.contentHeight;\n        } else if (e.altKey) {\n          deltaY = i.containerHeight;\n        } else {\n          deltaY = 30;\n        }\n        break;\n      case 39: // right\n        if (e.metaKey) {\n          deltaX = i.contentWidth;\n        } else if (e.altKey) {\n          deltaX = i.containerWidth;\n        } else {\n          deltaX = 30;\n        }\n        break;\n      case 40: // down\n        if (e.metaKey) {\n          deltaY = -i.contentHeight;\n        } else if (e.altKey) {\n          deltaY = -i.containerHeight;\n        } else {\n          deltaY = -30;\n        }\n        break;\n      case 32: // space bar\n        if (e.shiftKey) {\n          deltaY = i.containerHeight;\n        } else {\n          deltaY = -i.containerHeight;\n        }\n        break;\n      case 33: // page up\n        deltaY = i.containerHeight;\n        break;\n      case 34: // page down\n        deltaY = -i.containerHeight;\n        break;\n      case 36: // home\n        deltaY = i.contentHeight;\n        break;\n      case 35: // end\n        deltaY = -i.contentHeight;\n        break;\n      default:\n        return;\n    }\n\n    if (i.settings.suppressScrollX && deltaX !== 0) {\n      return;\n    }\n    if (i.settings.suppressScrollY && deltaY !== 0) {\n      return;\n    }\n\n    element.scrollTop -= deltaY;\n    element.scrollLeft += deltaX;\n    updateGeometry(i);\n\n    if (shouldPreventDefault(deltaX, deltaY)) {\n      e.preventDefault();\n    }\n  });\n}\n", "import * as CSS from '../lib/css';\nimport cls from '../lib/class-names';\nimport updateGeometry from '../update-geometry';\nimport { env } from '../lib/util';\n\nexport default function(i) {\n  const element = i.element;\n\n  let shouldPrevent = false;\n\n  function shouldPreventDefault(deltaX, deltaY) {\n    const roundedScrollTop = Math.floor(element.scrollTop);\n    const isTop = element.scrollTop === 0;\n    const isBottom =\n      roundedScrollTop + element.offsetHeight === element.scrollHeight;\n    const isLeft = element.scrollLeft === 0;\n    const isRight =\n      element.scrollLeft + element.offsetWidth === element.scrollWidth;\n\n    let hitsBound;\n\n    // pick axis with primary direction\n    if (Math.abs(deltaY) > Math.abs(deltaX)) {\n      hitsBound = isTop || isBottom;\n    } else {\n      hitsBound = isLeft || isRight;\n    }\n\n    return hitsBound ? !i.settings.wheelPropagation : true;\n  }\n\n  function getDeltaFromEvent(e) {\n    let deltaX = e.deltaX;\n    let deltaY = -1 * e.deltaY;\n\n    if (typeof deltaX === 'undefined' || typeof deltaY === 'undefined') {\n      // OS X Safari\n      deltaX = (-1 * e.wheelDeltaX) / 6;\n      deltaY = e.wheelDeltaY / 6;\n    }\n\n    if (e.deltaMode && e.deltaMode === 1) {\n      // Firefox in deltaMode 1: Line scrolling\n      deltaX *= 10;\n      deltaY *= 10;\n    }\n\n    if (deltaX !== deltaX && deltaY !== deltaY /* NaN checks */) {\n      // IE in some mouse drivers\n      deltaX = 0;\n      deltaY = e.wheelDelta;\n    }\n\n    if (e.shiftKey) {\n      // reverse axis with shift key\n      return [-deltaY, -deltaX];\n    }\n    return [deltaX, deltaY];\n  }\n\n  function shouldBeConsumedByChild(target, deltaX, deltaY) {\n    // FIXME: this is a workaround for <select> issue in FF and IE #571\n    if (!env.isWebKit && element.querySelector('select:focus')) {\n      return true;\n    }\n\n    if (!element.contains(target)) {\n      return false;\n    }\n\n    let cursor = target;\n\n    while (cursor && cursor !== element) {\n      if (cursor.classList.contains(cls.element.consuming)) {\n        return true;\n      }\n\n      const style = CSS.get(cursor);\n\n      // if deltaY && vertical scrollable\n      if (deltaY && style.overflowY.match(/(scroll|auto)/)) {\n        const maxScrollTop = cursor.scrollHeight - cursor.clientHeight;\n        if (maxScrollTop > 0) {\n          if (\n            (cursor.scrollTop > 0 && deltaY < 0) ||\n            (cursor.scrollTop < maxScrollTop && deltaY > 0)\n          ) {\n            return true;\n          }\n        }\n      }\n      // if deltaX && horizontal scrollable\n      if (deltaX && style.overflowX.match(/(scroll|auto)/)) {\n        const maxScrollLeft = cursor.scrollWidth - cursor.clientWidth;\n        if (maxScrollLeft > 0) {\n          if (\n            (cursor.scrollLeft > 0 && deltaX < 0) ||\n            (cursor.scrollLeft < maxScrollLeft && deltaX > 0)\n          ) {\n            return true;\n          }\n        }\n      }\n\n      cursor = cursor.parentNode;\n    }\n\n    return false;\n  }\n\n  function mousewheelHandler(e) {\n    const [deltaX, deltaY] = getDeltaFromEvent(e);\n\n    if (shouldBeConsumedByChild(e.target, deltaX, deltaY)) {\n      return;\n    }\n\n    let shouldPrevent = false;\n    if (!i.settings.useBothWheelAxes) {\n      // deltaX will only be used for horizontal scrolling and deltaY will\n      // only be used for vertical scrolling - this is the default\n      element.scrollTop -= deltaY * i.settings.wheelSpeed;\n      element.scrollLeft += deltaX * i.settings.wheelSpeed;\n    } else if (i.scrollbarYActive && !i.scrollbarXActive) {\n      // only vertical scrollbar is active and useBothWheelAxes option is\n      // active, so let's scroll vertical bar using both mouse wheel axes\n      if (deltaY) {\n        element.scrollTop -= deltaY * i.settings.wheelSpeed;\n      } else {\n        element.scrollTop += deltaX * i.settings.wheelSpeed;\n      }\n      shouldPrevent = true;\n    } else if (i.scrollbarXActive && !i.scrollbarYActive) {\n      // useBothWheelAxes and only horizontal bar is active, so use both\n      // wheel axes for horizontal bar\n      if (deltaX) {\n        element.scrollLeft += deltaX * i.settings.wheelSpeed;\n      } else {\n        element.scrollLeft -= deltaY * i.settings.wheelSpeed;\n      }\n      shouldPrevent = true;\n    }\n\n    updateGeometry(i);\n\n    shouldPrevent = shouldPrevent || shouldPreventDefault(deltaX, deltaY);\n    if (shouldPrevent && !e.ctrlKey) {\n      e.stopPropagation();\n      e.preventDefault();\n    }\n  }\n\n  if (typeof window.onwheel !== 'undefined') {\n    i.event.bind(element, 'wheel', mousewheelHandler);\n  } else if (typeof window.onmousewheel !== 'undefined') {\n    i.event.bind(element, 'mousewheel', mousewheelHandler);\n  }\n}\n", "import updateGeometry from '../update-geometry';\nimport cls from '../lib/class-names';\nimport * as CSS from '../lib/css';\nimport { env } from '../lib/util';\n\nexport default function(i) {\n  if (!env.supportsTouch && !env.supportsIePointer) {\n    return;\n  }\n\n  const element = i.element;\n\n  function shouldPrevent(deltaX, deltaY) {\n    const scrollTop = Math.floor(element.scrollTop);\n    const scrollLeft = element.scrollLeft;\n    const magnitudeX = Math.abs(deltaX);\n    const magnitudeY = Math.abs(deltaY);\n\n    if (magnitudeY > magnitudeX) {\n      // user is perhaps trying to swipe up/down the page\n\n      if (\n        (deltaY < 0 && scrollTop === i.contentHeight - i.containerHeight) ||\n        (deltaY > 0 && scrollTop === 0)\n      ) {\n        // set prevent for mobile Chrome refresh\n        return window.scrollY === 0 && deltaY > 0 && env.isChrome;\n      }\n    } else if (magnitudeX > magnitudeY) {\n      // user is perhaps trying to swipe left/right across the page\n\n      if (\n        (deltaX < 0 && scrollLeft === i.contentWidth - i.containerWidth) ||\n        (deltaX > 0 && scrollLeft === 0)\n      ) {\n        return true;\n      }\n    }\n\n    return true;\n  }\n\n  function applyTouchMove(differenceX, differenceY) {\n    element.scrollTop -= differenceY;\n    element.scrollLeft -= differenceX;\n\n    updateGeometry(i);\n  }\n\n  let startOffset = {};\n  let startTime = 0;\n  let speed = {};\n  let easingLoop = null;\n\n  function getTouch(e) {\n    if (e.targetTouches) {\n      return e.targetTouches[0];\n    } else {\n      // Maybe IE pointer\n      return e;\n    }\n  }\n\n  function shouldHandle(e) {\n    if (e.pointerType && e.pointerType === 'pen' && e.buttons === 0) {\n      return false;\n    }\n    if (e.targetTouches && e.targetTouches.length === 1) {\n      return true;\n    }\n    if (\n      e.pointerType &&\n      e.pointerType !== 'mouse' &&\n      e.pointerType !== e.MSPOINTER_TYPE_MOUSE\n    ) {\n      return true;\n    }\n    return false;\n  }\n\n  function touchStart(e) {\n    if (!shouldHandle(e)) {\n      return;\n    }\n\n    const touch = getTouch(e);\n\n    startOffset.pageX = touch.pageX;\n    startOffset.pageY = touch.pageY;\n\n    startTime = new Date().getTime();\n\n    if (easingLoop !== null) {\n      clearInterval(easingLoop);\n    }\n  }\n\n  function shouldBeConsumedByChild(target, deltaX, deltaY) {\n    if (!element.contains(target)) {\n      return false;\n    }\n\n    let cursor = target;\n\n    while (cursor && cursor !== element) {\n      if (cursor.classList.contains(cls.element.consuming)) {\n        return true;\n      }\n\n      const style = CSS.get(cursor);\n\n      // if deltaY && vertical scrollable\n      if (deltaY && style.overflowY.match(/(scroll|auto)/)) {\n        const maxScrollTop = cursor.scrollHeight - cursor.clientHeight;\n        if (maxScrollTop > 0) {\n          if (\n            (cursor.scrollTop > 0 && deltaY < 0) ||\n            (cursor.scrollTop < maxScrollTop && deltaY > 0)\n          ) {\n            return true;\n          }\n        }\n      }\n      // if deltaX && horizontal scrollable\n      if (deltaX && style.overflowX.match(/(scroll|auto)/)) {\n        const maxScrollLeft = cursor.scrollWidth - cursor.clientWidth;\n        if (maxScrollLeft > 0) {\n          if (\n            (cursor.scrollLeft > 0 && deltaX < 0) ||\n            (cursor.scrollLeft < maxScrollLeft && deltaX > 0)\n          ) {\n            return true;\n          }\n        }\n      }\n\n      cursor = cursor.parentNode;\n    }\n\n    return false;\n  }\n\n  function touchMove(e) {\n    if (shouldHandle(e)) {\n      const touch = getTouch(e);\n\n      const currentOffset = { pageX: touch.pageX, pageY: touch.pageY };\n\n      const differenceX = currentOffset.pageX - startOffset.pageX;\n      const differenceY = currentOffset.pageY - startOffset.pageY;\n\n      if (shouldBeConsumedByChild(e.target, differenceX, differenceY)) {\n        return;\n      }\n\n      applyTouchMove(differenceX, differenceY);\n      startOffset = currentOffset;\n\n      const currentTime = new Date().getTime();\n\n      const timeGap = currentTime - startTime;\n      if (timeGap > 0) {\n        speed.x = differenceX / timeGap;\n        speed.y = differenceY / timeGap;\n        startTime = currentTime;\n      }\n\n      if (shouldPrevent(differenceX, differenceY)) {\n        e.preventDefault();\n      }\n    }\n  }\n  function touchEnd() {\n    if (i.settings.swipeEasing) {\n      clearInterval(easingLoop);\n      easingLoop = setInterval(function() {\n        if (i.isInitialized) {\n          clearInterval(easingLoop);\n          return;\n        }\n\n        if (!speed.x && !speed.y) {\n          clearInterval(easingLoop);\n          return;\n        }\n\n        if (Math.abs(speed.x) < 0.01 && Math.abs(speed.y) < 0.01) {\n          clearInterval(easingLoop);\n          return;\n        }\n\n        applyTouchMove(speed.x * 30, speed.y * 30);\n\n        speed.x *= 0.8;\n        speed.y *= 0.8;\n      }, 10);\n    }\n  }\n\n  if (env.supportsTouch) {\n    i.event.bind(element, 'touchstart', touchStart);\n    i.event.bind(element, 'touchmove', touchMove);\n    i.event.bind(element, 'touchend', touchEnd);\n  } else if (env.supportsIePointer) {\n    if (window.PointerEvent) {\n      i.event.bind(element, 'pointerdown', touchStart);\n      i.event.bind(element, 'pointermove', touchMove);\n      i.event.bind(element, 'pointerup', touchEnd);\n    } else if (window.MSPointerEvent) {\n      i.event.bind(element, 'MSPointerDown', touchStart);\n      i.event.bind(element, 'MSPointerMove', touchMove);\n      i.event.bind(element, 'MSPointerUp', touchEnd);\n    }\n  }\n}\n", "import * as CSS from './lib/css';\nimport * as DOM from './lib/dom';\nimport cls from './lib/class-names';\nimport EventManager from './lib/event-manager';\nimport processScrollDiff from './process-scroll-diff';\nimport updateGeometry from './update-geometry';\nimport { toInt, outerWidth } from './lib/util';\n\nimport clickRail from './handlers/click-rail';\nimport dragThumb from './handlers/drag-thumb';\nimport keyboard from './handlers/keyboard';\nimport wheel from './handlers/mouse-wheel';\nimport touch from './handlers/touch';\n\nconst defaultSettings = () => ({\n  handlers: ['click-rail', 'drag-thumb', 'keyboard', 'wheel', 'touch'],\n  maxScrollbarLength: null,\n  minScrollbarLength: null,\n  scrollingThreshold: 1000,\n  scrollXMarginOffset: 0,\n  scrollYMarginOffset: 0,\n  suppressScrollX: false,\n  suppressScrollY: false,\n  swipeEasing: true,\n  useBothWheelAxes: false,\n  wheelPropagation: true,\n  wheelSpeed: 1,\n});\n\nconst handlers = {\n  'click-rail': clickRail,\n  'drag-thumb': dragThumb,\n  keyboard,\n  wheel,\n  touch,\n};\n\nexport default class PerfectScrollbar {\n  constructor(element, userSettings = {}) {\n    if (typeof element === 'string') {\n      element = document.querySelector(element);\n    }\n\n    if (!element || !element.nodeName) {\n      throw new Error('no element is specified to initialize PerfectScrollbar');\n    }\n\n    this.element = element;\n\n    element.classList.add(cls.main);\n\n    this.settings = defaultSettings();\n    for (const key in userSettings) {\n      this.settings[key] = userSettings[key];\n    }\n\n    this.containerWidth = null;\n    this.containerHeight = null;\n    this.contentWidth = null;\n    this.contentHeight = null;\n\n    const focus = () => element.classList.add(cls.state.focus);\n    const blur = () => element.classList.remove(cls.state.focus);\n\n    this.isRtl = CSS.get(element).direction === 'rtl';\n    if (this.isRtl === true) {\n      element.classList.add(cls.rtl);\n    }\n    this.isNegativeScroll = (() => {\n      const originalScrollLeft = element.scrollLeft;\n      let result = null;\n      element.scrollLeft = -1;\n      result = element.scrollLeft < 0;\n      element.scrollLeft = originalScrollLeft;\n      return result;\n    })();\n    this.negativeScrollAdjustment = this.isNegativeScroll\n      ? element.scrollWidth - element.clientWidth\n      : 0;\n    this.event = new EventManager();\n    this.ownerDocument = element.ownerDocument || document;\n\n    this.scrollbarXRail = DOM.div(cls.element.rail('x'));\n    element.appendChild(this.scrollbarXRail);\n    this.scrollbarX = DOM.div(cls.element.thumb('x'));\n    this.scrollbarXRail.appendChild(this.scrollbarX);\n    this.scrollbarX.setAttribute('tabindex', 0);\n    this.event.bind(this.scrollbarX, 'focus', focus);\n    this.event.bind(this.scrollbarX, 'blur', blur);\n    this.scrollbarXActive = null;\n    this.scrollbarXWidth = null;\n    this.scrollbarXLeft = null;\n    const railXStyle = CSS.get(this.scrollbarXRail);\n    this.scrollbarXBottom = parseInt(railXStyle.bottom, 10);\n    if (isNaN(this.scrollbarXBottom)) {\n      this.isScrollbarXUsingBottom = false;\n      this.scrollbarXTop = toInt(railXStyle.top);\n    } else {\n      this.isScrollbarXUsingBottom = true;\n    }\n    this.railBorderXWidth =\n      toInt(railXStyle.borderLeftWidth) + toInt(railXStyle.borderRightWidth);\n    // Set rail to display:block to calculate margins\n    CSS.set(this.scrollbarXRail, { display: 'block' });\n    this.railXMarginWidth =\n      toInt(railXStyle.marginLeft) + toInt(railXStyle.marginRight);\n    CSS.set(this.scrollbarXRail, { display: '' });\n    this.railXWidth = null;\n    this.railXRatio = null;\n\n    this.scrollbarYRail = DOM.div(cls.element.rail('y'));\n    element.appendChild(this.scrollbarYRail);\n    this.scrollbarY = DOM.div(cls.element.thumb('y'));\n    this.scrollbarYRail.appendChild(this.scrollbarY);\n    this.scrollbarY.setAttribute('tabindex', 0);\n    this.event.bind(this.scrollbarY, 'focus', focus);\n    this.event.bind(this.scrollbarY, 'blur', blur);\n    this.scrollbarYActive = null;\n    this.scrollbarYHeight = null;\n    this.scrollbarYTop = null;\n    const railYStyle = CSS.get(this.scrollbarYRail);\n    this.scrollbarYRight = parseInt(railYStyle.right, 10);\n    if (isNaN(this.scrollbarYRight)) {\n      this.isScrollbarYUsingRight = false;\n      this.scrollbarYLeft = toInt(railYStyle.left);\n    } else {\n      this.isScrollbarYUsingRight = true;\n    }\n    this.scrollbarYOuterWidth = this.isRtl ? outerWidth(this.scrollbarY) : null;\n    this.railBorderYWidth =\n      toInt(railYStyle.borderTopWidth) + toInt(railYStyle.borderBottomWidth);\n    CSS.set(this.scrollbarYRail, { display: 'block' });\n    this.railYMarginHeight =\n      toInt(railYStyle.marginTop) + toInt(railYStyle.marginBottom);\n    CSS.set(this.scrollbarYRail, { display: '' });\n    this.railYHeight = null;\n    this.railYRatio = null;\n\n    this.reach = {\n      x:\n        element.scrollLeft <= 0\n          ? 'start'\n          : element.scrollLeft >= this.contentWidth - this.containerWidth\n          ? 'end'\n          : null,\n      y:\n        element.scrollTop <= 0\n          ? 'start'\n          : element.scrollTop >= this.contentHeight - this.containerHeight\n          ? 'end'\n          : null,\n    };\n\n    this.isAlive = true;\n\n    this.settings.handlers.forEach(handlerName => handlers[handlerName](this));\n\n    this.lastScrollTop = Math.floor(element.scrollTop); // for onScroll only\n    this.lastScrollLeft = element.scrollLeft; // for onScroll only\n    this.event.bind(this.element, 'scroll', e => this.onScroll(e));\n    updateGeometry(this);\n  }\n\n  update() {\n    if (!this.isAlive) {\n      return;\n    }\n\n    // Recalcuate negative scrollLeft adjustment\n    this.negativeScrollAdjustment = this.isNegativeScroll\n      ? this.element.scrollWidth - this.element.clientWidth\n      : 0;\n\n    // Recalculate rail margins\n    CSS.set(this.scrollbarXRail, { display: 'block' });\n    CSS.set(this.scrollbarYRail, { display: 'block' });\n    this.railXMarginWidth =\n      toInt(CSS.get(this.scrollbarXRail).marginLeft) +\n      toInt(CSS.get(this.scrollbarXRail).marginRight);\n    this.railYMarginHeight =\n      toInt(CSS.get(this.scrollbarYRail).marginTop) +\n      toInt(CSS.get(this.scrollbarYRail).marginBottom);\n\n    // Hide scrollbars not to affect scrollWidth and scrollHeight\n    CSS.set(this.scrollbarXRail, { display: 'none' });\n    CSS.set(this.scrollbarYRail, { display: 'none' });\n\n    updateGeometry(this);\n\n    processScrollDiff(this, 'top', 0, false, true);\n    processScrollDiff(this, 'left', 0, false, true);\n\n    CSS.set(this.scrollbarXRail, { display: '' });\n    CSS.set(this.scrollbarYRail, { display: '' });\n  }\n\n  onScroll(e) {\n    if (!this.isAlive) {\n      return;\n    }\n\n    updateGeometry(this);\n    processScrollDiff(this, 'top', this.element.scrollTop - this.lastScrollTop);\n    processScrollDiff(\n      this,\n      'left',\n      this.element.scrollLeft - this.lastScrollLeft\n    );\n\n    this.lastScrollTop = Math.floor(this.element.scrollTop);\n    this.lastScrollLeft = this.element.scrollLeft;\n  }\n\n  destroy() {\n    if (!this.isAlive) {\n      return;\n    }\n\n    this.event.unbindAll();\n    DOM.remove(this.scrollbarX);\n    DOM.remove(this.scrollbarY);\n    DOM.remove(this.scrollbarXRail);\n    DOM.remove(this.scrollbarYRail);\n    this.removePsClasses();\n\n    // unset elements\n    this.element = null;\n    this.scrollbarX = null;\n    this.scrollbarY = null;\n    this.scrollbarXRail = null;\n    this.scrollbarYRail = null;\n\n    this.isAlive = false;\n  }\n\n  removePsClasses() {\n    this.element.className = this.element.className\n      .split(' ')\n      .filter(name => !name.match(/^ps([-_].+|)$/))\n      .join(' ');\n  }\n}\n"], "names": ["const", "let", "this", "processScrollDiff", "DOM.matches", "CSS.get", "DOM.query<PERSON><PERSON><PERSON>n", "DOM.remove", "CSS.set", "DOM.div"], "mappings": ";;;;;;AAAO,SAAS,GAAG,CAAC,OAAO,EAAE;EAC3B,OAAO,gBAAgB,CAAC,OAAO,CAAC,CAAC;CAClC;;AAEM,SAAS,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE;EAChC,KAAKA,IAAM,GAAG,IAAI,GAAG,EAAE;IACrBC,IAAI,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IACnB,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;MAC3B,GAAG,GAAM,GAAG,OAAI,CAAC;KAClB;IACD,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;GAC1B;EACD,OAAO,OAAO,CAAC;;;ACZV,SAAS,GAAG,CAAC,SAAS,EAAE;EAC7BD,IAAM,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;EAC1C,GAAG,CAAC,SAAS,GAAG,SAAS,CAAC;EAC1B,OAAO,GAAG,CAAC;CACZ;;AAEDA,IAAM,SAAS;EACb,OAAO,OAAO,KAAK,WAAW;GAC7B,OAAO,CAAC,SAAS,CAAC,OAAO;IACxB,OAAO,CAAC,SAAS,CAAC,qBAAqB;IACvC,OAAO,CAAC,SAAS,CAAC,kBAAkB;IACpC,OAAO,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;;AAEzC,AAAO,SAAS,OAAO,CAAC,OAAO,EAAE,KAAK,EAAE;EACtC,IAAI,CAAC,SAAS,EAAE;IACd,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;GACzD;;EAED,OAAO,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;CACvC;;AAED,AAAO,SAAS,MAAM,CAAC,OAAO,EAAE;EAC9B,IAAI,OAAO,CAAC,MAAM,EAAE;IAClB,OAAO,CAAC,MAAM,EAAE,CAAC;GAClB,MAAM;IACL,IAAI,OAAO,CAAC,UAAU,EAAE;MACtB,OAAO,CAAC,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;KACzC;GACF;CACF;;AAED,AAAO,SAAS,aAAa,CAAC,OAAO,EAAE,QAAQ,EAAE;EAC/C,OAAO,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,YAAE,OAAM,SACzD,OAAO,CAAC,KAAK,EAAE,QAAQ,IAAC;GACzB,CAAC;CACH;;ACnCDA,IAAM,GAAG,GAAG;EACV,IAAI,EAAE,IAAI;EACV,GAAG,EAAE,SAAS;EACd,OAAO,EAAE;IACP,KAAK,YAAE,GAAE,yBAAgB,CAAC,IAAE;IAC5B,IAAI,YAAE,GAAE,wBAAe,CAAC,IAAE;IAC1B,SAAS,EAAE,oBAAoB;GAChC;EACD,KAAK,EAAE;IACL,KAAK,EAAE,WAAW;IAClB,QAAQ,EAAE,cAAc;IACxB,MAAM,YAAE,GAAE,0BAAiB,CAAC,IAAE;IAC9B,SAAS,YAAE,GAAE,6BAAoB,CAAC,IAAE;GACrC;CACF,CAAC;;;;;AAOFA,IAAM,qBAAqB,GAAG,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC;;AAEnD,AAAO,SAAS,iBAAiB,CAAC,CAAC,EAAE,CAAC,EAAE;EACtCA,IAAM,SAAS,GAAG,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC;EACtCA,IAAM,SAAS,GAAG,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;;EAEzC,IAAI,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;IACjC,YAAY,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC;GACxC,MAAM;IACL,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;GAC1B;CACF;;AAED,AAAO,SAAS,oBAAoB,CAAC,CAAC,EAAE,CAAC,EAAE;EACzC,qBAAqB,CAAC,CAAC,CAAC,GAAG,UAAU;gBAChC,SAAG,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,IAAC;IACrE,CAAC,CAAC,QAAQ,CAAC,kBAAkB;GAC9B,CAAC;CACH;;AAED,AAAO,SAAS,0BAA0B,CAAC,CAAC,EAAE,CAAC,EAAE;EAC/C,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACxB,oBAAoB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;CAC5B;;AC5CD,IAAM,YAAY,GAChB,qBAAW,CAAC,OAAO,EAAE;EACnB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;EACvB,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;AACvB;;6DAAG;;AAEH,uBAAE,sBAAK,SAAS,EAAE,OAAO,EAAE;EACzB,IAAM,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,WAAW,EAAE;IACrD,IAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;GAC/B;EACH,IAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;EACvC,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,SAAS,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;AAC3D,EAAC;;AAEH,uBAAE,0BAAO,SAAS,EAAE,MAAM,EAAE;;AAAC;EACzB,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,MAAM,WAAC,SAAQ;IACjE,IAAI,MAAM,IAAI,OAAO,KAAK,MAAM,EAAE;MAClC,OAAS,IAAI,CAAC;KACb;IACDE,MAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,SAAS,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;IAC9D,OAAS,KAAK,CAAC;GACd,CAAC,CAAC;AACL,EAAC;;AAEH,uBAAE,kCAAY;EACZ,KAAOF,IAAM,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE;IAChC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;GACnB;AACH,EAAC;;AAEH,mBAAM,0BAAU;;AAAC;EACf,OAAS,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,KAAK;IACvC,UAAE,KAAI,SAAGE,MAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,MAAM,KAAK,IAAC;GACvC,CAAC;AACJ,CAAC;;sEACF;;AAEc,IAAM,YAAY,GAC/B,qBAAW,GAAG;EACZ,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;AAC1B,EAAC;;AAEH,uBAAE,sCAAa,OAAO,EAAE;EACtB,IAAM,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,WAAC,IAAG,SAAG,EAAE,CAAC,OAAO,KAAK,UAAO,CAAC,CAAC,CAAC,CAAC,CAAC;EACtE,IAAM,CAAC,EAAE,EAAE;IACP,EAAE,GAAG,IAAI,YAAY,CAAC,OAAO,CAAC,CAAC;IACjC,IAAM,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;GAC7B;EACH,OAAS,EAAE,CAAC;AACZ,EAAC;;AAEH,uBAAE,sBAAK,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE;EAChC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;AACtD,EAAC;;AAEH,uBAAE,0BAAO,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE;EACpC,IAAQ,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;EACxC,EAAI,CAAC,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;;EAE9B,IAAI,EAAE,CAAC,OAAO,EAAE;;IAEd,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;GAC9D;AACH,EAAC;;AAEH,uBAAE,kCAAY;EACV,IAAI,CAAC,aAAa,CAAC,OAAO,WAAC,GAAE,SAAG,CAAC,CAAC,SAAS,KAAE,CAAC,CAAC;EAC/C,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;AAC1B,EAAC;;AAEH,uBAAE,sBAAK,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE;EAClC,IAAQ,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;EACtCF,IAAM,WAAW,aAAG,KAAI;IACxB,EAAI,CAAC,MAAM,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;IAClC,OAAO,CAAC,GAAG,CAAC,CAAC;GACd,CAAC;EACJ,EAAI,CAAC,IAAI,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;AAClC,CAAC;;AC3EH,SAAS,WAAW,CAAC,IAAI,EAAE;EACzB,IAAI,OAAO,MAAM,CAAC,WAAW,KAAK,UAAU,EAAE;IAC5C,OAAO,IAAI,WAAW,CAAC,IAAI,CAAC,CAAC;GAC9B,MAAM;IACLA,IAAM,GAAG,GAAG,QAAQ,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;IAChD,GAAG,CAAC,eAAe,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;IACnD,OAAO,GAAG,CAAC;GACZ;CACF;;AAED,AAAe;EACb,CAAC;EACD,IAAI;EACJ,IAAI;EACJ,iBAAwB;EACxB,mBAA2B;EAC3B;uDAFiB,GAAG;2DACD,GAAG;AACrB;EACDC,IAAI,MAAM,CAAC;EACX,IAAI,IAAI,KAAK,KAAK,EAAE;IAClB,MAAM,GAAG;MACP,eAAe;MACf,iBAAiB;MACjB,WAAW;MACX,GAAG;MACH,IAAI;MACJ,MAAM,EACP,CAAC;GACH,MAAM,IAAI,IAAI,KAAK,MAAM,EAAE;IAC1B,MAAM,GAAG;MACP,cAAc;MACd,gBAAgB;MAChB,YAAY;MACZ,GAAG;MACH,MAAM;MACN,OAAO,EACR,CAAC;GACH,MAAM;IACL,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;GACrD;;EAEDE,mBAAiB,CAAC,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,iBAAiB,EAAE,mBAAmB,CAAC,CAAC;CAC5E;;AAED,SAASA,mBAAiB;EACxB,CAAC;EACD,IAAI;EACJ,GAAwD;EACxD,iBAAwB;EACxB,mBAA2B;EAC3B;6BAHgB;+BAAiB;yBAAW;iBAAG;kBAAI;;uDAClC,GAAG;2DACD,GAAG;AACrB;EACDH,IAAM,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC;;;EAG1B,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;;;EAGlB,IAAI,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE;IAC1B,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;GACtB;;;EAGD,IAAI,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE;IAClE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;GACpB;;EAED,IAAI,IAAI,EAAE;IACR,OAAO,CAAC,aAAa,CAAC,WAAW,iBAAc,CAAC,EAAG,CAAC,CAAC;;IAErD,IAAI,IAAI,GAAG,CAAC,EAAE;MACZ,OAAO,CAAC,aAAa,CAAC,WAAW,iBAAc,EAAE,EAAG,CAAC,CAAC;KACvD,MAAM,IAAI,IAAI,GAAG,CAAC,EAAE;MACnB,OAAO,CAAC,aAAa,CAAC,WAAW,iBAAc,IAAI,EAAG,CAAC,CAAC;KACzD;;IAED,IAAI,iBAAiB,EAAE;MACrB,0BAA0B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;KAClC;GACF;;EAED,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,mBAAmB,CAAC,EAAE;IAC/C,OAAO,CAAC,aAAa,CAAC,WAAW,UAAO,CAAC,gBAAU,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;GACnE;CACF;;ACjFM,SAAS,KAAK,CAAC,CAAC,EAAE;EACvB,OAAO,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;CAC7B;;AAED,AAAO,SAAS,UAAU,CAAC,EAAE,EAAE;EAC7B;IACEI,OAAW,CAAC,EAAE,EAAE,yBAAyB,CAAC;IAC1CA,OAAW,CAAC,EAAE,EAAE,0BAA0B,CAAC;IAC3CA,OAAW,CAAC,EAAE,EAAE,4BAA4B,CAAC;IAC7CA,OAAW,CAAC,EAAE,EAAE,0BAA0B,CAAC;IAC3C;CACH;;AAED,AAAO,SAAS,UAAU,CAAC,OAAO,EAAE;EAClCJ,IAAM,MAAM,GAAGK,GAAO,CAAC,OAAO,CAAC,CAAC;EAChC;IACE,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC;IACnB,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC;IACzB,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC;IAC1B,KAAK,CAAC,MAAM,CAAC,eAAe,CAAC;IAC7B,KAAK,CAAC,MAAM,CAAC,gBAAgB,CAAC;IAC9B;CACH;;AAED,AAAOL,IAAM,GAAG,GAAG;EACjB,QAAQ;IACN,OAAO,QAAQ,KAAK,WAAW;IAC/B,kBAAkB,IAAI,QAAQ,CAAC,eAAe,CAAC,KAAK;EACtD,aAAa;IACX,OAAO,MAAM,KAAK,WAAW;KAC5B,cAAc,IAAI,MAAM;OACtB,gBAAgB,IAAI,MAAM,CAAC,SAAS;QACnC,MAAM,CAAC,SAAS,CAAC,cAAc,GAAG,CAAC,CAAC;OACrC,MAAM,CAAC,aAAa,IAAI,QAAQ,YAAY,MAAM,CAAC,aAAa,CAAC,CAAC;EACvE,iBAAiB;IACf,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,gBAAgB;EAChE,QAAQ;IACN,OAAO,SAAS,KAAK,WAAW;IAChC,SAAS,CAAC,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,SAAS,CAAC;CACnD,CAAC;;ACrCa,wBAAS,CAAC,EAAE;EACzBA,IAAM,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC;EAC1BA,IAAM,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;EACvDA,IAAM,IAAI,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAC;;EAE7C,CAAC,CAAC,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EACzC,CAAC,CAAC,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;EAC3C,CAAC,CAAC,YAAY,GAAG,OAAO,CAAC,WAAW,CAAC;EACrC,CAAC,CAAC,aAAa,GAAG,OAAO,CAAC,YAAY,CAAC;;EAEvC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,cAAc,CAAC,EAAE;;IAEvCM,aAAiB,CAAC,OAAO,EAAE,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,WAAC,IAAG,SAC3DC,MAAU,CAAC,EAAE,IAAC;KACf,CAAC;IACF,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC;GACvC;EACD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,cAAc,CAAC,EAAE;;IAEvCD,aAAiB,CAAC,OAAO,EAAE,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,WAAC,IAAG,SAC3DC,MAAU,CAAC,EAAE,IAAC;KACf,CAAC;IACF,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC;GACvC;;EAED;IACE,CAAC,CAAC,CAAC,QAAQ,CAAC,eAAe;IAC3B,CAAC,CAAC,cAAc,GAAG,CAAC,CAAC,QAAQ,CAAC,mBAAmB,GAAG,CAAC,CAAC,YAAY;IAClE;IACA,CAAC,CAAC,gBAAgB,GAAG,IAAI,CAAC;IAC1B,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,cAAc,GAAG,CAAC,CAAC,gBAAgB,CAAC;IACrD,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,cAAc,GAAG,CAAC,CAAC,UAAU,CAAC;IAC/C,CAAC,CAAC,eAAe,GAAG,YAAY;MAC9B,CAAC;MACD,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,cAAc,IAAI,CAAC,CAAC,YAAY,CAAC;KAC1D,CAAC;IACF,CAAC,CAAC,cAAc,GAAG,KAAK;MACtB,CAAC,CAAC,CAAC,CAAC,wBAAwB,GAAG,OAAO,CAAC,UAAU;SAC9C,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,eAAe,CAAC;SACjC,CAAC,CAAC,YAAY,GAAG,CAAC,CAAC,cAAc,CAAC;KACtC,CAAC;GACH,MAAM;IACL,CAAC,CAAC,gBAAgB,GAAG,KAAK,CAAC;GAC5B;;EAED;IACE,CAAC,CAAC,CAAC,QAAQ,CAAC,eAAe;IAC3B,CAAC,CAAC,eAAe,GAAG,CAAC,CAAC,QAAQ,CAAC,mBAAmB,GAAG,CAAC,CAAC,aAAa;IACpE;IACA,CAAC,CAAC,gBAAgB,GAAG,IAAI,CAAC;IAC1B,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC,eAAe,GAAG,CAAC,CAAC,iBAAiB,CAAC;IACxD,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,eAAe,GAAG,CAAC,CAAC,WAAW,CAAC;IACjD,CAAC,CAAC,gBAAgB,GAAG,YAAY;MAC/B,CAAC;MACD,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC,eAAe,IAAI,CAAC,CAAC,aAAa,CAAC;KAC7D,CAAC;IACF,CAAC,CAAC,aAAa,GAAG,KAAK;MACrB,CAAC,gBAAgB,IAAI,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC,gBAAgB,CAAC;SACrD,CAAC,CAAC,aAAa,GAAG,CAAC,CAAC,eAAe,CAAC;KACxC,CAAC;GACH,MAAM;IACL,CAAC,CAAC,gBAAgB,GAAG,KAAK,CAAC;GAC5B;;EAED,IAAI,CAAC,CAAC,cAAc,IAAI,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,eAAe,EAAE;IACxD,CAAC,CAAC,cAAc,GAAG,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,eAAe,CAAC;GACrD;EACD,IAAI,CAAC,CAAC,aAAa,IAAI,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC,gBAAgB,EAAE;IACzD,CAAC,CAAC,aAAa,GAAG,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC,gBAAgB,CAAC;GACtD;;EAED,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;;EAEtB,IAAI,CAAC,CAAC,gBAAgB,EAAE;IACtB,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;GAC9C,MAAM;IACL,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;IAChD,CAAC,CAAC,eAAe,GAAG,CAAC,CAAC;IACtB,CAAC,CAAC,cAAc,GAAG,CAAC,CAAC;IACrB,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC,KAAK,KAAK,IAAI,GAAG,CAAC,CAAC,YAAY,GAAG,CAAC,CAAC;GAC5D;EACD,IAAI,CAAC,CAAC,gBAAgB,EAAE;IACtB,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;GAC9C,MAAM;IACL,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;IAChD,CAAC,CAAC,gBAAgB,GAAG,CAAC,CAAC;IACvB,CAAC,CAAC,aAAa,GAAG,CAAC,CAAC;IACpB,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC;GACvB;CACF;;AAED,SAAS,YAAY,CAAC,CAAC,EAAE,SAAS,EAAE;EAClC,IAAI,CAAC,CAAC,QAAQ,CAAC,kBAAkB,EAAE;IACjC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;GAChE;EACD,IAAI,CAAC,CAAC,QAAQ,CAAC,kBAAkB,EAAE;IACjC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;GAChE;EACD,OAAO,SAAS,CAAC;CAClB;;AAED,SAAS,SAAS,CAAC,OAAO,EAAE,CAAC,EAAE;EAC7BP,IAAM,WAAW,GAAG,EAAE,KAAK,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC;EAC5CA,IAAM,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;;EAEvD,IAAI,CAAC,CAAC,KAAK,EAAE;IACX,WAAW,CAAC,IAAI;MACd,CAAC,CAAC,wBAAwB;MAC1B,OAAO,CAAC,UAAU;MAClB,CAAC,CAAC,cAAc;MAChB,CAAC,CAAC,YAAY,CAAC;GAClB,MAAM;IACL,WAAW,CAAC,IAAI,GAAG,OAAO,CAAC,UAAU,CAAC;GACvC;EACD,IAAI,CAAC,CAAC,uBAAuB,EAAE;IAC7B,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;GAC5D,MAAM;IACL,WAAW,CAAC,GAAG,GAAG,CAAC,CAAC,aAAa,GAAG,gBAAgB,CAAC;GACtD;EACDQ,GAAO,CAAC,CAAC,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;;EAEvCR,IAAM,WAAW,GAAG,EAAE,GAAG,EAAE,gBAAgB,EAAE,MAAM,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC;EACrE,IAAI,CAAC,CAAC,sBAAsB,EAAE;IAC5B,IAAI,CAAC,CAAC,KAAK,EAAE;MACX,WAAW,CAAC,KAAK;QACf,CAAC,CAAC,YAAY;SACb,CAAC,CAAC,wBAAwB,GAAG,OAAO,CAAC,UAAU,CAAC;QACjD,CAAC,CAAC,eAAe;QACjB,CAAC,CAAC,oBAAoB;QACtB,CAAC,CAAC;KACL,MAAM;MACL,WAAW,CAAC,KAAK,GAAG,CAAC,CAAC,eAAe,GAAG,OAAO,CAAC,UAAU,CAAC;KAC5D;GACF,MAAM;IACL,IAAI,CAAC,CAAC,KAAK,EAAE;MACX,WAAW,CAAC,IAAI;QACd,CAAC,CAAC,wBAAwB;QAC1B,OAAO,CAAC,UAAU;QAClB,CAAC,CAAC,cAAc,GAAG,CAAC;QACpB,CAAC,CAAC,YAAY;QACd,CAAC,CAAC,cAAc;QAChB,CAAC,CAAC,oBAAoB,CAAC;KAC1B,MAAM;MACL,WAAW,CAAC,IAAI,GAAG,CAAC,CAAC,cAAc,GAAG,OAAO,CAAC,UAAU,CAAC;KAC1D;GACF;EACDQ,GAAO,CAAC,CAAC,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;;EAEvCA,GAAO,CAAC,CAAC,CAAC,UAAU,EAAE;IACpB,IAAI,EAAE,CAAC,CAAC,cAAc;IACtB,KAAK,EAAE,CAAC,CAAC,eAAe,GAAG,CAAC,CAAC,gBAAgB;GAC9C,CAAC,CAAC;EACHA,GAAO,CAAC,CAAC,CAAC,UAAU,EAAE;IACpB,GAAG,EAAE,CAAC,CAAC,aAAa;IACpB,MAAM,EAAE,CAAC,CAAC,gBAAgB,GAAG,CAAC,CAAC,gBAAgB;GAChD,CAAC,CAAC;CACJ;;AC/Jc,mBAAS,CAAC,EAAE;EACzBR,IAAM,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC;;EAE1B,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,EAAE,WAAW,YAAE,GAAE,SAAG,CAAC,CAAC,eAAe,KAAE,CAAC,CAAC;EAClE,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,cAAc,EAAE,WAAW,YAAE,GAAE;IAC5CA,IAAM,WAAW;MACf,CAAC,CAAC,KAAK;MACP,MAAM,CAAC,WAAW;MAClB,CAAC,CAAC,cAAc,CAAC,qBAAqB,EAAE,CAAC,GAAG,CAAC;IAC/CA,IAAM,SAAS,GAAG,WAAW,GAAG,CAAC,CAAC,aAAa,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;;IAEzD,CAAC,CAAC,OAAO,CAAC,SAAS,IAAI,SAAS,GAAG,CAAC,CAAC,eAAe,CAAC;IACrD,cAAc,CAAC,CAAC,CAAC,CAAC;;IAElB,CAAC,CAAC,eAAe,EAAE,CAAC;GACrB,CAAC,CAAC;;EAEH,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,EAAE,WAAW,YAAE,GAAE,SAAG,CAAC,CAAC,eAAe,KAAE,CAAC,CAAC;EAClE,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,cAAc,EAAE,WAAW,YAAE,GAAE;IAC5CA,IAAM,YAAY;MAChB,CAAC,CAAC,KAAK;MACP,MAAM,CAAC,WAAW;MAClB,CAAC,CAAC,cAAc,CAAC,qBAAqB,EAAE,CAAC,IAAI,CAAC;IAChDA,IAAM,SAAS,GAAG,YAAY,GAAG,CAAC,CAAC,cAAc,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;;IAE3D,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,SAAS,GAAG,CAAC,CAAC,cAAc,CAAC;IACrD,cAAc,CAAC,CAAC,CAAC,CAAC;;IAElB,CAAC,CAAC,eAAe,EAAE,CAAC;GACrB,CAAC,CAAC;CACJ;;ACvBc,mBAAS,CAAC,EAAE;EACzB,sBAAsB,CAAC,CAAC,EAAE;IACxB,gBAAgB;IAChB,cAAc;IACd,OAAO;IACP,YAAY;IACZ,YAAY;IACZ,iBAAiB;IACjB,YAAY;IACZ,GAAG;IACH,gBAAgB,EACjB,CAAC,CAAC;EACH,sBAAsB,CAAC,CAAC,EAAE;IACxB,iBAAiB;IACjB,eAAe;IACf,OAAO;IACP,aAAa;IACb,YAAY;IACZ,kBAAkB;IAClB,WAAW;IACX,GAAG;IACH,gBAAgB,EACjB,CAAC,CAAC;CACJ;;AAED,SAAS,sBAAsB;EAC7B,CAAC;EACD,GAUC;EACD;+BATE;6BACA;qBACA;2BACA;0BACA;gCACA;yBACA;iBACA;;AAED;EACDA,IAAM,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC;;EAE1BC,IAAI,iBAAiB,GAAG,IAAI,CAAC;EAC7BA,IAAI,kBAAkB,GAAG,IAAI,CAAC;EAC9BA,IAAI,QAAQ,GAAG,IAAI,CAAC;;EAEpB,SAAS,gBAAgB,CAAC,CAAC,EAAE;IAC3B,IAAI,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;MAC7B,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;KAC/B;IACD,OAAO,CAAC,SAAS,CAAC;MAChB,iBAAiB,GAAG,QAAQ,IAAI,CAAC,CAAC,KAAK,CAAC,GAAG,kBAAkB,CAAC,CAAC;IACjE,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACxB,cAAc,CAAC,CAAC,CAAC,CAAC;;IAElB,CAAC,CAAC,eAAe,EAAE,CAAC;IACpB,CAAC,CAAC,cAAc,EAAE,CAAC;GACpB;;EAED,SAAS,cAAc,GAAG;IACxB,oBAAoB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC3B,CAAC,CAAC,cAAc,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IACvD,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,aAAa,EAAE,WAAW,EAAE,gBAAgB,CAAC,CAAC;GAChE;;EAED,SAAS,SAAS,CAAC,CAAC,EAAE,SAAS,EAAE;IAC/B,iBAAiB,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;IACvC,IAAI,SAAS,IAAI,CAAC,CAAC,OAAO,EAAE;MAC1B,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;KAC/B;IACD,kBAAkB,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;IAC9B,QAAQ;MACN,CAAC,CAAC,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,eAAe,CAAC;OACrC,CAAC,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC;IACzC,IAAI,CAAC,SAAS,EAAE;MACd,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,aAAa,EAAE,WAAW,EAAE,gBAAgB,CAAC,CAAC;MAC7D,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,aAAa,EAAE,SAAS,EAAE,cAAc,CAAC,CAAC;MACzD,CAAC,CAAC,cAAc,EAAE,CAAC;KACpB,MAAM;MACL,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,aAAa,EAAE,WAAW,EAAE,gBAAgB,CAAC,CAAC;KAC9D;;IAED,CAAC,CAAC,cAAc,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;;IAEpD,CAAC,CAAC,eAAe,EAAE,CAAC;GACrB;;EAED,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,EAAE,WAAW,YAAE,GAAE;IACzC,SAAS,CAAC,CAAC,CAAC,CAAC;GACd,CAAC,CAAC;EACH,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,EAAE,YAAY,YAAE,GAAE;IAC1C,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;GACpB,CAAC,CAAC;CACJ;;ACjGc,kBAAS,CAAC,EAAE;EACzBD,IAAM,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC;;EAE1BA,IAAM,cAAc,eAAM,SAAGI,OAAW,CAAC,OAAO,EAAE,QAAQ,IAAC,CAAC;EAC5DJ,IAAM,gBAAgB,eAAM,SAC1BI,OAAW,CAAC,CAAC,CAAC,UAAU,EAAE,QAAQ,CAAC,IAAIA,OAAW,CAAC,CAAC,CAAC,UAAU,EAAE,QAAQ,IAAC,CAAC;;EAE7E,SAAS,oBAAoB,CAAC,MAAM,EAAE,MAAM,EAAE;IAC5CJ,IAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;IAChD,IAAI,MAAM,KAAK,CAAC,EAAE;MAChB,IAAI,CAAC,CAAC,CAAC,gBAAgB,EAAE;QACvB,OAAO,KAAK,CAAC;OACd;MACD;QACE,CAAC,SAAS,KAAK,CAAC,IAAI,MAAM,GAAG,CAAC;SAC7B,SAAS,IAAI,CAAC,CAAC,aAAa,GAAG,CAAC,CAAC,eAAe,IAAI,MAAM,GAAG,CAAC,CAAC;QAChE;QACA,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,gBAAgB,CAAC;OACrC;KACF;;IAEDA,IAAM,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;IACtC,IAAI,MAAM,KAAK,CAAC,EAAE;MAChB,IAAI,CAAC,CAAC,CAAC,gBAAgB,EAAE;QACvB,OAAO,KAAK,CAAC;OACd;MACD;QACE,CAAC,UAAU,KAAK,CAAC,IAAI,MAAM,GAAG,CAAC;SAC9B,UAAU,IAAI,CAAC,CAAC,YAAY,GAAG,CAAC,CAAC,cAAc,IAAI,MAAM,GAAG,CAAC,CAAC;QAC/D;QACA,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,gBAAgB,CAAC;OACrC;KACF;IACD,OAAO,IAAI,CAAC;GACb;;EAED,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,aAAa,EAAE,SAAS,YAAE,GAAE;IACzC;MACE,CAAC,CAAC,CAAC,kBAAkB,IAAI,CAAC,CAAC,kBAAkB,EAAE;MAC/C,CAAC,CAAC,gBAAgB;MAClB;MACA,OAAO;KACR;;IAED,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,gBAAgB,EAAE,EAAE;MAC5C,OAAO;KACR;;IAEDC,IAAI,aAAa,GAAG,QAAQ,CAAC,aAAa;QACtC,QAAQ,CAAC,aAAa;QACtB,CAAC,CAAC,aAAa,CAAC,aAAa,CAAC;IAClC,IAAI,aAAa,EAAE;MACjB,IAAI,aAAa,CAAC,OAAO,KAAK,QAAQ,EAAE;QACtC,aAAa,GAAG,aAAa,CAAC,eAAe,CAAC,aAAa,CAAC;OAC7D,MAAM;;QAEL,OAAO,aAAa,CAAC,UAAU,EAAE;UAC/B,aAAa,GAAG,aAAa,CAAC,UAAU,CAAC,aAAa,CAAC;SACxD;OACF;MACD,IAAI,UAAU,CAAC,aAAa,CAAC,EAAE;QAC7B,OAAO;OACR;KACF;;IAEDA,IAAI,MAAM,GAAG,CAAC,CAAC;IACfA,IAAI,MAAM,GAAG,CAAC,CAAC;;IAEf,QAAQ,CAAC,CAAC,KAAK;MACb,KAAK,EAAE;QACL,IAAI,CAAC,CAAC,OAAO,EAAE;UACb,MAAM,GAAG,CAAC,CAAC,CAAC,YAAY,CAAC;SAC1B,MAAM,IAAI,CAAC,CAAC,MAAM,EAAE;UACnB,MAAM,GAAG,CAAC,CAAC,CAAC,cAAc,CAAC;SAC5B,MAAM;UACL,MAAM,GAAG,CAAC,EAAE,CAAC;SACd;QACD,MAAM;MACR,KAAK,EAAE;QACL,IAAI,CAAC,CAAC,OAAO,EAAE;UACb,MAAM,GAAG,CAAC,CAAC,aAAa,CAAC;SAC1B,MAAM,IAAI,CAAC,CAAC,MAAM,EAAE;UACnB,MAAM,GAAG,CAAC,CAAC,eAAe,CAAC;SAC5B,MAAM;UACL,MAAM,GAAG,EAAE,CAAC;SACb;QACD,MAAM;MACR,KAAK,EAAE;QACL,IAAI,CAAC,CAAC,OAAO,EAAE;UACb,MAAM,GAAG,CAAC,CAAC,YAAY,CAAC;SACzB,MAAM,IAAI,CAAC,CAAC,MAAM,EAAE;UACnB,MAAM,GAAG,CAAC,CAAC,cAAc,CAAC;SAC3B,MAAM;UACL,MAAM,GAAG,EAAE,CAAC;SACb;QACD,MAAM;MACR,KAAK,EAAE;QACL,IAAI,CAAC,CAAC,OAAO,EAAE;UACb,MAAM,GAAG,CAAC,CAAC,CAAC,aAAa,CAAC;SAC3B,MAAM,IAAI,CAAC,CAAC,MAAM,EAAE;UACnB,MAAM,GAAG,CAAC,CAAC,CAAC,eAAe,CAAC;SAC7B,MAAM;UACL,MAAM,GAAG,CAAC,EAAE,CAAC;SACd;QACD,MAAM;MACR,KAAK,EAAE;QACL,IAAI,CAAC,CAAC,QAAQ,EAAE;UACd,MAAM,GAAG,CAAC,CAAC,eAAe,CAAC;SAC5B,MAAM;UACL,MAAM,GAAG,CAAC,CAAC,CAAC,eAAe,CAAC;SAC7B;QACD,MAAM;MACR,KAAK,EAAE;QACL,MAAM,GAAG,CAAC,CAAC,eAAe,CAAC;QAC3B,MAAM;MACR,KAAK,EAAE;QACL,MAAM,GAAG,CAAC,CAAC,CAAC,eAAe,CAAC;QAC5B,MAAM;MACR,KAAK,EAAE;QACL,MAAM,GAAG,CAAC,CAAC,aAAa,CAAC;QACzB,MAAM;MACR,KAAK,EAAE;QACL,MAAM,GAAG,CAAC,CAAC,CAAC,aAAa,CAAC;QAC1B,MAAM;MACR;QACE,OAAO;KACV;;IAED,IAAI,CAAC,CAAC,QAAQ,CAAC,eAAe,IAAI,MAAM,KAAK,CAAC,EAAE;MAC9C,OAAO;KACR;IACD,IAAI,CAAC,CAAC,QAAQ,CAAC,eAAe,IAAI,MAAM,KAAK,CAAC,EAAE;MAC9C,OAAO;KACR;;IAED,OAAO,CAAC,SAAS,IAAI,MAAM,CAAC;IAC5B,OAAO,CAAC,UAAU,IAAI,MAAM,CAAC;IAC7B,cAAc,CAAC,CAAC,CAAC,CAAC;;IAElB,IAAI,oBAAoB,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE;MACxC,CAAC,CAAC,cAAc,EAAE,CAAC;KACpB;GACF,CAAC,CAAC;CACJ;;AC9Ic,eAAS,CAAC,EAAE;EACzBD,IAAM,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC;;EAI1B,SAAS,oBAAoB,CAAC,MAAM,EAAE,MAAM,EAAE;IAC5CA,IAAM,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;IACvDA,IAAM,KAAK,GAAG,OAAO,CAAC,SAAS,KAAK,CAAC,CAAC;IACtCA,IAAM,QAAQ;MACZ,gBAAgB,GAAG,OAAO,CAAC,YAAY,KAAK,OAAO,CAAC,YAAY,CAAC;IACnEA,IAAM,MAAM,GAAG,OAAO,CAAC,UAAU,KAAK,CAAC,CAAC;IACxCA,IAAM,OAAO;MACX,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,WAAW,KAAK,OAAO,CAAC,WAAW,CAAC;;IAEnEC,IAAI,SAAS,CAAC;;;IAGd,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;MACvC,SAAS,GAAG,KAAK,IAAI,QAAQ,CAAC;KAC/B,MAAM;MACL,SAAS,GAAG,MAAM,IAAI,OAAO,CAAC;KAC/B;;IAED,OAAO,SAAS,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,gBAAgB,GAAG,IAAI,CAAC;GACxD;;EAED,SAAS,iBAAiB,CAAC,CAAC,EAAE;IAC5BA,IAAI,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC;IACtBA,IAAI,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;;IAE3B,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;;MAElE,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,WAAW,IAAI,CAAC,CAAC;MAClC,MAAM,GAAG,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC;KAC5B;;IAED,IAAI,CAAC,CAAC,SAAS,IAAI,CAAC,CAAC,SAAS,KAAK,CAAC,EAAE;;MAEpC,MAAM,IAAI,EAAE,CAAC;MACb,MAAM,IAAI,EAAE,CAAC;KACd;;IAED,IAAI,MAAM,KAAK,MAAM,IAAI,MAAM,KAAK,MAAM,mBAAmB;;MAE3D,MAAM,GAAG,CAAC,CAAC;MACX,MAAM,GAAG,CAAC,CAAC,UAAU,CAAC;KACvB;;IAED,IAAI,CAAC,CAAC,QAAQ,EAAE;;MAEd,OAAO,CAAC,CAAC,MAAM,EAAE,CAAC,MAAM,CAAC,CAAC;KAC3B;IACD,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;GACzB;;EAED,SAAS,uBAAuB,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE;;IAEvD,IAAI,CAAC,GAAG,CAAC,QAAQ,IAAI,OAAO,CAAC,aAAa,CAAC,cAAc,CAAC,EAAE;MAC1D,OAAO,IAAI,CAAC;KACb;;IAED,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;MAC7B,OAAO,KAAK,CAAC;KACd;;IAEDA,IAAI,MAAM,GAAG,MAAM,CAAC;;IAEpB,OAAO,MAAM,IAAI,MAAM,KAAK,OAAO,EAAE;MACnC,IAAI,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;QACpD,OAAO,IAAI,CAAC;OACb;;MAEDD,IAAM,KAAK,GAAGK,GAAO,CAAC,MAAM,CAAC,CAAC;;;MAG9B,IAAI,MAAM,IAAI,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,eAAe,CAAC,EAAE;QACpDL,IAAM,YAAY,GAAG,MAAM,CAAC,YAAY,GAAG,MAAM,CAAC,YAAY,CAAC;QAC/D,IAAI,YAAY,GAAG,CAAC,EAAE;UACpB;YACE,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,IAAI,MAAM,GAAG,CAAC;aAClC,MAAM,CAAC,SAAS,GAAG,YAAY,IAAI,MAAM,GAAG,CAAC,CAAC;YAC/C;YACA,OAAO,IAAI,CAAC;WACb;SACF;OACF;;MAED,IAAI,MAAM,IAAI,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,eAAe,CAAC,EAAE;QACpDA,IAAM,aAAa,GAAG,MAAM,CAAC,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC;QAC9D,IAAI,aAAa,GAAG,CAAC,EAAE;UACrB;YACE,CAAC,MAAM,CAAC,UAAU,GAAG,CAAC,IAAI,MAAM,GAAG,CAAC;aACnC,MAAM,CAAC,UAAU,GAAG,aAAa,IAAI,MAAM,GAAG,CAAC,CAAC;YACjD;YACA,OAAO,IAAI,CAAC;WACb;SACF;OACF;;MAED,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC;KAC5B;;IAED,OAAO,KAAK,CAAC;GACd;;EAED,SAAS,iBAAiB,CAAC,CAAC,EAAE;IAC5B,OAAsB,GAAG,iBAAiB,CAAC,CAAC;IAArC;IAAQ,oBAA+B;;IAE9C,IAAI,uBAAuB,CAAC,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE;MACrD,OAAO;KACR;;IAEDC,IAAI,aAAa,GAAG,KAAK,CAAC;IAC1B,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,gBAAgB,EAAE;;;MAGhC,OAAO,CAAC,SAAS,IAAI,MAAM,GAAG,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC;MACpD,OAAO,CAAC,UAAU,IAAI,MAAM,GAAG,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC;KACtD,MAAM,IAAI,CAAC,CAAC,gBAAgB,IAAI,CAAC,CAAC,CAAC,gBAAgB,EAAE;;;MAGpD,IAAI,MAAM,EAAE;QACV,OAAO,CAAC,SAAS,IAAI,MAAM,GAAG,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC;OACrD,MAAM;QACL,OAAO,CAAC,SAAS,IAAI,MAAM,GAAG,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC;OACrD;MACD,aAAa,GAAG,IAAI,CAAC;KACtB,MAAM,IAAI,CAAC,CAAC,gBAAgB,IAAI,CAAC,CAAC,CAAC,gBAAgB,EAAE;;;MAGpD,IAAI,MAAM,EAAE;QACV,OAAO,CAAC,UAAU,IAAI,MAAM,GAAG,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC;OACtD,MAAM;QACL,OAAO,CAAC,UAAU,IAAI,MAAM,GAAG,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC;OACtD;MACD,aAAa,GAAG,IAAI,CAAC;KACtB;;IAED,cAAc,CAAC,CAAC,CAAC,CAAC;;IAElB,aAAa,GAAG,aAAa,IAAI,oBAAoB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACtE,IAAI,aAAa,IAAI,CAAC,CAAC,CAAC,OAAO,EAAE;MAC/B,CAAC,CAAC,eAAe,EAAE,CAAC;MACpB,CAAC,CAAC,cAAc,EAAE,CAAC;KACpB;GACF;;EAED,IAAI,OAAO,MAAM,CAAC,OAAO,KAAK,WAAW,EAAE;IACzC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,iBAAiB,CAAC,CAAC;GACnD,MAAM,IAAI,OAAO,MAAM,CAAC,YAAY,KAAK,WAAW,EAAE;IACrD,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,YAAY,EAAE,iBAAiB,CAAC,CAAC;GACxD;CACF;;ACxJc,eAAS,CAAC,EAAE;EACzB,IAAI,CAAC,GAAG,CAAC,aAAa,IAAI,CAAC,GAAG,CAAC,iBAAiB,EAAE;IAChD,OAAO;GACR;;EAEDD,IAAM,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC;;EAE1B,SAAS,aAAa,CAAC,MAAM,EAAE,MAAM,EAAE;IACrCA,IAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;IAChDA,IAAM,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;IACtCA,IAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACpCA,IAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;;IAEpC,IAAI,UAAU,GAAG,UAAU,EAAE;;;MAG3B;QACE,CAAC,MAAM,GAAG,CAAC,IAAI,SAAS,KAAK,CAAC,CAAC,aAAa,GAAG,CAAC,CAAC,eAAe;SAC/D,MAAM,GAAG,CAAC,IAAI,SAAS,KAAK,CAAC,CAAC;QAC/B;;QAEA,OAAO,MAAM,CAAC,OAAO,KAAK,CAAC,IAAI,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC;OAC3D;KACF,MAAM,IAAI,UAAU,GAAG,UAAU,EAAE;;;MAGlC;QACE,CAAC,MAAM,GAAG,CAAC,IAAI,UAAU,KAAK,CAAC,CAAC,YAAY,GAAG,CAAC,CAAC,cAAc;SAC9D,MAAM,GAAG,CAAC,IAAI,UAAU,KAAK,CAAC,CAAC;QAChC;QACA,OAAO,IAAI,CAAC;OACb;KACF;;IAED,OAAO,IAAI,CAAC;GACb;;EAED,SAAS,cAAc,CAAC,WAAW,EAAE,WAAW,EAAE;IAChD,OAAO,CAAC,SAAS,IAAI,WAAW,CAAC;IACjC,OAAO,CAAC,UAAU,IAAI,WAAW,CAAC;;IAElC,cAAc,CAAC,CAAC,CAAC,CAAC;GACnB;;EAEDC,IAAI,WAAW,GAAG,EAAE,CAAC;EACrBA,IAAI,SAAS,GAAG,CAAC,CAAC;EAClBA,IAAI,KAAK,GAAG,EAAE,CAAC;EACfA,IAAI,UAAU,GAAG,IAAI,CAAC;;EAEtB,SAAS,QAAQ,CAAC,CAAC,EAAE;IACnB,IAAI,CAAC,CAAC,aAAa,EAAE;MACnB,OAAO,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;KAC3B,MAAM;;MAEL,OAAO,CAAC,CAAC;KACV;GACF;;EAED,SAAS,YAAY,CAAC,CAAC,EAAE;IACvB,IAAI,CAAC,CAAC,WAAW,IAAI,CAAC,CAAC,WAAW,KAAK,KAAK,IAAI,CAAC,CAAC,OAAO,KAAK,CAAC,EAAE;MAC/D,OAAO,KAAK,CAAC;KACd;IACD,IAAI,CAAC,CAAC,aAAa,IAAI,CAAC,CAAC,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE;MACnD,OAAO,IAAI,CAAC;KACb;IACD;MACE,CAAC,CAAC,WAAW;MACb,CAAC,CAAC,WAAW,KAAK,OAAO;MACzB,CAAC,CAAC,WAAW,KAAK,CAAC,CAAC,oBAAoB;MACxC;MACA,OAAO,IAAI,CAAC;KACb;IACD,OAAO,KAAK,CAAC;GACd;;EAED,SAAS,UAAU,CAAC,CAAC,EAAE;IACrB,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;MACpB,OAAO;KACR;;IAEDD,IAAM,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;;IAE1B,WAAW,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;IAChC,WAAW,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;;IAEhC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC;;IAEjC,IAAI,UAAU,KAAK,IAAI,EAAE;MACvB,aAAa,CAAC,UAAU,CAAC,CAAC;KAC3B;GACF;;EAED,SAAS,uBAAuB,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE;IACvD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;MAC7B,OAAO,KAAK,CAAC;KACd;;IAEDC,IAAI,MAAM,GAAG,MAAM,CAAC;;IAEpB,OAAO,MAAM,IAAI,MAAM,KAAK,OAAO,EAAE;MACnC,IAAI,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;QACpD,OAAO,IAAI,CAAC;OACb;;MAEDD,IAAM,KAAK,GAAGK,GAAO,CAAC,MAAM,CAAC,CAAC;;;MAG9B,IAAI,MAAM,IAAI,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,eAAe,CAAC,EAAE;QACpDL,IAAM,YAAY,GAAG,MAAM,CAAC,YAAY,GAAG,MAAM,CAAC,YAAY,CAAC;QAC/D,IAAI,YAAY,GAAG,CAAC,EAAE;UACpB;YACE,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,IAAI,MAAM,GAAG,CAAC;aAClC,MAAM,CAAC,SAAS,GAAG,YAAY,IAAI,MAAM,GAAG,CAAC,CAAC;YAC/C;YACA,OAAO,IAAI,CAAC;WACb;SACF;OACF;;MAED,IAAI,MAAM,IAAI,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,eAAe,CAAC,EAAE;QACpDA,IAAM,aAAa,GAAG,MAAM,CAAC,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC;QAC9D,IAAI,aAAa,GAAG,CAAC,EAAE;UACrB;YACE,CAAC,MAAM,CAAC,UAAU,GAAG,CAAC,IAAI,MAAM,GAAG,CAAC;aACnC,MAAM,CAAC,UAAU,GAAG,aAAa,IAAI,MAAM,GAAG,CAAC,CAAC;YACjD;YACA,OAAO,IAAI,CAAC;WACb;SACF;OACF;;MAED,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC;KAC5B;;IAED,OAAO,KAAK,CAAC;GACd;;EAED,SAAS,SAAS,CAAC,CAAC,EAAE;IACpB,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE;MACnBA,IAAM,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;;MAE1BA,IAAM,aAAa,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,CAAC;;MAEjEA,IAAM,WAAW,GAAG,aAAa,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC;MAC5DA,IAAM,WAAW,GAAG,aAAa,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC;;MAE5D,IAAI,uBAAuB,CAAC,CAAC,CAAC,MAAM,EAAE,WAAW,EAAE,WAAW,CAAC,EAAE;QAC/D,OAAO;OACR;;MAED,cAAc,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;MACzC,WAAW,GAAG,aAAa,CAAC;;MAE5BA,IAAM,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC;;MAEzCA,IAAM,OAAO,GAAG,WAAW,GAAG,SAAS,CAAC;MACxC,IAAI,OAAO,GAAG,CAAC,EAAE;QACf,KAAK,CAAC,CAAC,GAAG,WAAW,GAAG,OAAO,CAAC;QAChC,KAAK,CAAC,CAAC,GAAG,WAAW,GAAG,OAAO,CAAC;QAChC,SAAS,GAAG,WAAW,CAAC;OACzB;;MAED,IAAI,aAAa,CAAC,WAAW,EAAE,WAAW,CAAC,EAAE;QAC3C,CAAC,CAAC,cAAc,EAAE,CAAC;OACpB;KACF;GACF;EACD,SAAS,QAAQ,GAAG;IAClB,IAAI,CAAC,CAAC,QAAQ,CAAC,WAAW,EAAE;MAC1B,aAAa,CAAC,UAAU,CAAC,CAAC;MAC1B,UAAU,GAAG,WAAW,CAAC,WAAW;QAClC,IAAI,CAAC,CAAC,aAAa,EAAE;UACnB,aAAa,CAAC,UAAU,CAAC,CAAC;UAC1B,OAAO;SACR;;QAED,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;UACxB,aAAa,CAAC,UAAU,CAAC,CAAC;UAC1B,OAAO;SACR;;QAED,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,EAAE;UACxD,aAAa,CAAC,UAAU,CAAC,CAAC;UAC1B,OAAO;SACR;;QAED,cAAc,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;;QAE3C,KAAK,CAAC,CAAC,IAAI,GAAG,CAAC;QACf,KAAK,CAAC,CAAC,IAAI,GAAG,CAAC;OAChB,EAAE,EAAE,CAAC,CAAC;KACR;GACF;;EAED,IAAI,GAAG,CAAC,aAAa,EAAE;IACrB,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,YAAY,EAAE,UAAU,CAAC,CAAC;IAChD,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC;IAC9C,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;GAC7C,MAAM,IAAI,GAAG,CAAC,iBAAiB,EAAE;IAChC,IAAI,MAAM,CAAC,YAAY,EAAE;MACvB,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,aAAa,EAAE,UAAU,CAAC,CAAC;MACjD,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,aAAa,EAAE,SAAS,CAAC,CAAC;MAChD,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;KAC9C,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE;MAChC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,eAAe,EAAE,UAAU,CAAC,CAAC;MACnD,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,eAAe,EAAE,SAAS,CAAC,CAAC;MAClD,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,aAAa,EAAE,QAAQ,CAAC,CAAC;KAChD;GACF;CACF;;ACxMDA,IAAM,eAAe,eAAM,UAAI;EAC7B,QAAQ,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,CAAC;EACpE,kBAAkB,EAAE,IAAI;EACxB,kBAAkB,EAAE,IAAI;EACxB,kBAAkB,EAAE,IAAI;EACxB,mBAAmB,EAAE,CAAC;EACtB,mBAAmB,EAAE,CAAC;EACtB,eAAe,EAAE,KAAK;EACtB,eAAe,EAAE,KAAK;EACtB,WAAW,EAAE,IAAI;EACjB,gBAAgB,EAAE,KAAK;EACvB,gBAAgB,EAAE,IAAI;EACtB,UAAU,EAAE,CAAC;CACd,IAAC,CAAC;;AAEHA,IAAM,QAAQ,GAAG;EACf,YAAY,EAAE,SAAS;EACvB,YAAY,EAAE,SAAS;YACvB,QAAQ;SACR,KAAK;SACL,KAAK;CACN,CAAC;;AAEF,IAAqB,gBAAgB,GACnC,yBAAW,CAAC,OAAO,EAAE,YAAiB,EAAE;;6CAAP,GAAG;AAAK;EACvC,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;IACjC,OAAS,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;GAC3C;;EAEH,IAAM,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;IACjC,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAC;GAC3E;;EAED,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;;EAEzB,OAAS,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;;EAEhC,IAAI,CAAC,QAAQ,GAAG,eAAe,EAAE,CAAC;EAClC,KAAKA,IAAM,GAAG,IAAI,YAAY,EAAE;IAChC,IAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;GACxC;;EAED,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;EAC3B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;EAC5B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;EACzB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;;EAE1BA,IAAM,KAAK,eAAM,SAAG,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,IAAC,CAAC;EAC3DA,IAAM,IAAI,eAAM,SAAG,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,IAAC,CAAC;;EAE7D,IAAI,CAAC,KAAK,GAAGK,GAAO,CAAC,OAAO,CAAC,CAAC,SAAS,KAAK,KAAK,CAAC;EAClD,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,EAAE;IACzB,OAAS,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;GAChC;EACD,IAAI,CAAC,gBAAgB,GAAG,aAAI;IAC1BL,IAAM,kBAAkB,GAAG,OAAO,CAAC,UAAU,CAAC;IAC9CC,IAAI,MAAM,GAAG,IAAI,CAAC;IAClB,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;IACxB,MAAM,GAAG,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC;IAChC,OAAO,CAAC,UAAU,GAAG,kBAAkB,CAAC;IAC1C,OAAS,MAAM,CAAC;GACf,GAAG,CAAC;EACL,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,gBAAgB;MACjD,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW;MACzC,CAAC,CAAC;EACN,IAAI,CAAC,KAAK,GAAG,IAAI,YAAY,EAAE,CAAC;EAClC,IAAM,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,IAAI,QAAQ,CAAC;;EAEvD,IAAI,CAAC,cAAc,GAAGQ,GAAO,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;EACvD,OAAS,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;EACzC,IAAI,CAAC,UAAU,GAAGA,GAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;EACpD,IAAM,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;EACnD,IAAM,CAAC,UAAU,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;EAC5C,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;EACjD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;EAC/C,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;EAC7B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;EAC5B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;EAC3BT,IAAM,UAAU,GAAGK,GAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;EAChD,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;EACxD,IAAI,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAAE;IAChC,IAAI,CAAC,uBAAuB,GAAG,KAAK,CAAC;IACvC,IAAM,CAAC,aAAa,GAAG,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;GAC5C,MAAM;IACL,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;GACrC;EACH,IAAM,CAAC,gBAAgB;IACnB,KAAK,CAAC,UAAU,CAAC,eAAe,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC;;EAEzEG,GAAO,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC;EACrD,IAAM,CAAC,gBAAgB;IACnB,KAAK,CAAC,UAAU,CAAC,UAAU,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;EAC/DA,GAAO,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,CAAC;EAC9C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;EACvB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;;EAEvB,IAAI,CAAC,cAAc,GAAGC,GAAO,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;EACvD,OAAS,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;EACzC,IAAI,CAAC,UAAU,GAAGA,GAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;EACpD,IAAM,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;EACnD,IAAM,CAAC,UAAU,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;EAC5C,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;EACjD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;EAC/C,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;EAC7B,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;EAC7B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;EAC1BT,IAAM,UAAU,GAAGK,GAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;EAChD,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC,UAAU,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;EACtD,IAAI,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE;IAC/B,IAAI,CAAC,sBAAsB,GAAG,KAAK,CAAC;IACtC,IAAM,CAAC,cAAc,GAAG,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;GAC9C,MAAM;IACL,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;GACpC;EACD,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC;EAC9E,IAAM,CAAC,gBAAgB;IACnB,KAAK,CAAC,UAAU,CAAC,cAAc,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;EACzEG,GAAO,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC;EACrD,IAAM,CAAC,iBAAiB;IACpB,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;EAC/DA,GAAO,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,CAAC;EAC9C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;EACxB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;;EAEzB,IAAM,CAAC,KAAK,GAAG;IACX,CAAC;MACC,OAAO,CAAC,UAAU,IAAI,CAAC;UACnB,OAAO;UACP,OAAO,CAAC,UAAU,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,cAAc;UAC7D,KAAK;UACL,IAAI;IACV,CAAC;MACC,OAAO,CAAC,SAAS,IAAI,CAAC;UAClB,OAAO;UACP,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,eAAe;UAC9D,KAAK;UACL,IAAI;GACX,CAAC;;EAEF,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;;EAEpB,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,WAAC,aAAY,SAAG,QAAQ,CAAC,WAAW,CAAC,CAACN,MAAI,IAAC,CAAC,CAAC;;EAE3E,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;EACnD,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,UAAU,CAAC;EAC3C,IAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,YAAE,GAAE,SAAGA,MAAI,CAAC,QAAQ,CAAC,CAAC,IAAC,CAAC,CAAC;EAC/D,cAAc,CAAC,IAAI,CAAC,CAAC;AACvB,EAAC;;AAEH,2BAAE,4BAAS;EACP,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;IACjB,OAAO;GACR;;;EAGD,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,gBAAgB;MACjD,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW;MACnD,CAAC,CAAC;;;EAGNM,GAAO,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC;EACnDA,GAAO,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC;EACrD,IAAM,CAAC,gBAAgB;IACnB,KAAK,CAACH,GAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,UAAU,CAAC;IAC9C,KAAK,CAACA,GAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,WAAW,CAAC,CAAC;EACpD,IAAM,CAAC,iBAAiB;IACpB,KAAK,CAACA,GAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,SAAS,CAAC;IAC7C,KAAK,CAACA,GAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,YAAY,CAAC,CAAC;;;EAGnDG,GAAO,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC;EAClDA,GAAO,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC;;EAElD,cAAc,CAAC,IAAI,CAAC,CAAC;;EAErB,iBAAiB,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;EAC/C,iBAAiB,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;;EAEhDA,GAAO,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,CAAC;EAC9CA,GAAO,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,CAAC;AAChD,EAAC;;AAEH,2BAAE,8BAAS,CAAC,EAAE;EACV,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;IACjB,OAAO;GACR;;EAED,cAAc,CAAC,IAAI,CAAC,CAAC;EACrB,iBAAiB,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC;EAC5E,iBAAiB;IACf,IAAI;IACJ,MAAM;IACR,IAAM,CAAC,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC,cAAc;GAC9C,CAAC;;EAEF,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;EAC1D,IAAM,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC;AAChD,EAAC;;AAEH,2BAAE,8BAAU;EACR,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;IACjB,OAAO;GACR;;EAED,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;EACzBD,MAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;EAC9BA,MAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;EAC9BA,MAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;EAClCA,MAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;EAChC,IAAI,CAAC,eAAe,EAAE,CAAC;;;EAGvB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;EACpB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;EACvB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;EACvB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;EAC3B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;;EAE3B,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AACvB,EAAC;;AAEH,2BAAE,8CAAkB;EAClB,IAAM,CAAC,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS;KAC5C,KAAK,CAAC,GAAG,CAAC;KACV,MAAM,WAAC,MAAK,SAAG,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,IAAC,CAAC;KAC5C,IAAI,CAAC,GAAG,CAAC,CAAC;AACf,CAAC;;;;"}