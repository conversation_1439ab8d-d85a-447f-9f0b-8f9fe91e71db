{"errors": [], "warnings": [], "version": "1.15.0", "hash": "b05b6c6129779fa52c0a", "publicPath": "", "assetsByChunkName": {"main": "vanillaTextMask.js"}, "assets": [{"name": "vanillaTextMask.js", "size": 8528, "chunks": [0], "chunkNames": ["main"], "emitted": true}, {"name": "stats.json", "size": 0, "chunks": [], "chunkNames": []}], "chunks": [{"id": 0, "rendered": true, "initial": true, "entry": true, "extraAsync": false, "size": 40646, "names": ["main"], "files": ["vanillaTextMask.js"], "hash": "0bf39240be56919c292d", "parents": [], "modules": [{"id": 0, "identifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/vanilla/src/vanillaTextMask.js", "name": "./vanilla/src/vanillaTextMask.js", "index": 0, "index2": 5, "size": 1273, "cacheable": true, "built": true, "optional": false, "prefetched": false, "chunks": [0], "assets": [], "issuer": null, "failed": false, "errors": 0, "warnings": 0, "reasons": [], "source": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.conformToMask = undefined;\nexports.maskInput = maskInput;\n\nvar _conformToMask = require('../../core/src/conformToMask.js');\n\nObject.defineProperty(exports, 'conformToMask', {\n  enumerable: true,\n  get: function get() {\n    return _interopRequireDefault(_conformToMask).default;\n  }\n});\n\nvar _createTextMaskInputElement = require('../../core/src/createTextMaskInputElement');\n\nvar _createTextMaskInputElement2 = _interopRequireDefault(_createTextMaskInputElement);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction maskInput(textMaskConfig) {\n  var inputElement = textMaskConfig.inputElement;\n\n  var textMaskInputElement = (0, _createTextMaskInputElement2.default)(textMaskConfig);\n  var inputHandler = function inputHandler(_ref) {\n    var value = _ref.target.value;\n    return textMaskInputElement.update(value);\n  };\n\n  inputElement.addEventListener('input', inputHandler);\n\n  textMaskInputElement.update(inputElement.value);\n\n  return {\n    textMaskInputElement: textMaskInputElement,\n\n    destroy: function destroy() {\n      inputElement.removeEventListener('input', inputHandler);\n    }\n  };\n}\n\nexports.default = maskInput;"}, {"id": 1, "identifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/core/src/constants.js", "name": "./core/src/constants.js", "index": 3, "index2": 0, "size": 184, "cacheable": true, "built": true, "optional": false, "prefetched": false, "chunks": [0], "assets": [], "issuer": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/core/src/conformToMask.js", "failed": false, "errors": 0, "warnings": 0, "reasons": [{"moduleId": 2, "moduleIdentifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/core/src/conformToMask.js", "module": "./core/src/conformToMask.js", "moduleName": "./core/src/conformToMask.js", "type": "cjs require", "userRequest": "./constants", "loc": "13:17-39"}, {"moduleId": 3, "moduleIdentifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/core/src/utilities.js", "module": "./core/src/utilities.js", "moduleName": "./core/src/utilities.js", "type": "cjs require", "userRequest": "./constants", "loc": "12:17-39"}, {"moduleId": 5, "moduleIdentifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/core/src/createTextMaskInputElement.js", "module": "./core/src/createTextMaskInputElement.js", "moduleName": "./core/src/createTextMaskInputElement.js", "type": "cjs require", "userRequest": "./constants", "loc": "23:17-39"}], "source": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar placeholderChar = exports.placeholderChar = '_';\nvar strFunction = exports.strFunction = 'function';"}, {"id": 2, "identifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/core/src/conformToMask.js", "name": "./core/src/conformToMask.js", "index": 1, "index2": 2, "size": 13267, "cacheable": true, "built": true, "optional": false, "prefetched": false, "chunks": [0], "assets": [], "issuer": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/vanilla/src/vanillaTextMask.js", "failed": false, "errors": 0, "warnings": 0, "reasons": [{"moduleId": 0, "moduleIdentifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/vanilla/src/vanillaTextMask.js", "module": "./vanilla/src/vanillaTextMask.js", "moduleName": "./vanilla/src/vanillaTextMask.js", "type": "cjs require", "userRequest": "../../core/src/conformToMask.js", "loc": "9:21-63"}, {"moduleId": 5, "moduleIdentifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/core/src/createTextMaskInputElement.js", "module": "./core/src/createTextMaskInputElement.js", "moduleName": "./core/src/createTextMaskInputElement.js", "type": "cjs require", "userRequest": "./conformToMask", "loc": "17:22-48"}], "source": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; };\n\nexports.default = conformToMask;\n\nvar _utilities = require('./utilities');\n\nvar _constants = require('./constants');\n\nvar emptyArray = [];\nvar emptyString = '';\n\nfunction conformToMask() {\n  var rawValue = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : emptyString;\n  var mask = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : emptyArray;\n  var config = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n\n  if (!(0, _utilities.isArray)(mask)) {\n    // If someone passes a function as the mask property, we should call the\n    // function to get the mask array - Normally this is handled by the\n    // `createTextMaskInputElement:update` function - this allows mask functions\n    // to be used directly with `conformToMask`\n    if ((typeof mask === 'undefined' ? 'undefined' : _typeof(mask)) === _constants.strFunction) {\n      // call the mask function to get the mask array\n      mask = mask(rawValue, config);\n\n      // mask functions can setup caret traps to have some control over how the caret moves. We need to process\n      // the mask for any caret traps. `processCaretTraps` will remove the caret traps from the mask\n      mask = (0, _utilities.processCaretTraps)(mask).maskWithoutCaretTraps;\n    } else {\n      throw new Error('Text-mask:conformToMask; The mask property must be an array.');\n    }\n  }\n\n  // These configurations tell us how to conform the mask\n  var _config$guide = config.guide,\n      guide = _config$guide === undefined ? true : _config$guide,\n      _config$previousConfo = config.previousConformedValue,\n      previousConformedValue = _config$previousConfo === undefined ? emptyString : _config$previousConfo,\n      _config$placeholderCh = config.placeholderChar,\n      placeholderChar = _config$placeholderCh === undefined ? _constants.placeholderChar : _config$placeholderCh,\n      _config$placeholder = config.placeholder,\n      placeholder = _config$placeholder === undefined ? (0, _utilities.convertMaskToPlaceholder)(mask, placeholderChar) : _config$placeholder,\n      currentCaretPosition = config.currentCaretPosition,\n      keepCharPositions = config.keepCharPositions;\n\n  // The configs below indicate that the user wants the algorithm to work in *no guide* mode\n\n  var suppressGuide = guide === false && previousConformedValue !== undefined;\n\n  // Calculate lengths once for performance\n  var rawValueLength = rawValue.length;\n  var previousConformedValueLength = previousConformedValue.length;\n  var placeholderLength = placeholder.length;\n  var maskLength = mask.length;\n\n  // This tells us the number of edited characters and the direction in which they were edited (+/-)\n  var editDistance = rawValueLength - previousConformedValueLength;\n\n  // In *no guide* mode, we need to know if the user is trying to add a character or not\n  var isAddition = editDistance > 0;\n\n  // Tells us the index of the first change. For (************* to (38) 394-4938, that would be 1\n  var indexOfFirstChange = currentCaretPosition + (isAddition ? -editDistance : 0);\n\n  // We're also gonna need the index of last change, which we can derive as follows...\n  var indexOfLastChange = indexOfFirstChange + Math.abs(editDistance);\n\n  // If `conformToMask` is configured to keep character positions, that is, for mask 111, previous value\n  // _2_ and raw value 3_2_, the new conformed value should be 32_, not 3_2 (default behavior). That's in the case of\n  // addition. And in the case of deletion, previous value _23, raw value _3, the new conformed string should be\n  // __3, not _3_ (default behavior)\n  //\n  // The next block of logic handles keeping character positions for the case of deletion. (Keeping\n  // character positions for the case of addition is further down since it is handled differently.)\n  // To do this, we want to compensate for all characters that were deleted\n  if (keepCharPositions === true && !isAddition) {\n    // We will be storing the new placeholder characters in this variable.\n    var compensatingPlaceholderChars = emptyString;\n\n    // For every character that was deleted from a placeholder position, we add a placeholder char\n    for (var i = indexOfFirstChange; i < indexOfLastChange; i++) {\n      if (placeholder[i] === placeholderChar) {\n        compensatingPlaceholderChars += placeholderChar;\n      }\n    }\n\n    // Now we trick our algorithm by modifying the raw value to make it contain additional placeholder characters\n    // That way when the we start laying the characters again on the mask, it will keep the non-deleted characters\n    // in their positions.\n    rawValue = rawValue.slice(0, indexOfFirstChange) + compensatingPlaceholderChars + rawValue.slice(indexOfFirstChange, rawValueLength);\n  }\n\n  // Convert `rawValue` string to an array, and mark characters based on whether they are newly added or have\n  // existed in the previous conformed value. Identifying new and old characters is needed for `conformToMask`\n  // to work if it is configured to keep character positions.\n  var rawValueArr = rawValue.split(emptyString).map(function (char, i) {\n    return { char: char, isNew: i >= indexOfFirstChange && i < indexOfLastChange };\n  });\n\n  // The loop below removes masking characters from user input. For example, for mask\n  // `00 (111)`, the placeholder would be `00 (___)`. If user input is `00 (234)`, the loop below\n  // would remove all characters but `234` from the `rawValueArr`. The rest of the algorithm\n  // then would lay `234` on top of the available placeholder positions in the mask.\n  for (var _i = rawValueLength - 1; _i >= 0; _i--) {\n    var char = rawValueArr[_i].char;\n\n\n    if (char !== placeholderChar) {\n      var shouldOffset = _i >= indexOfFirstChange && previousConformedValueLength === maskLength;\n\n      if (char === placeholder[shouldOffset ? _i - editDistance : _i]) {\n        rawValueArr.splice(_i, 1);\n      }\n    }\n  }\n\n  // This is the variable that we will be filling with characters as we figure them out\n  // in the algorithm below\n  var conformedValue = emptyString;\n  var someCharsRejected = false;\n\n  // Ok, so first we loop through the placeholder looking for placeholder characters to fill up.\n  placeholderLoop: for (var _i2 = 0; _i2 < placeholderLength; _i2++) {\n    var charInPlaceholder = placeholder[_i2];\n\n    // We see one. Let's find out what we can put in it.\n    if (charInPlaceholder === placeholderChar) {\n      // But before that, do we actually have any user characters that need a place?\n      if (rawValueArr.length > 0) {\n        // We will keep chipping away at user input until either we run out of characters\n        // or we find at least one character that we can map.\n        while (rawValueArr.length > 0) {\n          // Let's retrieve the first user character in the queue of characters we have left\n          var _rawValueArr$shift = rawValueArr.shift(),\n              rawValueChar = _rawValueArr$shift.char,\n              isNew = _rawValueArr$shift.isNew;\n\n          // If the character we got from the user input is a placeholder character (which happens\n          // regularly because user input could be something like (540) 90_-____, which includes\n          // a bunch of `_` which are placeholder characters) and we are not in *no guide* mode,\n          // then we map this placeholder character to the current spot in the placeholder\n\n\n          if (rawValueChar === placeholderChar && suppressGuide !== true) {\n            conformedValue += placeholderChar;\n\n            // And we go to find the next placeholder character that needs filling\n            continue placeholderLoop;\n\n            // Else if, the character we got from the user input is not a placeholder, let's see\n            // if the current position in the mask can accept it.\n          } else if (mask[_i2].test(rawValueChar)) {\n            // we map the character differently based on whether we are keeping character positions or not.\n            // If any of the conditions below are met, we simply map the raw value character to the\n            // placeholder position.\n            if (keepCharPositions !== true || isNew === false || previousConformedValue === emptyString || guide === false || !isAddition) {\n              conformedValue += rawValueChar;\n            } else {\n              // We enter this block of code if we are trying to keep character positions and none of the conditions\n              // above is met. In this case, we need to see if there's an available spot for the raw value character\n              // to be mapped to. If we couldn't find a spot, we will discard the character.\n              //\n              // For example, for mask `1111`, previous conformed value `_2__`, raw value `942_2__`. We can map the\n              // `9`, to the first available placeholder position, but then, there are no more spots available for the\n              // `4` and `2`. So, we discard them and end up with a conformed value of `92__`.\n              var rawValueArrLength = rawValueArr.length;\n              var indexOfNextAvailablePlaceholderChar = null;\n\n              // Let's loop through the remaining raw value characters. We are looking for either a suitable spot, ie,\n              // a placeholder character or a non-suitable spot, ie, a non-placeholder character that is not new.\n              // If we see a suitable spot first, we store its position and exit the loop. If we see a non-suitable\n              // spot first, we exit the loop and our `indexOfNextAvailablePlaceholderChar` will stay as `null`.\n              for (var _i3 = 0; _i3 < rawValueArrLength; _i3++) {\n                var charData = rawValueArr[_i3];\n\n                if (charData.char !== placeholderChar && charData.isNew === false) {\n                  break;\n                }\n\n                if (charData.char === placeholderChar) {\n                  indexOfNextAvailablePlaceholderChar = _i3;\n                  break;\n                }\n              }\n\n              // If `indexOfNextAvailablePlaceholderChar` is not `null`, that means the character is not blocked.\n              // We can map it. And to keep the character positions, we remove the placeholder character\n              // from the remaining characters\n              if (indexOfNextAvailablePlaceholderChar !== null) {\n                conformedValue += rawValueChar;\n                rawValueArr.splice(indexOfNextAvailablePlaceholderChar, 1);\n\n                // If `indexOfNextAvailablePlaceholderChar` is `null`, that means the character is blocked. We have to\n                // discard it.\n              } else {\n                _i2--;\n              }\n            }\n\n            // Since we've mapped this placeholder position. We move on to the next one.\n            continue placeholderLoop;\n          } else {\n            someCharsRejected = true;\n          }\n        }\n      }\n\n      // We reach this point when we've mapped all the user input characters to placeholder\n      // positions in the mask. In *guide* mode, we append the left over characters in the\n      // placeholder to the `conformedString`, but in *no guide* mode, we don't wanna do that.\n      //\n      // That is, for mask `(111)` and user input `2`, we want to return `(2`, not `(2__)`.\n      if (suppressGuide === false) {\n        conformedValue += placeholder.substr(_i2, placeholderLength);\n      }\n\n      // And we break\n      break;\n\n      // Else, the charInPlaceholder is not a placeholderChar. That is, we cannot fill it\n      // with user input. So we just map it to the final output\n    } else {\n      conformedValue += charInPlaceholder;\n    }\n  }\n\n  // The following logic is needed to deal with the case of deletion in *no guide* mode.\n  //\n  // Consider the silly mask `(111) /// 1`. What if user tries to delete the last placeholder\n  // position? Something like `(589) /// `. We want to conform that to `(589`. Not `(589) /// `.\n  // That's why the logic below finds the last filled placeholder character, and removes everything\n  // from that point on.\n  if (suppressGuide && isAddition === false) {\n    var indexOfLastFilledPlaceholderChar = null;\n\n    // Find the last filled placeholder position and substring from there\n    for (var _i4 = 0; _i4 < conformedValue.length; _i4++) {\n      if (placeholder[_i4] === placeholderChar) {\n        indexOfLastFilledPlaceholderChar = _i4;\n      }\n    }\n\n    if (indexOfLastFilledPlaceholderChar !== null) {\n      // We substring from the beginning until the position after the last filled placeholder char.\n      conformedValue = conformedValue.substr(0, indexOfLastFilledPlaceholderChar + 1);\n    } else {\n      // If we couldn't find `indexOfLastFilledPlaceholderChar` that means the user deleted\n      // the first character in the mask. So we return an empty string.\n      conformedValue = emptyString;\n    }\n  }\n\n  return { conformedValue: conformedValue, meta: { someCharsRejected: someCharsRejected } };\n}"}, {"id": 3, "identifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/core/src/utilities.js", "name": "./core/src/utilities.js", "index": 2, "index2": 1, "size": 1897, "cacheable": true, "built": true, "optional": false, "prefetched": false, "chunks": [0], "assets": [], "issuer": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/core/src/conformToMask.js", "failed": false, "errors": 0, "warnings": 0, "reasons": [{"moduleId": 2, "moduleIdentifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/core/src/conformToMask.js", "module": "./core/src/conformToMask.js", "moduleName": "./core/src/conformToMask.js", "type": "cjs require", "userRequest": "./utilities", "loc": "11:17-39"}, {"moduleId": 5, "moduleIdentifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/core/src/createTextMaskInputElement.js", "module": "./core/src/createTextMaskInputElement.js", "moduleName": "./core/src/createTextMaskInputElement.js", "type": "cjs require", "userRequest": "./utilities", "loc": "21:17-39"}], "source": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.convertMaskToPlaceholder = convertMaskToPlaceholder;\nexports.isArray = isArray;\nexports.isString = isString;\nexports.isNumber = isNumber;\nexports.processCaretTraps = processCaretTraps;\n\nvar _constants = require('./constants');\n\nvar emptyArray = [];\n\nfunction convertMaskToPlaceholder() {\n  var mask = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : emptyArray;\n  var placeholderChar = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : _constants.placeholderChar;\n\n  if (!isArray(mask)) {\n    throw new Error('Text-mask:convertMaskToPlaceholder; The mask property must be an array.');\n  }\n\n  if (mask.indexOf(placeholderChar) !== -1) {\n    throw new Error('Placeholder character must not be used as part of the mask. Please specify a character ' + 'that is not present in your mask as your placeholder character.\\n\\n' + ('The placeholder character that was received is: ' + JSON.stringify(placeholderChar) + '\\n\\n') + ('The mask that was received is: ' + JSON.stringify(mask)));\n  }\n\n  return mask.map(function (char) {\n    return char instanceof RegExp ? placeholderChar : char;\n  }).join('');\n}\n\nfunction isArray(value) {\n  return Array.isArray && Array.isArray(value) || value instanceof Array;\n}\n\nfunction isString(value) {\n  return typeof value === 'string' || value instanceof String;\n}\n\nfunction isNumber(value) {\n  return typeof value === 'number' && value.length === undefined && !isNaN(value);\n}\n\nvar strCaretTrap = '[]';\nfunction processCaretTraps(mask) {\n  var indexes = [];\n\n  var indexOfCaretTrap = void 0;\n  while (indexOfCaretTrap = mask.indexOf(strCaretTrap), indexOfCaretTrap !== -1) {\n    // eslint-disable-line\n    indexes.push(indexOfCaretTrap);\n\n    mask.splice(indexOfCaretTrap, 1);\n  }\n\n  return { maskWithoutCaretTraps: mask, indexes: indexes };\n}"}, {"id": 4, "identifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/core/src/adjustCaretPosition.js", "name": "./core/src/adjustCaretPosition.js", "index": 5, "index2": 3, "size": 12699, "cacheable": true, "built": true, "optional": false, "prefetched": false, "chunks": [0], "assets": [], "issuer": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/core/src/createTextMaskInputElement.js", "failed": false, "errors": 0, "warnings": 0, "reasons": [{"moduleId": 5, "moduleIdentifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/core/src/createTextMaskInputElement.js", "module": "./core/src/createTextMaskInputElement.js", "moduleName": "./core/src/createTextMaskInputElement.js", "type": "cjs require", "userRequest": "./adjustCaretPosition", "loc": "13:27-59"}], "source": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = adjustCaretPosition;\nvar defaultArray = [];\nvar emptyString = '';\n\nfunction adjustCaretPosition(_ref) {\n  var _ref$previousConforme = _ref.previousConformedValue,\n      previousConformedValue = _ref$previousConforme === undefined ? emptyString : _ref$previousConforme,\n      _ref$previousPlacehol = _ref.previousPlaceholder,\n      previousPlaceholder = _ref$previousPlacehol === undefined ? emptyString : _ref$previousPlacehol,\n      _ref$currentCaretPosi = _ref.currentCaretPosition,\n      currentCaretPosition = _ref$currentCaretPosi === undefined ? 0 : _ref$currentCaretPosi,\n      conformedValue = _ref.conformedValue,\n      rawValue = _ref.rawValue,\n      placeholderChar = _ref.placeholderChar,\n      placeholder = _ref.placeholder,\n      _ref$indexesOfPipedCh = _ref.indexesOfPipedChars,\n      indexesOfPipedChars = _ref$indexesOfPipedCh === undefined ? defaultArray : _ref$indexesOfPipedCh,\n      _ref$caretTrapIndexes = _ref.caretTrapIndexes,\n      caretTrapIndexes = _ref$caretTrapIndexes === undefined ? defaultArray : _ref$caretTrapIndexes;\n\n  if (currentCaretPosition === 0 || !rawValue.length) {\n    return 0;\n  }\n\n  // Store lengths for faster performance?\n  var rawValueLength = rawValue.length;\n  var previousConformedValueLength = previousConformedValue.length;\n  var placeholderLength = placeholder.length;\n  var conformedValueLength = conformedValue.length;\n\n  // This tells us how long the edit is. If user modified input from `(2__)` to `(243__)`,\n  // we know the user in this instance pasted two characters\n  var editLength = rawValueLength - previousConformedValueLength;\n\n  // If the edit length is positive, that means the user is adding characters, not deleting.\n  var isAddition = editLength > 0;\n\n  // This is the first raw value the user entered that needs to be conformed to mask\n  var isFirstRawValue = previousConformedValueLength === 0;\n\n  // A partial multi-character edit happens when the user makes a partial selection in their\n  // input and edits that selection. That is going from `(*************` to `() 432-4348` by\n  // selecting the first 3 digits and pressing backspace.\n  //\n  // Such cases can also happen when the user presses the backspace while holding down the ALT\n  // key.\n  var isPartialMultiCharEdit = editLength > 1 && !isAddition && !isFirstRawValue;\n\n  // This algorithm doesn't support all cases of multi-character edits, so we just return\n  // the current caret position.\n  //\n  // This works fine for most cases.\n  if (isPartialMultiCharEdit) {\n    return currentCaretPosition;\n  }\n\n  // For a mask like (111), if the `previousConformedValue` is (1__) and user attempts to enter\n  // `f` so the `rawValue` becomes (1f__), the new `conformedValue` would be (1__), which is the\n  // same as the original `previousConformedValue`. We handle this case differently for caret\n  // positioning.\n  var possiblyHasRejectedChar = isAddition && (previousConformedValue === conformedValue || conformedValue === placeholder);\n\n  var startingSearchIndex = 0;\n  var trackRightCharacter = void 0;\n  var targetChar = void 0;\n\n  if (possiblyHasRejectedChar) {\n    startingSearchIndex = currentCaretPosition - editLength;\n  } else {\n    // At this point in the algorithm, we want to know where the caret is right before the raw input\n    // has been conformed, and then see if we can find that same spot in the conformed input.\n    //\n    // We do that by seeing what character lies immediately before the caret, and then look for that\n    // same character in the conformed input and place the caret there.\n\n    // First, we need to normalize the inputs so that letter capitalization between raw input and\n    // conformed input wouldn't matter.\n    var normalizedConformedValue = conformedValue.toLowerCase();\n    var normalizedRawValue = rawValue.toLowerCase();\n\n    // Then we take all characters that come before where the caret currently is.\n    var leftHalfChars = normalizedRawValue.substr(0, currentCaretPosition).split(emptyString);\n\n    // Now we find all the characters in the left half that exist in the conformed input\n    // This step ensures that we don't look for a character that was filtered out or rejected by `conformToMask`.\n    var intersection = leftHalfChars.filter(function (char) {\n      return normalizedConformedValue.indexOf(char) !== -1;\n    });\n\n    // The last character in the intersection is the character we want to look for in the conformed\n    // value and the one we want to adjust the caret close to\n    targetChar = intersection[intersection.length - 1];\n\n    // Calculate the number of mask characters in the previous placeholder\n    // from the start of the string up to the place where the caret is\n    var previousLeftMaskChars = previousPlaceholder.substr(0, intersection.length).split(emptyString).filter(function (char) {\n      return char !== placeholderChar;\n    }).length;\n\n    // Calculate the number of mask characters in the current placeholder\n    // from the start of the string up to the place where the caret is\n    var leftMaskChars = placeholder.substr(0, intersection.length).split(emptyString).filter(function (char) {\n      return char !== placeholderChar;\n    }).length;\n\n    // Has the number of mask characters up to the caret changed?\n    var masklengthChanged = leftMaskChars !== previousLeftMaskChars;\n\n    // Detect if `targetChar` is a mask character and has moved to the left\n    var targetIsMaskMovingLeft = previousPlaceholder[intersection.length - 1] !== undefined && placeholder[intersection.length - 2] !== undefined && previousPlaceholder[intersection.length - 1] !== placeholderChar && previousPlaceholder[intersection.length - 1] !== placeholder[intersection.length - 1] && previousPlaceholder[intersection.length - 1] === placeholder[intersection.length - 2];\n\n    // If deleting and the `targetChar` `is a mask character and `masklengthChanged` is true\n    // or the mask is moving to the left, we can't use the selected `targetChar` any longer\n    // if we are not at the end of the string.\n    // In this case, change tracking strategy and track the character to the right of the caret.\n    if (!isAddition && (masklengthChanged || targetIsMaskMovingLeft) && previousLeftMaskChars > 0 && placeholder.indexOf(targetChar) > -1 && rawValue[currentCaretPosition] !== undefined) {\n      trackRightCharacter = true;\n      targetChar = rawValue[currentCaretPosition];\n    }\n\n    // It is possible that `targetChar` will appear multiple times in the conformed value.\n    // We need to know not to select a character that looks like our target character from the placeholder or\n    // the piped characters, so we inspect the piped characters and the placeholder to see if they contain\n    // characters that match our target character.\n\n    // If the `conformedValue` got piped, we need to know which characters were piped in so that when we look for\n    // our `targetChar`, we don't select a piped char by mistake\n    var pipedChars = indexesOfPipedChars.map(function (index) {\n      return normalizedConformedValue[index];\n    });\n\n    // We need to know how many times the `targetChar` occurs in the piped characters.\n    var countTargetCharInPipedChars = pipedChars.filter(function (char) {\n      return char === targetChar;\n    }).length;\n\n    // We need to know how many times it occurs in the intersection\n    var countTargetCharInIntersection = intersection.filter(function (char) {\n      return char === targetChar;\n    }).length;\n\n    // We need to know if the placeholder contains characters that look like\n    // our `targetChar`, so we don't select one of those by mistake.\n    var countTargetCharInPlaceholder = placeholder.substr(0, placeholder.indexOf(placeholderChar)).split(emptyString).filter(function (char, index) {\n      return (\n        // Check if `char` is the same as our `targetChar`, so we account for it\n        char === targetChar &&\n\n        // but also make sure that both the `rawValue` and placeholder don't have the same character at the same\n        // index because if they are equal, that means we are already counting those characters in\n        // `countTargetCharInIntersection`\n        rawValue[index] !== char\n      );\n    }).length;\n\n    // The number of times we need to see occurrences of the `targetChar` before we know it is the one we're looking\n    // for is:\n    var requiredNumberOfMatches = countTargetCharInPlaceholder + countTargetCharInIntersection + countTargetCharInPipedChars + (\n    // The character to the right of the caret isn't included in `intersection`\n    // so add one if we are tracking the character to the right\n    trackRightCharacter ? 1 : 0);\n\n    // Now we start looking for the location of the `targetChar`.\n    // We keep looping forward and store the index in every iteration. Once we have encountered\n    // enough occurrences of the target character, we break out of the loop\n    // If are searching for the second `1` in `1214`, `startingSearchIndex` will point at `4`.\n    var numberOfEncounteredMatches = 0;\n    for (var i = 0; i < conformedValueLength; i++) {\n      var conformedValueChar = normalizedConformedValue[i];\n\n      startingSearchIndex = i + 1;\n\n      if (conformedValueChar === targetChar) {\n        numberOfEncounteredMatches++;\n      }\n\n      if (numberOfEncounteredMatches >= requiredNumberOfMatches) {\n        break;\n      }\n    }\n  }\n\n  // At this point, if we simply return `startingSearchIndex` as the adjusted caret position,\n  // most cases would be handled. However, we want to fast forward or rewind the caret to the\n  // closest placeholder character if it happens to be in a non-editable spot. That's what the next\n  // logic is for.\n\n  // In case of addition, we fast forward.\n  if (isAddition) {\n    // We want to remember the last placeholder character encountered so that if the mask\n    // contains more characters after the last placeholder character, we don't forward the caret\n    // that far to the right. Instead, we stop it at the last encountered placeholder character.\n    var lastPlaceholderChar = startingSearchIndex;\n\n    for (var _i = startingSearchIndex; _i <= placeholderLength; _i++) {\n      if (placeholder[_i] === placeholderChar) {\n        lastPlaceholderChar = _i;\n      }\n\n      if (\n      // If we're adding, we can position the caret at the next placeholder character.\n      placeholder[_i] === placeholderChar ||\n\n      // If a caret trap was set by a mask function, we need to stop at the trap.\n      caretTrapIndexes.indexOf(_i) !== -1 ||\n\n      // This is the end of the placeholder. We cannot move any further. Let's put the caret there.\n      _i === placeholderLength) {\n        return lastPlaceholderChar;\n      }\n    }\n  } else {\n    // In case of deletion, we rewind.\n    if (trackRightCharacter) {\n      // Searching for the character that was to the right of the caret\n      // We start at `startingSearchIndex` - 1 because it includes one character extra to the right\n      for (var _i2 = startingSearchIndex - 1; _i2 >= 0; _i2--) {\n        // If tracking the character to the right of the cursor, we move to the left until\n        // we found the character and then place the caret right before it\n\n        if (\n        // `targetChar` should be in `conformedValue`, since it was in `rawValue`, just\n        // to the right of the caret\n        conformedValue[_i2] === targetChar ||\n\n        // If a caret trap was set by a mask function, we need to stop at the trap.\n        caretTrapIndexes.indexOf(_i2) !== -1 ||\n\n        // This is the beginning of the placeholder. We cannot move any further.\n        // Let's put the caret there.\n        _i2 === 0) {\n          return _i2;\n        }\n      }\n    } else {\n      // Searching for the first placeholder or caret trap to the left\n\n      for (var _i3 = startingSearchIndex; _i3 >= 0; _i3--) {\n        // If we're deleting, we stop the caret right before the placeholder character.\n        // For example, for mask `(111) 11`, current conformed input `(456) 86`. If user\n        // modifies input to `(456 86`. That is, they deleted the `)`, we place the caret\n        // right after the first `6`\n\n        if (\n        // If we're deleting, we can position the caret right before the placeholder character\n        placeholder[_i3 - 1] === placeholderChar ||\n\n        // If a caret trap was set by a mask function, we need to stop at the trap.\n        caretTrapIndexes.indexOf(_i3) !== -1 ||\n\n        // This is the beginning of the placeholder. We cannot move any further.\n        // Let's put the caret there.\n        _i3 === 0) {\n          return _i3;\n        }\n      }\n    }\n  }\n}"}, {"id": 5, "identifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/core/src/createTextMaskInputElement.js", "name": "./core/src/createTextMaskInputElement.js", "index": 4, "index2": 4, "size": 11326, "cacheable": true, "built": true, "optional": false, "prefetched": false, "chunks": [0], "assets": [], "issuer": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/vanilla/src/vanillaTextMask.js", "failed": false, "errors": 0, "warnings": 0, "reasons": [{"moduleId": 0, "moduleIdentifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/vanilla/src/vanillaTextMask.js", "module": "./vanilla/src/vanillaTextMask.js", "moduleName": "./vanilla/src/vanillaTextMask.js", "type": "cjs require", "userRequest": "../../core/src/createTextMaskInputElement", "loc": "18:34-86"}], "source": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; };\n\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; };\n\nexports.default = createTextMaskInputElement;\n\nvar _adjustCaretPosition = require('./adjustCaretPosition');\n\nvar _adjustCaretPosition2 = _interopRequireDefault(_adjustCaretPosition);\n\nvar _conformToMask2 = require('./conformToMask');\n\nvar _conformToMask3 = _interopRequireDefault(_conformToMask2);\n\nvar _utilities = require('./utilities');\n\nvar _constants = require('./constants');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar emptyString = '';\nvar strNone = 'none';\nvar strObject = 'object';\nvar isAndroid = typeof navigator !== 'undefined' && /Android/i.test(navigator.userAgent);\nvar defer = typeof requestAnimationFrame !== 'undefined' ? requestAnimationFrame : setTimeout;\n\nfunction createTextMaskInputElement(config) {\n  // Anything that we will need to keep between `update` calls, we will store in this `state` object.\n  var state = { previousConformedValue: undefined, previousPlaceholder: undefined };\n\n  return {\n    state: state,\n\n    // `update` is called by framework components whenever they want to update the `value` of the input element.\n    // The caller can send a `rawValue` to be conformed and set on the input element. However, the default use-case\n    // is for this to be read from the `inputElement` directly.\n    update: function update(rawValue) {\n      var _ref = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : config,\n          inputElement = _ref.inputElement,\n          providedMask = _ref.mask,\n          guide = _ref.guide,\n          pipe = _ref.pipe,\n          _ref$placeholderChar = _ref.placeholderChar,\n          placeholderChar = _ref$placeholderChar === undefined ? _constants.placeholderChar : _ref$placeholderChar,\n          _ref$keepCharPosition = _ref.keepCharPositions,\n          keepCharPositions = _ref$keepCharPosition === undefined ? false : _ref$keepCharPosition,\n          _ref$showMask = _ref.showMask,\n          showMask = _ref$showMask === undefined ? false : _ref$showMask;\n\n      // if `rawValue` is `undefined`, read from the `inputElement`\n      if (typeof rawValue === 'undefined') {\n        rawValue = inputElement.value;\n      }\n\n      // If `rawValue` equals `state.previousConformedValue`, we don't need to change anything. So, we return.\n      // This check is here to handle controlled framework components that repeat the `update` call on every render.\n      if (rawValue === state.previousConformedValue) {\n        return;\n      }\n\n      // Text Mask accepts masks that are a combination of a `mask` and a `pipe` that work together. If such a `mask` is\n      // passed, we destructure it below, so the rest of the code can work normally as if a separate `mask` and a `pipe`\n      // were passed.\n      if ((typeof providedMask === 'undefined' ? 'undefined' : _typeof(providedMask)) === strObject && providedMask.pipe !== undefined && providedMask.mask !== undefined) {\n        pipe = providedMask.pipe;\n        providedMask = providedMask.mask;\n      }\n\n      // The `placeholder` is an essential piece of how Text Mask works. For a mask like `(111)`, the placeholder would\n      // be `(___)` if the `placeholderChar` is set to `_`.\n      var placeholder = void 0;\n\n      // We don't know what the mask would be yet. If it is an array, we take it as is, but if it's a function, we will\n      // have to call that function to get the mask array.\n      var mask = void 0;\n\n      // If the provided mask is an array, we can call `convertMaskToPlaceholder` here once and we'll always have the\n      // correct `placeholder`.\n      if (providedMask instanceof Array) {\n        placeholder = (0, _utilities.convertMaskToPlaceholder)(providedMask, placeholderChar);\n      }\n\n      // In framework components that support reactivity, it's possible to turn off masking by passing\n      // `false` for `mask` after initialization. See https://github.com/text-mask/text-mask/pull/359\n      if (providedMask === false) {\n        return;\n      }\n\n      // We check the provided `rawValue` before moving further.\n      // If it's something we can't work with `getSafeRawValue` will throw.\n      var safeRawValue = getSafeRawValue(rawValue);\n\n      // `selectionEnd` indicates to us where the caret position is after the user has typed into the input\n      var currentCaretPosition = inputElement.selectionEnd;\n\n      // We need to know what the `previousConformedValue` and `previousPlaceholder` is from the previous `update` call\n\n      var previousConformedValue = state.previousConformedValue,\n          previousPlaceholder = state.previousPlaceholder;\n\n\n      var caretTrapIndexes = void 0;\n\n      // If the `providedMask` is a function. We need to call it at every `update` to get the `mask` array.\n      // Then we also need to get the `placeholder`\n      if ((typeof providedMask === 'undefined' ? 'undefined' : _typeof(providedMask)) === _constants.strFunction) {\n        mask = providedMask(safeRawValue, { currentCaretPosition: currentCaretPosition, previousConformedValue: previousConformedValue, placeholderChar: placeholderChar });\n\n        // disable masking if `mask` is `false`\n        if (mask === false) {\n          return;\n        }\n\n        // mask functions can setup caret traps to have some control over how the caret moves. We need to process\n        // the mask for any caret traps. `processCaretTraps` will remove the caret traps from the mask and return\n        // the indexes of the caret traps.\n\n        var _processCaretTraps = (0, _utilities.processCaretTraps)(mask),\n            maskWithoutCaretTraps = _processCaretTraps.maskWithoutCaretTraps,\n            indexes = _processCaretTraps.indexes;\n\n        mask = maskWithoutCaretTraps; // The processed mask is what we're interested in\n        caretTrapIndexes = indexes; // And we need to store these indexes because they're needed by `adjustCaretPosition`\n\n        placeholder = (0, _utilities.convertMaskToPlaceholder)(mask, placeholderChar);\n\n        // If the `providedMask` is not a function, we just use it as-is.\n      } else {\n        mask = providedMask;\n      }\n\n      // The following object will be passed to `conformToMask` to determine how the `rawValue` will be conformed\n      var conformToMaskConfig = {\n        previousConformedValue: previousConformedValue,\n        guide: guide,\n        placeholderChar: placeholderChar,\n        pipe: pipe,\n        placeholder: placeholder,\n        currentCaretPosition: currentCaretPosition,\n        keepCharPositions: keepCharPositions\n\n        // `conformToMask` returns `conformedValue` as part of an object for future API flexibility\n      };\n      var _conformToMask = (0, _conformToMask3.default)(safeRawValue, mask, conformToMaskConfig),\n          conformedValue = _conformToMask.conformedValue;\n\n      // The following few lines are to support the `pipe` feature.\n\n\n      var piped = (typeof pipe === 'undefined' ? 'undefined' : _typeof(pipe)) === _constants.strFunction;\n\n      var pipeResults = {};\n\n      // If `pipe` is a function, we call it.\n      if (piped) {\n        // `pipe` receives the `conformedValue` and the configurations with which `conformToMask` was called.\n        pipeResults = pipe(conformedValue, _extends({ rawValue: safeRawValue }, conformToMaskConfig));\n\n        // `pipeResults` should be an object. But as a convenience, we allow the pipe author to just return `false` to\n        // indicate rejection. Or return just a string when there are no piped characters.\n        // If the `pipe` returns `false` or a string, the block below turns it into an object that the rest\n        // of the code can work with.\n        if (pipeResults === false) {\n          // If the `pipe` rejects `conformedValue`, we use the `previousConformedValue`, and set `rejected` to `true`.\n          pipeResults = { value: previousConformedValue, rejected: true };\n        } else if ((0, _utilities.isString)(pipeResults)) {\n          pipeResults = { value: pipeResults };\n        }\n      }\n\n      // Before we proceed, we need to know which conformed value to use, the one returned by the pipe or the one\n      // returned by `conformToMask`.\n      var finalConformedValue = piped ? pipeResults.value : conformedValue;\n\n      // After determining the conformed value, we will need to know where to set\n      // the caret position. `adjustCaretPosition` will tell us.\n      var adjustedCaretPosition = (0, _adjustCaretPosition2.default)({\n        previousConformedValue: previousConformedValue,\n        previousPlaceholder: previousPlaceholder,\n        conformedValue: finalConformedValue,\n        placeholder: placeholder,\n        rawValue: safeRawValue,\n        currentCaretPosition: currentCaretPosition,\n        placeholderChar: placeholderChar,\n        indexesOfPipedChars: pipeResults.indexesOfPipedChars,\n        caretTrapIndexes: caretTrapIndexes\n      });\n\n      // Text Mask sets the input value to an empty string when the condition below is set. It provides a better UX.\n      var inputValueShouldBeEmpty = finalConformedValue === placeholder && adjustedCaretPosition === 0;\n      var emptyValue = showMask ? placeholder : emptyString;\n      var inputElementValue = inputValueShouldBeEmpty ? emptyValue : finalConformedValue;\n\n      state.previousConformedValue = inputElementValue; // store value for access for next time\n      state.previousPlaceholder = placeholder;\n\n      // In some cases, this `update` method will be repeatedly called with a raw value that has already been conformed\n      // and set to `inputElement.value`. The below check guards against needlessly readjusting the input state.\n      // See https://github.com/text-mask/text-mask/issues/231\n      if (inputElement.value === inputElementValue) {\n        return;\n      }\n\n      inputElement.value = inputElementValue; // set the input value\n      safeSetSelection(inputElement, adjustedCaretPosition); // adjust caret position\n    }\n  };\n}\n\nfunction safeSetSelection(element, selectionPosition) {\n  if (document.activeElement === element) {\n    if (isAndroid) {\n      defer(function () {\n        return element.setSelectionRange(selectionPosition, selectionPosition, strNone);\n      }, 0);\n    } else {\n      element.setSelectionRange(selectionPosition, selectionPosition, strNone);\n    }\n  }\n}\n\nfunction getSafeRawValue(inputValue) {\n  if ((0, _utilities.isString)(inputValue)) {\n    return inputValue;\n  } else if ((0, _utilities.isNumber)(inputValue)) {\n    return String(inputValue);\n  } else if (inputValue === undefined || inputValue === null) {\n    return emptyString;\n  } else {\n    throw new Error(\"The 'value' provided to Text Mask needs to be a string or a number. The value \" + ('received was:\\n\\n ' + JSON.stringify(inputValue)));\n  }\n}"}], "filteredModules": 0, "origins": [{"moduleId": 0, "module": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/vanilla/src/vanillaTextMask.js", "moduleIdentifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/vanilla/src/vanillaTextMask.js", "moduleName": "./vanilla/src/vanillaTextMask.js", "loc": "", "name": "main", "reasons": []}]}], "modules": [{"id": 0, "identifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/vanilla/src/vanillaTextMask.js", "name": "./vanilla/src/vanillaTextMask.js", "index": 0, "index2": 5, "size": 1273, "cacheable": true, "built": true, "optional": false, "prefetched": false, "chunks": [0], "assets": [], "issuer": null, "failed": false, "errors": 0, "warnings": 0, "reasons": [], "source": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.conformToMask = undefined;\nexports.maskInput = maskInput;\n\nvar _conformToMask = require('../../core/src/conformToMask.js');\n\nObject.defineProperty(exports, 'conformToMask', {\n  enumerable: true,\n  get: function get() {\n    return _interopRequireDefault(_conformToMask).default;\n  }\n});\n\nvar _createTextMaskInputElement = require('../../core/src/createTextMaskInputElement');\n\nvar _createTextMaskInputElement2 = _interopRequireDefault(_createTextMaskInputElement);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction maskInput(textMaskConfig) {\n  var inputElement = textMaskConfig.inputElement;\n\n  var textMaskInputElement = (0, _createTextMaskInputElement2.default)(textMaskConfig);\n  var inputHandler = function inputHandler(_ref) {\n    var value = _ref.target.value;\n    return textMaskInputElement.update(value);\n  };\n\n  inputElement.addEventListener('input', inputHandler);\n\n  textMaskInputElement.update(inputElement.value);\n\n  return {\n    textMaskInputElement: textMaskInputElement,\n\n    destroy: function destroy() {\n      inputElement.removeEventListener('input', inputHandler);\n    }\n  };\n}\n\nexports.default = maskInput;"}, {"id": 1, "identifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/core/src/constants.js", "name": "./core/src/constants.js", "index": 3, "index2": 0, "size": 184, "cacheable": true, "built": true, "optional": false, "prefetched": false, "chunks": [0], "assets": [], "issuer": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/core/src/conformToMask.js", "failed": false, "errors": 0, "warnings": 0, "reasons": [{"moduleId": 2, "moduleIdentifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/core/src/conformToMask.js", "module": "./core/src/conformToMask.js", "moduleName": "./core/src/conformToMask.js", "type": "cjs require", "userRequest": "./constants", "loc": "13:17-39"}, {"moduleId": 3, "moduleIdentifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/core/src/utilities.js", "module": "./core/src/utilities.js", "moduleName": "./core/src/utilities.js", "type": "cjs require", "userRequest": "./constants", "loc": "12:17-39"}, {"moduleId": 5, "moduleIdentifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/core/src/createTextMaskInputElement.js", "module": "./core/src/createTextMaskInputElement.js", "moduleName": "./core/src/createTextMaskInputElement.js", "type": "cjs require", "userRequest": "./constants", "loc": "23:17-39"}], "source": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar placeholderChar = exports.placeholderChar = '_';\nvar strFunction = exports.strFunction = 'function';"}, {"id": 2, "identifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/core/src/conformToMask.js", "name": "./core/src/conformToMask.js", "index": 1, "index2": 2, "size": 13267, "cacheable": true, "built": true, "optional": false, "prefetched": false, "chunks": [0], "assets": [], "issuer": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/vanilla/src/vanillaTextMask.js", "failed": false, "errors": 0, "warnings": 0, "reasons": [{"moduleId": 0, "moduleIdentifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/vanilla/src/vanillaTextMask.js", "module": "./vanilla/src/vanillaTextMask.js", "moduleName": "./vanilla/src/vanillaTextMask.js", "type": "cjs require", "userRequest": "../../core/src/conformToMask.js", "loc": "9:21-63"}, {"moduleId": 5, "moduleIdentifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/core/src/createTextMaskInputElement.js", "module": "./core/src/createTextMaskInputElement.js", "moduleName": "./core/src/createTextMaskInputElement.js", "type": "cjs require", "userRequest": "./conformToMask", "loc": "17:22-48"}], "source": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; };\n\nexports.default = conformToMask;\n\nvar _utilities = require('./utilities');\n\nvar _constants = require('./constants');\n\nvar emptyArray = [];\nvar emptyString = '';\n\nfunction conformToMask() {\n  var rawValue = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : emptyString;\n  var mask = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : emptyArray;\n  var config = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n\n  if (!(0, _utilities.isArray)(mask)) {\n    // If someone passes a function as the mask property, we should call the\n    // function to get the mask array - Normally this is handled by the\n    // `createTextMaskInputElement:update` function - this allows mask functions\n    // to be used directly with `conformToMask`\n    if ((typeof mask === 'undefined' ? 'undefined' : _typeof(mask)) === _constants.strFunction) {\n      // call the mask function to get the mask array\n      mask = mask(rawValue, config);\n\n      // mask functions can setup caret traps to have some control over how the caret moves. We need to process\n      // the mask for any caret traps. `processCaretTraps` will remove the caret traps from the mask\n      mask = (0, _utilities.processCaretTraps)(mask).maskWithoutCaretTraps;\n    } else {\n      throw new Error('Text-mask:conformToMask; The mask property must be an array.');\n    }\n  }\n\n  // These configurations tell us how to conform the mask\n  var _config$guide = config.guide,\n      guide = _config$guide === undefined ? true : _config$guide,\n      _config$previousConfo = config.previousConformedValue,\n      previousConformedValue = _config$previousConfo === undefined ? emptyString : _config$previousConfo,\n      _config$placeholderCh = config.placeholderChar,\n      placeholderChar = _config$placeholderCh === undefined ? _constants.placeholderChar : _config$placeholderCh,\n      _config$placeholder = config.placeholder,\n      placeholder = _config$placeholder === undefined ? (0, _utilities.convertMaskToPlaceholder)(mask, placeholderChar) : _config$placeholder,\n      currentCaretPosition = config.currentCaretPosition,\n      keepCharPositions = config.keepCharPositions;\n\n  // The configs below indicate that the user wants the algorithm to work in *no guide* mode\n\n  var suppressGuide = guide === false && previousConformedValue !== undefined;\n\n  // Calculate lengths once for performance\n  var rawValueLength = rawValue.length;\n  var previousConformedValueLength = previousConformedValue.length;\n  var placeholderLength = placeholder.length;\n  var maskLength = mask.length;\n\n  // This tells us the number of edited characters and the direction in which they were edited (+/-)\n  var editDistance = rawValueLength - previousConformedValueLength;\n\n  // In *no guide* mode, we need to know if the user is trying to add a character or not\n  var isAddition = editDistance > 0;\n\n  // Tells us the index of the first change. For (************* to (38) 394-4938, that would be 1\n  var indexOfFirstChange = currentCaretPosition + (isAddition ? -editDistance : 0);\n\n  // We're also gonna need the index of last change, which we can derive as follows...\n  var indexOfLastChange = indexOfFirstChange + Math.abs(editDistance);\n\n  // If `conformToMask` is configured to keep character positions, that is, for mask 111, previous value\n  // _2_ and raw value 3_2_, the new conformed value should be 32_, not 3_2 (default behavior). That's in the case of\n  // addition. And in the case of deletion, previous value _23, raw value _3, the new conformed string should be\n  // __3, not _3_ (default behavior)\n  //\n  // The next block of logic handles keeping character positions for the case of deletion. (Keeping\n  // character positions for the case of addition is further down since it is handled differently.)\n  // To do this, we want to compensate for all characters that were deleted\n  if (keepCharPositions === true && !isAddition) {\n    // We will be storing the new placeholder characters in this variable.\n    var compensatingPlaceholderChars = emptyString;\n\n    // For every character that was deleted from a placeholder position, we add a placeholder char\n    for (var i = indexOfFirstChange; i < indexOfLastChange; i++) {\n      if (placeholder[i] === placeholderChar) {\n        compensatingPlaceholderChars += placeholderChar;\n      }\n    }\n\n    // Now we trick our algorithm by modifying the raw value to make it contain additional placeholder characters\n    // That way when the we start laying the characters again on the mask, it will keep the non-deleted characters\n    // in their positions.\n    rawValue = rawValue.slice(0, indexOfFirstChange) + compensatingPlaceholderChars + rawValue.slice(indexOfFirstChange, rawValueLength);\n  }\n\n  // Convert `rawValue` string to an array, and mark characters based on whether they are newly added or have\n  // existed in the previous conformed value. Identifying new and old characters is needed for `conformToMask`\n  // to work if it is configured to keep character positions.\n  var rawValueArr = rawValue.split(emptyString).map(function (char, i) {\n    return { char: char, isNew: i >= indexOfFirstChange && i < indexOfLastChange };\n  });\n\n  // The loop below removes masking characters from user input. For example, for mask\n  // `00 (111)`, the placeholder would be `00 (___)`. If user input is `00 (234)`, the loop below\n  // would remove all characters but `234` from the `rawValueArr`. The rest of the algorithm\n  // then would lay `234` on top of the available placeholder positions in the mask.\n  for (var _i = rawValueLength - 1; _i >= 0; _i--) {\n    var char = rawValueArr[_i].char;\n\n\n    if (char !== placeholderChar) {\n      var shouldOffset = _i >= indexOfFirstChange && previousConformedValueLength === maskLength;\n\n      if (char === placeholder[shouldOffset ? _i - editDistance : _i]) {\n        rawValueArr.splice(_i, 1);\n      }\n    }\n  }\n\n  // This is the variable that we will be filling with characters as we figure them out\n  // in the algorithm below\n  var conformedValue = emptyString;\n  var someCharsRejected = false;\n\n  // Ok, so first we loop through the placeholder looking for placeholder characters to fill up.\n  placeholderLoop: for (var _i2 = 0; _i2 < placeholderLength; _i2++) {\n    var charInPlaceholder = placeholder[_i2];\n\n    // We see one. Let's find out what we can put in it.\n    if (charInPlaceholder === placeholderChar) {\n      // But before that, do we actually have any user characters that need a place?\n      if (rawValueArr.length > 0) {\n        // We will keep chipping away at user input until either we run out of characters\n        // or we find at least one character that we can map.\n        while (rawValueArr.length > 0) {\n          // Let's retrieve the first user character in the queue of characters we have left\n          var _rawValueArr$shift = rawValueArr.shift(),\n              rawValueChar = _rawValueArr$shift.char,\n              isNew = _rawValueArr$shift.isNew;\n\n          // If the character we got from the user input is a placeholder character (which happens\n          // regularly because user input could be something like (540) 90_-____, which includes\n          // a bunch of `_` which are placeholder characters) and we are not in *no guide* mode,\n          // then we map this placeholder character to the current spot in the placeholder\n\n\n          if (rawValueChar === placeholderChar && suppressGuide !== true) {\n            conformedValue += placeholderChar;\n\n            // And we go to find the next placeholder character that needs filling\n            continue placeholderLoop;\n\n            // Else if, the character we got from the user input is not a placeholder, let's see\n            // if the current position in the mask can accept it.\n          } else if (mask[_i2].test(rawValueChar)) {\n            // we map the character differently based on whether we are keeping character positions or not.\n            // If any of the conditions below are met, we simply map the raw value character to the\n            // placeholder position.\n            if (keepCharPositions !== true || isNew === false || previousConformedValue === emptyString || guide === false || !isAddition) {\n              conformedValue += rawValueChar;\n            } else {\n              // We enter this block of code if we are trying to keep character positions and none of the conditions\n              // above is met. In this case, we need to see if there's an available spot for the raw value character\n              // to be mapped to. If we couldn't find a spot, we will discard the character.\n              //\n              // For example, for mask `1111`, previous conformed value `_2__`, raw value `942_2__`. We can map the\n              // `9`, to the first available placeholder position, but then, there are no more spots available for the\n              // `4` and `2`. So, we discard them and end up with a conformed value of `92__`.\n              var rawValueArrLength = rawValueArr.length;\n              var indexOfNextAvailablePlaceholderChar = null;\n\n              // Let's loop through the remaining raw value characters. We are looking for either a suitable spot, ie,\n              // a placeholder character or a non-suitable spot, ie, a non-placeholder character that is not new.\n              // If we see a suitable spot first, we store its position and exit the loop. If we see a non-suitable\n              // spot first, we exit the loop and our `indexOfNextAvailablePlaceholderChar` will stay as `null`.\n              for (var _i3 = 0; _i3 < rawValueArrLength; _i3++) {\n                var charData = rawValueArr[_i3];\n\n                if (charData.char !== placeholderChar && charData.isNew === false) {\n                  break;\n                }\n\n                if (charData.char === placeholderChar) {\n                  indexOfNextAvailablePlaceholderChar = _i3;\n                  break;\n                }\n              }\n\n              // If `indexOfNextAvailablePlaceholderChar` is not `null`, that means the character is not blocked.\n              // We can map it. And to keep the character positions, we remove the placeholder character\n              // from the remaining characters\n              if (indexOfNextAvailablePlaceholderChar !== null) {\n                conformedValue += rawValueChar;\n                rawValueArr.splice(indexOfNextAvailablePlaceholderChar, 1);\n\n                // If `indexOfNextAvailablePlaceholderChar` is `null`, that means the character is blocked. We have to\n                // discard it.\n              } else {\n                _i2--;\n              }\n            }\n\n            // Since we've mapped this placeholder position. We move on to the next one.\n            continue placeholderLoop;\n          } else {\n            someCharsRejected = true;\n          }\n        }\n      }\n\n      // We reach this point when we've mapped all the user input characters to placeholder\n      // positions in the mask. In *guide* mode, we append the left over characters in the\n      // placeholder to the `conformedString`, but in *no guide* mode, we don't wanna do that.\n      //\n      // That is, for mask `(111)` and user input `2`, we want to return `(2`, not `(2__)`.\n      if (suppressGuide === false) {\n        conformedValue += placeholder.substr(_i2, placeholderLength);\n      }\n\n      // And we break\n      break;\n\n      // Else, the charInPlaceholder is not a placeholderChar. That is, we cannot fill it\n      // with user input. So we just map it to the final output\n    } else {\n      conformedValue += charInPlaceholder;\n    }\n  }\n\n  // The following logic is needed to deal with the case of deletion in *no guide* mode.\n  //\n  // Consider the silly mask `(111) /// 1`. What if user tries to delete the last placeholder\n  // position? Something like `(589) /// `. We want to conform that to `(589`. Not `(589) /// `.\n  // That's why the logic below finds the last filled placeholder character, and removes everything\n  // from that point on.\n  if (suppressGuide && isAddition === false) {\n    var indexOfLastFilledPlaceholderChar = null;\n\n    // Find the last filled placeholder position and substring from there\n    for (var _i4 = 0; _i4 < conformedValue.length; _i4++) {\n      if (placeholder[_i4] === placeholderChar) {\n        indexOfLastFilledPlaceholderChar = _i4;\n      }\n    }\n\n    if (indexOfLastFilledPlaceholderChar !== null) {\n      // We substring from the beginning until the position after the last filled placeholder char.\n      conformedValue = conformedValue.substr(0, indexOfLastFilledPlaceholderChar + 1);\n    } else {\n      // If we couldn't find `indexOfLastFilledPlaceholderChar` that means the user deleted\n      // the first character in the mask. So we return an empty string.\n      conformedValue = emptyString;\n    }\n  }\n\n  return { conformedValue: conformedValue, meta: { someCharsRejected: someCharsRejected } };\n}"}, {"id": 3, "identifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/core/src/utilities.js", "name": "./core/src/utilities.js", "index": 2, "index2": 1, "size": 1897, "cacheable": true, "built": true, "optional": false, "prefetched": false, "chunks": [0], "assets": [], "issuer": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/core/src/conformToMask.js", "failed": false, "errors": 0, "warnings": 0, "reasons": [{"moduleId": 2, "moduleIdentifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/core/src/conformToMask.js", "module": "./core/src/conformToMask.js", "moduleName": "./core/src/conformToMask.js", "type": "cjs require", "userRequest": "./utilities", "loc": "11:17-39"}, {"moduleId": 5, "moduleIdentifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/core/src/createTextMaskInputElement.js", "module": "./core/src/createTextMaskInputElement.js", "moduleName": "./core/src/createTextMaskInputElement.js", "type": "cjs require", "userRequest": "./utilities", "loc": "21:17-39"}], "source": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.convertMaskToPlaceholder = convertMaskToPlaceholder;\nexports.isArray = isArray;\nexports.isString = isString;\nexports.isNumber = isNumber;\nexports.processCaretTraps = processCaretTraps;\n\nvar _constants = require('./constants');\n\nvar emptyArray = [];\n\nfunction convertMaskToPlaceholder() {\n  var mask = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : emptyArray;\n  var placeholderChar = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : _constants.placeholderChar;\n\n  if (!isArray(mask)) {\n    throw new Error('Text-mask:convertMaskToPlaceholder; The mask property must be an array.');\n  }\n\n  if (mask.indexOf(placeholderChar) !== -1) {\n    throw new Error('Placeholder character must not be used as part of the mask. Please specify a character ' + 'that is not present in your mask as your placeholder character.\\n\\n' + ('The placeholder character that was received is: ' + JSON.stringify(placeholderChar) + '\\n\\n') + ('The mask that was received is: ' + JSON.stringify(mask)));\n  }\n\n  return mask.map(function (char) {\n    return char instanceof RegExp ? placeholderChar : char;\n  }).join('');\n}\n\nfunction isArray(value) {\n  return Array.isArray && Array.isArray(value) || value instanceof Array;\n}\n\nfunction isString(value) {\n  return typeof value === 'string' || value instanceof String;\n}\n\nfunction isNumber(value) {\n  return typeof value === 'number' && value.length === undefined && !isNaN(value);\n}\n\nvar strCaretTrap = '[]';\nfunction processCaretTraps(mask) {\n  var indexes = [];\n\n  var indexOfCaretTrap = void 0;\n  while (indexOfCaretTrap = mask.indexOf(strCaretTrap), indexOfCaretTrap !== -1) {\n    // eslint-disable-line\n    indexes.push(indexOfCaretTrap);\n\n    mask.splice(indexOfCaretTrap, 1);\n  }\n\n  return { maskWithoutCaretTraps: mask, indexes: indexes };\n}"}, {"id": 4, "identifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/core/src/adjustCaretPosition.js", "name": "./core/src/adjustCaretPosition.js", "index": 5, "index2": 3, "size": 12699, "cacheable": true, "built": true, "optional": false, "prefetched": false, "chunks": [0], "assets": [], "issuer": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/core/src/createTextMaskInputElement.js", "failed": false, "errors": 0, "warnings": 0, "reasons": [{"moduleId": 5, "moduleIdentifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/core/src/createTextMaskInputElement.js", "module": "./core/src/createTextMaskInputElement.js", "moduleName": "./core/src/createTextMaskInputElement.js", "type": "cjs require", "userRequest": "./adjustCaretPosition", "loc": "13:27-59"}], "source": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = adjustCaretPosition;\nvar defaultArray = [];\nvar emptyString = '';\n\nfunction adjustCaretPosition(_ref) {\n  var _ref$previousConforme = _ref.previousConformedValue,\n      previousConformedValue = _ref$previousConforme === undefined ? emptyString : _ref$previousConforme,\n      _ref$previousPlacehol = _ref.previousPlaceholder,\n      previousPlaceholder = _ref$previousPlacehol === undefined ? emptyString : _ref$previousPlacehol,\n      _ref$currentCaretPosi = _ref.currentCaretPosition,\n      currentCaretPosition = _ref$currentCaretPosi === undefined ? 0 : _ref$currentCaretPosi,\n      conformedValue = _ref.conformedValue,\n      rawValue = _ref.rawValue,\n      placeholderChar = _ref.placeholderChar,\n      placeholder = _ref.placeholder,\n      _ref$indexesOfPipedCh = _ref.indexesOfPipedChars,\n      indexesOfPipedChars = _ref$indexesOfPipedCh === undefined ? defaultArray : _ref$indexesOfPipedCh,\n      _ref$caretTrapIndexes = _ref.caretTrapIndexes,\n      caretTrapIndexes = _ref$caretTrapIndexes === undefined ? defaultArray : _ref$caretTrapIndexes;\n\n  if (currentCaretPosition === 0 || !rawValue.length) {\n    return 0;\n  }\n\n  // Store lengths for faster performance?\n  var rawValueLength = rawValue.length;\n  var previousConformedValueLength = previousConformedValue.length;\n  var placeholderLength = placeholder.length;\n  var conformedValueLength = conformedValue.length;\n\n  // This tells us how long the edit is. If user modified input from `(2__)` to `(243__)`,\n  // we know the user in this instance pasted two characters\n  var editLength = rawValueLength - previousConformedValueLength;\n\n  // If the edit length is positive, that means the user is adding characters, not deleting.\n  var isAddition = editLength > 0;\n\n  // This is the first raw value the user entered that needs to be conformed to mask\n  var isFirstRawValue = previousConformedValueLength === 0;\n\n  // A partial multi-character edit happens when the user makes a partial selection in their\n  // input and edits that selection. That is going from `(*************` to `() 432-4348` by\n  // selecting the first 3 digits and pressing backspace.\n  //\n  // Such cases can also happen when the user presses the backspace while holding down the ALT\n  // key.\n  var isPartialMultiCharEdit = editLength > 1 && !isAddition && !isFirstRawValue;\n\n  // This algorithm doesn't support all cases of multi-character edits, so we just return\n  // the current caret position.\n  //\n  // This works fine for most cases.\n  if (isPartialMultiCharEdit) {\n    return currentCaretPosition;\n  }\n\n  // For a mask like (111), if the `previousConformedValue` is (1__) and user attempts to enter\n  // `f` so the `rawValue` becomes (1f__), the new `conformedValue` would be (1__), which is the\n  // same as the original `previousConformedValue`. We handle this case differently for caret\n  // positioning.\n  var possiblyHasRejectedChar = isAddition && (previousConformedValue === conformedValue || conformedValue === placeholder);\n\n  var startingSearchIndex = 0;\n  var trackRightCharacter = void 0;\n  var targetChar = void 0;\n\n  if (possiblyHasRejectedChar) {\n    startingSearchIndex = currentCaretPosition - editLength;\n  } else {\n    // At this point in the algorithm, we want to know where the caret is right before the raw input\n    // has been conformed, and then see if we can find that same spot in the conformed input.\n    //\n    // We do that by seeing what character lies immediately before the caret, and then look for that\n    // same character in the conformed input and place the caret there.\n\n    // First, we need to normalize the inputs so that letter capitalization between raw input and\n    // conformed input wouldn't matter.\n    var normalizedConformedValue = conformedValue.toLowerCase();\n    var normalizedRawValue = rawValue.toLowerCase();\n\n    // Then we take all characters that come before where the caret currently is.\n    var leftHalfChars = normalizedRawValue.substr(0, currentCaretPosition).split(emptyString);\n\n    // Now we find all the characters in the left half that exist in the conformed input\n    // This step ensures that we don't look for a character that was filtered out or rejected by `conformToMask`.\n    var intersection = leftHalfChars.filter(function (char) {\n      return normalizedConformedValue.indexOf(char) !== -1;\n    });\n\n    // The last character in the intersection is the character we want to look for in the conformed\n    // value and the one we want to adjust the caret close to\n    targetChar = intersection[intersection.length - 1];\n\n    // Calculate the number of mask characters in the previous placeholder\n    // from the start of the string up to the place where the caret is\n    var previousLeftMaskChars = previousPlaceholder.substr(0, intersection.length).split(emptyString).filter(function (char) {\n      return char !== placeholderChar;\n    }).length;\n\n    // Calculate the number of mask characters in the current placeholder\n    // from the start of the string up to the place where the caret is\n    var leftMaskChars = placeholder.substr(0, intersection.length).split(emptyString).filter(function (char) {\n      return char !== placeholderChar;\n    }).length;\n\n    // Has the number of mask characters up to the caret changed?\n    var masklengthChanged = leftMaskChars !== previousLeftMaskChars;\n\n    // Detect if `targetChar` is a mask character and has moved to the left\n    var targetIsMaskMovingLeft = previousPlaceholder[intersection.length - 1] !== undefined && placeholder[intersection.length - 2] !== undefined && previousPlaceholder[intersection.length - 1] !== placeholderChar && previousPlaceholder[intersection.length - 1] !== placeholder[intersection.length - 1] && previousPlaceholder[intersection.length - 1] === placeholder[intersection.length - 2];\n\n    // If deleting and the `targetChar` `is a mask character and `masklengthChanged` is true\n    // or the mask is moving to the left, we can't use the selected `targetChar` any longer\n    // if we are not at the end of the string.\n    // In this case, change tracking strategy and track the character to the right of the caret.\n    if (!isAddition && (masklengthChanged || targetIsMaskMovingLeft) && previousLeftMaskChars > 0 && placeholder.indexOf(targetChar) > -1 && rawValue[currentCaretPosition] !== undefined) {\n      trackRightCharacter = true;\n      targetChar = rawValue[currentCaretPosition];\n    }\n\n    // It is possible that `targetChar` will appear multiple times in the conformed value.\n    // We need to know not to select a character that looks like our target character from the placeholder or\n    // the piped characters, so we inspect the piped characters and the placeholder to see if they contain\n    // characters that match our target character.\n\n    // If the `conformedValue` got piped, we need to know which characters were piped in so that when we look for\n    // our `targetChar`, we don't select a piped char by mistake\n    var pipedChars = indexesOfPipedChars.map(function (index) {\n      return normalizedConformedValue[index];\n    });\n\n    // We need to know how many times the `targetChar` occurs in the piped characters.\n    var countTargetCharInPipedChars = pipedChars.filter(function (char) {\n      return char === targetChar;\n    }).length;\n\n    // We need to know how many times it occurs in the intersection\n    var countTargetCharInIntersection = intersection.filter(function (char) {\n      return char === targetChar;\n    }).length;\n\n    // We need to know if the placeholder contains characters that look like\n    // our `targetChar`, so we don't select one of those by mistake.\n    var countTargetCharInPlaceholder = placeholder.substr(0, placeholder.indexOf(placeholderChar)).split(emptyString).filter(function (char, index) {\n      return (\n        // Check if `char` is the same as our `targetChar`, so we account for it\n        char === targetChar &&\n\n        // but also make sure that both the `rawValue` and placeholder don't have the same character at the same\n        // index because if they are equal, that means we are already counting those characters in\n        // `countTargetCharInIntersection`\n        rawValue[index] !== char\n      );\n    }).length;\n\n    // The number of times we need to see occurrences of the `targetChar` before we know it is the one we're looking\n    // for is:\n    var requiredNumberOfMatches = countTargetCharInPlaceholder + countTargetCharInIntersection + countTargetCharInPipedChars + (\n    // The character to the right of the caret isn't included in `intersection`\n    // so add one if we are tracking the character to the right\n    trackRightCharacter ? 1 : 0);\n\n    // Now we start looking for the location of the `targetChar`.\n    // We keep looping forward and store the index in every iteration. Once we have encountered\n    // enough occurrences of the target character, we break out of the loop\n    // If are searching for the second `1` in `1214`, `startingSearchIndex` will point at `4`.\n    var numberOfEncounteredMatches = 0;\n    for (var i = 0; i < conformedValueLength; i++) {\n      var conformedValueChar = normalizedConformedValue[i];\n\n      startingSearchIndex = i + 1;\n\n      if (conformedValueChar === targetChar) {\n        numberOfEncounteredMatches++;\n      }\n\n      if (numberOfEncounteredMatches >= requiredNumberOfMatches) {\n        break;\n      }\n    }\n  }\n\n  // At this point, if we simply return `startingSearchIndex` as the adjusted caret position,\n  // most cases would be handled. However, we want to fast forward or rewind the caret to the\n  // closest placeholder character if it happens to be in a non-editable spot. That's what the next\n  // logic is for.\n\n  // In case of addition, we fast forward.\n  if (isAddition) {\n    // We want to remember the last placeholder character encountered so that if the mask\n    // contains more characters after the last placeholder character, we don't forward the caret\n    // that far to the right. Instead, we stop it at the last encountered placeholder character.\n    var lastPlaceholderChar = startingSearchIndex;\n\n    for (var _i = startingSearchIndex; _i <= placeholderLength; _i++) {\n      if (placeholder[_i] === placeholderChar) {\n        lastPlaceholderChar = _i;\n      }\n\n      if (\n      // If we're adding, we can position the caret at the next placeholder character.\n      placeholder[_i] === placeholderChar ||\n\n      // If a caret trap was set by a mask function, we need to stop at the trap.\n      caretTrapIndexes.indexOf(_i) !== -1 ||\n\n      // This is the end of the placeholder. We cannot move any further. Let's put the caret there.\n      _i === placeholderLength) {\n        return lastPlaceholderChar;\n      }\n    }\n  } else {\n    // In case of deletion, we rewind.\n    if (trackRightCharacter) {\n      // Searching for the character that was to the right of the caret\n      // We start at `startingSearchIndex` - 1 because it includes one character extra to the right\n      for (var _i2 = startingSearchIndex - 1; _i2 >= 0; _i2--) {\n        // If tracking the character to the right of the cursor, we move to the left until\n        // we found the character and then place the caret right before it\n\n        if (\n        // `targetChar` should be in `conformedValue`, since it was in `rawValue`, just\n        // to the right of the caret\n        conformedValue[_i2] === targetChar ||\n\n        // If a caret trap was set by a mask function, we need to stop at the trap.\n        caretTrapIndexes.indexOf(_i2) !== -1 ||\n\n        // This is the beginning of the placeholder. We cannot move any further.\n        // Let's put the caret there.\n        _i2 === 0) {\n          return _i2;\n        }\n      }\n    } else {\n      // Searching for the first placeholder or caret trap to the left\n\n      for (var _i3 = startingSearchIndex; _i3 >= 0; _i3--) {\n        // If we're deleting, we stop the caret right before the placeholder character.\n        // For example, for mask `(111) 11`, current conformed input `(456) 86`. If user\n        // modifies input to `(456 86`. That is, they deleted the `)`, we place the caret\n        // right after the first `6`\n\n        if (\n        // If we're deleting, we can position the caret right before the placeholder character\n        placeholder[_i3 - 1] === placeholderChar ||\n\n        // If a caret trap was set by a mask function, we need to stop at the trap.\n        caretTrapIndexes.indexOf(_i3) !== -1 ||\n\n        // This is the beginning of the placeholder. We cannot move any further.\n        // Let's put the caret there.\n        _i3 === 0) {\n          return _i3;\n        }\n      }\n    }\n  }\n}"}, {"id": 5, "identifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/core/src/createTextMaskInputElement.js", "name": "./core/src/createTextMaskInputElement.js", "index": 4, "index2": 4, "size": 11326, "cacheable": true, "built": true, "optional": false, "prefetched": false, "chunks": [0], "assets": [], "issuer": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/vanilla/src/vanillaTextMask.js", "failed": false, "errors": 0, "warnings": 0, "reasons": [{"moduleId": 0, "moduleIdentifier": "/Users/<USER>/Projects/text-mask/node_modules/babel-loader/lib/index.js!/Users/<USER>/Projects/text-mask/vanilla/src/vanillaTextMask.js", "module": "./vanilla/src/vanillaTextMask.js", "moduleName": "./vanilla/src/vanillaTextMask.js", "type": "cjs require", "userRequest": "../../core/src/createTextMaskInputElement", "loc": "18:34-86"}], "source": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; };\n\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; };\n\nexports.default = createTextMaskInputElement;\n\nvar _adjustCaretPosition = require('./adjustCaretPosition');\n\nvar _adjustCaretPosition2 = _interopRequireDefault(_adjustCaretPosition);\n\nvar _conformToMask2 = require('./conformToMask');\n\nvar _conformToMask3 = _interopRequireDefault(_conformToMask2);\n\nvar _utilities = require('./utilities');\n\nvar _constants = require('./constants');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar emptyString = '';\nvar strNone = 'none';\nvar strObject = 'object';\nvar isAndroid = typeof navigator !== 'undefined' && /Android/i.test(navigator.userAgent);\nvar defer = typeof requestAnimationFrame !== 'undefined' ? requestAnimationFrame : setTimeout;\n\nfunction createTextMaskInputElement(config) {\n  // Anything that we will need to keep between `update` calls, we will store in this `state` object.\n  var state = { previousConformedValue: undefined, previousPlaceholder: undefined };\n\n  return {\n    state: state,\n\n    // `update` is called by framework components whenever they want to update the `value` of the input element.\n    // The caller can send a `rawValue` to be conformed and set on the input element. However, the default use-case\n    // is for this to be read from the `inputElement` directly.\n    update: function update(rawValue) {\n      var _ref = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : config,\n          inputElement = _ref.inputElement,\n          providedMask = _ref.mask,\n          guide = _ref.guide,\n          pipe = _ref.pipe,\n          _ref$placeholderChar = _ref.placeholderChar,\n          placeholderChar = _ref$placeholderChar === undefined ? _constants.placeholderChar : _ref$placeholderChar,\n          _ref$keepCharPosition = _ref.keepCharPositions,\n          keepCharPositions = _ref$keepCharPosition === undefined ? false : _ref$keepCharPosition,\n          _ref$showMask = _ref.showMask,\n          showMask = _ref$showMask === undefined ? false : _ref$showMask;\n\n      // if `rawValue` is `undefined`, read from the `inputElement`\n      if (typeof rawValue === 'undefined') {\n        rawValue = inputElement.value;\n      }\n\n      // If `rawValue` equals `state.previousConformedValue`, we don't need to change anything. So, we return.\n      // This check is here to handle controlled framework components that repeat the `update` call on every render.\n      if (rawValue === state.previousConformedValue) {\n        return;\n      }\n\n      // Text Mask accepts masks that are a combination of a `mask` and a `pipe` that work together. If such a `mask` is\n      // passed, we destructure it below, so the rest of the code can work normally as if a separate `mask` and a `pipe`\n      // were passed.\n      if ((typeof providedMask === 'undefined' ? 'undefined' : _typeof(providedMask)) === strObject && providedMask.pipe !== undefined && providedMask.mask !== undefined) {\n        pipe = providedMask.pipe;\n        providedMask = providedMask.mask;\n      }\n\n      // The `placeholder` is an essential piece of how Text Mask works. For a mask like `(111)`, the placeholder would\n      // be `(___)` if the `placeholderChar` is set to `_`.\n      var placeholder = void 0;\n\n      // We don't know what the mask would be yet. If it is an array, we take it as is, but if it's a function, we will\n      // have to call that function to get the mask array.\n      var mask = void 0;\n\n      // If the provided mask is an array, we can call `convertMaskToPlaceholder` here once and we'll always have the\n      // correct `placeholder`.\n      if (providedMask instanceof Array) {\n        placeholder = (0, _utilities.convertMaskToPlaceholder)(providedMask, placeholderChar);\n      }\n\n      // In framework components that support reactivity, it's possible to turn off masking by passing\n      // `false` for `mask` after initialization. See https://github.com/text-mask/text-mask/pull/359\n      if (providedMask === false) {\n        return;\n      }\n\n      // We check the provided `rawValue` before moving further.\n      // If it's something we can't work with `getSafeRawValue` will throw.\n      var safeRawValue = getSafeRawValue(rawValue);\n\n      // `selectionEnd` indicates to us where the caret position is after the user has typed into the input\n      var currentCaretPosition = inputElement.selectionEnd;\n\n      // We need to know what the `previousConformedValue` and `previousPlaceholder` is from the previous `update` call\n\n      var previousConformedValue = state.previousConformedValue,\n          previousPlaceholder = state.previousPlaceholder;\n\n\n      var caretTrapIndexes = void 0;\n\n      // If the `providedMask` is a function. We need to call it at every `update` to get the `mask` array.\n      // Then we also need to get the `placeholder`\n      if ((typeof providedMask === 'undefined' ? 'undefined' : _typeof(providedMask)) === _constants.strFunction) {\n        mask = providedMask(safeRawValue, { currentCaretPosition: currentCaretPosition, previousConformedValue: previousConformedValue, placeholderChar: placeholderChar });\n\n        // disable masking if `mask` is `false`\n        if (mask === false) {\n          return;\n        }\n\n        // mask functions can setup caret traps to have some control over how the caret moves. We need to process\n        // the mask for any caret traps. `processCaretTraps` will remove the caret traps from the mask and return\n        // the indexes of the caret traps.\n\n        var _processCaretTraps = (0, _utilities.processCaretTraps)(mask),\n            maskWithoutCaretTraps = _processCaretTraps.maskWithoutCaretTraps,\n            indexes = _processCaretTraps.indexes;\n\n        mask = maskWithoutCaretTraps; // The processed mask is what we're interested in\n        caretTrapIndexes = indexes; // And we need to store these indexes because they're needed by `adjustCaretPosition`\n\n        placeholder = (0, _utilities.convertMaskToPlaceholder)(mask, placeholderChar);\n\n        // If the `providedMask` is not a function, we just use it as-is.\n      } else {\n        mask = providedMask;\n      }\n\n      // The following object will be passed to `conformToMask` to determine how the `rawValue` will be conformed\n      var conformToMaskConfig = {\n        previousConformedValue: previousConformedValue,\n        guide: guide,\n        placeholderChar: placeholderChar,\n        pipe: pipe,\n        placeholder: placeholder,\n        currentCaretPosition: currentCaretPosition,\n        keepCharPositions: keepCharPositions\n\n        // `conformToMask` returns `conformedValue` as part of an object for future API flexibility\n      };\n      var _conformToMask = (0, _conformToMask3.default)(safeRawValue, mask, conformToMaskConfig),\n          conformedValue = _conformToMask.conformedValue;\n\n      // The following few lines are to support the `pipe` feature.\n\n\n      var piped = (typeof pipe === 'undefined' ? 'undefined' : _typeof(pipe)) === _constants.strFunction;\n\n      var pipeResults = {};\n\n      // If `pipe` is a function, we call it.\n      if (piped) {\n        // `pipe` receives the `conformedValue` and the configurations with which `conformToMask` was called.\n        pipeResults = pipe(conformedValue, _extends({ rawValue: safeRawValue }, conformToMaskConfig));\n\n        // `pipeResults` should be an object. But as a convenience, we allow the pipe author to just return `false` to\n        // indicate rejection. Or return just a string when there are no piped characters.\n        // If the `pipe` returns `false` or a string, the block below turns it into an object that the rest\n        // of the code can work with.\n        if (pipeResults === false) {\n          // If the `pipe` rejects `conformedValue`, we use the `previousConformedValue`, and set `rejected` to `true`.\n          pipeResults = { value: previousConformedValue, rejected: true };\n        } else if ((0, _utilities.isString)(pipeResults)) {\n          pipeResults = { value: pipeResults };\n        }\n      }\n\n      // Before we proceed, we need to know which conformed value to use, the one returned by the pipe or the one\n      // returned by `conformToMask`.\n      var finalConformedValue = piped ? pipeResults.value : conformedValue;\n\n      // After determining the conformed value, we will need to know where to set\n      // the caret position. `adjustCaretPosition` will tell us.\n      var adjustedCaretPosition = (0, _adjustCaretPosition2.default)({\n        previousConformedValue: previousConformedValue,\n        previousPlaceholder: previousPlaceholder,\n        conformedValue: finalConformedValue,\n        placeholder: placeholder,\n        rawValue: safeRawValue,\n        currentCaretPosition: currentCaretPosition,\n        placeholderChar: placeholderChar,\n        indexesOfPipedChars: pipeResults.indexesOfPipedChars,\n        caretTrapIndexes: caretTrapIndexes\n      });\n\n      // Text Mask sets the input value to an empty string when the condition below is set. It provides a better UX.\n      var inputValueShouldBeEmpty = finalConformedValue === placeholder && adjustedCaretPosition === 0;\n      var emptyValue = showMask ? placeholder : emptyString;\n      var inputElementValue = inputValueShouldBeEmpty ? emptyValue : finalConformedValue;\n\n      state.previousConformedValue = inputElementValue; // store value for access for next time\n      state.previousPlaceholder = placeholder;\n\n      // In some cases, this `update` method will be repeatedly called with a raw value that has already been conformed\n      // and set to `inputElement.value`. The below check guards against needlessly readjusting the input state.\n      // See https://github.com/text-mask/text-mask/issues/231\n      if (inputElement.value === inputElementValue) {\n        return;\n      }\n\n      inputElement.value = inputElementValue; // set the input value\n      safeSetSelection(inputElement, adjustedCaretPosition); // adjust caret position\n    }\n  };\n}\n\nfunction safeSetSelection(element, selectionPosition) {\n  if (document.activeElement === element) {\n    if (isAndroid) {\n      defer(function () {\n        return element.setSelectionRange(selectionPosition, selectionPosition, strNone);\n      }, 0);\n    } else {\n      element.setSelectionRange(selectionPosition, selectionPosition, strNone);\n    }\n  }\n}\n\nfunction getSafeRawValue(inputValue) {\n  if ((0, _utilities.isString)(inputValue)) {\n    return inputValue;\n  } else if ((0, _utilities.isNumber)(inputValue)) {\n    return String(inputValue);\n  } else if (inputValue === undefined || inputValue === null) {\n    return emptyString;\n  } else {\n    throw new Error(\"The 'value' provided to Text Mask needs to be a string or a number. The value \" + ('received was:\\n\\n ' + JSON.stringify(inputValue)));\n  }\n}"}], "filteredModules": 0, "children": []}