{"version": 3, "sources": ["_core.scss", "_variables.scss", "_mixins.scss", "_collapsible.scss", "_hoverable.scss", "_direction.scss"], "names": [], "mappings": "AACA,cACE,kBAAkB,AAElB,yBCIyC,ADLzC,gBAAiB,CA0LlB,AA5LD,sBAMI,cAAc,AAEd,gBAAgB,AADhB,eAAe,AAEf,eAAgB,CAKjB,AAdH,4BAYM,cAAe,CAChB,AAbL,2BAiBI,gBAAgB,AAChB,mBCL6E,ADM7E,WCIkC,ADHlC,mBCRsC,ADStC,gBAAiB,AAEjB,mBAAmB,AADnB,wBAAyB,CAS1B,AA/BH,oEA2BM,kBAAkB,AAClB,aAAsD,AACtD,SAAU,CACX,AA9BL,4BAkCI,aAAa,AAGb,UCJwC,CDOzC,AAxCH,qDAmCI,mBCtB6E,AD0B7E,gBAAgB,AAHhB,kBCzBqC,AD2BrC,kBAAmB,CAtCvB,AA+EG,yBAnCC,cAAc,AADd,kBAAkB,AAQlB,UAAU,AAJV,WChBwC,ADiBxC,oBAAqB,CA+BtB,AA/EH,oCAuDM,kBAAkB,AADlB,aAAa,AAIb,WCjCiC,ADgCjC,eC1CkC,ADyClC,iBAAkB,CAGnB,AA3DL,gEA+DM,kBAAkB,AAClB,aAAsD,AAEtD,UAAU,AADV,gBAAiB,CAElB,AEnCH,+BFuCI,6BC1C6C,AD4C7C,iBAAiB,AAHjB,cC7DqC,AD+DrC,oBAAqB,CExCJ,AFhCvB,0CA4EQ,UCvD8B,CDwD/B,AEjDL,+BF2CI,6BC1C6C,AD4C7C,iBAAiB,AAHjB,cC7DqC,AD+DrC,oBAAqB,CE5CJ,AF5BvB,0CA4EQ,UCvD8B,CDwD/B,AA7EP,oCAqFM,YAAa,CACd,AAtFL,8DA0FQ,6BC7D2C,AD8D3C,aClFmC,CDsFpC,AA/FP,yEA6FU,UCxE4B,CDyE7B,AA9FT,+CAoGQ,6BCtE2C,ADuE3C,UCpEoC,CDwErC,AAzGP,0DAuGU,UCjF4B,CDkF7B,AAxGT,yBA+GI,iBAAkB,CAgBnB,AA/HH,+BAkHM,kBAAkB,AAClB,UAAU,AACV,yBC5G0D,ADyG1D,QAAS,CAIV,AArHL,wFAwHM,gBAAiB,CAClB,AAzHL,0CA4HM,mBAAmB,AACnB,UAAU,AAFV,WAAY,CAGb,AA9HL,0CAoIM,UChHgC,CDqHjC,AAzIL,mJA4IM,yBC1HuD,ADyHvD,UCtHgC,CDwHjC,AA7IL,qDEaI,iCDJuC,ADuIrC,yBC7HwD,AD8HxD,UC3HgC,CD6HjC,AAnJL,gDAwJQ,iBAAwC,CACzC,AAzJP,sDA8JU,iBAAwC,CACzC,AA/JT,4DAoKY,iBAAyC,CAC1C,AArKX,0MAiLM,UC1JgC,CD2JjC,AAlLL,ocAuLQ,6BAA6B,AAC7B,UCjK8B,CDkK/B,AAOP,sDAEI,kBAAkB,AAElB,UAAU,AADV,QAAS,CAEV,AALH,8DAUQ,wBCvLsD,CDwLvD,AAXP,mDAcM,aAAc,CACf,AAfL,sDAqBM,gBAAgB,AAChB,eAAe,AACf,gBAAgB,AAHhB,iBAAkB,CAgBnB,AApCL,0HA2BQ,MAAM,AACN,YAA+B,CAChC,AA7BP,iEAgCQ,SAAS,AACT,iBAAiB,AACjB,iBAAkB,CACnB,AAnCP,2EAsCM,YAAa,CACd,AAvCL,6CA+CM,yBAAiB,AAAjB,sBAAiB,AAAjB,qBAAiB,AAFjB,kBAAkB,AAClB,UAAU,AAJV,SAAS,AACT,SAAS,AACT,gBAAgB,AAGhB,gBAAiB,CAClB,AAhDL,iFE1KM,uCF0O0C,AE1O1C,kCF0O0C,AAZxC,cAAc,AADd,kBAAkB,AAElB,MAAM,AACN,UAAU,AAKV,kBAAkB,AAClB,UAAU,AEtOZ,+BF0O0C,AARxC,sBAA0E,AAK1E,yBC3OqD,ADqOrD,6BAA+E,AAE/E,QAAQ,AACR,SAAS,AAIT,mBAAmB,AACnB,kBAAmB,CAEpB,AAjEP,uFAuEU,mBAAmB,AACnB,UAAU,AAHV,WAAW,AACX,WAAY,CAGb,AAzET,4EA6EM,wBC1PwD,CD2PzD,AA9EL,oJAkFQ,wBC3QmC,CD4QpC,AAnFP,uEAwFU,YAAa,CACd,AAzFT,2EA2FU,mBC9QuE,ADgRvE,YAAY,AADZ,eAAgB,CAEjB,AG9RT,0DASM,oCAA4B,AAA5B,gCAA4B,AAA5B,+BAA4B,AAL5B,qBAAqB,AAErB,kBAAkB,AAElB,QAAQ,AADR,aAAiC,AAEjC,4BAA4B,AAE5B,UAAU,AADV,WAAW,AAEX,gBAAgB,AAChB,kBAAkB,AARlB,WAAY,CASb,AAdL,wIAkBM,WACF,CAAC,AAnBL,+CAuBI,mBAAmB,AACnB,UAAU,AACV,eAAgB,CACjB,AA1BH,yEA6BM,WAAY,CACb,AAKL,8EAIM,6BAAqB,AAArB,yBAAqB,AAArB,wBAAqB,AACrB,4BAAoB,AAApB,wBAAoB,AAApB,uBAAoB,AACpB,qEAAwD,AAAxD,2DAAwD,AADxD,oBAAoB,AADpB,qBAAqB,AAErB,oDAAwD,CACzD,AAPL,uFAUM,kBAAmB,CACpB,AAXL,yFAcM,YAAa,CACd,AAfL,iFAmBM,kBAAkB,AAElB,MAAM,AACN,UAAU,AAEV,mBAAmB,AACnB,UAAU,AACV,sBAA0E,AAN1E,YAAY,AAGZ,WAAY,CAIb,AA3BL,mFA6BM,iBAAwC,CACzC,AA9BL,6FAmCM,gBAAgB,AAEhB,SAAS,AACT,UAAU,AAFV,UAAW,CAGZ,AAvCL,+FA0CQ,iBAAwC,CACzC,AA3CP,qGA8CU,iBAAwC,CACzC,AClFT,6CAEI,6BAAqB,AAArB,yBAAqB,AAArB,wBAAqB,AFoBnB,qEEnB8D,AFmB9D,2DEnB8D,AADhE,qBAAqB,AFoBnB,oDEnB8D,CACjE,AAJH,8DAOI,mBAAoB,CACrB,AAEH,4EFYM,oDET8D,CACjE,AAJH,sEAOI,mBAAoB,CACrB,AAIH,4IAYM,2BAAmB,AAAnB,uBAAmB,AAAnB,sBAAmB,AAPnB,kBAAkB,AAElB,MAAM,AACN,UAAU,AAIV,mBAAmB,AAFnB,mBAAmB,AACnB,UAAU,AALV,YAAY,AAGZ,WAAY,CAIb,AAbL,4SAiBQ,iBAAwC,CACzC,AAlBP,gHAiCQ,mCAA2B,AAA3B,+BAA2B,AAA3B,8BAA2B,AAT3B,kBAAkB,AAElB,QAAQ,AADR,QAAQ,AAQR,2BAA2B,AAJ3B,iCAAiC,AAGjC,+BH9CwD,AG4CxD,oCAAoC,AAHpC,QAAQ,AACR,SAAS,AALT,UAAW,CAWZ,AAMP,+EAYM,2BAAmB,AAAnB,uBAAmB,AAAnB,sBAAmB,AARnB,kBAAkB,AAElB,MAAM,AACN,UAAU,AAKV,mBAAmB,AAHnB,mBAAmB,AACnB,UAAU,AACV,sBAA0E,AAN1E,YAAY,AAGZ,WAAY,CASb,AAjBL,qGAeQ,iBAAwC,CACzC,AC7EP,8DHYI,iCDJuC,CIJtC,AAJL,mFAUQ,WAAW,AACX,WAA8B,CAC/B,AAZP,uGAeU,WAAW,AACX,UAAU,AAEV,sBJb2B,AIY3B,4BAA+E,CAEhF,AAOT,uKAQM,uBAAwB,CACzB,AATL,wLAiBQ,WAAW,AADX,SAAU,CAEX,AAlBP,gNAsBU,WAAW,AACX,OAAO,AAEP,iCAAiC,AACjC,6BJ7CsD,CI8CvD,AA3BT,4MAqCQ,WAAW,AADX,SAAU,CAEX", "file": "stacked-menu.min.css", "sourcesContent": ["/** The root StackedMenu */\n.#{$stacked-menu-class-prefix} {\n  position: relative;\n  padding: .25rem 0;\n  background-color: $stacked-menu-bg;\n\n  ul.menu {\n    display: block;\n    padding-left: 0;\n    margin-bottom: 0;\n    list-style: none;\n\n    + .menu {\n      margin-top: 1em;\n    }\n  }\n\n  .menu-header {\n    margin: .5em 0 0;\n    padding: $stacked-menu-padding;\n    color: $stacked-menu-color-disabled;\n    font-size: $stacked-menu-header-font-size;\n    font-weight: bold;\n    text-transform: uppercase;\n    letter-spacing: 1px;\n\n    > .badge,\n    > .label {\n      position: absolute;\n      right: $stacked-menu-padder + ($stacked-menu-padder/2);\n      z-index: 2;\n    }\n  }\n\n  .menu-subhead {\n    display: none;\n    padding: $stacked-menu-padding;\n    font-size: $stacked-menu-font-size;\n    color: $stacked-menu-child-active-color;\n    line-height: 1.75em;\n    overflow: hidden;\n  }\n\n  .menu-link {\n    position: relative;\n    display: block;\n    padding: $stacked-menu-padding;\n    font-size: $stacked-menu-font-size;\n    color: $stacked-menu-child-color;\n    text-decoration: none;\n    line-height: 1.75em;\n    overflow: hidden;\n    outline: 0;\n\n    > .menu-icon {\n      width: 1.25em;\n      margin-right: .5em;\n      text-align: center;\n      font-size: $stacked-menu-icon-size;\n      color: $stacked-menu-icon-color;\n    }\n\n    > .badge,\n    > .label {\n      position: absolute;\n      right: $stacked-menu-padder + ($stacked-menu-padder/2);\n      margin-top: .25em;\n      z-index: 2;\n    }\n\n    @include hover-focus {\n      color: $stacked-menu-child-hover-color;\n      background-color: $stacked-menu-child-hover-bg;\n      text-decoration: none;\n      overflow: visible;\n\n      .menu-icon {\n        color: $stacked-menu-icon-hover-color;\n      }\n    }\n  }\n\n  /* global state */\n  .menu-item {\n\n    + .menu-item {\n      margin-top: 0;\n    }\n\n    &.has-open:not(.has-active) {\n      > .menu-link {\n        background-color: $stacked-menu-child-hover-bg;\n        color: $stacked-menu-child-hover-color;\n        .menu-icon {\n          color: $stacked-menu-icon-hover-color;\n        }\n      }\n    }\n\n    &.has-active {\n      > .menu-link {\n        background-color: $stacked-menu-child-active-bg;\n        color: $stacked-menu-child-active-color;\n        .menu-icon {\n          color: $stacked-menu-icon-active-color;\n        }\n      }\n    }\n  }\n\n  /* nav child */\n  .has-child {\n    position: relative;\n    > .menu {\n      height: 0;\n      visibility: hidden;\n      opacity: 0;\n      background-color: $stacked-menu-child-bg;\n    }\n    &.has-active:hover > .menu,\n    &.has-open > .menu {\n      overflow: visible;\n    }\n    &.has-active > .menu {\n      height: auto;\n      visibility: visible;\n      opacity: 1;\n    }\n  }\n\n  /* nav level 1 */\n  > .menu > .menu-item {\n    > .menu-link {\n      color: $stacked-menu-color;\n      @include hover-focus {\n        color: $stacked-menu-hover-color;\n        background-color: $stacked-menu-hover-bg;\n      }\n    }\n    &.has-open > .menu-link {\n      color: $stacked-menu-hover-color;\n      background-color: $stacked-menu-hover-bg;\n    }\n\n    &.has-active > .menu-link {\n      background-color: $stacked-menu-active-bg;\n      color: $stacked-menu-active-color;\n      @include box-shadow(inset 3px 0 0 $stacked-menu-accent-color);\n    }\n\n    /* level 1 */\n    > .menu {\n      .menu-link {\n        padding-left: ($stacked-menu-padder * 6);\n      }\n\n      /* level 2 */\n      .menu {\n        .menu-link {\n          padding-left: ($stacked-menu-padder * 8);\n        }\n\n        /* level 3 */\n        .menu {\n          .menu-link {\n            padding-left: ($stacked-menu-padder * 10);\n          }\n        }\n      }\n    }\n  }\n\n  /* disabled state */\n  .menu-item.disabled > .menu-link,\n  .menu-item > .menu-link.disabled {\n    color: $stacked-menu-color-disabled;\n\n    .menu-icon {\n      color: $stacked-menu-color-disabled;\n    }\n\n    @include hover-focus {\n      &,\n      .menu-icon {\n        background-color: transparent;\n        color: $stacked-menu-color-disabled;\n      }\n    }\n  }\n}\n\n\n/* Compact */\n.#{$stacked-menu-class-prefix}-has-compact {\n  .has-child.has-active > .menu {\n    visibility: hidden;\n    height: 0;\n    opacity: 0;\n  }\n\n  .has-child {\n    &.has-active {\n      .menu-subhead {\n        background-color: $stacked-menu-active-bg;\n      }\n    }\n    .menu-subhead {\n      display: block;\n    }\n  }\n\n  > .menu {\n    > .menu-item > .menu-link {\n      text-align: center;\n      padding-right: 0;\n      padding-left: 0;\n      max-height: 40px;\n\n      > .badge,\n      > .label {\n        top: 0;\n        right: ($stacked-menu-padder/2);\n      }\n\n      .menu-icon {\n        margin: 0;\n        font-size: 1.25em;\n        line-height: 1.5em;\n      }\n    }\n    > .menu-item.has-child > .menu-link .menu-text {\n      display: none;\n    }\n    > .menu-header {\n      // transform to empty space\n      margin: 0;\n      height: 0;\n      overflow: hidden;\n      visibility: hidden;\n      opacity: 0;\n      user-select: none;\n    }\n    > .menu-item:not(.has-child) > .menu-link {\n      .menu-text {\n        position: absolute;\n        display: block;\n        top: 0;\n        left: 100%;\n        padding: $stacked-menu-padder ($stacked-menu-padder*1.5) $stacked-menu-padder 0;\n        border-radius: 0 $stacked-menu-border-radius $stacked-menu-border-radius 0;\n        width: 0;\n        height: 0;\n        visibility: hidden;\n        opacity: 0;\n        background-color: $stacked-menu-hover-bg;\n        line-height: 1.75em;\n        white-space: nowrap;\n        @include transition(opacity 150ms linear);\n      }\n\n      @include hover {\n        .menu-text {\n          width: auto;\n          height: auto;\n          visibility: visible;\n          opacity: 1;\n        }\n      }\n    }\n    > .menu-item.has-active > .menu-link .menu-text {\n      background-color: $stacked-menu-active-bg;\n    }\n    > .menu-item.disabled > .menu-link,\n    > .menu-item > .menu-link.disabled {\n      .menu-text {\n        background-color: $stacked-menu-bg;\n      }\n    }\n    > .menu-item.has-child {\n      > .menu-link {\n        &:before {\n          content: none;\n        }\n        .menu-text {\n          padding: $stacked-menu-padding;\n          text-align: left;\n          width: 200px;\n        }\n      }\n    }\n  }\n}\n", "$enable-shadows:              true !default;\n$enable-transitions:          true !default;\n$transition-base:             all 150ms linear !default;\n\n$stacked-menu-class-prefix:         'stacked-menu' !default;\n$stacked-menu-breakpoint:           992px !default;\n$stacked-menu-padder:               .5rem !default;\n$stacked-menu-border-radius:        0 !default;\n$stacked-menu-bg:                   #15191d !default;\n$stacked-menu-child-bg:             lighten($stacked-menu-bg, 3) !default;\n$stacked-menu-accent-color:         #5d67ad !default;\n// nav menu\n$stacked-menu-font-size:          .875rem !default;\n$stacked-menu-header-font-size:   .8125rem !default;\n$stacked-menu-padding:            $stacked-menu-padder ($stacked-menu-padder * 2) !default;\n\n$stacked-menu-icon-size:            1rem !default;\n\n// theme\n$stacked-menu-hover-bg:           darken($stacked-menu-bg, 3) !default;\n$stacked-menu-active-bg:          lighten($stacked-menu-bg, 5) !default;\n$stacked-menu-color:              #ccc !default;\n$stacked-menu-hover-color:        #eee !default;\n$stacked-menu-active-color:       #eee !default;\n$stacked-menu-color-disabled:     #555 !default;\n// icon\n$stacked-menu-icon-color:          #999 !default;\n$stacked-menu-icon-hover-color:    $stacked-menu-hover-color !default;\n$stacked-menu-icon-active-color:   $stacked-menu-active-color !default;\n// child\n$stacked-menu-child-hover-bg:           transparent !default;\n$stacked-menu-child-active-bg:          transparent !default;\n$stacked-menu-child-color:              #999 !default;\n$stacked-menu-child-hover-color:        $stacked-menu-accent-color !default;\n$stacked-menu-child-active-color:       #fff !default;", "@mixin breakpoint-up($breakpoint) {\n  @media (min-width: ($breakpoint + 1px)) {\n    @content;\n  }\n}\n\n@mixin breakpoint-down($breakpoint) {\n  @media (max-width: ($breakpoint - 1px)) {\n    @content;\n  }\n}\n\n@mixin box-shadow($shadow...) {\n  @if $enable-shadows {\n    box-shadow: $shadow;\n  }\n}\n\n@mixin transition($transition...) {\n  @if $enable-transitions {\n    @if length($transition) == 0 {\n      transition: $transition-base;\n    } @else {\n      transition: $transition;\n    }\n  }\n}\n\n@mixin hover {\n  &:hover { @content }\n}\n\n@mixin hover-focus {\n  &:focus { @content }\n  @include hover { @content }\n}\n\n@mixin active {\n  &:active,\n  &.active { @content }\n}\n\n@mixin hover-focus-active {\n  &:focus,\n  &:active,\n  &.active {\n    @content\n  }\n  @include hover { @content }\n}\n", "/* Behavior */\n.#{$stacked-menu-class-prefix}-has-collapsible {\n  /** caret angle, Only show on collapsible mode */\n  .has-child {\n    > .menu-link::after {\n      display: inline-block;\n      content: '+';\n      position: absolute;\n      right: ($stacked-menu-padder*1.5);\n      top: 50%;\n      transform: translateY(-.5em);\n      height: 1em;\n      width: 1em;\n      line-height: 1em;\n      text-align: center;\n    }\n\n    &.has-active > .menu-link::after,\n    &.has-open > .menu-link::after {\n      content: '-'\n    }\n  }\n\n  .has-child > .menu {\n    visibility: visible;\n    opacity: 1;\n    overflow: hidden;\n  }\n  .has-child.has-open:not(.has-active) {\n    > .menu {\n      height: auto;\n    }\n  }\n}\n\n/** collapsible on compact mode */\n.#{$stacked-menu-class-prefix}-has-collapsible.#{$stacked-menu-class-prefix}-has-compact {\n  /** hide caret on level 1 */\n  > .menu > .has-child {\n    > .menu {\n      transform-origin: 0 0;\n      transform: scale(.6);\n      transition: opacity 150ms linear, transform 150ms linear;\n    }\n\n    &.has-open > .menu {\n      transform: scale(1);\n    }\n\n    > .menu-link:after {\n      display: none;\n    }\n  }\n  .has-child {\n    &.has-open > .menu {\n      position: absolute;\n      width: 200px;\n      top: 0;\n      left: 100%;\n      height: auto;\n      visibility: visible;\n      opacity: 1;\n      border-radius: 0 $stacked-menu-border-radius $stacked-menu-border-radius 0;\n    }\n    > .menu .menu-link {\n      padding-left: ($stacked-menu-padder * 2);\n    }\n  }\n  /** child level 2 up */\n  .menu .menu .has-child {\n    &.has-open > .menu {\n      position: static;\n      width: auto;\n      top: auto;\n      left: auto;\n    }\n    > .menu {\n      .menu-link {\n        padding-left: ($stacked-menu-padder * 4);\n      }\n      .menu {\n        .menu-link {\n          padding-left: ($stacked-menu-padder * 6);\n        }\n      }\n    }\n  }\n}\n", "/* Give a transition */\n.#{$stacked-menu-class-prefix}-has-hoverable {\n  .has-child > .menu {\n    transform-origin: 0 0;\n    @include transition(opacity 150ms linear, transform 150ms linear);\n  }\n  /** except level 1 .has-active */\n  .has-child:not(.has-active) > .menu {\n    transform: scale(.6);\n  }\n}\n.#{$stacked-menu-class-prefix}-has-hoverable.#{$stacked-menu-class-prefix}-has-compact {\n  /** level 1 */\n  > .menu > .has-child > .menu {\n    @include transition(opacity 150ms linear, transform 150ms linear);\n  }\n  /** overwrite for all */\n  .has-child > .menu {\n    transform: scale(.6);\n  }\n}\n\n/* Behavior */\n.#{$stacked-menu-class-prefix}-has-hoverable,\n.#{$stacked-menu-class-prefix}-has-compact {\n  /** except level 1 .has-active */\n  .has-child {\n    &.has-open:not(.has-active) > .menu {\n      position: absolute;\n      width: 200px;\n      top: 0;\n      left: 100%;\n      height: auto;\n      visibility: visible;\n      opacity: 1;\n      transform: scale(1);\n    }\n    > .menu {\n      .menu-item > .menu .menu-link,\n      .menu-item > .menu .menu .menu-link {\n        padding-left: ($stacked-menu-padder * 2);\n      }\n    }\n    /** caret */\n    > .menu-link {\n      &:before {\n        content: '';\n        position: absolute;\n        right: 0;\n        top: 50%;\n        width: 0;\n        height: 0;\n        border-top: 8px solid transparent;\n        border-bottom: 8px solid transparent;\n        /** the caret */\n        border-right:8px solid $stacked-menu-child-bg;\n        transform: translateY(-8px);\n      }\n    }\n  }\n}\n\n/** hoverable on compact mode */\n.#{$stacked-menu-class-prefix}-has-hoverable.#{$stacked-menu-class-prefix}-has-compact {\n  /** overwrite for all */\n  .has-child.has-open {\n    > .menu {\n      position: absolute;\n      width: 200px;\n      top: 0;\n      left: 100%;\n      height: auto;\n      visibility: visible;\n      opacity: 1;\n      border-radius: 0 $stacked-menu-border-radius $stacked-menu-border-radius 0;\n      transform: scale(1);\n\n      .menu-item > .menu-link {\n        padding-left: ($stacked-menu-padder * 2);\n      }\n    }\n  }\n}\n", "/** open menu to left direction */\n/** Core */\n.#{$stacked-menu-class-prefix}-has-left {\n  > .menu > .menu-item {\n    &.has-active > .menu-link {\n      @include box-shadow(inset -3px 0 0 $stacked-menu-accent-color);\n    }\n  }\n  /** compact */\n  &.#{$stacked-menu-class-prefix}-has-compact {\n    > .menu {\n      > .menu-item > .menu-link > .badge {\n        right: auto;\n        left: ($stacked-menu-padder/2);\n      }\n      > .menu-item:not(.has-child) > .menu-link {\n        .menu-text {\n          right: 100%;\n          left: auto;\n          padding: $stacked-menu-padder 0 $stacked-menu-padder ($stacked-menu-padder*1.5);\n          border-radius: $stacked-menu-border-radius 0 0 $stacked-menu-border-radius;\n        }\n      }\n    }\n  }\n}\n\n/** Behavior */\n.#{$stacked-menu-class-prefix}-has-left {\n  &.#{$stacked-menu-class-prefix}-has-hoverable {\n    .has-child > .menu {\n      transform-origin: 100% 0;\n    }\n  }\n  &.#{$stacked-menu-class-prefix}-has-collapsible.#{$stacked-menu-class-prefix}-has-compact {\n    > .menu > .has-child > .menu {\n      transform-origin: 100% 0;\n    }\n  }\n\n  &.#{$stacked-menu-class-prefix}-has-hoverable,\n  &.#{$stacked-menu-class-prefix}-has-compact {\n    .has-child.has-open:not(.has-active) {\n      > .menu {\n        left: auto;\n        right: 100%;\n      }\n      // caret\n      > .menu-link {\n        &:before {\n          right: auto;\n          left: 0;\n          // the caret\n          border-right: 0 solid transparent;\n          border-left: 8px solid $stacked-menu-child-bg;\n        }\n      }\n    }\n  }\n  /** both hoverable & collapsible on compact mode */\n  &.#{$stacked-menu-class-prefix}-has-hoverable.#{$stacked-menu-class-prefix}-has-compact,\n  &.#{$stacked-menu-class-prefix}-has-collapsible.#{$stacked-menu-class-prefix}-has-compact {\n    .has-child.has-open {\n      > .menu {\n        left: auto;\n        right: 100%;\n      }\n    }\n  }\n}\n"]}