{"version": 3, "file": "stacked-menu.min.js", "sources": ["src/scripts/stacked-menu.js"], "sourcesContent": ["/**\n * A flexible stacked navigation menu.\n * @class\n *\n * @example <caption>The StackedMenu basic template looks like:</caption>\n * <div id=\"stacked-menu\" class=\"stacked-menu\">\n *   <nav class=\"menu\">\n *     <li class=\"menu-item\">\n *      <a href=\"home.html\" class=\"menu-link\">\n *        <i class=\"menu-icon fa fa-home\"></i>\n *        <span class=\"menu-text\">Home</span>\n *        <span class=\"badge badge-danger\">9+</span>\n *      </a>\n *     <li>\n *   </nav>\n * </div>\n *\n * @example <caption>Instance the StackedMenu:</caption>\n * var menus = new StackedMenu();\n */\nclass StackedMenu {\n\n  /**\n   * Create a StackedMenu.\n   * @constructor\n   * @param  {Object} options - An object containing key:value that representing the current StackedMenu.\n   */\n  constructor(options) {\n    /**\n     * The StackedMenu options.\n     * @type {Object}\n     * @property {Boolean} compact=false                  - Transform StackedMenu items (except item childs) to small size.\n     * @property {Boolean} hoverable=false                - How StackedMenu triggered `open`/`close` state. Use `false` for hoverable and `true` for collapsible (clickable).\n     * @property {Boolean} closeOther=true                - Control whether expanding an item will cause the other items to close. Only available when `hoverable=false`.\n     * @property {String} align='left'                    - Where StackedMenu items childs will open when `hoverable=true` (`left`/`right`).\n     * @property {String} selector='#stacked-menu'        - The StackedMenu element selector.\n     * @property {String} selectorClass='stacked-menu'    - The css class name that will be added to the StackedMenu and used for css prefix classes.\n     * @example\n     * var options = {\n     *   closeOther: false,\n     *   align: 'right',\n     * };\n     *\n     * var menus = new StackedMenu(options);\n     */\n    this.options = {\n      compact: false,\n      hoverable: false,\n      closeOther: true,\n      align: 'right',\n      selector: '#stacked-menu',\n      selectorClass: 'stacked-menu'\n    }\n\n    // mixed default and custom options\n    this.options = this._extend({}, this.options, options)\n\n    /**\n     * The StackedMenu element.\n     * @type {Element}\n     */\n    this.selector = document.querySelector(this.options.selector)\n\n    /**\n     * The StackedMenu items.\n     * @type {Element}\n     */\n    this.items = this.selector ? this.selector.querySelectorAll('.menu-item') : null\n\n    // forEach fallback\n    if (!Array.prototype.forEach) {\n      Array.prototype.forEach = function forEach(cb, arg) {\n        if(typeof cb !== 'function') throw new TypeError(`${cb} is not a function`)\n\n        let array = this\n        arg = arg || this\n        for(let i = 0; i < array.length; i++) {\n          cb.call(arg, array[i], i, array)\n        }\n      }\n    }\n    this.each = Array.prototype.forEach\n\n    /**\n     * Lists of feature classes that will be added to the StackedMenu depend to current options.\n     * Used selectorClass for prefix.\n     * @type {Object}\n     */\n    this.classes = {\n      alignLeft: this.options.selectorClass + '-has-left',\n      compact: this.options.selectorClass + '-has-compact',\n      collapsible: this.options.selectorClass + '-has-collapsible',\n      hoverable: this.options.selectorClass + '-has-hoverable',\n      hasChild: 'has-child',\n      hasActive: 'has-active',\n      hasOpen: 'has-open'\n    }\n\n    /** states element */\n    /**\n     * The active item.\n     * @type {Element}\n     */\n    this.active = null\n\n    /**\n     * The open item(s).\n     * @type {Element}\n     */\n    this.open = []\n\n    /**\n     * The StackedMenu element.\n     * @type {Boolean}\n     */\n    this.turbolinksAvailable = typeof window.Turbolinks === 'object' && window.Turbolinks.supported\n\n    /** event handlers */\n    this.handlerClickDoc = []\n    this.handlerOver = []\n    this.handlerOut = []\n    this.handlerClick = []\n\n    // Initialization\n    this.init()\n  }\n\n  /** Private methods */\n  /**\n   * Listen on document when the page is ready.\n   * @private\n   * @param  {Function} handler - The callback function when page is ready.\n   * @return {void}\n   */\n  _onReady(handler) {\n    if(document.readyState != 'loading') {\n      handler()\n    } else {\n      document.addEventListener('DOMContentLoaded', handler, false)\n    }\n  }\n\n  /**\n   * Handles clicking on menu leaves. Turbolinks friendly.\n   * @private\n   * @param  {Object} self - The StackedMenu self instance.\n   * @return {void}\n   */\n  _handleNavigation(self) {\n    self.each.call(this.items, (el) => {\n      self._on(el, 'click', function(e) {\n        // Stop propagating the event to parent links\n        e.stopPropagation()\n        // if Turbolinks are available preventDefault immediatelly.\n        self.turbolinksAvailable ? e.preventDefault() : null\n        // if the element is \"parent\" and Turbolinks are not available,\n        // maintain the original behaviour. Otherwise navigate programmatically\n        if (self._hasChild(el)) {\n          self.turbolinksAvailable ? null : e.preventDefault()\n        } else {\n          self.turbolinksAvailable ? window.Turbolinks.visit(el.firstElementChild.href) : null\n        }\n      })\n    })\n  }\n\n\n  /**\n   * Merge the contents of two or more objects together into the first object.\n   * @private\n   * @param  {Object} obj - An object containing additional properties to merge in.\n   * @return {Object} The merged object.\n   */\n  _extend(obj) {\n    obj = obj || {}\n    const args = arguments\n    for (let i = 1; i < args.length; i++) {\n      if (!args[i]) continue\n      for (let key in args[i]) {\n        if (args[i].hasOwnProperty(key))\n          obj[key] = args[i][key]\n      }\n    }\n    return obj\n  }\n\n  /**\n   * Attach an event to StackedMenu selector.\n   * @private\n   * @param  {String} type - The name of the event (case-insensitive).\n   * @param  {(Boolean|Number|String|Array|Object)} data - The custom data that will be added to event.\n   * @return {void}\n   */\n  _emit(type, data) {\n    let e\n    if (document.createEvent) {\n      e = document.createEvent('Event')\n      e.initEvent(type, true, true)\n    } else {\n      e = document.createEventObject()\n      e.eventType = type\n    }\n    e.eventName = type\n    e.data = data || this\n    // attach event to selector\n    document.createEvent\n      ? this.selector.dispatchEvent(e)\n      : this.selector.fireEvent('on' + type, e)\n  }\n\n  /**\n   * Bind one or two handlers to the element, to be executed when the mouse pointer enters and leaves the element.\n   * @private\n   * @param  {Element} el - The target element.\n   * @param  {Function} handlerOver - A function to execute when the mouse pointer enters the element.\n   * @param  {Function} handlerOut  - A function to execute when the mouse pointer leaves the element.\n   * @return {void}\n   */\n  _hover(el, handlerOver, handlerOut) {\n    if (el.tagName === 'A') {\n      this._on(el, 'focus', handlerOver)\n      this._on(el, 'blur', handlerOut)\n    } else {\n      this._on(el, 'mouseover', handlerOver)\n      this._on(el, 'mouseout', handlerOut)\n    }\n  }\n\n  /**\n   * Registers the specified listener on the element.\n   * @private\n   * @param  {Element} el - The target element.\n   * @param  {String} type - The name of the event.\n   * @param  {Function} handler - The callback function when event type is fired.\n   * @return {void}\n   */\n  _on(el, type, handler) {\n    let types = type.split(' ')\n    for (let i = 0; i < types.length; i++) {\n      el[window.addEventListener ? 'addEventListener' : 'attachEvent']( window.addEventListener ? types[i] : `on${types[i]}` , handler, false)\n    }\n  }\n\n  /**\n   * Removes the event listener previously registered with [_on()]{@link StackedMenu#_on} method.\n   * @private\n   * @param  {Element} el - The target element.\n   * @param  {String} type - The name of the event.\n   * @param  {Function} handler - The callback function when event type is fired.\n   * @return {void}\n   */\n  _off(el, type, handler) {\n    let types = type.split(' ')\n    for (let i = 0; i < types.length; i++) {\n      el[window.removeEventListener ? 'removeEventListener' : 'detachEvent']( window.removeEventListener ? types[i] : `on${types[i]}` , handler, false)\n    }\n  }\n\n  /**\n   * Adds one or more class names to the target element.\n   * @private\n   * @param {Element} el - The target element.\n   * @param {String} className - Specifies one or more class names to be added.\n   * @return {void}\n   */\n  _addClass(el, className) {\n    let classes = className.split(' ')\n    for (let i = 0; i < classes.length; i++) {\n      if (el.classList) el.classList.add(classes[i])\n      else el.classes[i] += ' ' + classes[i]\n    }\n  }\n\n  /**\n   * Removes one or more class names to the target element.\n   * @private\n   * @param {Element} el - The target element.\n   * @param {String} className - Specifies one or more class names to be added.\n   * @return {void}\n   */\n  _removeClass(el, className) {\n    let classes = className.split(' ')\n    for (let i = 0; i < classes.length; i++) {\n      if (el.classList) el.classList.remove(classes[i])\n      else el.classes[i] = el.classes[i].replace(new RegExp('(^|\\\\b)' + classes[i].split(' ').join('|') + '(\\\\b|$)', 'gi'), ' ')\n    }\n  }\n\n  /**\n   * Determine whether the element is assigned the given class.\n   * @private\n   * @param {Element} el - The target element.\n   * @param {String} className - The class name to search for.\n   * @return {Boolean} is has className.\n   */\n  _hasClass(el, className) {\n    if (el.classList) return el.classList.contains(className)\n    return new RegExp('(^| )' + className + '( |$)', 'gi').test(el.className)\n  }\n\n  /**\n   * Determine whether the element is a menu child.\n   * @private\n   * @param {Element} el - The target element.\n   * @return {Boolean} is has child.\n   */\n  _hasChild(el) {\n    return this._hasClass(el, this.classes.hasChild)\n  }\n\n  /**\n   * Determine whether the element is a active menu.\n   * @private\n   * @param {Element} el - The target element.\n   * @return {Boolean} is has active state.\n   */\n  _hasActive(el) {\n    return this._hasClass(el, this.classes.hasActive)\n  }\n\n  /**\n   * Determine whether the element is a open menu.\n   * @private\n   * @param {Element} el - The target element.\n   * @return {Boolean} is has open state.\n   */\n  _hasOpen(el) {\n    return this._hasClass(el, this.classes.hasOpen)\n  }\n\n  /**\n   * Determine whether the element is a level menu.\n   * @private\n   * @param {Element} el - The target element.\n   * @return {Boolean} is a level menu.\n   */\n  _isLevelMenu (el) {\n    return this._hasClass(el.parentNode.parentNode, this.options.selectorClass)\n  }\n\n  /**\n   * Attach an event to menu item depend on hoverable option.\n   * @private\n   * @param {Element} el - The target element.\n   * @param {Number} index - An array index from each menu item use to detach the current event.\n   * @return {void}\n   */\n  _menuTrigger(el, index) {\n    let elHover = el.querySelector('a')\n\n    // remove exist listener\n    this._off(el, 'mouseover', this.handlerOver[index])\n    this._off(el, 'mouseout', this.handlerOut[index])\n    this._off(elHover, 'focus', this.handlerOver[index])\n    this._off(elHover, 'blur', this.handlerOut[index])\n    this._off(el, 'click', this.handlerClick[index])\n\n    // handler listener\n    this.handlerOver[index] = this.openMenu.bind(this, el)\n    this.handlerOut[index] = this.closeMenu.bind(this, el)\n    this.handlerClick[index] = this.toggleMenu.bind(this, el)\n\n    // add listener\n    if (this.isHoverable()) {\n      if (this._hasChild(el)) {\n        this._hover(el, this.handlerOver[index], this.handlerOut[index])\n        this._hover(elHover, this.handlerOver[index], this.handlerOut[index])\n      }\n    } else {\n      this._on(el, 'click', this.handlerClick[index])\n    }\n  }\n\n  /**\n   * Handle for menu items interactions.\n   * @private\n   * @param {Element} items - The element of menu items.\n   * @return {void}\n   */\n  _handleInteractions(items) {\n    const self = this\n\n    this.each.call(items, (el, i) => {\n      if (self._hasChild(el)) {\n        self._menuTrigger(el, i)\n      }\n\n      if(self._hasActive(el)) self.active =  el\n    })\n  }\n\n  /**\n   * Get the parent menu item text of menu to be use on menu subhead.\n   * @private\n   * @param {Element} el - The target element.\n   * @return {void}\n   */\n  _getSubhead(el) {\n    return el.querySelector('.menu-text').textContent\n  }\n\n  /**\n   * Generate the subhead element for each child menu.\n   * @private\n   * @return {void}\n   */\n  _generateSubhead() {\n    const self = this\n    let menus = this.selector.children\n    let link, menu, subhead, label\n    this.each.call(menus, el => {\n      self.each.call(el.children, child => {\n        if (self._hasChild(child)) {\n          self.each.call(child.children, cc => {\n            if(self._hasClass(cc, 'menu-link')) link = cc\n          })\n\n          menu = link.nextElementSibling\n          subhead = document.createElement('li')\n          label = document.createTextNode(self._getSubhead(link))\n          subhead.appendChild(label)\n          self._addClass(subhead, 'menu-subhead')\n\n          menu.insertBefore(subhead, menu.firstChild)\n        }\n      })\n    })\n  }\n\n  /**\n   * Handle menu link tabindex depend on parent states.\n   * @return {void}\n   */\n  _handleTabIndex () {\n    const self = this\n    this.each.call(this.items, el => {\n      let container = el.parentNode.parentNode\n      if (!self._isLevelMenu(el)) {\n        el.querySelector('a').setAttribute('tabindex', '-1')\n      }\n      if (self._hasActive(container) || self._hasOpen(container)) {\n        el.querySelector('a').removeAttribute('tabindex')\n      }\n    })\n  }\n\n  /**\n   * Animate slide menu item.\n   * @private\n   * @param  {Object} el - The target element.\n   * @param  {String} direction - Up/Down slide direction.\n   * @param  {Number} speed - Animation Speed in millisecond.\n   * @param  {String} easing - CSS Animation effect.\n   * @return {Promise} resolve\n   */\n  _slide(el, direction, speed, easing) {\n    speed = speed || 300\n    easing = easing || 'ease'\n    let self = this\n    let menu = el.querySelector('.menu')\n    let es = window.getComputedStyle(el)['height']\n    // wait to resolve\n    let walkSpeed = speed + 50\n    // wait to clean style attribute\n    let clearSpeed = walkSpeed + 100\n\n    menu.style.transition = `height ${speed}ms ${easing}, opacity ${speed/2}ms ${easing}, visibility ${speed/2}ms ${easing}`\n\n    // slideDown\n    if (direction === 'down') {\n      // element\n      el.style.overflow = 'hidden'\n      el.style.height = es\n      // menu\n      menu.style.height = 'auto'\n      // get the current menu height\n      let height = window.getComputedStyle(menu)['height']\n      menu.style.height = 0\n      menu.style.visibility = 'hidden'\n      menu.style.opacity = 0\n      // remove element style\n      el.style.overflow = ''\n      el.style.height = ''\n\n      setTimeout(function() {\n        menu.style.height = height\n        menu.style.opacity = 1\n        menu.style.visibility = 'visible'\n      }, 0)\n    } else if (direction === 'up') {\n      // get the menu height\n      let height = window.getComputedStyle(menu)['height']\n      menu.style.height = height\n      menu.style.visibility = 'visible'\n      menu.style.opacity = 1\n\n      setTimeout(function() {\n        menu.style.height = 0\n        menu.style.visibility = 'hidden'\n        menu.style.opacity = 0\n      }, 0)\n    }\n\n    let done = new Promise(function(resolve) {\n      // remove the temporary styles\n      setTimeout(function() {\n        resolve(el)\n        // emit event\n        self._emit('menu:slide' + direction)\n      }, walkSpeed)\n    })\n\n    // remove styles after done has resolve\n    setTimeout(function() {\n      menu.removeAttribute('style')\n    }, clearSpeed)\n\n    return done\n  }\n\n  /** Public methods */\n  /**\n   * The first process that called after constructs the StackedMenu instance.\n   * @public\n   * @fires StackedMenu#menu:init\n   * @return {void}\n   */\n  init() {\n    const self = this\n    let opts = this.options\n\n    this._addClass(this.selector, opts.selectorClass)\n\n    // generate subhead\n    this._generateSubhead()\n\n    // implement compact feature\n    this.compact(opts.compact)\n    // implement hoverable feature\n    this.hoverable(opts.hoverable)\n\n    // handle menu link tabindex\n    this._handleTabIndex()\n\n    // handle menu click with or without Turbolinks\n    this._handleNavigation(self)\n\n    // close on outside click, only on collapsible with compact mode\n    this._on(document.body, 'click', function () {\n      if (!self.isHoverable() && self.isCompact()) {\n        // handle listener\n        self.closeAllMenu()\n      }\n    })\n\n    // on ready state\n    this._onReady(() => {\n\n      /**\n       * This event is fired when the Menu has completed init.\n       *\n       * @event StackedMenu#menu:init\n       * @type {Object}\n       * @property {Object} data - The StackedMenu data instance.\n       *\n       * @example\n       * document.querySelector('#stacked-menu').addEventListener('menu:init', function(e) {\n       *   console.log(e.data);\n       * });\n       * @example <caption>Or using jQuery:</caption>\n       * $('#stacked-menu').on('menu:init', function() {\n       *   console.log('fired on menu:init!!');\n       * });\n       */\n      self._emit('menu:init')\n    })\n  }\n\n  /**\n   * Open/show the target menu item. This method didn't take effect to an active item if not on compact mode.\n   * @public\n   * @fires StackedMenu#menu:open\n   * @param {Element} el - The target element.\n   * @param {Boolean} emiter - are the element will fire menu:open or not.\n   * @return {Object} The StackedMenu instance.\n   *\n   * @example\n   * var menuItem2 = menu.querySelectorAll('.menu-item.has-child')[1];\n   * menu.openMenu(menuItem2);\n   */\n  openMenu(el, emiter = true) {\n    // prevent open on active item if not on compact mode\n    if(this._hasActive(el) && !this.isCompact()) return\n    const self = this\n    let blockedSlide = this._isLevelMenu(el) && this.isCompact()\n\n    // open menu\n    if (this.isHoverable() || blockedSlide) {\n      this._addClass(el, this.classes.hasOpen)\n      // handle tabindex\n      this._handleTabIndex()\n    } else {\n      // slide down\n      this._slide(el, 'down', 150, 'linear').then(function() {\n        self._addClass(el, self.classes.hasOpen)\n        // handle tabindex\n        self._handleTabIndex()\n      })\n    }\n\n    this.open.push(el)\n\n    // child menu behavior\n    if (this.isHoverable() || (this.isCompact() && !this.hoverable())) {\n      const clientHeight = document.documentElement.clientHeight\n      const child = el.querySelector('.menu')\n      const pos = child.getBoundingClientRect()\n      const tolerance = pos.height - 20\n      const bottom = clientHeight - pos.top\n      const transformOriginX = this.options.align === 'left' ? '100%' : '0px'\n\n      if (pos.top >= 500 || tolerance >= bottom) {\n        child.style.top = 'auto'\n        child.style.bottom = 0\n        child.style.transformOrigin = `${transformOriginX} 100% 0`\n      }\n    }\n\n    /**\n     * This event is fired when the Menu has open.\n     *\n     * @event StackedMenu#menu:open\n     * @type {Object}\n     * @property {Object} data - The StackedMenu data instance.\n     *\n     * @example\n     * document.querySelector('#stacked-menu').addEventListener('menu:open', function(e) {\n     *   console.log(e.data);\n     * });\n     * @example <caption>Or using jQuery:</caption>\n     * $('#stacked-menu').on('menu:open', function() {\n     *   console.log('fired on menu:open!!');\n     * });\n     */\n    if (emiter) {\n      this._emit('menu:open')\n    }\n\n    return this\n  }\n\n  /**\n   * Close/hide the target menu item.\n   * @public\n   * @fires StackedMenu#menu:close\n   * @param {Element} el - The target element.\n   * @param {Boolean} emiter - are the element will fire menu:open or not.\n   * @return {Object} The StackedMenu instance.\n   *\n   * @example\n   * var menuItem2 = menu.querySelectorAll('.menu-item.has-child')[1];\n   * menu.closeMenu(menuItem2);\n   */\n  closeMenu(el, emiter = true) {\n    const self = this\n    let blockedSlide = this._isLevelMenu(el) && this.isCompact()\n    // open menu\n    if (this.isHoverable() || blockedSlide) {\n      this._removeClass(el, this.classes.hasOpen)\n      // handle tabindex\n      this._handleTabIndex()\n    } else {\n      if (!this._hasActive(el)) {\n        // slide up\n        this._slide(el, 'up', 150, 'linear').then(function() {\n          self._removeClass(el, self.classes.hasOpen)\n          // handle tabindex\n          self._handleTabIndex()\n        })\n      }\n    }\n\n    this.each.call(this.open, (v, i) => {\n      if (el == v) self.open.splice(i, 1)\n    })\n\n    // remove child menu behavior style\n    if (this.isHoverable() || (this.isCompact() && !this.hoverable())) {\n      const child = el.querySelector('.menu')\n\n      child.style.top = ''\n      child.style.bottom = ''\n      child.style.transformOrigin = ''\n    }\n\n    /**\n     * This event is fired when the Menu has close.\n     *\n     * @event StackedMenu#menu:close\n     * @type {Object}\n     * @property {Object} data - The StackedMenu data instance.\n     *\n     * @example\n     * document.querySelector('#stacked-menu').addEventListener('menu:close', function(e) {\n     *   console.log(e.data);\n     * });\n     * @example <caption>Or using jQuery:</caption>\n     * $('#stacked-menu').on('menu:close', function() {\n     *   console.log('fired on menu:close!!');\n     * });\n     */\n    if (emiter) {\n      this._emit('menu:close')\n    }\n\n    return this\n  }\n\n  /**\n   * Close all opened menu items.\n   * @public\n   * @fires StackedMenu#menu:close\n   * @return {Object} The StackedMenu instance.\n   *\n   * @example\n   * menu.closeAllMenu();\n   */\n  closeAllMenu() {\n    const self = this\n    this.each.call(this.items, el => {\n      if (self._hasOpen(el)) {\n        self.closeMenu(el, false)\n      }\n    })\n\n    return this\n  }\n\n  /**\n   * Toggle open/close the target menu item.\n   * @public\n   * @fires StackedMenu#menu:open\n   * @fires StackedMenu#menu:close\n   * @param {Element} el - The target element.\n   * @return {Object} The StackedMenu instance.\n   *\n   * @example\n   * var menuItem2 = menu.querySelectorAll('.menu-item.has-child')[1];\n   * menu.toggleMenu(menuItem2);\n   */\n  toggleMenu(el) {\n    const method = this._hasOpen(el) ? 'closeMenu': 'openMenu'\n    const self = this\n    let itemParent, elParent\n\n    // close other\n    this.each.call(this.items, item => {\n      itemParent = item.parentNode.parentNode\n      itemParent = self._hasClass(itemParent, 'menu-item') ? itemParent : itemParent.parentNode\n      elParent = el.parentNode.parentNode\n      elParent = self._hasClass(elParent, 'menu-item') ? elParent : elParent.parentNode\n\n      // close other except parents that has open state and an active item\n      if(!self._hasOpen(elParent) && self._hasChild(itemParent)) {\n        if (self.options.closeOther || (!self.options.closeOther && self.isCompact())) {\n          if (self._hasOpen(itemParent)) {\n            self.closeMenu(itemParent, false)\n          }\n        }\n      }\n    })\n    // open target el\n    if (this._hasChild(el)) this[method](el)\n\n    return this\n  }\n\n  /**\n   * Set the open menu position to `left` or `right`.\n   * @public\n   * @fires StackedMenu#menu:align\n   * @param  {String} position - The position that will be set to the Menu.\n   * @return {Object} The StackedMenu instance.\n   *\n   * @example\n   * menu.align('left');\n   */\n  align(position) {\n    const method = (position === 'left') ? '_addClass': '_removeClass'\n    const classes = this.classes\n\n    this[method](this.selector, classes.alignLeft)\n\n    this.options.align = position\n\n    /**\n     * This event is fired when the Menu has changed align position.\n     *\n     * @event StackedMenu#menu:align\n     * @type {Object}\n     * @property {Object} data - The StackedMenu data instance.\n     *\n     * @example\n     * document.querySelector('#stacked-menu').addEventListener('menu:align', function(e) {\n     *   console.log(e.data);\n     * });\n     * @example <caption>Or using jQuery:</caption>\n     * $('#stacked-menu').on('menu:align', function() {\n     *   console.log('fired on menu:align!!');\n     * });\n     */\n    this._emit('menu:align')\n\n    return this\n  }\n\n  /**\n   * Determine whether the Menu is currently compact.\n   * @public\n   * @return {Boolean} is compact.\n   *\n   * @example\n   * var isCompact = menu.isCompact();\n   */\n  isCompact() {\n    return this.options.compact\n  }\n\n  /**\n   * Toggle the Menu compact mode.\n   * @public\n   * @fires StackedMenu#menu:compact\n   * @param  {Boolean} isCompact - The compact mode.\n   * @return {Object} The StackedMenu instance.\n   *\n   * @example\n   * menu.compact(true);\n   */\n  compact(isCompact) {\n    const method = (isCompact) ? '_addClass': '_removeClass'\n    const classes = this.classes\n\n    this[method](this.selector, classes.compact)\n\n    this.options.compact = isCompact\n    // reset interactions\n    this._handleInteractions(this.items)\n\n    /**\n     * This event is fired when the Menu has completed toggle compact mode.\n     *\n     * @event StackedMenu#menu:compact\n     * @type {Object}\n     * @property {Object} data - The StackedMenu data instance.\n     *\n     * @example\n     * document.querySelector('#stacked-menu').addEventListener('menu:compact', function(e) {\n     *   console.log(e.data);\n     * });\n     * @example <caption>Or using jQuery:</caption>\n     * $('#stacked-menu').on('menu:compact', function() {\n     *   console.log('fired on menu:compact!!');\n     * });\n     */\n    this._emit('menu:compact')\n\n    return this\n  }\n\n  /**\n   * Determine whether the Menu is currently hoverable.\n   * @public\n   * @return {Boolean} is hoverable.\n   *\n   * @example\n   * var isHoverable = menu.isHoverable();\n   */\n  isHoverable() {\n    return this.options.hoverable\n  }\n\n  /**\n   * Toggle the Menu (interaction) hoverable.\n   * @public\n   * @fires StackedMenu#menu:hoverable\n   * @param  {Boolean} isHoverable - `true` for hoverable and `false` for collapsible (clickable).\n   * @return {Object} The StackedMenu instance.\n   *\n   * @example\n   * menu.hoverable(true);\n   */\n  hoverable(isHoverable) {\n    const classes = this.classes\n\n    if (isHoverable) {\n      this._addClass(this.selector, classes.hoverable)\n      this._removeClass(this.selector, classes.collapsible)\n    } else {\n      this._addClass(this.selector, classes.collapsible)\n      this._removeClass(this.selector, classes.hoverable)\n    }\n\n    this.options.hoverable = isHoverable\n    // reset interactions\n    this._handleInteractions(this.items)\n\n    /**\n     * This event is fired when the Menu has completed toggle hoverable.\n     *\n     * @event StackedMenu#menu:hoverable\n     * @type {Object}\n     * @property {Object} data - The StackedMenu data instance.\n     *\n     * @example\n     * document.querySelector('#stacked-menu').addEventListener('menu:hoverable', function(e) {\n     *   console.log(e.data);\n     * });\n     * @example <caption>Or using jQuery:</caption>\n     * $('#stacked-menu').on('menu:hoverable', function() {\n     *   console.log('fired on menu:hoverable!!');\n     * });\n     */\n    this._emit('menu:hoverable')\n\n    return this\n  }\n}\n\nexport default StackedMenu\n"], "names": ["StackedMenu", "options", "this", "compact", "hoverable", "closeOther", "align", "selector", "selectorClass", "_extend", "document", "querySelector", "items", "querySelectorAll", "Array", "prototype", "for<PERSON>ach", "cb", "arg", "TypeError", "let", "array", "i", "length", "call", "each", "classes", "alignLeft", "collapsible", "<PERSON><PERSON><PERSON><PERSON>", "hasActive", "hasOpen", "active", "open", "turbolinksAvailable", "window", "Turbolinks", "supported", "handlerClickDoc", "handlerOver", "handlerOut", "handlerClick", "init", "_onReady", "handler", "readyState", "addEventListener", "_handleNavigation", "self", "el", "_on", "e", "stopPropagation", "preventDefault", "_has<PERSON><PERSON>d", "visit", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "href", "obj", "args", "arguments", "key", "hasOwnProperty", "_emit", "type", "data", "createEvent", "initEvent", "createEventObject", "eventType", "eventName", "dispatchEvent", "fireEvent", "_hover", "tagName", "types", "split", "_off", "removeEventListener", "_addClass", "className", "classList", "add", "_removeClass", "remove", "replace", "RegExp", "join", "_hasClass", "contains", "test", "_hasActive", "_hasOpen", "_isLevelMenu", "parentNode", "_menuTrigger", "index", "elHover", "openMenu", "bind", "closeMenu", "toggleMenu", "isHoverable", "_handleInteractions", "const", "_getSubhead", "textContent", "_generateSubhead", "link", "menu", "subhead", "label", "menus", "children", "child", "cc", "nextElement<PERSON><PERSON>ling", "createElement", "createTextNode", "append<PERSON><PERSON><PERSON>", "insertBefore", "<PERSON><PERSON><PERSON><PERSON>", "_handleTabIndex", "container", "setAttribute", "removeAttribute", "_slide", "direction", "speed", "easing", "es", "getComputedStyle", "walkSpeed", "clearSpeed", "style", "transition", "overflow", "height", "visibility", "opacity", "setTimeout", "done", "Promise", "resolve", "opts", "body", "isCompact", "closeAllMenu", "emiter", "blockedSlide", "then", "push", "clientHeight", "documentElement", "pos", "getBoundingClientRect", "tolerance", "bottom", "top", "transformOriginX", "transform<PERSON><PERSON>in", "v", "splice", "itemParent", "<PERSON><PERSON><PERSON><PERSON>", "method", "item", "position"], "mappings": "uCAoBA,IAAMA,GAOJ,SAAYC,GAkBZC,KAAOD,SACLE,SAAW,EACXC,WAAa,EACbC,YAAc,EACdC,MAAS,QACTC,SAAY,gBACZC,cAAiB,gBAIjBN,KAAKD,QAAUC,KAAKO,WAAYP,KAAKD,QAASA,GAM9CC,KAAKK,SAAWG,SAASC,cAAcT,KAAKD,QAAQM,UAMpDL,KAAKU,MAAQV,KAAKK,SAAWL,KAAKK,SAASM,iBAAiB,cAAgB,KAGvEC,MAAMC,UAAUC,UACnBF,MAAMC,UAAUC,QAAU,SAAiBC,EAAIC,GAC7C,GAAiB,kBAAPD,GAAmB,KAAM,IAAIE,WAAaF,uBAEpDG,IAAIC,GAAQnB,IACZgB,GAAMA,GAAOhB,IACb,KAAIkB,GAAIE,GAAI,EAAGA,EAAID,EAAME,OAAQD,IAC/BL,EAAGO,KAAKN,EAAKG,EAAMC,GAAIA,EAAGD,KAIlCnB,KAAOuB,KAAOX,MAAMC,UAAUC,QAO9Bd,KAAOwB,SACLC,UAAazB,KAAKD,QAAQO,cAAgB,YAC1CL,QAAWD,KAAKD,QAAQO,cAAgB,eACxCoB,YAAe1B,KAAKD,QAAQO,cAAgB,mBAC5CJ,UAAaF,KAAKD,QAAQO,cAAgB,iBAC1CqB,SAAY,YACZC,UAAa,aACbC,QAAW,YAQX7B,KAAK8B,OAAS,KAMd9B,KAAK+B,QAML/B,KAAKgC,oBAAmD,gBAAtBC,QAAOC,YAA2BD,OAAOC,WAAWC,UAGtFnC,KAAKoC,mBACLpC,KAAKqC,eACLrC,KAAKsC,cACLtC,KAAKuC,gBAGPvC,KAAOwC,cAUT1C,aAAE2C,kBAASC,GACmB,WAAvBlC,SAASmC,WACVD,IAEFlC,SAAWoC,iBAAiB,mBAAoBF,GAAS,IAU7D5C,YAAE+C,2BAAkBC,GAChBA,EAAKvB,KAAKD,KAAKtB,KAAKU,eAAQqC,GAC5BD,EAAOE,IAAID,EAAI,QAAS,SAASE,GAE/BA,EAAIC,kBAEJJ,EAAOd,qBAAsBiB,EAAEE,iBAGzBL,EAAKM,UAAUL,GACnBD,EAAOd,qBAA6BiB,EAAEE,iBAEpCL,EAAKd,qBAAsBC,OAAOC,WAAWmB,MAAMN,EAAGO,kBAAkBC,WAalFzD,YAAES,iBAAQiD,GACNA,EAAMA,KAEN,KAAKtC,GADCuC,GAAOC,UACJtC,EAAI,EAAGA,EAAIqC,EAAKpC,OAAQD,IAC/B,GAAKqC,EAAKrC,GACZ,IAAOF,GAAIyC,KAAOF,GAAKrC,GACfqC,EAAKrC,GAAGwC,eAAeD,KACzBH,EAAIG,GAAOF,EAAKrC,GAAGuC,GAGzB,OAAOH,IAUX1D,YAAE+D,eAAMC,EAAMC,GACZ,GAAMd,EACAzC,UAASwD,aACXf,EAAIzC,SAASwD,YAAY,UACvBC,UAAUH,GAAM,GAAM,IAExBb,EAAIzC,SAAS0D,qBACXC,UAAYL,EAEhBb,EAAEmB,UAAYN,EACdb,EAAEc,KAAOA,GAAQ/D,KAEnBQ,SAAWwD,YACLhE,KAAKK,SAASgE,cAAcpB,GAC5BjD,KAAKK,SAASiE,UAAU,KAAOR,EAAMb,IAW7CnD,YAAEyE,gBAAOxB,EAAIV,EAAaC,GACH,MAAfS,EAAGyB,SACPxE,KAAOgD,IAAID,EAAI,QAASV,GACxBrC,KAAOgD,IAAID,EAAI,OAAQT,KAEvBtC,KAAOgD,IAAID,EAAI,YAAaV,GAC5BrC,KAAOgD,IAAID,EAAI,WAAYT,KAY/BxC,YAAEkD,aAAID,EAAIe,EAAMpB,GAEZ,IAAKxB,GADDuD,GAAQX,EAAKY,MAAM,KACdtD,EAAI,EAAGA,EAAIqD,EAAMpD,OAAQD,IAChC2B,EAAGd,OAAOW,iBAAmB,mBAAqB,eAAgBX,OAAOW,iBAAmB6B,EAAMrD,QAAUqD,EAAMrD,GAAOsB,GAAS,IAYxI5C,YAAE6E,cAAK5B,EAAIe,EAAMpB,GAEb,IAAKxB,GADDuD,GAAQX,EAAKY,MAAM,KACdtD,EAAI,EAAGA,EAAIqD,EAAMpD,OAAQD,IAChC2B,EAAGd,OAAO2C,oBAAsB,sBAAwB,eAAgB3C,OAAO2C,oBAAsBH,EAAMrD,QAAUqD,EAAMrD,GAAOsB,GAAS,IAWjJ5C,YAAE+E,mBAAU9B,EAAI+B,GAEZ,IAAK5D,GADDM,GAAUsD,EAAUJ,MAAM,KACrBtD,EAAI,EAAGA,EAAII,EAAQH,OAAQD,IAC9B2B,EAAGgC,UAAWhC,EAAGgC,UAAUC,IAAIxD,EAAQJ,IACtC2B,EAAGvB,QAAQJ,IAAM,IAAMI,EAAQJ,IAW1CtB,YAAEmF,sBAAalC,EAAI+B,GAEf,IAAK5D,GADDM,GAAUsD,EAAUJ,MAAM,KACrBtD,EAAI,EAAGA,EAAII,EAAQH,OAAQD,IAC9B2B,EAAGgC,UAAWhC,EAAGgC,UAAUG,OAAO1D,EAAQJ,IACzC2B,EAAGvB,QAAQJ,GAAK2B,EAAGvB,QAAQJ,GAAG+D,QAAQ,GAAIC,QAAO,UAAY5D,EAAQJ,GAAGsD,MAAM,KAAKW,KAAK,KAAO,UAAW,MAAO,MAW5HvF,YAAEwF,mBAAUvC,EAAI+B,GACZ,MAAI/B,GAAGgC,UAAkBhC,EAAGgC,UAAUQ,SAAST,GACxC,GAAIM,QAAO,QAAUN,EAAY,QAAS,MAAMU,KAAKzC,EAAG+B,YASnEhF,YAAEsD,mBAAUL,GACR,MAAO/C,MAAKsF,UAAUvC,EAAI/C,KAAKwB,QAAQG,WAS3C7B,YAAE2F,oBAAW1C,GACT,MAAO/C,MAAKsF,UAAUvC,EAAI/C,KAAKwB,QAAQI,YAS3C9B,YAAE4F,kBAAS3C,GACP,MAAO/C,MAAKsF,UAAUvC,EAAI/C,KAAKwB,QAAQK,UAS3C/B,YAAE6F,sBAAc5C,GACZ,MAAO/C,MAAKsF,UAAUvC,EAAG6C,WAAWA,WAAY5F,KAAKD,QAAQO,gBAUjER,YAAE+F,sBAAa9C,EAAI+C,GACjB,GAAMC,GAAUhD,EAAGtC,cAAc,IAG/BT,MAAK2E,KAAK5B,EAAI,YAAa/C,KAAKqC,YAAYyD,IAC5C9F,KAAK2E,KAAK5B,EAAI,WAAY/C,KAAKsC,WAAWwD,IAC1C9F,KAAK2E,KAAKoB,EAAS,QAAS/F,KAAKqC,YAAYyD,IAC7C9F,KAAK2E,KAAKoB,EAAS,OAAQ/F,KAAKsC,WAAWwD,IAC3C9F,KAAK2E,KAAK5B,EAAI,QAAS/C,KAAKuC,aAAauD,IAGzC9F,KAAKqC,YAAYyD,GAAS9F,KAAKgG,SAASC,KAAKjG,KAAM+C,GACnD/C,KAAKsC,WAAWwD,GAAS9F,KAAKkG,UAAUD,KAAKjG,KAAM+C,GACnD/C,KAAKuC,aAAauD,GAAS9F,KAAKmG,WAAWF,KAAKjG,KAAM+C,GAGlD/C,KAAKoG,cACHpG,KAAKoD,UAAUL,KACjB/C,KAAKuE,OAAOxB,EAAI/C,KAAKqC,YAAYyD,GAAQ9F,KAAKsC,WAAWwD,IACzD9F,KAAKuE,OAAOwB,EAAS/F,KAAKqC,YAAYyD,GAAQ9F,KAAKsC,WAAWwD,KAGhE9F,KAAKgD,IAAID,EAAI,QAAS/C,KAAKuC,aAAauD,KAU9ChG,YAAEuG,6BAAoB3F,GAClB4F,GAAMxD,GAAO9C,IAEbA,MAAKuB,KAAKD,KAAKZ,WAAQqC,EAAI3B,GACrB0B,EAAKM,UAAUL,IACjBD,EAAK+C,aAAa9C,EAAI3B,GAGrB0B,EAAK2C,WAAW1C,KAAKD,EAAKhB,OAAQiB,MAU3CjD,YAAEyG,qBAAYxD,GACZ,MAASA,GAAGtC,cAAc,cAAc+F,aAQ1C1G,YAAE2G,4BACEH,GAEII,GAAMC,EAAMC,EAASC,EAFnB/D,EAAO9C,KACT8G,EAAQ9G,KAAKK,SAAS0G,QAE5B/G,MAAOuB,KAAKD,KAAKwF,WAAO/D,GACpBD,EAAKvB,KAAKD,KAAKyB,EAAGgE,kBAAUC,GACtBlE,EAAKM,UAAU4D,KACjBlE,EAAKvB,KAAKD,KAAK0F,EAAMD,kBAAUE,GAC1BnE,EAAKwC,UAAU2B,EAAI,eAAcP,EAAOO,KAG7CN,EAAOD,EAAKQ,mBACZN,EAAUpG,SAAS2G,cAAc,MACjCN,EAAQrG,SAAS4G,eAAetE,EAAKyD,YAAYG,IACjDE,EAAQS,YAAYR,GACpB/D,EAAK+B,UAAU+B,EAAS,gBAE1BD,EAAOW,aAAaV,EAASD,EAAKY,kBAU1CzH,YAAE0H,2BACElB,GAAMxD,GAAO9C,IACbA,MAAKuB,KAAKD,KAAKtB,KAAKU,eAAOqC,GAC3B,GAAM0E,GAAY1E,EAAG6C,WAAWA,UACzB9C,GAAK6C,aAAa5C,IACrBA,EAAGtC,cAAc,KAAKiH,aAAa,WAAY,OAE7C5E,EAAK2C,WAAWgC,IAAc3E,EAAK4C,SAAS+B,KAChD1E,EAAKtC,cAAc,KAAKkH,gBAAgB,eAc9C7H,YAAE8H,gBAAO7E,EAAI8E,EAAWC,EAAOC,GAC3BD,EAAQA,GAAS,IACjBC,EAASA,GAAU,MACnB7G,IAAI4B,GAAO9C,KACP2G,EAAO5D,EAAGtC,cAAc,SACxBuH,EAAK/F,OAAOgG,iBAAiBlF,GAAY,OAEzCmF,EAAYJ,EAAQ,GAEpBK,EAAaD,EAAY,GAK7B,IAHFvB,EAAOyB,MAAMC,WAAa,UAAUP,QAAWC,eAAmBD,EAAM,QAAOC,kBAAsBD,EAAM,QAAOC,EAG9F,SAAdF,EAAsB,CAExB9E,EAAGqF,MAAME,SAAW,SACpBvF,EAAGqF,MAAMG,OAASP,EAElBrB,EAAKyB,MAAMG,OAAS,MAEpBrH,IAAIqH,GAAStG,OAAOgG,iBAAiBtB,GAAc,MACnDA,GAAKyB,MAAMG,OAAS,EACpB5B,EAAKyB,MAAMI,WAAa,SACxB7B,EAAKyB,MAAMK,QAAU,EAErB1F,EAAGqF,MAAME,SAAW,GACpBvF,EAAGqF,MAAMG,OAAS,GAEpBG,WAAa,WACT/B,EAAKyB,MAAMG,OAASA,EACpB5B,EAAKyB,MAAMK,QAAU,EACrB9B,EAAKyB,MAAMI,WAAa,WACvB,OACE,IAAkB,OAAdX,EAAoB,CAE7B3G,GAAIqH,GAAStG,OAAOgG,iBAAiBtB,GAAc,MACnDA,GAAKyB,MAAMG,OAASA,EACpB5B,EAAKyB,MAAMI,WAAa,UACxB7B,EAAKyB,MAAMK,QAAU,EAEvBC,WAAa,WACT/B,EAAKyB,MAAMG,OAAS,EACpB5B,EAAKyB,MAAMI,WAAa,SACxB7B,EAAKyB,MAAMK,QAAU,GACpB,GAGP,GAAME,GAAO,GAAIC,SAAQ,SAASC,GAEhCH,WAAa,WACXG,EAAU9F,GAERD,EAAKe,MAAM,aAAegE,IACzBK,IAQL,OAJFQ,YAAa,WACT/B,EAAKgB,gBAAgB,UACpBQ,GAEIQ,GAUX7I,YAAE0C,gBACE8D,GAAMxD,GAAO9C,KACT8I,EAAO9I,KAAKD,OAElBC,MAAO6E,UAAU7E,KAAKK,SAAUyI,EAAKxI,eAGrCN,KAAOyG,mBAGLzG,KAAKC,QAAQ6I,EAAK7I,SAElBD,KAAKE,UAAU4I,EAAK5I,WAGtBF,KAAOwH,kBAGLxH,KAAK6C,kBAAkBC,GAGzB9C,KAAOgD,IAAIxC,SAASuI,KAAM,QAAS,YAC1BjG,EAAKsD,eAAiBtD,EAAKkG,aAEhClG,EAAOmG,iBAKTjJ,KAAKyC,oBAkBHK,EAAKe,MAAM,gBAgBjB/D,YAAEkG,kBAASjD,EAAImG,GAEX,mBAFoB,IAEjBlJ,KAAKyF,WAAW1C,IAAQ/C,KAAKgJ,YAAhC,CACA1C,GAAMxD,GAAO9C,KACTmJ,EAAenJ,KAAK2F,aAAa5C,IAAO/C,KAAKgJ,WAmBjD,IAhBIhJ,KAAKoG,eAAiB+C,GAC1BnJ,KAAO6E,UAAU9B,EAAI/C,KAAKwB,QAAQK,SAElC7B,KAAOwH,mBAGLxH,KAAK4H,OAAO7E,EAAI,OAAQ,IAAK,UAAUqG,KAAK,WAC5CtG,EAAO+B,UAAU9B,EAAID,EAAKtB,QAAQK,SAElCiB,EAAO0E,oBAITxH,KAAK+B,KAAKsH,KAAKtG,GAGX/C,KAAKoG,eAAkBpG,KAAKgJ,cAAgBhJ,KAAKE,YAAc,CACnE,GAAQoJ,GAAe9I,SAAS+I,gBAAgBD,aACxCtC,EAAQjE,EAAGtC,cAAc,SACzB+I,EAAMxC,EAAMyC,wBACZC,EAAYF,EAAIjB,OAAS,GACzBoB,EAASL,EAAeE,EAAII,IAC5BC,EAA0C,SAAvB7J,KAAKD,QAAQK,MAAmB,OAAS,OAE9DoJ,EAAII,KAAO,KAAOF,GAAaC,KACjC3C,EAAMoB,MAAMwB,IAAM,OAClB5C,EAAMoB,MAAMuB,OAAS,EACvB3C,EAAQoB,MAAM0B,gBAAqBD,aAwBrC,MAJIX,IACFlJ,KAAK6D,MAAM,aAGN7D,OAeXF,YAAEoG,mBAAUnD,EAAImG,mBAAS,EACrB5C,IAAMxD,GAAO9C,KACTmJ,EAAenJ,KAAK2F,aAAa5C,IAAO/C,KAAKgJ,WAsBjD,IApBIhJ,KAAKoG,eAAiB+C,GAC1BnJ,KAAOiF,aAAalC,EAAI/C,KAAKwB,QAAQK,SAErC7B,KAAOwH,mBAEAxH,KAAKyF,WAAW1C,IAEnB/C,KAAK4H,OAAO7E,EAAI,KAAM,IAAK,UAAUqG,KAAK,WAC1CtG,EAAOmC,aAAalC,EAAID,EAAKtB,QAAQK,SAErCiB,EAAO0E,oBAKXxH,KAAKuB,KAAKD,KAAKtB,KAAK+B,cAAOgI,EAAG3I,GACxB2B,GAAMgH,GAAGjH,EAAKf,KAAKiI,OAAO5I,EAAG,KAI/BpB,KAAKoG,eAAkBpG,KAAKgJ,cAAgBhJ,KAAKE,YAAc,CACnE,GAAQ8G,GAAQjE,EAAGtC,cAAc,QAE/BuG,GAAMoB,MAAMwB,IAAM,GAClB5C,EAAMoB,MAAMuB,OAAS,GACrB3C,EAAMoB,MAAM0B,gBAAkB,GAuBhC,MAJIZ,IACFlJ,KAAK6D,MAAM,cAGN7D,MAYXF,YAAEmJ,wBACE3C,GAAMxD,GAAO9C,IAOb,OANAA,MAAKuB,KAAKD,KAAKtB,KAAKU,eAAOqC,GACrBD,EAAK4C,SAAS3C,IAChBD,EAAKoD,UAAUnD,GAAI,KAIhB/C,MAeXF,YAAEqG,oBAAWpD,GACTuD,GAEI2D,GAAYC,EAFVC,EAASnK,KAAK0F,SAAS3C,GAAM,YAAa,WAC1CD,EAAO9C,IAsBb,OAlBAA,MAAKuB,KAAKD,KAAKtB,KAAKU,eAAO0J,GACzBH,EAAaG,EAAKxE,WAAWA,WAC7BqE,EAAanH,EAAKwC,UAAU2E,EAAY,aAAeA,EAAaA,EAAWrE,WAC/EsE,EAAWnH,EAAG6C,WAAWA,WACzBsE,EAAWpH,EAAKwC,UAAU4E,EAAU,aAAeA,EAAWA,EAAStE,YAGnE9C,EAAK4C,SAASwE,IAAapH,EAAKM,UAAU6G,KACxCnH,EAAK/C,QAAQI,aAAgB2C,EAAK/C,QAAQI,YAAc2C,EAAKkG,cAC3DlG,EAAK4C,SAASuE,IAChBnH,EAAKoD,UAAU+D,GAAY,KAM/BjK,KAAKoD,UAAUL,IAAK/C,KAAKmK,GAAQpH,GAE9B/C,MAaXF,YAAEM,eAAMiK,GACJ/D,GAAM6D,GAAuB,SAAbE,EAAuB,YAAa,eAC9C7I,EAAUxB,KAAKwB,OAwBrB,OAtBAxB,MAAKmK,GAAQnK,KAAKK,SAAUmB,EAAQC,WAEpCzB,KAAKD,QAAQK,MAAQiK,EAkBrBrK,KAAK6D,MAAM,cAEJ7D,MAWXF,YAAEkJ,qBACE,MAAOhJ,MAAKD,QAAQE,SAaxBH,YAAEG,iBAAQ+I,GACR,GAAQmB,GAAS,EAAc,YAAa,eACpC3I,EAAUxB,KAAKwB,OA0BrB,OAxBAxB,MAAKmK,GAAQnK,KAAKK,SAAUmB,EAAQvB,SAEpCD,KAAKD,QAAQE,QAAU+I,EAEvBhJ,KAAKqG,oBAAoBrG,KAAKU,OAkB9BV,KAAK6D,MAAM,gBAEJ7D,MAWXF,YAAEsG,uBACE,MAAOpG,MAAKD,QAAQG,WAaxBJ,YAAEI,mBAAUkG,GACRE,GAAM9E,GAAUxB,KAAKwB,OAgCrB,OA9BI4E,IACJpG,KAAO6E,UAAU7E,KAAKK,SAAUmB,EAAQtB,WACxCF,KAAOiF,aAAajF,KAAKK,SAAUmB,EAAQE,eAE3C1B,KAAO6E,UAAU7E,KAAKK,SAAUmB,EAAQE,aACxC1B,KAAOiF,aAAajF,KAAKK,SAAUmB,EAAQtB,YAG3CF,KAAKD,QAAQG,UAAYkG,EAEzBpG,KAAKqG,oBAAoBrG,KAAKU,OAkB9BV,KAAK6D,MAAM,kBAEJ7D"}