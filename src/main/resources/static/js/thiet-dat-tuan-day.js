$(document).ready(function() {
    console.log('Thiet dat tuan day page loaded');
    // Khởi tạo các component
    initComponents();
    loadInitialData();
    bindEvents();
});

function initComponents() {
    console.log('Initializing components...');

    // Khởi tạo Select2
    if (typeof $.fn.select2 !== 'undefined') {
        $('.select2').select2({
            placeholder: "-- Chọn --",
            allowClear: true
        });
        console.log('Select2 initialized');
    } else {
        console.error('Select2 not loaded');
    }

    // Khởi tạo DatePicker
    if (typeof $.fn.datepicker !== 'undefined') {
        $('.datepicker').datepicker({
            format: 'dd/mm/yyyy',
            autoclose: true,
            todayHighlight: true,
            language: 'vi',
            weekStart: 1
        });

        // Set ngày hiện tại cho công cụ tính tuần
        $('#inputNgayCanTinh').datepicker('setDate', new Date());
        console.log('DatePicker initialized');
    } else {
        console.error('DatePicker not loaded');
    }
}

function loadInitialData() {
    // Load danh sách niên khóa
    $.ajax({
        url: '/lich-giang-day/lay-nien-khoa',
        type: 'GET',
        success: function(response) {
            console.log('Response from server:', response);
            if (response.resCode === 1) {
                var select = $('#selectNienKhoa');
                select.empty().append('<option value="">-- Chọn niên khóa --</option>');

                $.each(response.resData, function(index, item) {
                    select.append('<option value="' + item.idNienKhoa + '">' +
                                item.tenNienKhoa + ' (' + item.nam + ')</option>');
                });

                select.trigger('change');
            } else {
                showMessage(response.resMsg || 'Không thể tải danh sách niên khóa', 'error');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error loading nien khoa:', xhr, status, error);
            showMessage('Lỗi khi tải danh sách niên khóa: ' + error, 'error');
        }
    });
}

function bindEvents() {
    // Khi chọn niên khóa
    $('#selectNienKhoa').on('change', function() {
        var idNienKhoa = $(this).val();
        loadHocKy(idNienKhoa);
    });

    // Khi chọn học kỳ
    $('#selectHocKy').on('change', function() {
        var idHocKy = $(this).val();
        if (idHocKy) {
            loadThongTinHocKy(idHocKy);
        } else {
            $('#thongTinHocKy').hide();
        }
    });

    // Lưu thiết đặt
    $('#btnLuu').on('click', function() {
        luuThietDat();
    });

    // Xem thông tin tuần dạy
    $('#btnXemThongTin').on('click', function() {
        xemThongTinTuanDay();
    });

    // Hiển thị công cụ tính tuần
    $('#btnTinhTuan').on('click', function() {
        $('#boxTinhTuan').toggle();
    });

    // Tính tuần cụ thể
    $('#btnTinhTuanCuThe').on('click', function() {
        tinhTuanCuThe();
    });
}

function loadHocKy(idNienKhoa) {
    var select = $('#selectHocKy');
    select.empty().append('<option value="">-- Chọn học kỳ --</option>');
    
    if (!idNienKhoa) {
        select.trigger('change');
        return;
    }

    $.ajax({
        url: '/lich-giang-day/lay-hoc-ky-theo-nien-khoa',
        type: 'GET',
        data: { idNienKhoa: idNienKhoa },
        success: function(response) {
            console.log('HocKy response:', response);
            if (response.resCode === 1) {
                $.each(response.resData, function(index, item) {
                    select.append('<option value="' + item.idHocKy + '">' +
                                item.tenHocKy + '</option>');
                });
                select.trigger('change');
            } else {
                showMessage(response.resMsg || 'Không thể tải danh sách học kỳ', 'error');
            }
        },
        error: function() {
            showMessage('Lỗi khi tải danh sách học kỳ', 'error');
        }
    });
}

function loadThongTinHocKy(idHocKy) {
    $.ajax({
        url: '/lich-giang-day/lay-thong-tin-tuan-day',
        type: 'GET',
        data: { idHocKy: idHocKy },
        success: function(response) {
            console.log('ThongTinHocKy response:', response);
            if (response.resCode === 1) {
                var data = response.resData;
                var thongTin = 'Học kỳ: ' + data.tenHocKy + '<br>';
                thongTin += 'Số tuần: ' + (data.soTuan || 'Chưa thiết đặt') + '<br>';
                thongTin += 'Ngày bắt đầu HK: ' + formatDate(data.ngayBatDau) + '<br>';
                thongTin += 'Ngày kết thúc HK: ' + formatDate(data.ngayKetThuc) + '<br>';
                
                if (data.ngayBatDauTuanDay) {
                    thongTin += 'Ngày bắt đầu tuần 1: ' + formatDate(data.ngayBatDauTuanDay) + '<br>';
                    thongTin += 'Tuần hiện tại: ' + (data.tuanHienTai || 'Không xác định');
                    
                    // Set giá trị cho input
                    $('#inputNgayBatDauTuanDay').datepicker('setDate', 
                        parseDate(data.ngayBatDauTuanDay));
                } else {
                    thongTin += '<span class="text-warning">Chưa thiết đặt tuần dạy</span>';
                }
                
                $('#thongTinText').html(thongTin);
                $('#thongTinHocKy').show();
            }
        },
        error: function() {
            showMessage('Lỗi khi tải thông tin học kỳ', 'error');
        }
    });
}

function luuThietDat() {
    var idHocKy = $('#selectHocKy').val();
    var ngayBatDauTuanDay = $('#inputNgayBatDauTuanDay').val();

    console.log('Saving thiet dat with:', {
        idHocKy: idHocKy,
        ngayBatDauTuanDay: ngayBatDauTuanDay
    });

    if (!idHocKy) {
        showMessage('Vui lòng chọn học kỳ', 'warning');
        return;
    }

    if (!ngayBatDauTuanDay) {
        showMessage('Vui lòng chọn ngày bắt đầu tuần 1', 'warning');
        return;
    }

    // Chuyển đổi định dạng ngày từ dd/mm/yyyy sang yyyy-mm-dd
    var dateParts = ngayBatDauTuanDay.split('/');
    var formattedDate = dateParts[2] + '-' + dateParts[1] + '-' + dateParts[0];

    console.log('Formatted date:', formattedDate);

    // Disable button to prevent double submission
    $('#btnLuu').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Đang lưu...');

    $.ajax({
        url: '/lich-giang-day/thiet-dat-tuan-day',
        type: 'POST',
        data: {
            idHocKy: idHocKy,
            ngayBatDauTuanDay: formattedDate
        },
        success: function(response) {
            console.log('LuuThietDat response:', response);
            if (response.resCode === 1) {
                showMessage('Thiết đặt tuần dạy thành công', 'success');
                // Reload thông tin học kỳ
                loadThongTinHocKy(idHocKy);
            } else {
                showMessage(response.resMsg || 'Có lỗi xảy ra', 'error');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error saving thiet dat:', xhr, status, error);
            console.error('Response text:', xhr.responseText);
            showMessage('Lỗi khi lưu thiết đặt: ' + error, 'error');
        },
        complete: function() {
            // Re-enable button
            $('#btnLuu').prop('disabled', false).html('<i class="fa fa-save"></i> Lưu thiết đặt');
        }
    });
}

function xemThongTinTuanDay() {
    var idHocKy = $('#selectHocKy').val();
    
    if (!idHocKy) {
        showMessage('Vui lòng chọn học kỳ', 'warning');
        return;
    }

    $.ajax({
        url: '/lich-giang-day/lay-thong-tin-tuan-day',
        type: 'GET',
        data: { idHocKy: idHocKy },
        success: function(response) {
            console.log('XemThongTinTuanDay response:', response);
            if (response.resCode === 1) {
                var data = response.resData;
                if (!data.ngayBatDauTuanDay) {
                    showMessage('Học kỳ chưa được thiết đặt tuần dạy', 'warning');
                    return;
                }

                hienThiDanhSachTuan(data);
                $('#boxThongTinTuan').show();
            } else {
                showMessage(response.resMsg || 'Có lỗi xảy ra', 'error');
            }
        },
        error: function() {
            showMessage('Lỗi khi tải thông tin tuần dạy', 'error');
        }
    });
}

function hienThiDanhSachTuan(data) {
    var tbody = $('#tableBodyTuanDay');
    tbody.empty();
    
    var soTuan = data.soTuan || 20; // Mặc định 20 tuần
    var ngayBatDau = new Date(data.ngayBatDauTuanDay);
    var ngayHienTai = new Date();
    
    for (var i = 1; i <= soTuan; i++) {
        var ngayBatDauTuan = new Date(ngayBatDau);
        ngayBatDauTuan.setDate(ngayBatDau.getDate() + (i - 1) * 7);
        
        var ngayKetThucTuan = new Date(ngayBatDauTuan);
        ngayKetThucTuan.setDate(ngayBatDauTuan.getDate() + 6);
        
        var trangThai = '';
        if (ngayKetThucTuan < ngayHienTai) {
            trangThai = '<span class="badge badge-secondary">Đã qua</span>';
        } else if (ngayBatDauTuan <= ngayHienTai && ngayHienTai <= ngayKetThucTuan) {
            trangThai = '<span class="badge badge-primary">Hiện tại</span>';
        } else {
            trangThai = '<span class="badge badge-light">Sắp tới</span>';
        }
        
        var row = '<tr>' +
                    '<td>Tuần ' + i + '</td>' +
                    '<td>' + formatDateFromDate(ngayBatDauTuan) + '</td>' +
                    '<td>' + formatDateFromDate(ngayKetThucTuan) + '</td>' +
                    '<td>' + trangThai + '</td>' +
                  '</tr>';
        tbody.append(row);
    }
}

function tinhTuanCuThe() {
    var idHocKy = $('#selectHocKy').val();
    var ngayCanTinh = $('#inputNgayCanTinh').val();
    
    if (!idHocKy) {
        showMessage('Vui lòng chọn học kỳ', 'warning');
        return;
    }
    
    if (!ngayCanTinh) {
        showMessage('Vui lòng chọn ngày cần tính', 'warning');
        return;
    }

    // Chuyển đổi định dạng ngày
    var dateParts = ngayCanTinh.split('/');
    var formattedDate = dateParts[2] + '-' + dateParts[1] + '-' + dateParts[0];

    $.ajax({
        url: '/lich-giang-day/tinh-tuan-hoc',
        type: 'GET',
        data: {
            idHocKy: idHocKy,
            ngayCanTinh: formattedDate
        },
        success: function(response) {
            console.log('TinhTuanCuThe response:', response);
            if (response.resCode === 1) {
                var data = response.resData;
                $('#tuanHocKetQua').text('Tuần ' + data.tuanHoc);
                $('#khoangThoiGian').text(formatDate(data.ngayBatDauTuan) + ' - ' + formatDate(data.ngayKetThucTuan));
                $('#ketQuaTinhTuan').show();
            } else {
                showMessage(response.resMsg || 'Có lỗi xảy ra', 'error');
            }
        },
        error: function() {
            showMessage('Lỗi khi tính tuần học', 'error');
        }
    });
}

// Utility functions
function formatDate(dateString) {
    if (!dateString) return '';
    var date = new Date(dateString);
    return ('0' + date.getDate()).slice(-2) + '/' + 
           ('0' + (date.getMonth() + 1)).slice(-2) + '/' + 
           date.getFullYear();
}

function formatDateFromDate(date) {
    return ('0' + date.getDate()).slice(-2) + '/' + 
           ('0' + (date.getMonth() + 1)).slice(-2) + '/' + 
           date.getFullYear();
}

function parseDate(dateString) {
    if (!dateString) return null;
    var date = new Date(dateString);
    return date;
}

function showMessage(message, type) {
    $('#noiDungThongBao').text(message);
    $('#modalThongBao').modal('show');
    
    // Thay đổi màu sắc modal dựa trên type
    var modalContent = $('#modalThongBao .modal-content');
    modalContent.removeClass('border-success border-warning border-danger');
    
    if (type === 'success') {
        modalContent.addClass('border-success');
    } else if (type === 'warning') {
        modalContent.addClass('border-warning');
    } else if (type === 'error') {
        modalContent.addClass('border-danger');
    }
}
