var pref_url="/lich-giang-day";
var editingData = null; // Store data for editing

$(document).ready(function(){
	$('#grid-ds').jdGrid({
		columns:[
			{name:'ngayGiangText',title:'<PERSON><PERSON><PERSON> giảng',css:{'text-align':'center','width':'90px'}},
			{name:'thuText',title:'Thứ',css:{'text-align':'center','width':'50px'}},
			{name:'tuanGiangText',title:'Tuần',css:{'text-align':'center','width':'60px'}},
			{name:'tietText',title:'Tiết',css:{'text-align':'center','width':'80px'}},
			{name:'tenBaiHoc',title:'Bài học',css:{'width':'150px'}},
			{name:'tenMonHoc',title:'<PERSON><PERSON><PERSON> học',css:{'width':'120px'}},
			{name:'tenLop',title:'Lớp',css:{'text-align':'center','width':'80px'}},
			{name:'tenGiangVien',title:'Giảng viên',css:{'width':'120px'}},
			{name:'tenPhong',title:'Phòng',css:{'text-align':'center','width':'80px'}},
			{name:'hinhThucText',title:'Hình thức',css:{'text-align':'center','width':'80px'}},
			{name:'trangThaiText',title:'Trạng thái',css:{'text-align':'center','width':'90px'}},
			{name:'noiDungDay',title:'Nội dung đã dạy',css:{'width':'150px'}},
			{name:'soSvVang',title:'SV vắng',css:{'text-align':'center','width':'70px'}},
			{name:'col2',title:'T.Tác',type:'control',css:{'text-align':'center','width':'150px'},content:function(obj){
				return '<a href="#" class="cmd cmd-edit" rid="'+obj.idLichGiang+'" title="Sửa"><i class="fa fa-edit text-primary"></i></a> '
				+'<a href="#" class="row-del-1 cmd cmd-del" rid="'+obj.idLichGiang+'" title="Xóa"><i class="fa fa-trash text-danger"></i></a> '
				+'<a href="#" class="cmd cmd-status" rid="'+obj.idLichGiang+'" title="Cập nhật trạng thái"><i class="fa fa-check-circle text-success"></i></a> ';
				}}
		],
		height:'431px',
        extclass:'tbl-primary',
        shwno:true,
        nocss:{'text-align':'center','width':'50px'},
        nolabel:'STT'

	});
    $('.select2').select2({
        width: '100%'
    });
	$('#page-ds').jdPage({
		onPageChanged:function(p){
			timKiem(p-1);
		},
		onRowOnPageChanged:function(num) {
            timKiem(0);
        }
	});

	$('#lichGiangForm').submit(function(e){
		e.preventDefault();
		luu();
	});

	$('#statusForm').submit(function(e) {
        e.preventDefault();
        updateStatus();
    });
	$('#txt-keyword').keypress(function(e) {
        var keycode = (event.keyCode ? event.keyCode : event.which);
        if(keycode == '13') {
            e.preventDefault();
            timKiem(0);
        }
    });

	 $('#btn-search').click(function() {
	        timKiem(0);
	    });
	 $('#cmb-nienKhoa-search, #cmb-hocKy-search, #cmb-giangVien-search, #cmb-monHoc-search, #cmb-lop-search, #cmb-phong-search, #cmb-hinhThuc-search, #cmb-thu-search, #tuNgay, #denNgay').change(function(){
		 timKiem(0);
	    });

	 // Cascade dropdown handlers
	 $('#cmb-nienKhoa-search').on('change', function() {
	     loadHocKyByNienKhoa($(this).val(), '#cmb-hocKy-search');
	 });

	 $('#idNienKhoa').on('change', function() {
	     loadHocKyByNienKhoa($(this).val(), '#idHocKy');
	 });

	 $('#idMonHoc').on('change', function() {
	     // Always load lessons when subject changes, but don't auto-select during editing
	     loadBaiHocByMonHoc($(this).val(), '#idBaiHoc');
	 });

	 $('#coSo').on('change', function() {
	     loadPhongHocByCoSo($(this).val(), '#idPhong');
	 });

	 layDuLieuBanDau();
	 $('#addLichGiangModal').on('shown.bs.modal', function (e) {
		 if (editingData) {
			 // Fill form with editing data
			 console.log('Modal shown, filling with editing data:', editingData);
			 fillFormWithData(editingData);
			 editingData = null; // Clear after use
		 } else {
			 // Clear form for new record
			 $('#modalTitle').text('Thêm lịch giảng');
			 clearForm();
		 }
	   });

	 // Event handlers for export Excel functions
	 $('#btn-export-giang-vien').click(function() {
		 xuatExcelLichGiangVien();
	 });

	 $('#btn-export-lop').click(function() {
		 xuatExcelLichLop();
	 });

	 $('#btn-export-toan-truong').click(function() {
		 xuatExcelToanTruong();
	 });

	 // Event handlers for conflict report
	 $('#btn-conflict-report').click(function() {
		 showConflictReport();
	 });

	 // Event handlers for multiple weeks
	 $('#btn-multiple-weeks').click(function() {
		 showMultipleWeeksModal();
	 });

	 // Week change handlers for export modals
	 $('#exportTuan').change(function() {
		 updateDateRangeFromWeek('#exportTuan');
	 });

	 $('#exportTuanLop').change(function() {
		 updateDateRangeFromWeek('#exportTuanLop');
	 });

	 $('#exportTuanToanTruong').change(function() {
		 updateDateRangeFromWeek('#exportTuanToanTruong');
	 });

	 // Initialize select2 for all select elements
	 initializeSelect2();

	 // Set Vietnam timezone as default
	 setVietnamTimezoneDefault();

	 // Initialize date inputs with Vietnamese format
	 initializeDateInputs();

	 // Re-initialize select2 when modals are shown
	 $('#addLichGiangModal').on('shown.bs.modal', function() {
		 initializeSelect2();
		 initializeDateInputs();
	 });

	 $('#statusModal').on('shown.bs.modal', function() {
		 initializeSelect2();
	 });

	 // Clear status modal when hidden (only if not being filled)
	 $('#statusModal').on('hidden.bs.modal', function() {
		 // Only clear if modal was closed by user, not during data filling
		 setTimeout(function() {
			 clearStatusForm();
		 }, 100);
	 });
});

function layDuLieuBanDau() {
    $.ajax({
    	url:pref_url+'/lay-du-lieu-ban-dau',
        method:'get',
        beforeSend:function() {
            showBoxLoading('box-frm');
        }, success:function(res) {
            	 // Populate search dropdowns
            	 $('#cmb-nienKhoa-search').find('option').remove();
            	 $('#cmb-nienKhoa-search').append($('<option>', {
	                    value: '',
	                    text : '-- Tất cả --'
	                }));
            	 $.each(res.resData['nienKhoa'],function(i,obj){
                     $('#cmb-nienKhoa-search').append($('<option>', {
                         value: obj.idNienKhoa,
                         text : obj.tenNienKhoa
                     }));
                 });

                 // Populate lecturers
                 $('#cmb-giangVien-search').find('option').remove();
                 $('#cmb-giangVien-search').append($('<option>', {
                     value: '',
                     text : '-- Tất cả --'
                 }));
                 $.each(res.resData['canBo'],function(i,obj){
                     $('#cmb-giangVien-search').append($('<option>', {
                         value: obj.idCanBo,
                         text : obj.hoTen
                     }));
                 });

                 // Populate subjects
                 $('#cmb-monHoc-search').find('option').remove();
                 $('#cmb-monHoc-search').append($('<option>', {
                     value: '',
                     text : '-- Tất cả --'
                 }));
                 $.each(res.resData['monHoc'],function(i,obj){
                     $('#cmb-monHoc-search').append($('<option>', {
                         value: obj.idMonHoc,
                         text : obj.tenMonHoc
                     }));
                 });

                 // Populate classrooms
                 $('#cmb-phong-search').find('option').remove();
                 $('#cmb-phong-search').append($('<option>', {
                     value: '',
                     text : '-- Tất cả --'
                 }));
                 $.each(res.resData['phongHoc'],function(i,obj){
                     $('#cmb-phong-search').append($('<option>', {
                         value: obj.idPhong,
                         text : obj.tenPhong
                     }));
                 });



                 // Populate modal dropdowns
                 populateModalDropdowns(res.resData);

                 // Load classes separately
                 loadClasses();

                 timKiem(0);
        }, error:function(jqXHR) {
            showErr('Thông báo', 'Đã có lỗi xảy ra, vui lòng thử lại sau');
        }, complete:function() {
            hideBoxLoading('box-frm');
        }
    });
}

function loadClasses() {
    $.ajax({
        url: pref_url + '/lay-lop',
        method: 'get',
        success: function(res) {
            if (res.resCode > 0) {
                // Populate search dropdown
                $('#cmb-lop-search').find('option').remove();
                $('#cmb-lop-search').append($('<option>', {
                    value: '',
                    text : '-- Tất cả --'
                }));
                $.each(res.resData, function(i, obj) {
                    $('#cmb-lop-search').append($('<option>', {
                        value: obj.idLop,
                        text : obj.tenLop
                    }));
                });

                // Populate modal dropdown
                $('#idLop').find('option').remove();
                $('#idLop').append($('<option>', {
                    value: '',
                    text : '-- Chọn lớp --'
                }));
                $.each(res.resData, function(i, obj) {
                    $('#idLop').append($('<option>', {
                        value: obj.idLop,
                        text : obj.tenLop
                    }));
                });
            }
        },
        error: function() {
            showErr('Thông báo', 'Lỗi tải danh sách lớp học');
        }
    });
}

function populateModalDropdowns(data) {
    // Populate modal academic years
    $('#idNienKhoa').find('option').remove();
    $('#idNienKhoa').append($('<option>', {
        value: '',
        text : '-- Chọn niên khóa --'
    }));
    $.each(data['nienKhoa'],function(i,obj){
        $('#idNienKhoa').append($('<option>', {
            value: obj.idNienKhoa,
            text : obj.tenNienKhoa
        }));
    });

    // Populate modal lecturers
    $('#idGiangVien').find('option').remove();
    $('#idGiangVien').append($('<option>', {
        value: '',
        text : '-- Chọn giảng viên --'
    }));
    $.each(data['canBo'],function(i,obj){
        $('#idGiangVien').append($('<option>', {
            value: obj.idCanBo,
            text : obj.hoTen
        }));
    });

    // Populate modal subjects
    $('#idMonHoc').find('option').remove();
    $('#idMonHoc').append($('<option>', {
        value: '',
        text : '-- Chọn môn học --'
    }));
    $.each(data['monHoc'],function(i,obj){
        $('#idMonHoc').append($('<option>', {
            value: obj.idMonHoc,
            text : obj.tenMonHoc
        }));
    });

    // Populate modal classrooms
    $('#idPhong').find('option').remove();
    $('#idPhong').append($('<option>', {
        value: '',
        text : '-- Chọn phòng --'
    }));
    $.each(data['phongHoc'],function(i,obj){
        $('#idPhong').append($('<option>', {
            value: obj.idPhong,
            text : obj.tenPhong
        }));
    });

}

// ===== MISSING CASCADE LOADING FUNCTIONS =====

/**
 * Load semesters by academic year
 */
function loadHocKyByNienKhoa(idNienKhoa, targetSelector, callback) {
    var targetSelect = $(targetSelector);
    
    // Clear existing options except default
    targetSelect.find('option:not(:first)').remove();
    
    if (!idNienKhoa) {
        // Disable semester dropdown if no academic year selected
        targetSelect.prop('disabled', true);
        targetSelect.val('').trigger('change');
        return;
    }
    
    // Enable and show loading
    targetSelect.prop('disabled', false);
    targetSelect.append('<option value="">Đang tải...</option>');
    
    $.ajax({
        url: pref_url + '/lay-hoc-ky-theo-nien-khoa',
        type: 'GET',
        data: { idNienKhoa: idNienKhoa },
        success: function(response) {
            // Clear loading option
            targetSelect.find('option:not(:first)').remove();
            
            if (response && response.resCode === 1) {
                $.each(response.resData, function(index, hocKy) {
                    targetSelect.append('<option value="' + hocKy.idHocKy + '">' + hocKy.tenHocKy + '</option>');
                });
            }
            
            if (callback) callback();
        },
        error: function(xhr, status, error) {
            console.error('Error loading semesters:', error);
            targetSelect.find('option:not(:first)').remove();
            targetSelect.append('<option value="">Lỗi tải dữ liệu</option>');
        }
    });
}

/**
 * Load lessons by subject
 */
function loadBaiHocByMonHoc(idMonHoc, targetSelector, callback) {
    console.log('=== LOADING BAI HOC BY MON HOC ===');
    console.log('idMonHoc:', idMonHoc, 'targetSelector:', targetSelector);

    var targetSelect = $(targetSelector);

    // Clear existing options except default
    targetSelect.find('option:not(:first)').remove();

    if (!idMonHoc) {
        console.log('No idMonHoc provided, clearing dropdown');
        targetSelect.val('');
        if (callback) callback();
        return;
    }

    // Show loading
    targetSelect.append('<option value="">Đang tải...</option>');
    console.log('Loading lessons for subject ID:', idMonHoc);

    $.ajax({
        url: pref_url + '/lay-bai-hoc',
        type: 'GET',
        data: { idMonHoc: idMonHoc },
        success: function(response) {
            console.log('Lessons response:', response);

            // Clear loading option
            targetSelect.find('option:not(:first)').remove();

            if (response && response.resCode === 1 && response.resData) {
                console.log('Found', response.resData.length, 'lessons');
                $.each(response.resData, function(index, baiHoc) {
                    var optionHtml = '<option value="' + baiHoc.idBaiHoc + '">' + baiHoc.tenBaiHoc + '</option>';
                    targetSelect.append(optionHtml);
                    console.log('Added lesson option:', baiHoc.idBaiHoc, '-', baiHoc.tenBaiHoc);
                });
            } else {
                console.warn('No lessons found or invalid response:', response);
                targetSelect.append('<option value="">Không có bài học</option>');
            }

            console.log('Lessons loaded, calling callback');
            if (callback) callback();
        },
        error: function(xhr, status, error) {
            console.error('Error loading lessons:', error);
            console.error('XHR status:', xhr.status);
            console.error('Response text:', xhr.responseText);
            targetSelect.find('option:not(:first)').remove();
            targetSelect.append('<option value="">Lỗi tải dữ liệu</option>');
            if (callback) callback();
        }
    });
}

/**
 * Load classrooms by campus
 */
function loadPhongHocByCoSo(coSo, targetSelector, callback) {
    var targetSelect = $(targetSelector);
    
    // Clear existing options except default
    targetSelect.find('option:not(:first)').remove();
    
    // Show loading
    targetSelect.append('<option value="">Đang tải...</option>');
    
    $.ajax({
        url: pref_url + '/lay-phong-hoc-theo-co-so',
        type: 'GET',
        data: { coSo: coSo || '' },
        success: function(response) {
            // Clear loading option
            targetSelect.find('option:not(:first)').remove();
            
            if (response && response.resCode === 1) {
                $.each(response.resData, function(index, phongHoc) {
                    targetSelect.append('<option value="' + phongHoc.idPhong + '">' + phongHoc.tenPhong + '</option>');
                });
            }
            
            if (callback) callback();
        },
        error: function(xhr, status, error) {
            console.error('Error loading classrooms:', error);
            targetSelect.find('option:not(:first)').remove();
            targetSelect.append('<option value="">Lỗi tải dữ liệu</option>');
        }
    });
}

// ===== SEARCH AND GRID FUNCTIONS =====

function timKiem(page) {
    console.log('=== EXECUTING SEARCH ===');

    var data = new FormData($('#frmSearch')[0]);
    data.append('page', page);

    // Get page size, default to 20 if pagination not initialized yet
    var pageSize = 20;
    var jdPageData = $('#page-ds').data('jdpage');
    if (jdPageData && typeof jdPageData.getRowOnPage === 'function') {
        pageSize = jdPageData.getRowOnPage();
    }
    data.append('size', pageSize);

    // Convert date formats from dd/mm/yyyy (datepicker) to yyyy-MM-dd (backend)
    var tuNgayVN = $('#tuNgay').val(); // dd/mm/yyyy format from datepicker
    var denNgayVN = $('#denNgay').val(); // dd/mm/yyyy format from datepicker

    if (tuNgayVN) {
        var tuNgayISO = formatDateToISO(tuNgayVN);
        if (tuNgayISO) {
            data.set('tuNgay', tuNgayISO);
            console.log('Converted tuNgay from', tuNgayVN, 'to', tuNgayISO);
        } else {
            console.warn('Invalid tuNgay format:', tuNgayVN);
            data.delete('tuNgay');
        }
    }

    if (denNgayVN) {
        var denNgayISO = formatDateToISO(denNgayVN);
        if (denNgayISO) {
            data.set('denNgay', denNgayISO);
            console.log('Converted denNgay from', denNgayVN, 'to', denNgayISO);
        } else {
            console.warn('Invalid denNgay format:', denNgayVN);
            data.delete('denNgay');
        }
    }

    // Debug: log the search parameters
    console.log('Searching with parameters:', {
        idNienKhoa: data.get('idNienKhoa'),
        idHocKy: data.get('idHocKy'),
        idGiangVien: data.get('idGiangVien'),
        idMonHoc: data.get('idMonHoc'),
        idLop: data.get('idLop'),
        idPhong: data.get('idPhong'),
        hinhThuc: data.get('hinhThuc'),
        thu: data.get('thu'),
        tuNgay: data.get('tuNgay'), // Now in yyyy-MM-dd format
        denNgay: data.get('denNgay'), // Now in yyyy-MM-dd format
        keyword: data.get('keyword'),
        page: data.get('page'),
        size: data.get('size')
    });

    $.ajax({
        url:pref_url+'/tim-kiem',
        method:'post',
        data:data,
        processData:false,
        contentType:false,
        beforeSend:function() {
            showBoxLoading('box-frm');
        }, success:function(res) {
            console.log('Search response:', res);
            var jdGrid=$('#grid-ds').data('jdgrid');
            if (jdGrid) {
                // Process data to add tuanGiangText before filling grid
                var processedData = processLichGiangData(res.resData.content);
                jdGrid.setPage(res.resData.number+1);
                jdGrid.setRowOnPage(res.resData.size);
                jdGrid.fillData(processedData);
            }

            var jdPage = $('#page-ds').data('jdpage');
            if (jdPage) {
                jdPage.setData({totalPage:res.resData.totalPages,currentPage:res.resData.number+1,totalItem:res.resData.totalElements,itemOnPage:res.resData.size});
            }
            $('.cmd-edit').click(function(e){
                e.preventDefault();
                $('#modalTitle').text('Sửa lịch giảng');
                var id = $(this).attr('rid');
                layChiTiet(id);
            });
            $('.cmd-del').click(function(e){
                e.preventDefault();
                var id = $(this).attr('rid');
                showCfm('Xác nhận', 'Bạn chắc muốn xóa?', function(){
                    xoa(id);
                });
            });
            $('.cmd-status').click(function(e){
                e.preventDefault();
                var id = $(this).attr('rid');
                showUpdateStatusModal(id);
            });
            $('.cmd-content').click(function(e){
                e.preventDefault();
                var id = $(this).attr('rid');
                showContentModal(id);
            });
        }, error:function(jqXHR) {
            showErr('Thông báo', 'Đã có lỗi xảy ra, vui lòng thử lại sau');
        }, complete:function() {
            hideBoxLoading('box-frm');
        }
    });
}

// ===== SAVE FUNCTION =====

function luu() {
    console.log('=== SAVING FORM DATA ===');

    // Validate required fields
    if (!validateForm()) {
        return;
    }

    // Collect form data
    var ngayGiangVN = $('#ngayGiang').val(); // dd/mm/yyyy format from datepicker
    var ngayGiangISO = null;

    // Convert date format from dd/mm/yyyy to yyyy-MM-dd for backend
    if (ngayGiangVN) {
        ngayGiangISO = formatDateToISO(ngayGiangVN);
        if (!ngayGiangISO) {
            showToastErr('Định dạng ngày giảng không hợp lệ: ' + ngayGiangVN, 3000);
            return;
        }
        console.log('Converted ngayGiang from', ngayGiangVN, 'to', ngayGiangISO);
    }

    var formData = {
        idLichGiang: $('#idLichGiang').val() || null,
        idNienKhoa: $('#idNienKhoa').val(),
        idHocKy: $('#idHocKy').val(),
        idMonHoc: $('#idMonHoc').val(),
        idBaiHoc: $('#idBaiHoc').val(),
        idLop: $('#idLop').val(),
        idNhom: $('#idNhom').val() || null,
        idGiangVien: $('#idGiangVien').val(),
        idPhong: $('#idPhong').val(),
        hinhThuc: $('#hinhThuc').val(),
        soTiet: parseInt($('#soTiet').val()) || 1,
        heSo: parseFloat($('#heSo').val()) || 1.0,
        thu: parseInt($('#thu').val()),
        buoi: $('#buoi').val(),
        tietBatDau: parseInt($('#tietBatDau').val()),
        tietKetThuc: parseInt($('#tietKetThuc').val()),
        ngayGiang: ngayGiangISO, // Now in yyyy-MM-dd format
        tuanGiang: parseInt($('#tuanGiang').val()) || null,
        ghiChu: $('#ghiChu').val() || null,
        noiDungDay: $('#noiDungDay').val() || null,
        soSvVang: parseInt($('#soSvVang').val()) || 0,
        trangThai: parseInt($('#trangThai').val()) || 0
    };

    console.log('Form data to save:', formData);

    $.ajax({
        url: pref_url + '/luu',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(formData),
        beforeSend: function() {
            showBoxLoading('box-frm');
        },
        success: function(response) {
            hideBoxLoading('box-frm');
            console.log('Save response:', response);

            if (response.resCode === 1) {
                showToastSuc('Đã lưu lịch giảng thành công');
                $('#addLichGiangModal').modal('hide');
                // Refresh the grid
                timKiem(0);
            } else {
                showErr('Lỗi', response.resMsg || 'Không thể lưu lịch giảng');
            }
        },
        error: function(xhr, status, error) {
            hideBoxLoading('box-frm');
            console.error('Error saving:', error);
            showErr('Lỗi', 'Có lỗi xảy ra khi lưu dữ liệu');
        }
    });
}

function validateForm() {
    var requiredFields = [
        { id: 'idNienKhoa', name: 'Niên khóa' },
        { id: 'idHocKy', name: 'Học kỳ' },
        { id: 'idMonHoc', name: 'Môn học' },
        { id: 'idBaiHoc', name: 'Bài học' },
        { id: 'idLop', name: 'Lớp' },
        { id: 'idGiangVien', name: 'Giảng viên' },
        { id: 'idPhong', name: 'Phòng học' },
        { id: 'hinhThuc', name: 'Hình thức' },
        { id: 'thu', name: 'Thứ' },
        { id: 'buoi', name: 'Buổi' },
        { id: 'tietBatDau', name: 'Tiết bắt đầu' },
        { id: 'tietKetThuc', name: 'Tiết kết thúc' },
        { id: 'ngayGiang', name: 'Ngày giảng' }
    ];

    for (var i = 0; i < requiredFields.length; i++) {
        var field = requiredFields[i];
        var value = $('#' + field.id).val();
        if (!value || value.trim() === '') {
            showErr('Lỗi', 'Vui lòng nhập ' + field.name);
            $('#' + field.id).focus();
            return false;
        }
    }

    // Validate period logic
    var tietBatDau = parseInt($('#tietBatDau').val());
    var tietKetThuc = parseInt($('#tietKetThuc').val());
    if (tietBatDau >= tietKetThuc) {
        showErr('Lỗi', 'Tiết bắt đầu phải nhỏ hơn tiết kết thúc');
        $('#tietBatDau').focus();
        return false;
    }

    return true;
}

// ===== FORM FILLING AND MODAL FUNCTIONS =====

// Function to get details for editing
function layChiTiet(idLichGiang) {
    console.log('=== GETTING DETAILS FOR EDITING ===');
    console.log('ID Lich Giang:', idLichGiang);

    if (!idLichGiang) {
        showErr('Lỗi', 'ID lịch giảng không hợp lệ');
        return;
    }

    showBoxLoading('box-frm');

    $.ajax({
        url: pref_url + '/lay-chi-tiet',
        type: 'GET',
        data: { idLichGiang: idLichGiang },
        dataType: 'json',
        success: function(response) {
            hideBoxLoading('box-frm');
            console.log('layChiTiet response:', response);

            if (response.resCode === 1 && response.resData) {
                console.log('Received data for editing:', response.resData);

                // Store data globally for modal event
                editingData = response.resData;

                // Set modal title
                $('#modalTitle').text('Sửa lịch giảng');

                // Show modal - the modal shown event will handle form filling
                $('#addLichGiangModal').modal('show');

            } else {
                showErr('Lỗi', response.resMsg || 'Không thể lấy thông tin chi tiết');
            }
        },
        error: function(xhr, status, error) {
            hideBoxLoading('box-frm');
            console.error('Error getting details:', error);
            console.error('XHR:', xhr);
            showErr('Lỗi', 'Có lỗi xảy ra khi lấy thông tin chi tiết: ' + error);
        }
    });
}

function fillFormWithData(data) {
    try {
        // Set basic ID
        $('#idLichGiang').val(data.idLichGiang || '');
        console.log('Set idLichGiang:', data.idLichGiang);

        // Set dropdown fields with proper sequence
        setDropdownFieldsSequentially(data);
        $('#ngayGiang').datepicker('setDate', data.ngayGiangText).trigger('change');

    } catch (error) {
        console.error('Error filling form with data:', error);
        console.error('Error stack:', error.stack);
    }
}

function setDropdownValue(selector, value, fieldName) {
    var $dropdown = $(selector);

    console.log('Setting dropdown value - selector:', selector, 'value:', value, 'fieldName:', fieldName);

    // Check if option exists
    var optionExists = $dropdown.find('option[value="' + value + '"]').length > 0;

    if (optionExists) {
        $dropdown.val(value);
        console.log('✅ Set ' + fieldName + ':', value);

        // Trigger change event for select2 and other plugins
        $dropdown.trigger('change');
    } else {
        console.warn('⚠️ Option not found for ' + fieldName + ':', value);
        console.log('Available options for ' + fieldName + ':');
        $dropdown.find('option').each(function() {
            console.log('  ' + $(this).val() + ': ' + $(this).text());
        });

        // For critical dropdowns like baiHoc, try to wait and retry
        if (fieldName === 'idBaiHoc' && value && value !== '') {
            console.log('Retrying to set baiHoc after delay...');
            setTimeout(function() {
                var retryOptionExists = $dropdown.find('option[value="' + value + '"]').length > 0;
                if (retryOptionExists) {
                    $dropdown.val(value);
                    $dropdown.trigger('change');
                    console.log('✅ Successfully set ' + fieldName + ' on retry:', value);
                } else {
                    // Add temporary option as last resort
                    $dropdown.append('<option value="' + value + '">Bài học ID: ' + value + '</option>');
                    $dropdown.val(value);
                    $dropdown.trigger('change');
                    console.log('⚠️ Added temporary option for ' + fieldName + ':', value);
                }
            }, 500);
        } else if (value && value !== '') {
            // For other dropdowns, add temporary option immediately
            $dropdown.append('<option value="' + value + '">ID: ' + value + '</option>');
            $dropdown.val(value);
            $dropdown.trigger('change');
            console.log('Added temporary option for ' + fieldName + ':', value);
        }
    }
}

function setDropdownFieldsSequentially(data) {
    console.log('=== SETTING DROPDOWN FIELDS SEQUENTIALLY ===');
    console.log('Data for dropdowns:', data);

    // Step 1: Set academic year first
    var nienKhoaId = data.idNienKhoa || (data.nienKhoa ? data.nienKhoa.idNienKhoa : null);
    if (nienKhoaId) {
        setDropdownValue('#idNienKhoa', nienKhoaId, 'idNienKhoa');

        // Step 2: Load and set semester
        var hocKyId = data.idHocKy || (data.hocKy ? data.hocKy.idHocKy : null);
        if (hocKyId) {
            setTimeout(function() {
                loadHocKyByNienKhoa(nienKhoaId, '#idHocKy', function() {
                    setDropdownValue('#idHocKy', hocKyId, 'idHocKy');
                });
            }, 200);
        }
    }

    // Step 3: Set subject and load lessons
    var monHocId = data.idMonHoc || (data.monHoc ? data.monHoc.idMonHoc : null);
    var baiHocId = data.idBaiHoc || (data.baiHoc ? data.baiHoc.idBaiHoc : null);

    console.log('Setting subject and lesson - monHocId:', monHocId, 'baiHocId:', baiHocId);

    if (monHocId) {
        setDropdownValue('#idMonHoc', monHocId, 'idMonHoc');

        // Always load lessons when we have a subject, regardless of whether we have baiHocId
        setTimeout(function() {
            console.log('Loading lessons for subject:', monHocId);
            loadBaiHocByMonHoc(monHocId, '#idBaiHoc', function() {
                console.log('Lessons loaded, now setting baiHocId:', baiHocId);
                if (baiHocId) {
                    // Wait a bit more for options to be populated
                    setTimeout(function() {
                        setDropdownValue('#idBaiHoc', baiHocId, 'idBaiHoc');
                    }, 100);
                }
            });
        }, 400);
    }

    // Step 4: Set independent dropdowns
    setTimeout(function() {
        var lopId = data.idLop || (data.lop ? data.lop.idLop : null);
        if (lopId) {
            setDropdownValue('#idLop', lopId, 'idLop');
        }

        var giangVienId = data.idGiangVien || (data.giangVien ? data.giangVien.idCanBo : null);
        if (giangVienId) {
            setDropdownValue('#idGiangVien', giangVienId, 'idGiangVien');
        }

        var nhomId = data.idNhom || (data.nhom ? data.nhom.idNhom : null);
        if (nhomId) {
            setDropdownValue('#idNhom', nhomId, 'idNhom');
        }
    }, 100);

    // Step 5: Set campus and classroom
    var coSo = data.coSo || (data.phongHoc ? data.phongHoc.coSo : null);
    var phongId = data.idPhong || (data.phongHoc ? data.phongHoc.idPhong : null);

    if (coSo) {
        setDropdownValue('#coSo', coSo, 'coSo');

        if (phongId) {
            setTimeout(function() {
                loadPhongHocByCoSo(coSo, '#idPhong', function() {
                    setDropdownValue('#idPhong', phongId, 'idPhong');
                });
            }, 600);
        }
    } else if (phongId) {
        // If we have phongId but no coSo, just set the phong directly
        setTimeout(function() {
            setDropdownValue('#idPhong', phongId, 'idPhong');
        }, 100);
    }

    //set tietBatDau and tietKetThuc
    if (data.tietBatDau) {
        $('#tietBatDau').val(data.tietBatDau).trigger('change');
    }

    if (data.tietKetThuc) {
        $('#tietKetThuc').val(data.tietKetThuc).trigger('change');
    }

    //set thu and buoi
    if (data.thu) {
        $('#thu').val(data.thu).trigger('change');
    }

    if (data.buoi) {
        $('#buoi').val(data.buoi).trigger('change');
    }

    //set hình thức
    if (data.hinhThuc) {
        $('#hinhThuc').val(data.hinhThuc).trigger('change');
    }
    //set noiDungDay
    if (data.noiDungDay) {
        $('#noiDungDay').val(data.noiDungDay);
    }
    //set soSvVang
    if (data.soSvVang) {
        $('#soSvVang').val(data.soSvVang);
    }
    //setTrangThai
    if (data.trangThai) {
        $('#trangThai').val(data.trangThai);
    }

    // set Ghi chu
    if (data.ghiChu) {
        $('#ghiChu').val(data.ghiChu);
    }
}

// Function to clear form
function clearForm() {
    console.log('=== CLEARING FORM ===');

    // Reset the form
    $('#lichGiangForm')[0].reset();

    // Clear hidden ID field
    $('#idLichGiang').val('');

    // Reset all dropdowns to default and refresh select2
    var dropdowns = ['#idNienKhoa', '#idHocKy', '#idMonHoc', '#idBaiHoc', '#idLop', '#idNhom', '#idGiangVien', '#coSo', '#idPhong'];

    dropdowns.forEach(function(selector) {
        var $dropdown = $(selector);
        $dropdown.val('');

        // Refresh select2 if it exists
        if ($dropdown.hasClass('select2-hidden-accessible')) {
            $dropdown.trigger('change');
        }
    });

    // Clear dependent dropdowns
    $('#idHocKy').find('option:not(:first)').remove();
    $('#idBaiHoc').find('option:not(:first)').remove();
    $('#idPhong').find('option:not(:first)').remove();

    // Refresh select2 for all elements
    setTimeout(function() {
        initializeSelect2();
    }, 100);

    console.log('Form cleared successfully');
}

// Function to clear status form
function clearStatusForm() {
    console.log('=== CLEARING STATUS FORM ===');

    // Reset the status form
    $('#statusForm')[0].reset();

    // Clear all fields explicitly
    $('#statusIdLichGiang').val('');
    $('#statusTrangThai').val('').trigger('change');
    $('#statusNoiDungDay').val('');
    $('#statusSoSvVang').val('');
    $('#statusGhiChu').val('');

    console.log('Status form cleared successfully');
}

// ===== DELETE AND STATUS FUNCTIONS =====

// Function to delete a record
function xoa(idLichGiang) {
    console.log('=== DELETING RECORD ===');
    console.log('ID Lich Giang:', idLichGiang);

    showBoxLoading('box-frm');

    $.ajax({
        url: pref_url + '/xoa',
        type: 'POST',
        data: { idLichGiang: idLichGiang },
        dataType: 'json',
        success: function(response) {
            hideBoxLoading('box-frm');
            console.log('Delete response:', response);

            if (response.resCode === 1) {
                showToastSuc('Đã xóa thành công');
                // Refresh the grid
                timKiem(0);
            } else {
                showErr('Lỗi', response.resMsg || 'Không thể xóa bản ghi');
            }
        },
        error: function(xhr, status, error) {
            hideBoxLoading('box-frm');
            console.error('Error deleting record:', error);
            showErr('Lỗi', 'Có lỗi xảy ra khi xóa bản ghi');
        }
    });
}

// Function to show update status modal
function showUpdateStatusModal(idLichGiang) {
    console.log('=== SHOWING UPDATE STATUS MODAL ===');
    console.log('ID Lich Giang:', idLichGiang);

    if (!idLichGiang) {
        showToastErr('ID lịch giảng không hợp lệ', 3000);
        return;
    }

    // Clear form first
    clearStatusForm();

    // Get current data to pre-fill the form
    $.ajax({
        url: pref_url + '/lay-chi-tiet',
        type: 'GET',
        data: { idLichGiang: idLichGiang },
        beforeSend: function() {
            showBoxLoading('statusModal');
        },
        success: function(response) {
            hideBoxLoading('statusModal');
            console.log('Status modal data response:', response);

            if (response.resCode === 1 && response.resData) {
                var data = response.resData;
                console.log('Setting status form data:', data);

                // Set the ID in the status modal
                $('#statusIdLichGiang').val(idLichGiang);
                console.log('Set statusIdLichGiang:', idLichGiang);

                // Show the modal first
                $('#statusModal').modal('show');

                // Wait for modal to be fully shown, then fill data
                $('#statusModal').one('shown.bs.modal', function() {
                    console.log('Modal shown, now filling data...');

                    // Pre-fill current values with detailed logging
                    var trangThai = data.trangThai;
                    console.log('Original trangThai value:', trangThai, 'type:', typeof trangThai);

                    // Ensure trangThai is a string for select value
                    if (trangThai !== null && trangThai !== undefined) {
                        trangThai = trangThai.toString();
                    }

                    console.log('Setting trangThai to:', trangThai);
                    $('#statusTrangThai').val(trangThai);

                    // Verify the value was set
                    var setValue = $('#statusTrangThai').val();
                    console.log('Value after setting:', setValue);

                    // Check available options
                    console.log('Available options:');
                    $('#statusTrangThai option').each(function() {
                        console.log('  Option value:', $(this).val(), 'text:', $(this).text());
                    });

                    // Set other fields
                    $('#statusNoiDungDay').val(data.noiDungDay || '');
                    $('#statusSoSvVang').val(data.soSvVang || 0);
                    $('#statusGhiChu').val(data.ghiChu || '');

                    console.log('All status form fields set');

                    // Trigger change event for select2 if it exists
                    $('#statusTrangThai').trigger('change');

                    // Backup method: try setting again after a delay
                    setTimeout(function() {
                        var currentVal = $('#statusTrangThai').val();
                        if (!currentVal || currentVal !== trangThai) {
                            console.log('Backup method: re-setting status value');
                            $('#statusTrangThai').val(trangThai);
                            $('#statusTrangThai').trigger('change');

                            // Final verification
                            setTimeout(function() {
                                var finalVal = $('#statusTrangThai').val();
                                console.log('Final status value:', finalVal);
                                if (finalVal !== trangThai) {
                                    console.warn('Status value still not set correctly!');
                                    // Force set by finding and selecting the option
                                    $('#statusTrangThai option').each(function() {
                                        if ($(this).val() === trangThai) {
                                            $(this).prop('selected', true);
                                            $('#statusTrangThai').trigger('change');
                                            console.log('Force selected option with value:', trangThai);
                                        }
                                    });
                                }
                            }, 200);
                        }
                    }, 500);
                });

            } else {
                showToastErr('Không thể lấy thông tin chi tiết', 3000);
            }
        },
        error: function(xhr, status, error) {
            hideBoxLoading('statusModal');
            console.error('Error getting details for status update:', error);
            showToastErr('Có lỗi xảy ra khi lấy thông tin', 3000);
        }
    });
}

// Function to update status
function updateStatus() {
    console.log('=== UPDATING STATUS ===');

    // Validate required fields
    var trangThai = $('#statusTrangThai').val();
    if (!trangThai) {
        showToastErr('Vui lòng chọn trạng thái', 3000);
        return;
    }

    var formData = {
        idLichGiang: $('#statusIdLichGiang').val(),
        trangThai: parseInt(trangThai),
        noiDungDay: $('#statusNoiDungDay').val(),
        soSvVang: parseInt($('#statusSoSvVang').val()) || 0,
        ghiChu: $('#statusGhiChu').val()
    };

    console.log('Status update data:', formData);

    $.ajax({
        url: pref_url + '/cap-nhat-trang-thai-chi-tiet',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(formData),
        beforeSend: function() {
            showBoxLoading('statusModal');
        },
        success: function(response) {
            hideBoxLoading('statusModal');
            console.log('Status update response:', response);

            if (response.resCode === 1) {
                showToastSuc('Đã cập nhật trạng thái thành công', 3000);
                $('#statusModal').modal('hide');
                // Refresh the grid
                timKiem(0);
            } else {
                showToastErr(response.resMsg || 'Không thể cập nhật trạng thái', 3000);
            }
        },
        error: function(xhr, status, error) {
            hideBoxLoading('statusModal');
            console.error('Error updating status:', error);
            showToastErr('Có lỗi xảy ra khi cập nhật trạng thái', 3000);
        }
    });
}

// Function to show content modal
function showContentModal(idLichGiang) {
    console.log('=== SHOWING CONTENT MODAL ===');
    console.log('ID Lich Giang:', idLichGiang);

    if (!idLichGiang) {
        showToastErr('ID lịch giảng không hợp lệ', 3000);
        return;
    }

    showBoxLoading('contentModal');

    // Get details and show in content modal
    $.ajax({
        url: pref_url + '/lay-chi-tiet',
        type: 'GET',
        data: { idLichGiang: idLichGiang },
        success: function(response) {
            hideBoxLoading('contentModal');
            console.log('Content modal response:', response);

            if (response.resCode === 1 && response.resData) {
                var data = response.resData;

                // Populate content modal fields with proper fallbacks
                $('#contentMonHoc').text(data.tenMonHoc || (data.monHoc ? data.monHoc.tenMonHoc : 'N/A'));
                $('#contentBaiHoc').text(data.tenBaiHoc || (data.baiHoc ? data.baiHoc.tenBaiHoc : 'N/A'));
                $('#contentLop').text(data.tenLop || (data.lop ? data.lop.tenLop : 'N/A'));
                $('#contentGiangVien').text(data.tenGiangVien || (data.giangVien ? data.giangVien.hoTen : 'N/A'));
                $('#contentNgayGiang').text(data.ngayGiangText || 'N/A');
                $('#contentTiet').text(data.tietText || (data.tietBatDau && data.tietKetThuc ? 'Tiết ' + data.tietBatDau + '-' + data.tietKetThuc : 'N/A'));

                // Handle content with HTML formatting
                var noiDungDay = data.noiDungDay || 'Chưa có nội dung';
                if (noiDungDay && noiDungDay.trim() !== '') {
                    $('#contentNoiDungDay').html(noiDungDay.replace(/\n/g, '<br>'));
                } else {
                    $('#contentNoiDungDay').html('<em class="text-muted">Chưa có nội dung</em>');
                }

                $('#contentSoSvVang').text(data.soSvVang || '0');

                // Format status text
                var trangThaiText = '';
                switch(data.trangThai) {
                    case 0: trangThaiText = '<span class="label label-warning">Chưa dạy</span>'; break;
                    case 1: trangThaiText = '<span class="label label-success">Đã dạy</span>'; break;
                    case 2: trangThaiText = '<span class="label label-danger">Hủy</span>'; break;
                    case 3: trangThaiText = '<span class="label label-info">Hoãn</span>'; break;
                    default: trangThaiText = '<span class="label label-default">N/A</span>';
                }
                $('#contentTrangThai').html(trangThaiText);

                // Show the modal
                $('#contentModal').modal('show');
            } else {
                showToastErr('Không thể lấy thông tin chi tiết', 3000);
            }
        },
        error: function(xhr, status, error) {
            hideBoxLoading('contentModal');
            console.error('Error getting content details:', error);
            showToastErr('Có lỗi xảy ra khi lấy thông tin chi tiết', 3000);
        }
    });
}

// ===== ADDITIONAL UTILITY FUNCTIONS =====

// Function to check schedule conflicts
function kiemTraTrungLich() {
    console.log('=== CHECKING SCHEDULE CONFLICTS ===');

    var formData = {
        idLichGiang: $('#idLichGiang').val() || null,
        idGiangVien: $('#idGiangVien').val(),
        idPhong: $('#idPhong').val(),
        ngayGiang: $('#ngayGiang').val(),
        tietBatDau: parseInt($('#tietBatDau').val()),
        tietKetThuc: parseInt($('#tietKetThuc').val())
    };

    if (!formData.idGiangVien || !formData.idPhong || !formData.ngayGiang ||
        !formData.tietBatDau || !formData.tietKetThuc) {
        showErr('Lỗi', 'Vui lòng nhập đầy đủ thông tin để kiểm tra trùng lịch');
        return;
    }

    $.ajax({
        url: pref_url + '/kiem-tra-trung-lich',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(formData),
        beforeSend: function() {
            showBoxLoading('box-frm');
        },
        success: function(response) {
            hideBoxLoading('box-frm');

            if (response.resCode === 1) {
                if (response.resData && response.resData.length > 0) {
                    var conflicts = response.resData;
                    var message = 'Phát hiện xung đột lịch:\n';
                    conflicts.forEach(function(conflict) {
                        message += '- ' + conflict.message + '\n';
                    });
                    showErr('Xung đột lịch', message);
                } else {
                    showToastSuc('Không có xung đột lịch');
                }
            } else {
                showErr('Lỗi', response.resMsg || 'Không thể kiểm tra trùng lịch');
            }
        },
        error: function(xhr, status, error) {
            hideBoxLoading('box-frm');
            console.error('Error checking conflicts:', error);
            showErr('Lỗi', 'Có lỗi xảy ra khi kiểm tra trùng lịch');
        }
    });
}

// ===== VIETNAM TIMEZONE CONSTANTS AND UTILITIES =====

/**
 * Vietnam timezone offset in minutes (GMT+7)
 */
var VIETNAM_TIMEZONE_OFFSET = 7 * 60; // GMT+7 = 420 minutes

/**
 * Convert any date to Vietnam timezone (GMT+7)
 */
function convertToVietnamTimezone(date) {
    if (!date) return null;

    var inputDate = new Date(date);
    if (isNaN(inputDate.getTime())) return null;

    // Get current timezone offset
    var currentOffset = inputDate.getTimezoneOffset(); // in minutes

    // Calculate difference to Vietnam timezone
    var offsetDiff = VIETNAM_TIMEZONE_OFFSET + currentOffset; // Difference in minutes

    // Apply offset
    var vietnamDate = new Date(inputDate.getTime() + (offsetDiff * 60 * 1000));

    console.log('convertToVietnamTimezone:', {
        input: date,
        inputDate: inputDate,
        currentOffset: currentOffset,
        vietnamOffset: VIETNAM_TIMEZONE_OFFSET,
        offsetDiff: offsetDiff,
        vietnamDate: vietnamDate
    });

    return vietnamDate;
}

/**
 * Set application default timezone to Vietnam (GMT+7)
 */
function setVietnamTimezoneDefault() {
    console.log('=== SETTING VIETNAM TIMEZONE AS DEFAULT ===');

    // Log current timezone info
    var now = new Date();
    console.log('Current date:', now);
    console.log('Current timezone offset:', now.getTimezoneOffset(), 'minutes');
    console.log('Vietnam timezone offset:', VIETNAM_TIMEZONE_OFFSET, 'minutes (GMT+7)');

    // Test with current date
    var vietnamNow = convertToVietnamTimezone(now);
    console.log('Current time in Vietnam (GMT+7):', vietnamNow);
    console.log('Vietnam date format:', formatDateToVietnameseLocal(vietnamNow));

    console.log('✅ Vietnam timezone (GMT+7) set as default for all date operations');
}

// ===== EXPORT EXCEL FUNCTIONS =====

/**
 * Show export Excel modal for lecturer schedule
 */
function xuatExcelLichGiangVien() {
    console.log('=== SHOWING EXPORT EXCEL MODAL FOR LECTURER ===');

    // Load lecturers into dropdown
    loadGiangVienForExport();

    // Set default week to current week
    setDefaultWeek('#exportTuan');

    $('#exportExcelModal').modal('show');
}

/**
 * Show export Excel modal for class schedule
 */
function xuatExcelLichLop() {
    console.log('=== SHOWING EXPORT EXCEL MODAL FOR CLASS ===');

    // Load classes into dropdown
    loadLopForExport();

    // Set default week to current week
    setDefaultWeek('#exportTuanLop');

    $('#exportExcelLopModal').modal('show');
}

/**
 * Show export Excel modal for school-wide schedule
 */
function xuatExcelToanTruong() {
    console.log('=== SHOWING EXPORT EXCEL MODAL FOR SCHOOL ===');

    // Set default week to current week
    setDefaultWeek('#exportTuanToanTruong');

    $('#exportExcelToanTruongModal').modal('show');
}

/**
 * Load lecturers for export dropdown
 */
function loadGiangVienForExport() {
    $.ajax({
        url: pref_url + '/lay-du-lieu-ban-dau',
        method: 'get',
        success: function(res) {
            if (res.resCode > 0 && res.resData['canBo']) {
                var $select = $('#exportGiangVien');
                $select.find('option:not(:first)').remove();

                $.each(res.resData['canBo'], function(i, obj) {
                    $select.append($('<option>', {
                        value: obj.idCanBo,
                        text: obj.hoTen
                    }));
                });
            }
        },
        error: function() {
            showToastErr('Không thể tải danh sách giảng viên', 3000);
        }
    });
}

/**
 * Load classes for export dropdown
 */
function loadLopForExport() {
    $.ajax({
        url: pref_url + '/lay-lop',
        method: 'get',
        success: function(res) {
            if (res.resCode > 0) {
                var $select = $('#exportLop');
                $select.find('option:not(:first)').remove();

                $.each(res.resData, function(i, obj) {
                    $select.append($('<option>', {
                        value: obj.idLop,
                        text: obj.tenLop
                    }));
                });
            }
        },
        error: function() {
            showToastErr('Không thể tải danh sách lớp học', 3000);
        }
    });
}

/**
 * Set default week to current week
 */
function setDefaultWeek(selector) {
    var today = new Date();
    var year = today.getFullYear();
    var week = getWeekNumber(today);

    // Format: YYYY-WXX
    var weekString = year + '-W' + (week < 10 ? '0' + week : week);
    $(selector).val(weekString);

    // Update date range using datepicker
    updateDateRangeFromWeek(selector);
}

/**
 * Get week number from date
 */
function getWeekNumber(date) {
    var d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
    var dayNum = d.getUTCDay() || 7;
    d.setUTCDate(d.getUTCDate() + 4 - dayNum);
    var yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
    return Math.ceil((((d - yearStart) / 86400000) + 1) / 7);
}

/**
 * Update date range when week changes
 */
function updateDateRangeFromWeek(weekSelector) {
    var weekValue = $(weekSelector).val();
    if (!weekValue) return;

    var parts = weekValue.split('-W');
    if (parts.length !== 2) return;

    var year = parseInt(parts[0]);
    var week = parseInt(parts[1]);

    // Calculate Monday of the week
    var jan1 = new Date(year, 0, 1);
    var daysToAdd = (week - 1) * 7 - jan1.getDay() + 1;
    var monday = new Date(year, 0, 1 + daysToAdd);

    // Calculate Sunday of the week
    var sunday = new Date(monday);
    sunday.setDate(monday.getDate() + 6);

    // Format dates to YYYY-MM-DD for HTML5 date inputs
    var mondayStr = monday.toISOString().split('T')[0];
    var sundayStr = sunday.toISOString().split('T')[0];

    // Convert to Vietnamese format for datepicker
    var mondayVN = formatDateToVietnamese(mondayStr);
    var sundayVN = formatDateToVietnamese(sundayStr);

    // Update corresponding date fields using datepicker
    if (weekSelector.includes('exportTuan') && !weekSelector.includes('Lop') && !weekSelector.includes('ToanTruong')) {
        $('#exportTuanBatDau').datepicker('setDate', mondayVN);
        $('#exportTuanKetThuc').datepicker('setDate', sundayVN);
    } else if (weekSelector.includes('exportTuanLop')) {
        $('#exportTuanBatDauLop').datepicker('setDate', mondayVN);
        $('#exportTuanKetThucLop').datepicker('setDate', sundayVN);
    } else if (weekSelector.includes('exportTuanToanTruong')) {
        $('#exportTuanBatDauToanTruong').datepicker('setDate', mondayVN);
        $('#exportTuanKetThucToanTruong').datepicker('setDate', sundayVN);
    }
}

/**
 * Confirm and execute Excel export for lecturer
 */
function confirmExportExcel() {
    console.log('=== CONFIRMING EXCEL EXPORT FOR LECTURER ===');

    var giangVienId = $('#exportGiangVien').val();
    var tuNgayVN = $('#exportTuanBatDau').val(); // dd/mm/yyyy format
    var denNgayVN = $('#exportTuanKetThuc').val(); // dd/mm/yyyy format

    if (!giangVienId) {
        showToastErr('Vui lòng chọn giảng viên', 3000);
        return;
    }

    if (!tuNgayVN || !denNgayVN) {
        showToastErr('Vui lòng chọn tuần', 3000);
        return;
    }

    // Convert to ISO format for backend
    var tuNgay = formatDateToISO(tuNgayVN);
    var denNgay = formatDateToISO(denNgayVN);

    console.log('Exporting for lecturer:', giangVienId, 'from', tuNgay, 'to', denNgay);

    // Create download URL - using existing endpoint
    var url = pref_url + '/xuat-excel-lich-giang-vien-mau?' +
              'idGiangVien=' + encodeURIComponent(giangVienId) +
              '&tuNgay=' + encodeURIComponent(tuNgay) +
              '&denNgay=' + encodeURIComponent(denNgay);

    // Download file
    window.open(url, '_blank');

    // Close modal
    $('#exportExcelModal').modal('hide');

    // Use system toast notification
    showToastSuc('Đang tải file Excel cho giảng viên...', 3000);
}

/**
 * Confirm and execute Excel export for class
 */
function confirmExportExcelLop() {
    console.log('=== CONFIRMING EXCEL EXPORT FOR CLASS ===');

    var lopId = $('#exportLop').val();
    var tuNgayVN = $('#exportTuanBatDauLop').val(); // dd/mm/yyyy format
    var denNgayVN = $('#exportTuanKetThucLop').val(); // dd/mm/yyyy format

    if (!lopId) {
        showToastErr('Vui lòng chọn lớp', 3000);
        return;
    }

    if (!tuNgayVN || !denNgayVN) {
        showToastErr('Vui lòng chọn tuần', 3000);
        return;
    }

    // Convert to ISO format for backend
    var tuNgay = formatDateToISO(tuNgayVN);
    var denNgay = formatDateToISO(denNgayVN);

    console.log('Exporting for class:', lopId, 'from', tuNgay, 'to', denNgay);

    // Create download URL - using existing endpoint
    var url = pref_url + '/xuat-excel-lich-lop-mau?' +
              'idLop=' + encodeURIComponent(lopId) +
              '&tuNgay=' + encodeURIComponent(tuNgay) +
              '&denNgay=' + encodeURIComponent(denNgay);

    // Download file
    window.open(url, '_blank');

    // Close modal
    $('#exportExcelLopModal').modal('hide');

    // Use system toast notification
    showToastSuc('Đang tải file Excel cho lớp học...', 3000);
}

/**
 * Confirm and execute Excel export for school-wide
 */
function confirmExportExcelToanTruong() {
    console.log('=== CONFIRMING EXCEL EXPORT FOR SCHOOL ===');

    var tuNgayVN = $('#exportTuanBatDauToanTruong').val(); // dd/mm/yyyy format
    var denNgayVN = $('#exportTuanKetThucToanTruong').val(); // dd/mm/yyyy format

    if (!tuNgayVN || !denNgayVN) {
        showToastErr('Vui lòng chọn tuần', 3000);
        return;
    }

    // Convert to ISO format for backend
    var tuNgay = formatDateToISO(tuNgayVN);
    var denNgay = formatDateToISO(denNgayVN);

    console.log('Exporting school-wide from', tuNgay, 'to', denNgay);

    // Create download URL - using existing endpoint
    var url = pref_url + '/xuat-excel-lich-toan-truong-mau?' +
              'tuNgay=' + encodeURIComponent(tuNgay) +
              '&denNgay=' + encodeURIComponent(denNgay);

    // Download file
    window.open(url, '_blank');

    // Close modal
    $('#exportExcelToanTruongModal').modal('hide');

    // Use system toast notification
    showToastSuc('Đang tải file Excel toàn trường...', 3000);
}

// ===== CONFLICT REPORT FUNCTIONS =====

/**
 * Show conflict report modal
 */
function showConflictReport() {
    console.log('=== SHOWING CONFLICT REPORT MODAL ===');

    // Set default date range (current week)
    var today = new Date();
    var monday = new Date(today);
    monday.setDate(today.getDate() - today.getDay() + 1);
    var sunday = new Date(monday);
    sunday.setDate(monday.getDate() + 6);

    var mondayStr = monday.toISOString().split('T')[0];
    var sundayStr = sunday.toISOString().split('T')[0];

    // Convert to Vietnamese format and set using datepicker
    var mondayVN = formatDateToVietnamese(mondayStr);
    var sundayVN = formatDateToVietnamese(sundayStr);

    $('#conflictFromDate').datepicker('setDate', mondayVN);
    $('#conflictToDate').datepicker('setDate', sundayVN);

    $('#conflictReportModal').modal('show');
}

/**
 * Generate conflict report
 */
function generateConflictReport() {
    console.log('=== GENERATING CONFLICT REPORT ===');

    var tuNgayVN = $('#conflictFromDate').val(); // dd/mm/yyyy format
    var denNgayVN = $('#conflictToDate').val(); // dd/mm/yyyy format

    if (!tuNgayVN || !denNgayVN) {
        showToastErr('Vui lòng chọn khoảng thời gian', 3000);
        return;
    }

    // Convert Vietnamese format to ISO format for backend
    var tuNgay = formatDateToISO(tuNgayVN);
    var denNgay = formatDateToISO(denNgayVN);

    if (!tuNgay || !denNgay) {
        showToastErr('Định dạng ngày không hợp lệ', 3000);
        return;
    }

    if (new Date(tuNgay) > new Date(denNgay)) {
        showToastErr('Ngày bắt đầu phải nhỏ hơn ngày kết thúc', 3000);
        return;
    }

    console.log('Generating conflict report from', tuNgay, 'to', denNgay);

    showBoxLoading('conflictReportModal');

    $.ajax({
        url: pref_url + '/bao-cao-xung-dot',
        type: 'GET',
        data: {
            tuNgay: tuNgay,
            denNgay: denNgay
        },
        success: function(response) {
            hideBoxLoading('conflictReportModal');
            console.log('Conflict report response:', response);

            if (response.resCode === 1) {
                // Handle different response formats
                var conflicts = response.resData;
                if (response.resData && response.resData.conflicts) {
                    conflicts = response.resData.conflicts;
                }
                displayConflictReport(conflicts);
            } else {
                showToastErr(response.resMsg || 'Không thể tạo báo cáo xung đột', 3000);
            }
        },
        error: function(xhr, status, error) {
            hideBoxLoading('conflictReportModal');
            console.error('Error generating conflict report:', error);
            showToastErr('Có lỗi xảy ra khi tạo báo cáo xung đột', 3000);
        }
    });
}

/**
 * Display conflict report results
 */
function displayConflictReport(conflicts) {
    console.log('=== DISPLAYING CONFLICT REPORT ===');
    console.log('Conflicts found:', conflicts);

    var $resultDiv = $('#conflictResults');
    $resultDiv.empty();

    if (!conflicts || conflicts.length === 0) {
        $resultDiv.html('<div class="alert alert-success"><i class="fa fa-check-circle"></i> Không phát hiện xung đột lịch trong khoảng thời gian này.</div>');
        return;
    }

    var html = '<div class="alert alert-warning"><i class="fa fa-exclamation-triangle"></i> Phát hiện ' + conflicts.length + ' xung đột lịch:</div>';
    html += '<div class="table-responsive"><table class="table table-striped table-bordered">';
    html += '<thead><tr>';
    html += '<th>Loại xung đột</th>';
    html += '<th>Thời gian</th>';
    html += '<th>Chi tiết</th>';
    html += '<th>Hành động</th>';
    html += '</tr></thead><tbody>';

    conflicts.forEach(function(conflict, index) {
        html += '<tr>';
        html += '<td><span class="label label-danger">' + (conflict.type || 'Xung đột') + '</span></td>';
        html += '<td>' + (conflict.time || '') + '</td>';
        html += '<td>' + (conflict.description || conflict.message || '') + '</td>';
        html += '<td><button class="btn btn-sm btn-primary" onclick="resolveConflict(' + index + ')">Xử lý</button></td>';
        html += '</tr>';
    });

    html += '</tbody></table></div>';
    $resultDiv.html(html);
}

/**
 * Resolve a specific conflict
 */
function resolveConflict(conflictIndex) {
    console.log('=== RESOLVING CONFLICT ===');
    console.log('Conflict index:', conflictIndex);

    showToastSuc('Chức năng xử lý xung đột sẽ được phát triển trong phiên bản tiếp theo', 4000);
}

// ===== MULTIPLE WEEKS SCHEDULE FUNCTIONS =====

/**
 * Show multiple weeks creation modal
 */
function showMultipleWeeksModal() {
    console.log('=== SHOWING MULTIPLE WEEKS MODAL ===');

    // Load template schedules
    loadTemplateSchedules();

    $('#multipleWeeksModal').modal('show');
}

/**
 * Load template schedules for selection
 */
function loadTemplateSchedules() {
    console.log('=== LOADING TEMPLATE SCHEDULES ===');

    $.ajax({
        url: pref_url + '/lay-lich-mau',
        type: 'GET',
        success: function(response) {
            console.log('Template schedules response:', response);

            var $select = $('#templateScheduleId');
            $select.find('option:not(:first)').remove();

            if (response.resCode === 1 && response.resData) {
                $.each(response.resData, function(index, schedule) {
                    var optionText = schedule.tenMonHoc + ' - ' + schedule.tenLop + ' (' + schedule.ngayGiangText + ')';
                    $select.append('<option value="' + schedule.idLichGiang + '">' + optionText + '</option>');
                });
            } else {
                $select.append('<option value="">Không có lịch mẫu</option>');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error loading template schedules:', error);
            var $select = $('#templateScheduleId');
            $select.find('option:not(:first)').remove();
            $select.append('<option value="">Lỗi tải dữ liệu</option>');
        }
    });
}

/**
 * Create multiple weeks schedule
 */
function createMultipleWeeks() {
    console.log('=== CREATING MULTIPLE WEEKS SCHEDULE ===');

    var templateId = $('#templateScheduleId').val();
    var dateList = $('#weekDateList').val();

    if (!templateId) {
        showToastErr('Vui lòng chọn lịch mẫu', 3000);
        return;
    }

    if (!dateList || dateList.trim() === '') {
        showToastErr('Vui lòng nhập danh sách ngày', 3000);
        return;
    }

    // Validate date format
    var dates = dateList.split('\n').map(function(date) {
        return date.trim();
    }).filter(function(date) {
        return date !== '';
    });

    if (dates.length === 0) {
        showToastErr('Vui lòng nhập ít nhất một ngày', 3000);
        return;
    }

    // Validate date format (YYYY-MM-DD)
    var dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    var invalidDates = dates.filter(function(date) {
        return !dateRegex.test(date);
    });

    if (invalidDates.length > 0) {
        showToastErr('Định dạng ngày không hợp lệ: ' + invalidDates.join(', ') + '. Vui lòng sử dụng định dạng YYYY-MM-DD', 5000);
        return;
    }

    console.log('Creating schedule for template:', templateId, 'dates:', dates);

    showBoxLoading('multipleWeeksModal');

    $.ajax({
        url: pref_url + '/tao-lich-nhieu-tuan',
        type: 'POST',
        data: {
            idLichMau: templateId,
            danhSachNgay: dates.join(',')
        },
        success: function(response) {
            hideBoxLoading('multipleWeeksModal');
            console.log('Multiple weeks creation response:', response);

            if (response.resCode === 1) {
                showToastSuc('Đã tạo lịch cho ' + dates.length + ' tuần thành công', 4000);
                $('#multipleWeeksModal').modal('hide');

                // Refresh the grid
                timKiem(0);
            } else {
                showToastErr(response.resMsg || 'Không thể tạo lịch nhiều tuần', 3000);
            }
        },
        error: function(xhr, status, error) {
            hideBoxLoading('multipleWeeksModal');
            console.error('Error creating multiple weeks:', error);
            showToastErr('Có lỗi xảy ra khi tạo lịch nhiều tuần', 3000);
        }
    });
}

// ===== UTILITY FUNCTIONS =====

/**
 * Helper function to format date for display (Vietnamese format dd/mm/yyyy) with GMT+7 timezone
 */
function formatDateForDisplay(dateString) {
    if (!dateString) return '';

    console.log('formatDateForDisplay input:', dateString);

    // Use the Vietnam timezone function for consistency
    return formatDateToVietnameseLocal(dateString);
}

/**
 * Convert YYYY-MM-DD to dd/mm/yyyy format with proper timezone handling
 */
function formatDateToVietnamese(dateString) {
    if (!dateString) return '';

    console.log('formatDateToVietnamese input:', dateString);

    // Use the new local date formatting function
    var result = formatDateToVietnameseLocal(dateString);
    console.log('formatDateToVietnamese output:', result);
    return result;
}

/**
 * Convert dd/mm/yyyy to YYYY-MM-DD format
 */
function formatDateToISO(dateString) {
    if (!dateString) return '';

    // Handle dd/mm/yyyy format
    if (dateString.match(/^\d{1,2}\/\d{1,2}\/\d{4}$/)) {
        var parts = dateString.split('/');
        var day = parts[0].padStart(2, '0');
        var month = parts[1].padStart(2, '0');
        var year = parts[2];

        // Validate date
        var date = new Date(year, month - 1, day);
        if (date.getFullYear() == year && date.getMonth() == month - 1 && date.getDate() == day) {
            return year + '-' + month + '-' + day;
        }
    }

    return dateString;
}

/**
 * Parse date string without timezone conversion (for local dates)
 */
function parseLocalDate(dateString) {
    if (!dateString) return null;

    console.log('parseLocalDate input:', dateString);

    // Handle YYYY-MM-DD format directly (no timezone conversion)
    if (dateString.match(/^\d{4}-\d{2}-\d{2}$/)) {
        var parts = dateString.split('-');
        var year = parseInt(parts[0]);
        var month = parseInt(parts[1]) - 1; // Month is 0-indexed
        var day = parseInt(parts[2]);

        // Create date in local timezone
        var localDate = new Date(year, month, day);
        console.log('parseLocalDate created local date:', localDate);
        return localDate;
    }

    // Handle other formats
    return new Date(dateString);
}

/**
 * Format date to Vietnamese with fixed GMT+7 timezone (Vietnam timezone)
 */
function formatDateToVietnameseLocal(dateInput) {
    if (!dateInput) return '';

    console.log('formatDateToVietnameseLocal input:', dateInput);

    var date;

    // Handle different input types
    if (typeof dateInput === 'string') {
        if (dateInput.match(/^\d{4}-\d{2}-\d{2}$/)) {
            // YYYY-MM-DD format - parse as Vietnam timezone (GMT+7)
            var parts = dateInput.split('-');
            var year = parseInt(parts[0]);
            var month = parseInt(parts[1]) - 1; // Month is 0-indexed
            var day = parseInt(parts[2]);

            // Create date in Vietnam timezone (GMT+7)
            date = new Date(year, month, day, 12, 0, 0); // Set to noon to avoid timezone edge cases
            console.log('Created local date for Vietnam timezone:', date);
        } else {
            // Handle ISO strings or other formats
            date = new Date(dateInput);

            // Convert to Vietnam timezone (GMT+7)
            var vietnamOffset = 7 * 60; // GMT+7 in minutes
            var currentOffset = date.getTimezoneOffset(); // Current timezone offset in minutes
            var offsetDiff = vietnamOffset + currentOffset; // Difference to GMT+7

            date = new Date(date.getTime() + (offsetDiff * 60 * 1000));
            console.log('Converted to Vietnam timezone (GMT+7):', date);
        }
    } else if (typeof dateInput === 'number') {
        // Handle timestamp
        date = new Date(dateInput);

        // Convert to Vietnam timezone (GMT+7)
        var vietnamOffset = 7 * 60; // GMT+7 in minutes
        var currentOffset = date.getTimezoneOffset();
        var offsetDiff = vietnamOffset + currentOffset;

        date = new Date(date.getTime() + (offsetDiff * 60 * 1000));
        console.log('Converted timestamp to Vietnam timezone (GMT+7):', date);
    } else if (dateInput instanceof Date) {
        // Handle Date object
        date = new Date(dateInput.getTime());

        // Convert to Vietnam timezone (GMT+7)
        var vietnamOffset = 7 * 60; // GMT+7 in minutes
        var currentOffset = date.getTimezoneOffset();
        var offsetDiff = vietnamOffset + currentOffset;

        date = new Date(date.getTime() + (offsetDiff * 60 * 1000));
        console.log('Converted Date object to Vietnam timezone (GMT+7):', date);
    } else {
        return '';
    }

    if (isNaN(date.getTime())) {
        console.warn('Invalid date:', dateInput);
        return '';
    }

    var day = date.getDate().toString().padStart(2, '0');
    var month = (date.getMonth() + 1).toString().padStart(2, '0');
    var year = date.getFullYear();

    var result = day + '/' + month + '/' + year;
    console.log('formatDateToVietnameseLocal output (GMT+7):', result);
    return result;
}

/**
 * Helper function to validate date range
 */
function validateDateRange(fromDate, toDate) {
    if (!fromDate || !toDate) {
        return { valid: false, message: 'Vui lòng chọn đầy đủ ngày bắt đầu và kết thúc' };
    }

    var from = new Date(fromDate);
    var to = new Date(toDate);

    if (isNaN(from.getTime()) || isNaN(to.getTime())) {
        return { valid: false, message: 'Định dạng ngày không hợp lệ' };
    }

    if (from > to) {
        return { valid: false, message: 'Ngày bắt đầu phải nhỏ hơn ngày kết thúc' };
    }

    // Check if range is too large (more than 3 months)
    var diffTime = Math.abs(to - from);
    var diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays > 90) {
        return { valid: false, message: 'Khoảng thời gian không được vượt quá 3 tháng' };
    }

    return { valid: true };
}

/**
 * Helper function to show loading state
 */
function showBoxLoading(containerId) {
    var $container = $('#' + containerId);
    if ($container.length === 0) {
        $container = $(containerId);
    }

    if ($container.length > 0) {
        $container.append('<div class="loading-overlay"><div class="loading-spinner"><i class="fa fa-spinner fa-spin fa-2x"></i><br>Đang xử lý...</div></div>');
    }
}

/**
 * Helper function to hide loading state
 */
function hideBoxLoading(containerId) {
    var $container = $('#' + containerId);
    if ($container.length === 0) {
        $container = $(containerId);
    }

    if ($container.length > 0) {
        $container.find('.loading-overlay').remove();
    }
}

// Note: showToastSuc, showErr, showInfo, showCfm functions are available globally from script.js
// We don't need to redefine them here, just use the global functions directly

// ===== SELECT2 INITIALIZATION =====

/**
 * Initialize select2 for all select elements
 */
function initializeSelect2() {
    // Initialize select2 for all select elements that don't have it yet
    $('select:not(.select2-hidden-accessible)').each(function() {
        var $select = $(this);

        // Skip if already initialized
        if ($select.hasClass('select2-hidden-accessible')) {
            return;
        }

        // Initialize with basic config
        $select.select2({
            placeholder: $select.find('option:first').text() || 'Chọn...',
            allowClear: true,
            width: '100%'
        });
    });

    console.log('Select2 initialized for all select elements');
}

// ===== DATE INPUT INITIALIZATION =====

/**
 * Initialize date inputs with Vietnamese format (dd/mm/yyyy) using Bootstrap datepicker
 */
function initializeDateInputs() {
    console.log('=== INITIALIZING DATE INPUTS WITH BOOTSTRAP DATEPICKER ===');

    // Find all date inputs and convert them to text inputs for datepicker
    var dateInputs = [
        '#tuNgay', '#denNgay', '#ngayGiang',
        '#conflictFromDate', '#conflictToDate',
        '#exportTuanBatDau', '#exportTuanKetThuc',
        '#exportTuanBatDauLop', '#exportTuanKetThucLop',
        '#exportTuanBatDauToanTruong', '#exportTuanKetThucToanTruong'
    ];

    dateInputs.forEach(function(selector) {
        var $input = $(selector);
        if ($input.length > 0) {
            initializeSingleDateInput($input);
        }
    });

    console.log('Date inputs initialized with Bootstrap datepicker (dd/mm/yyyy format)');
}

/**
 * Initialize a single date input with Bootstrap datepicker (dd/mm/yyyy format)
 */
function initializeSingleDateInput($input) {
    console.log('Initializing datepicker for:', $input.attr('id'));

    // Convert date input to text input for datepicker
    if ($input.attr('type') === 'date') {
        $input.attr('type', 'text');
    }

    // Add date-picker class and placeholder
    $input.addClass('date-picker form-control');
    $input.attr('placeholder', 'dd/mm/yyyy');
    $input.attr('readonly', true); // Make readonly to force datepicker usage

    // Destroy existing datepicker if any
    if ($input.data('datepicker')) {
        $input.datepicker('destroy');
    }

    // Initialize Bootstrap datepicker with Vietnamese format
    $input.datepicker({
        autoclose: true,
        todayHighlight: true,
        format: 'dd/mm/yyyy',
        language: 'vi',
        orientation: 'bottom auto',
        weekStart: 1, // Monday
        daysOfWeekHighlighted: [0, 6], // Highlight weekends
        clearBtn: true,
        todayBtn: 'linked'
    });

    console.log('Datepicker initialized for:', $input.attr('id'));
}

// ===== DATA PROCESSING FUNCTIONS =====

/**
 * Process lich giang data to add tuanGiangText field
 */
function processLichGiangData(data) {
    if (!data || !Array.isArray(data)) {
        return data;
    }

    return data.map(function(item) {
        // Add tuanGiangText field
        if (item.tuanGiang) {
            item.tuanGiangText = 'T' + item.tuanGiang;
        } else {
            item.tuanGiangText = '';
        }

        return item;
    });
}

/**
 * Get current week number for a semester
 */
function getCurrentWeekNumber(idHocKy, callback) {
    if (!idHocKy) {
        callback(null);
        return;
    }

    $.ajax({
        url: pref_url + '/lay-thong-tin-tuan-day',
        type: 'GET',
        data: { idHocKy: idHocKy },
        success: function(response) {
            if (response.resCode === 1 && response.resData.tuanHienTai) {
                callback(response.resData.tuanHienTai);
            } else {
                callback(null);
            }
        },
        error: function() {
            callback(null);
        }
    });
}

/**
 * Calculate week number from date and semester start date
 */
function calculateWeekNumber(ngayGiang, idHocKy, callback) {
    if (!ngayGiang || !idHocKy) {
        callback(null);
        return;
    }

    // Convert date format if needed
    var formattedDate = ngayGiang;
    if (ngayGiang.includes('/')) {
        var dateParts = ngayGiang.split('/');
        formattedDate = dateParts[2] + '-' + dateParts[1] + '-' + dateParts[0];
    }

    $.ajax({
        url: pref_url + '/tinh-tuan-hoc',
        type: 'GET',
        data: {
            idHocKy: idHocKy,
            ngayCanTinh: formattedDate
        },
        success: function(response) {
            if (response.resCode === 1 && response.resData.tuanHoc) {
                callback(response.resData.tuanHoc);
            } else {
                callback(null);
            }
        },
        error: function() {
            callback(null);
        }
    });
}
