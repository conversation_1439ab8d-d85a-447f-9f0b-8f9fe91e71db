:root {
    --primary-color:#3c8dbc;
    --success-color:#008d4c;
}

@media (max-width: 767px) {
    .form-inline .form-group {
        display: inline-block;
        margin-bottom: 0;
        vertical-align: middle;
    }
}

@media (min-width: 768px) {
    .modal-dialog {
        width: 750px;
        margin: 30px auto;
    }
}

body {
    font-family: "Roboto", "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}

/* Loading overlay styles */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    text-align: center;
    color: #337ab7;
    font-size: 14px;
}

.loading-spinner i {
    margin-bottom: 10px;
}

/* Date input styles for Vietnamese format */
input[type="date"] {
    position: relative;
}

input[type="date"]::-webkit-calendar-picker-indicator {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"><path fill="%23999" d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"/></svg>');
    cursor: pointer;
}

/* Add Vietnamese date format hint */
input[type="date"]::before {
    content: attr(placeholder);
    color: #999;
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
    font-size: 14px;
}

input[type="date"]:focus::before,
input[type="date"]:valid::before {
    display: none;
}

/* Date input with Vietnamese format indicator */
.date-input-vietnamese {
    position: relative;
}

.date-input-vietnamese::after {
    content: "dd/mm/yyyy";
    position: absolute;
    right: 30px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
    font-size: 12px;
    pointer-events: none;
    background: white;
    padding: 0 5px;
}

textarea {
	resize:none;
}

.table>tbody>tr>td,.table>thead>tr>th {
    vertical-align:middle;
}

.tbl-primary thead th {
    /*border:1px solid var(--primary-color);
    background-color:var(--primary-color);
    color:#fff;
    background-color:#fff;
    box-shadow: 2px 0px 3px #888888;*/
}

/*.tbl-primary thead th {
    border-bottom:1px solid var(--primary-color) !important;
}

.tbl-primary th:first-child {
    border-left-color:var(--primary-color);
}

.tbl-primary th:last-child {
    border-right-color:var(--primary-color);
}*/

.tbl-success thead {
    border:1px solid var(--success-color);
    background-color:var(--success-color);
    color:#fff;
}

.tbl-success th {
    border-bottom:1px solid var(--success-color) !important;
}

.tbl-success th:first-child {
    border-left-color:var(--success-color);
}

.tbl-success th:last-child {
    border-right-color:var(--success-color);
}

.text-danger {
    color:#dd4b39;
}

.table-bordered>tbody>tr>td, .table-bordered>tbody>tr>th, .table-bordered>tfoot>tr>td, .table-bordered>tfoot>tr>th, .table-bordered>thead>tr>td, .table-bordered>thead>tr>th {
    border: 1px solid #ddd !important;
}

/*.box-body .form-group:last-child {
    margin-bottom:0px;
}*/

.inline-comm {
    padding:2px;
}

.page-overlay {
    position:fixed;
    width:100%;
    height:100%;
    top:0;
    left:0;
    background: rgba(255,255,255,0.7);
    z-index:10000;
}

.page-overlay .fa {
    position: absolute;
    top: 50%;
    left: 50%;
    margin-left: -15px;
    margin-top: -15px;
    color: #000;
    font-size: 30px;
}

.modal-err {
	background-color: #dd4b39 !important;
	color:#fff;
}

.modal-cfrm {
	background-color: #d58512 !important;
	color:#fff;
}

.modal-pri .modal-header {
	background-color: var(--primary-color);;
	color:#fff;
}

.modal-suc .modal-header {
	background-color: var(--success-color);;
	color:#fff;
}

.btn {
    outline:none !important;
}

.toast {
	position: fixed;
	max-width: 50%;
	color: #fff;
	top:65px;
	left:50%;
	border-radius: 100px;
	padding-top:5px;
	padding-bottom:5px;
	padding-left:15px;
	padding-right:15px;
	z-index:1000000;
	box-shadow: 0px 0px 10px #666666;
	text-align: center;
	transform: translate(-50%);
	display: none;
}

.toast-info {
	background-color: #00a65a;
}

.toast-err {
	background-color: #ac2925;
}

/*
.active-suc a {
    background-color: #00a65a !important;
    border-color: #008d4c !important;
    color:#fff !important;
}*/

.pdfobject-container {
	width: 100%;
	height: 600px;
}

/*.fa {
    font-size:14px;
}*/

.form .select2-container, form .select2-container  {
    width:100% !important;
}

.select2 .selection {
    text-align:left;
}

/*.form-inline .select2-container {
    width:initial !important;
}*/

.cmd {
    margin:3px;
}

.box.box-solid>.box-header>.box-tools .btn {
    border: 1px solid #d2d6de;
}

.box-trans {
    border-top:0px;
    box-shadow:none;
    position: relative;
    border-radius: 0px;
    background: #ffffff;
    margin-bottom: 20px;
}

.box-trans .overlay {
    z-index: 50;
    background: rgba(255,255,255,0.7);
    border-radius: 3px;
}
.box-trans>.overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.box-trans .overlay>.fa {
    position: absolute;
    top: 50%;
    left: 50%;
    margin-left: -15px;
    margin-top: -15px;
    color: #000;
    font-size: 30px;
}
/* check box linhCMU */
.switch {
    position: relative;
    display: inline-block;
    width: 30px;
    height: 20px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    -webkit-transition: .4s;
    transition: .4s;
}

.slider:before {
    position: absolute;
    content: "";
    height: 15px;
    width: 15px;
    left: 2px;
    bottom: 3px;
    background-color: white;
    -webkit-transition: .4s;
    transition: .4s;
}

input:checked + .slider {
    /* background-color: #2196F3; */
    background-color: #11b406;
}

input:focus + .slider {
    box-shadow: 0 0 1px #2196F3;
}

input:checked + .slider:before {
    -webkit-transform: translateX(13px);
    -ms-transform: translateX(13px);
    transform: translateX(13px);
}

/* Rounded sliders */
.slider.round {
    border-radius: 34px;
}

.slider.round:before {
    border-radius: 50%;
}
/* end: check box linhCMU */

.required:after {
    content:" (*)";
    color: red;
    position:absolute;
    top:5;
    font-size: 10px;
  }

.displayNone {
    display: none;
}

@font-face {
    font-family: 'Material Icons';
    font-style: normal;
    font-weight: 400;
    src: url(https://fonts.gstatic.com/s/materialicons/v52/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');
  }
  
  .material-icons {
    font-family: 'Material Icons';
    font-weight: normal;
    font-style: normal;
    font-size: inherit;
    line-height: 1;
    letter-spacing: normal;
    text-transform: none;
    display: inline-block;
    white-space: nowrap;
    word-wrap: normal;
    direction: ltr;
    -webkit-font-smoothing: antialiased;
  }

.fa[data-count]{
    position:relative;
}
.fa[data-count]:after{
    position: absolute;
    right: -1.25em;
    top: -1.25em;
    content: attr(data-count);
    padding: .5em;
    border-radius: 10em;
    line-height: .9em;
    color: white;
    background: rgba(255,0,0,.75);
    text-align: center;
    min-width: 2em;
    font: bold .46em sans-serif;
}

.tab-title {
    display: inline-block;
    font-size: 18px;
    margin: 0;
    line-height: 1;
}
.icon-cons{
    position: relative;
}
.icon-cons::after{
    position: absolute;
    bottom: -7px;
    right: -13px;
    font-size: 11px;
    /* text-shadow: 1px 1px grey; */
    font-weight: bold;
    padding: 1px 3px;
    background: white;
    border-radius: 7px;
    border: solid 1px rgba(0, 0, 0, 0.2);
}
.k-dat::after{
    content: 'K';
    color: red;
}
.dat::after{
    content: 'Đ';
    color: blue;
}
.text-icon::after{
    bottom: -7px;
    right: -13px;
    font-size: 11px;
    /* text-shadow: 1px 1px grey; */
    font-weight: bold;
    padding: 1px 3px;
    background: white;
    border-radius: 7px;
    border: solid 1px rgba(0, 0, 0, 0.2);
}
.gxn::after{
    content: 'GXN';
    color: blue;
}

.gxnvv::after{
    content: 'GXNVV';
    color: green;
}

.syll::after{
    content: 'SYLL';
    color: blue;
}
.disabled-select {
    background-color: #d5d5d5;
    opacity: 0.5;
    border-radius: 3px;
    cursor: not-allowed;
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
  }
  
  select[readonly].select2-hidden-accessible + .select2-container {
    pointer-events: none;
    touch-action: none;
  }
  
  select[readonly].select2-hidden-accessible + .select2-container .select2-selection {
    background: #eee;
    box-shadow: none;
  }
  
  select[readonly].select2-hidden-accessible + .select2-container .select2-selection__arrow,
  select[readonly].select2-hidden-accessible + .select2-container .select2-selection__clear {
    display: none;
  }

/* Material Design Table Styling */
/* General styling for the table */
.table-material {
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    overflow: hidden;
}

.table-material th {
    background-color: #03a9f4;
    color: white;
    font-weight: 600;
    text-align: left;
    padding: 12px;
    position: sticky;
    top: 0;
    z-index: 1;
}

.table-material th:first-child {
    border-top-left-radius: 10px;
}

.table-material th:last-child {
    border-top-right-radius: 10px;
}

.table-material td {
    padding: 0;
    border-bottom: 1px solid #ddd;
    position: relative;
    vertical-align: middle; /* Ensure content is centered vertically */
}

/* Style for cells without input fields */
.table-material td:not(:has(input)) {
    background-color: #f0f0f0; /* Light gray background */
    color: #333; /* Darker text color */
    padding-left: 12px; /* Padding to align text content nicely */
}

/* Style for cells with input fields */
.table-material td:has(input) {
    background-color: #ffffff; /* White background */
    padding: 0; /* Remove padding to align input fields nicely */
}
.table-material td:has(input) .input-sm {
    width: 100%;
    height: 100%;
    padding: 0px;
    border: none;
    box-sizing: border-box;
    font-size: 14px;
    color: #333;
    background-color: transparent;
    outline: none;
}

/* Readonly inputs with a distinct background */
.table-material td input[readonly] {
    background-color: #e9ecef; /* Light gray background for readonly fields */
    color: #6c757d; /* Darker text color for readonly fields */
}

/* Focused cell styling */
.table-material td.focused {
    border: 2px solid #007bff;
    box-shadow: 0px 0px 8px rgba(0, 123, 255, 0.5);
    z-index: 2;
}

/* Prevent focus styling on readonly inputs */
.table-material td input[readonly]:focus {
    border: none;
    box-shadow: none;
}

.notification-banner {
    float: left;
    background-color: transparent;
    background-image: none;
    padding-top: 15px;
}

.notification-banner >span {
    font-size: 14px;
    font-weight: 400;
    color: #ffc107;
    margin: 0;
    padding: 0;
}

.notification-banner >i {
    font-size: 14px;
    font-weight: 400;
    color: #ffc107;
    margin: 0;
    padding: 0;
}

.modal-thu-phi .modal-content {
    border-radius: 0;
}

.modal-thu-phi .modal-header {
    border-bottom: 3px double #00a65a;
    background: #00a65a;
    color: #00a65a;
}

.modal-thu-phi .modal-title {
    text-align: center;
    font-weight: bold;
    width: 100%;
    color: #FFFFFF;
}

.modal-thu-phi .close {
    color: #FFFFFF;
    opacity: 1;
}

.modal-thu-phi .form-horizontal .row {
    border-bottom: 1px dotted #00a65a;
    margin: 0;
    padding: 8px 0;
}

.modal-thu-phi .control-label {
    font-weight: normal;
    color: #00a65a;
}

.modal-thu-phi .form-control-static {
    font-weight: bold;
    color: #333;
}

.modal-thu-phi #grid-no {
    border: none;
    margin: 15px 0;
}

.modal-thu-phi .jdgrid th {
    border-top: 2px solid #00a65a;
    border-bottom: 2px solid #00a65a;
    background: #fff;
    color: #00a65a;
}

.modal-thu-phi .jdgrid td {
    border-bottom: 1px dotted #00a65a;
}

.modal-thu-phi .input-group-addon {
    border: none;
    background: none;
    font-weight: bold;
    color: #00a65a;
}

.modal-thu-phi .form-control {
    border: none;
    border-bottom: 1px dotted #00a65a;
    border-radius: 0;
    box-shadow: none;
    font-weight: bold;
}

.modal-thu-phi #txt-tt {
    text-align: right;
    font-weight: bold;
    color: #00a65a;
}

.modal-thu-phi .btn-success {
    background: #00a65a;
    border-color: #008d4c;
    border-radius: 0;
}

.modal-fullscreen {
    max-width: 100%;
    margin: 0;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    /* height: 100vh; */
    /* display: flex
; */
    /* position: fixed; */
    width: auto;
}

/* ===== DATATABLES PAGINATION STYLING ===== */
/* Container for pagination */
.dataTables_paginate.paging_simple_numbers {
    text-align: center;
    margin: 20px 0;
    padding: 10px 0;
}

/* Pagination wrapper */
.dataTables_paginate .paginate_button {
    display: inline-block;
    padding: 8px 12px;
    margin: 0 2px;
    border: 1px solid #ddd;
    background: #fff;
    color: #333;
    text-decoration: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
    min-width: 40px;
    text-align: center;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

/* Hover effect */
.dataTables_paginate .paginate_button:hover {
    background: #f8f9fa;
    border-color: #007bff;
    color: #007bff;
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0,123,255,0.2);
}

/* Current/Active page */
.dataTables_paginate .paginate_button.current {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border-color: #007bff;
    color: #fff;
    font-weight: 600;
    box-shadow: 0 3px 8px rgba(0,123,255,0.3);
    transform: translateY(-1px);
}

.dataTables_paginate .paginate_button.current:hover {
    background: linear-gradient(135deg, #0056b3, #004085);
    color: #fff;
    transform: translateY(-1px);
}

/* Disabled buttons (Previous/Next when not available) */
.dataTables_paginate .paginate_button.disabled {
    background: #f8f9fa;
    border-color: #e9ecef;
    color: #6c757d;
    cursor: not-allowed;
    opacity: 0.6;
    box-shadow: none;
}

.dataTables_paginate .paginate_button.disabled:hover {
    background: #f8f9fa;
    border-color: #e9ecef;
    color: #6c757d;
    transform: none;
    box-shadow: none;
}

/* Previous and Next buttons styling */
.dataTables_paginate .paginate_button.previous,
.dataTables_paginate .paginate_button.next {
    font-weight: 600;
    padding: 8px 16px;
    background: #6c757d;
    color: #fff;
    border-color: #6c757d;
}

.dataTables_paginate .paginate_button.previous:hover,
.dataTables_paginate .paginate_button.next:hover {
    background: #5a6268;
    border-color: #545b62;
    color: #fff;
}

/* First and Last buttons (if enabled) */
.dataTables_paginate .paginate_button.first,
.dataTables_paginate .paginate_button.last {
    background: #28a745;
    color: #fff;
    border-color: #28a745;
    font-weight: 600;
}

.dataTables_paginate .paginate_button.first:hover,
.dataTables_paginate .paginate_button.last:hover {
    background: #218838;
    border-color: #1e7e34;
    color: #fff;
}

/* Ellipsis styling */
.dataTables_paginate .paginate_button.ellipsis {
    background: transparent;
    border: none;
    color: #6c757d;
    cursor: default;
    box-shadow: none;
    font-weight: bold;
}

.dataTables_paginate .paginate_button.ellipsis:hover {
    background: transparent;
    border: none;
    color: #6c757d;
    transform: none;
    box-shadow: none;
}

/* Responsive design for mobile */
@media (max-width: 768px) {
    .dataTables_paginate .paginate_button {
        padding: 6px 8px;
        margin: 0 1px;
        font-size: 12px;
        min-width: 32px;
    }

    .dataTables_paginate .paginate_button.previous,
    .dataTables_paginate .paginate_button.next {
        padding: 6px 12px;
    }
}

/* Animation for page transitions */
.dataTables_paginate .paginate_button {
    position: relative;
    overflow: hidden;
}

.dataTables_paginate .paginate_button::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255,255,255,0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
}

.dataTables_paginate .paginate_button:active::before {
    width: 100px;
    height: 100px;
}

/* Custom styling for specific themes */
.dataTables_paginate.pagination-primary .paginate_button.current {
    background: linear-gradient(135deg, #3c8dbc, #2e6da4);
    border-color: #3c8dbc;
}

.dataTables_paginate.pagination-success .paginate_button.current {
    background: linear-gradient(135deg, #00a65a, #008d4c);
    border-color: #00a65a;
}

.dataTables_paginate.pagination-warning .paginate_button.current {
    background: linear-gradient(135deg, #f39c12, #e08e0b);
    border-color: #f39c12;
}

/* Info text styling (showing X to Y of Z entries) */
.dataTables_info {
    color: #6c757d;
    font-size: 14px;
    margin: 10px 0;
    font-weight: 500;
}

/* Length menu styling */
.dataTables_length select {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 4px 8px;
    margin: 0 5px;
    background: #fff;
    color: #333;
    font-size: 14px;
}

.dataTables_length select:focus {
    border-color: #007bff;
    outline: none;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}