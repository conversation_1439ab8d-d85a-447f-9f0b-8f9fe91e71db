# ===============================
# = DATA SOURCE
# ===============================
spring.datasource.url = ****************************************************************************************************************************
spring.datasource.username = qldt_cdytcm_usr
spring.datasource.password = Cmu#2020
# ===============================
#spring.datasource.url = *************************************************************************************************************************
#spring.datasource.username = qldt_usr
#spring.datasource.password = Cmu#2020
# ===== DEMO DATABASE ====
#spring.datasource.url = *************************************************************************************************************************
#spring.datasource.username = qldt_usr
#spring.datasource.password = Cmu#2020
# ===============================
# = SERVER
# ===============================
server.port = 8888
# ===============================
# = JPA / HIBERNATE
# ===============================
spring.jpa.properties.hibernate.dialect = org.hibernate.dialect.MySQL5InnoDBDialect
spring.jpa.hibernate.naming.physical-strategy = org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
#spring.jpa.properties.hibernate.proc.param_null_passing = true
spring.jpa.show-sql = false
# ===============================
# = FILE UPLOAD
# ===============================
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=100MB
upload.store.dir=/var/qldt-cdytcm/upload/
app.image.cdn=/storage/
app.nhapdiem.cdn=/nhapdiem/
#upload.store.dir=C\://tax-report/upload/
#firebase.store.dir=C\://tax-report/firebase/
# ===============================
# = JACKSON
# ===============================
#spring.jackson.time-zone = Asia/Bangkok
spring.jackson.serialization.write-dates-as-timestamps = true
# ======= Static Resources
#spring.resources.static-locations=classpath:/templates/,classpath:/META-INF/resources/,classpath:/resources/,classpath:/static/,classpath:/public/

# ===============================
# = CUSTOM PROPERTIES
# ===============================
#report.path=C\:/tax-report/report/
app.version=@project.version@

# ===============================
# = VNPAY https://pay.vnpay.vn/vpcpay.html ; https://sandbox.vnpayment.vn/paymentv2/vpcpay.html
# ===============================
vnpay.payment.url=https://pay.vnpay.vn/vpcpay.html
vnpay.return.url=http://cdytcamau.vnptcamau.vn/online-payment-return/vnpay/return
vnpay.ipn.url=http://cdytcamau.vnptcamau.vn/online-payment-return/vnpay/ipn

google.recaptcha.site=6Lfx5IIqAAAAALcyoObGfgeH8uqi1NtHWVWj8Mz2
google.recaptcha.secret=6Lfx5IIqAAAAALgiYg5BaI_FzVkxGM_QV9YZIdSy
