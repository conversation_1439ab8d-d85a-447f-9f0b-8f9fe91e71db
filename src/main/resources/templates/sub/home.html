<!-- <PERSON><PERSON> đổi mật khẩu -->
<div class="modal fade" id="changePasswordModal" data-backdrop="static" data-keyboard="false">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" disabled>
					<span aria-hidden="true">&times;</span>
				</button>
				<h4 class="modal-title"><i class="fa fa-key"></i> Đổi mật khẩu mặc định</h4>
			</div>
			<div class="modal-body">
				<!-- Alert Info -->
				<div class="alert alert-info alert-dismissible">
					<h4><i class="icon fa fa-info"></i> Lưu ý!</h4>
					Vui lòng đổi mật khẩu mặc định để đảm bảo an toàn cho tài khoản của bạn.
				</div>

				<!-- Form đổi mật khẩu -->
				<form id="changePasswordForm" class="form-horizontal">
					<div class="box-body">
						<!-- M<PERSON><PERSON> khẩu mới -->
						<input type="hidden" th:value="${MODEL.nguoiDung.idNguoiDung}" id="idNguoiDung" name="idNguoiDung">
						<div class="form-group">
							<label class="col-sm-4 control-label">Mật khẩu mới</label>
							<div class="col-sm-8">
								<div class="input-group">
									<input type="password" class="form-control" id="newPassword" name="newPassword" placeholder="Nhập mật khẩu mới">
									<span class="input-group-addon">
                    <i class="fa fa-lock"></i>
                  </span>
								</div>
								<span class="help-block"></span>
							</div>
						</div>

						<!-- Xác nhận mật khẩu -->
						<div class="form-group">
							<label class="col-sm-4 control-label">Xác nhận mật khẩu</label>
							<div class="col-sm-8">
								<div class="input-group">
									<input type="password" class="form-control" id="confirmPassword" placeholder="Nhập lại mật khẩu mới">
									<span class="input-group-addon">
                    <i class="fa fa-lock"></i>
                  </span>
								</div>
								<span class="help-block"></span>
							</div>
						</div>

						<!-- Password Strength Meter -->
						<div class="form-group">
							<div class="col-sm-offset-4 col-sm-8">
								<div class="password-strength-meter">
									<div class="progress" style="margin-bottom: 5px;">
										<div class="progress-bar" role="progressbar" style="width: 0%"></div>
									</div>
									<div class="strength-text small"></div>
								</div>

								<!-- Password Requirements -->
								<ul class="password-requirements text-muted small list-unstyled">
									<li><i class="fa fa-check-circle-o"></i> Ít nhất 8 ký tự</li>
									<li><i class="fa fa-check-circle-o"></i> Ít nhất một chữ hoa</li>
									<li><i class="fa fa-check-circle-o"></i> Ít nhất một chữ thường</li>
									<li><i class="fa fa-check-circle-o"></i> Ít nhất một số</li>
									<li><i class="fa fa-check-circle-o"></i> Ít nhất một ký tự đặc biệt</li>
								</ul>
							</div>
						</div>
					</div>
				</form>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-primary" id="btnChangePassword">
					<i class="fa fa-check"></i> Xác nhận
				</button>
			</div>
		</div>
	</div>
</div>
<div class="row dashboard-section" style="display: none">
	<!-- Statistics Cards -->
	<div class="col-md-12">
		<div class="row stats-cards">
			<div class="col-lg-3 col-md-6">
				<div class="small-box bg-primary">
					<div class="inner">
						<h3 id="studyingCount">1,202</h3>
						<p>Đang học</p>
					</div>
					<div class="icon">
						<i class="fa fa-user-circle-o"></i>
					</div>
				</div>
			</div>

			<div class="col-lg-3 col-md-6">
				<div class="small-box bg-success">
					<div class="inner">
						<h3 id="graduatedCount">675</h3>
						<p>Đã tốt nghiệp</p>
					</div>
					<div class="icon">
						<i class="fa fa-graduation-cap"></i>
					</div>
				</div>
			</div>

			<div class="col-lg-3 col-md-6">
				<div class="small-box bg-warning">
					<div class="inner">
						<h3 id="preservedCount">10</h3>
						<p>Bảo lưu</p>
					</div>
					<div class="icon">
						<i class="fa fa-pause-circle"></i>
					</div>
				</div>
			</div>

			<div class="col-lg-3 col-md-6">
				<div class="small-box bg-danger">
					<div class="inner">
						<h3 id="droppedCount">375</h3>
						<p>Thôi học</p>
					</div>
					<div class="icon">
						<i class="fa fa-user-times"></i>
					</div>
				</div>
			</div>
		</div>
	</div>

	<!-- Charts Grid -->
	<div class="col-md-12">
		<div class="row charts-grid">
			<!-- Biểu đồ tổng quan trạng thái sinh viên -->
			<div class="col-md-6">
				<div class="card">
					<div class="card-header">
						<h3 class="card-title">
							<i class="fa fa-chart-pie"></i>
							Tổng quan trạng thái sinh viên
						</h3>
					</div>
					<div class="card-body">
						<canvas id="studentStatusChart"></canvas>
					</div>
				</div>
			</div>

			<!-- Biểu đồ sinh viên theo khoa -->
			<div class="col-md-6">
				<div class="card">
					<div class="card-header">
						<h3 class="card-title">
							<i class="fa fa-chart-bar"></i>
							Thống kê sinh viên theo khoa
						</h3>
					</div>
					<div class="card-body">
						<canvas id="departmentChart"></canvas>
					</div>
				</div>
			</div>
			<div class="col-md-6">
				<div class="card">
					<div class="card-header">
						<h3 class="card-title">
							<i class="fa fa-graduation-cap"></i>
							Thống kê theo ngành học
						</h3>
					</div>
					<div class="card-body">
						<canvas id="majorChart"></canvas>
					</div>
				</div>
			</div>

			<div class="col-md-6">
				<div class="card">
					<div class="card-header">
						<h3 class="card-title">
							<i class="fa fa-graduation-cap"></i>
							Thống kê tỷ lệ tốt nghiệp
						</h3>
					</div>
					<div class="card-body">
						<div class="chart-container">
							<canvas id="graduationChart" style="height: 300px;"></canvas>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="row charts-grid">
			<!-- Tỷ lệ Nam/Nữ -->
			<div class="col-md-4">
				<div class="card">
					<div class="card-header">
						<h3 class="card-title">
							<i class="fa fa-users"></i>
							Tỷ lệ Nam/Nữ
						</h3>
					</div>
					<div class="card-body">
						<canvas id="genderChart"></canvas>
					</div>
				</div>
			</div>

			<!-- Thống kê theo năm học -->
			<div class="col-md-8">
				<div class="card">
					<div class="card-header">
						<h3 class="card-title">
							<i class="fa fa-calendar"></i>
							Số lượng sinh viên theo năm học
						</h3>
					</div>
					<div class="card-body">
						<canvas id="yearChart"></canvas>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<style>
	.dashboard-section {
		margin: 15px;
		border-radius: 8px;
	}

	.small-box {
		border-radius: 8px;
		position: relative;
		display: block;
		margin-bottom: 20px;
		box-shadow: 0 4px 8px rgba(0,0,0,0.1);
		overflow: hidden;
	}

	.small-box > .inner {
		padding: 20px;
	}

	.small-box h3 {
		font-size: 38px;
		font-weight: bold;
		margin: 0;
		white-space: nowrap;
		padding: 0;
		color: #fff;
	}

	.small-box p {
		font-size: 15px;
		color: #fff;
		margin-bottom: 0;
	}

	.small-box .icon {
		position: absolute;
		top: 20px;
		right: 20px;
		font-size: 60px;
		color: rgba(255,255,255,0.2);
	}

	.card {
		background: #fff;
		border-radius: 8px;
		box-shadow: 0 4px 8px rgba(0,0,0,0.1);
		margin-bottom: 20px;
	}

	.card-header {
		background: #f8f9fa;
		padding: 15px 20px;
		border-bottom: 1px solid #e9ecef;
		border-top-left-radius: 8px;
		border-top-right-radius: 8px;
	}

	.card-title {
		margin: 0;
		font-size: 18px;
		color: #444;
	}

	.card-title i {
		margin-right: 8px;
	}

	.card-body {
		padding: 20px;
		min-height: 350px;
	}

	.bg-primary {
		background-color: #007bff !important;
	}

	.bg-success {
		background-color: #28a745 !important;
	}

	.bg-warning {
		background-color: #ffc107 !important;
	}

	.bg-danger {
		background-color: #dc3545 !important;
	}

	.charts-grid {
		margin-top: 20px;
	}

	/* Hover effects */
	.small-box:hover {
		transform: translateY(-3px);
		transition: all 0.3s ease;
	}

	.small-box:hover .icon {
		font-size: 65px;
		transition: all 0.3s ease;
	}

	@media (max-width: 767px) {
		.small-box {
			text-align: center;
		}

		.small-box .icon {
			display: none;
		}

		.small-box h3 {
			font-size: 24px;
		}
	}
	/* Modal Styles */
	#changePasswordModal .modal-content {
		border-radius: 3px;
		-webkit-box-shadow: 0 2px 3px rgba(0,0,0,.125);
		box-shadow: 0 2px 3px rgba(0,0,0,.125);
		border: 0;
	}

	#changePasswordModal .modal-header {
		border-bottom-color: #f4f4f4;
		background-color: #367fa9;
		color: #fff;
	}

	#changePasswordModal .close {
		color: #fff;
		opacity: .5;
	}

	#changePasswordModal .modal-title {
		font-size: 16px;
		font-weight: 600;
	}

	#changePasswordModal .form-control {
		border-radius: 0;
		box-shadow: none;
		border-color: #d2d6de;
	}

	#changePasswordModal .form-control:focus {
		border-color: #3c8dbc;
		box-shadow: none;
	}

	#changePasswordModal .form-group {
		margin-bottom: 15px;
	}

	#changePasswordModal .control-label {
		font-weight: 600;
	}

	#changePasswordModal .input-group-addon {
		border-radius: 0;
		background-color: #f4f4f4;
		border-color: #d2d6de;
	}

	/* Password Strength Meter */
	.password-strength-meter .progress {
		height: 5px;
		margin-top: 10px;
		margin-bottom: 5px;
		border-radius: 0;
	}

	.password-strength-meter .progress-bar {
		transition: width .3s ease;
	}

	.password-strength-meter .strength-text {
		font-size: 12px;
		margin-bottom: 10px;
	}

	/* Password Requirements */
	.password-requirements {
		margin-top: 10px;
	}

	.password-requirements li {
		margin-bottom: 5px;
		color: #666;
	}

	.password-requirements li i {
		margin-right: 5px;
	}

	/* Password Validation States */
	.form-group.has-error .form-control,
	.form-group.has-error .input-group-addon {
		border-color: #dd4b39;
	}

	.form-group.has-success .form-control,
	.form-group.has-success .input-group-addon {
		border-color: #00a65a;
	}

	.help-block.error-msg {
		color: #dd4b39;
		font-size: 12px;
		margin-top: 5px;
	}

	/* Alert Styles */
	.alert {
		border-radius: 3px;
		border: 1px solid transparent;
	}

	.alert-info {
		background-color: #00c0ef !important;
		border-color: #00acd6;
	}

	.alert-info h4 {
		color: #fff;
		font-size: 16px;
		font-weight: 600;
		margin-bottom: 5px;
	}

	/* Button Styles */
	.btn {
		border-radius: 3px;
		-webkit-box-shadow: none;
		box-shadow: none;
		border: 1px solid transparent;
	}

	.btn-primary {
		background-color: #3c8dbc;
		border-color: #367fa9;
	}

	.btn-primary:hover,
	.btn-primary:focus,
	.btn-primary:active {
		background-color: #367fa9;
		border-color: #204d74;
	}
</style>
<!--  <div class="row">
    <div class="col-md-12"> -->
<!--	    <footer class="box-footer footer">-->
<!--			<div class="thong-tin-truong col-md-12">-->
<!--				<h2>TRƯỜNG CAO ĐẲNG Y TẾ CÀ MAU</h2>-->
<!--				<div class="footer-contact"><p><em class="material-icons">place</em>&nbsp;Địa chỉ: 146 Nguyễn Trung Trực - phường 8 - Tp.Cà Mau - tỉnh Cà Mau</p></div>-->
<!--				<div class="footer-contact"><p><em class="material-icons">mail</em>&nbsp;Email: <EMAIL></p></div>-->
<!--				<div class="footer-contact"><p><em class="material-icons">phone</em>&nbsp;Điện thoại: 02903 828575, 02903 828304 - Fax: 02903 581476</p></div>-->
<!--			</div>-->
<!--		</footer>-->
<style>
p{
font-size:20px;
}
*{
	margin: 0;
	padding: 0;
}
.content{
	padding:0 !important;
}
@font-face {
  font-family: 'Material Icons';
  font-style: normal;
  font-weight: 400;
  src: url(https://fonts.gstatic.com/s/materialicons/v52/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');
}
.box-footer{
    width: 100%;
    color: #fff;
    background-color: rgba(49,49,49,.6);
    display: block;
    position: absolute;
    font-size:18px;
    border-top:none;
    bottom:52px;
    }
.content-wrapper{
    background: url(dist/img/hinhdaidien.jfif);
    background-size:cover;
}
footer {
background: url(dist/img/world-map.png) no-repeat right;
background-size:contain;
}
.material-icons {
  	font-family: 'Material Icons';
  	font-weight: normal;
  	font-style: normal;
	font-size:20px;
  	letter-spacing: normal;
  	text-transform: none;
  	display: inline-block;
  	white-space: nowrap;
  	word-wrap: normal;
  	direction: ltr;
  	-webkit-font-smoothing: antialiased;
}
</style>