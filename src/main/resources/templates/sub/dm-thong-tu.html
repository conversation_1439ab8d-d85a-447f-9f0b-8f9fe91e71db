<!--Modal Thêm thông tư-->
<div class="modal modal-pri" role="dialog" id="addModal" data-backdrop="static" data-keyboard="false">
	<div class="modal-dialog box-trans" role="document" id="box-add box-edit">
		<div class="modal-content box box-solid box-success">
			<div class="box-header with-border">
				<button type="button" class="close btn-flat" data-dismiss="modal" aria-label="Close"><span
						aria-hidden="true">&times;</span></button>
				<h4 class="modal-title">Thông tin thông tư</h4>
			</div>
			<div class="modal-body">
				<form id="frm-1">
					<div class="row">
						<!--1. Tên thông tư -->
						<div class="form-group col-sm-6">
							<label class="required">Tên thông tư</label>
							<input type="text" name="tenThongTu" id="txt-tenThongTu" class="form-control" />
						</div>

						<!-- 2.Ghi chú -->
						<div class="form-group col-sm-6">
							<label>Ghi chú</label>
							<input type="text" name="ghiChu" id="txt-ghiChu" class="form-control" />
						</div>
					</div>
					<input type="hidden" name="idThongTu" id="txt-idThongTu" />
					<input type="hidden" name="idHeDaoTao" id="txt-idHeDaoTao" />
				</form>
			</div>
			<div class="modal-footer">
				<button class="btn btn-success btn-flat" type="button" id="btn-luu"><i class="fa fa-check-circle"></i>
					Lưu</button>
				<button class="btn btn-default btn-flat" type="button" id="btn-Dong" data-dismiss="modal"><i
						class="fa fa-times-circle"></i> Đóng</button>
			</div>
		</div><!-- /.modal-content -->
	</div><!-- /.modal-dialog -->
</div><!-- /.modal -->
<div class="row">
	<div class="col-md-12">
		<div class="box box-solid box-primary" id="box-frm">
			<div class="box-header">
				<h4 class="box-title">Danh mục Thông tư</h4>
			</div>
			<div class="box-body">
				<!-- <form name="frmSearch" id="frmSearch">
		                <div class="row">
		                    <div class="col-md-12 form-group">
		                        <label>Từ khóa</label>
		                        <div class="input-group">
		                            <input type="text" class="form-control" id="txt-keyword" name="keyword" placeholder="Tên thông tư">
		                            <span class="input-group-btn">
		                                <button type="button" class="btn btn-primary btn-block btn-flat" id="btn-search" title="Tìm kiếm"><i class="fa fa-search"></i></button>
		                            </span>
		                        </div>
		                    </div>
		                </div>
            	    </form> -->
				<div class="row">
					<div class="col-md-12 text-center">
						<button type="button" class="btn btn-primary btn-flat" data-target="#addModal" id="themThongTu"
							data-toggle="modal"><i class="fa fa-plus"></i> Thêm thông tư</button>
					</div>
				</div>
			</div>
			<div class="box-footer table-responsive">
				<div class="jdgrid" id="grid-ds"></div>
			</div>
			<div class="box-footer">
				<div class="jdpage" id="page-ds"></div>
			</div>
		</div>
	</div>
</div>
<div class="modal modal-pri" role="dialog" id="addModalTstt" data-backdrop="static" data-keyboard="false">
	<div class="modal-dialog box-trans" role="document" id="box-tstt-add box-tstt-edit">
		<div class="modal-content box box-solid box-success">
			<div class="box-header with-border">
				<button type="button" class="close btn-flat" data-dismiss="modal" aria-label="Close"><span
						aria-hidden="true">&times;</span></button>
				<h4 class="modal-title">Thông số thông tư </h4>
			</div>
			<div class="modal-body form-horizontal">
				<form id="frm-ts">
					<input type="hidden" name="idThongTu" id="txt-idtt" />
					<div class="row" style="margin-bottom:15px">
						<!--1. Tên thông tư -->
						<div class="col-sm-6">
							<label class="required">Tên thông tư</label>
							<input type="text" name="tenThongTu" id="txt-tt" class="form-control" />
						</div>
						<div class="col-sm-6">
							<label>Hệ đào tạo áp dụng</label>
							<input type="text" name="tenHeDaoTao" disabled id="txt-hdt" class="form-control" />
							<!-- <select name="idHeDaoTao" id="cmb-hdtts" class="form-control select2"></select> -->
						</div>
					</div>
					<div class="panel-group">
						<div class="panel box-solid box-default box">
							<div class="box-header with-border panel-header">
								<h4 class="box-title panel-title">Điều kiện dự thi kết thúc môn</h4>
								<div class="box-tools pull-right">
									<!-- Collapse Button -->
									<button type="button" class="btn btn-box-tool" data-widget="collapse">
										<i class="fa fa-minus"></i>
									</button>
								</div>
								<!-- /.box-tools -->
							</div>
							<!-- /.box-header -->
							<!-- /.box-body -->
							<div class="box-body">
								<div class="form-group">
									<label class="col-sm-6 control-label required">Tỉ lệ số tiết vắng lý thuyết</label>
									<div class="col-sm-6">
										<div class="input-group">
											<input type="text" name="ptTietVang" id="txt-ptTietVang"
												class="form-control" />
											<span class="input-group-addon deco">%</span>

										</div>
									</div>
								</div>
								<div class="form-group">
									<label class="col-sm-6 control-label required">Số tiết vắng thực hành</label>
									<div class="col-sm-6">
										<input type="text" name="soTietVangTh" id="txt-soTietVangTh"
											class="form-control" />
									</div>
								</div>
								<div class="form-group">
									<label class="col-sm-6 control-label required">Điểm trung bình kiểm tra tối
										thiểu</label>
									<div class="col-sm-6">
										<input type="text" name="minTbkt" id="txt-minTbkt" class="form-control" />
									</div>
								</div>
							</div>
						</div>
						<div class="panel box-solid box-default box">
							<div class="box-header with-border panel-header">
								<h4 class="box-title panel-title">Điều kiện qua môn</h4>
								<div class="box-tools pull-right">
									<!-- Collapse Button -->
									<button type="button" class="btn btn-box-tool" data-widget="collapse">
										<i class="fa fa-minus"></i>
									</button>
								</div>
								<!-- /.box-tools -->
							</div>
							<!-- /.box-header -->
							<!-- /.box-body -->
							<div class="box-body">
								<div class="form-group">
									<label class="col-sm-6 control-label required">Điểm thi kết thúc môn tối
										thiểu</label>
									<div class="col-sm-6">
										<input type="text" name="diemThiMin" id="txt-diemThiMin" class="form-control" />
									</div>
								</div>
							</div>
						</div>
						<div class="panel box-solid box-default box collapsed-box">
							<div class="box-header with-border panel-header">
								<h4 class="box-title panel-title">Hệ số tính điểm trung bình môn</h4>
								<div class="box-tools pull-right">
									<!-- Collapse Button -->
									<button type="button" class="btn btn-box-tool" data-widget="collapse">
										<i class="fa fa-plus"></i>
									</button>
								</div>
								<!-- /.box-tools -->
							</div>
							<!-- /.box-header -->
							<div class="box-body">
								<div class="form-group">
									<label class="col-sm-6 control-label required">Hệ số điểm trung bình kiểm
										tra</label>
									<div class="col-sm-6">
										<input type="text" name="hsDtbkt" id="txt-hsDtbkt" class="form-control" />
									</div>
								</div>
								<div class="form-group">
									<label class="col-sm-6 control-label required">Hệ số điểm thi</label>
									<div class="col-sm-6">
										<input type="text" name="hsDiemThi" id="txt-hsDiemThi" class="form-control" />
									</div>
								</div>
							</div>
						</div>
						<!-- /.box -->
						<div class="panel box-solid box-default box collapsed-box">
							<div class="box-header with-border panel-header">
								<h4 class="box-title panel-title">Hệ số tính điểm tốt nghiệp</h4>
								<div class="box-tools pull-right">
									<!-- Collapse Button -->
									<button type="button" class="btn btn-box-tool" data-widget="collapse">
										<i class="fa fa-plus"></i>
									</button>
								</div>
								<!-- /.box-tools -->
							</div>
							<!-- 6. Hệ số điểm trung bình toàn khóa -->
							<div class="box-body">
								<div class="form-group">
									<label class="col-sm-6 control-label required">Hệ số điểm trung bình toàn
										khóa</label>
									<div class="col-sm-6">
										<input type="text" name="hsDtbtk" id="txt-hsDtbtk" class="form-control" />
									</div>
								</div>
								<div class="form-group">
									<label class="col-sm-6 control-label required">Hệ số điểm THNN</label>
									<div class="col-sm-6">
										<input type="text" name="hsDthnn" id="txt-hsDthnn" class="form-control" />
									</div>
								</div>
								<div class="form-group">
									<label class="col-sm-6 required control-label">Hệ số điểm lý thuyết THNN</label>
									<div class="col-sm-6">
										<input type="text" name="hsDltthnn" id="txt-hsDltthnn" class="form-control" />
									</div>
								</div>
							</div>
						</div>
						<!-- /.box -->
						<div class="panel box-solid box-default box collapsed-box">
							<div class="box-header with-border panel-header">
								<h4 class="box-title panel-title">Thang điểm xếp loại tốt nghiệp</h4>
								<div class="box-tools pull-right">
									<!-- Collapse Button -->
									<button type="button" class="btn btn-box-tool" data-widget="collapse">
										<i class="fa fa-plus"></i>
									</button>
								</div>
								<!-- /.box-tools -->
							</div>
							<!-- 9. Xếp loại tốt nghiệp - trung bình -->
							<div class="box-body">
								<table class="table table-bordered table-striped" cellspacing="0" cellpadding="5">
									<thead>
										<tr>
											<th></th>
											<th>Niên chế</th>
											<th>Tín chỉ</th>
										</tr>
									</thead>
									<tbody >
										<tr>
											<td class="text-bold">Trung bình</td>
											<td>Từ <span>5</span> đến dưới <input type="text" class="input-sm inline-input xltn"  name="xltnTb" id="txt-xltnTb" /></td>
											<td>Từ <span>2</span> đến dưới <input type="text" class="input-sm inline-input xltn"  name="xltnTb4" id="txt-xltnTb4" /></td>
										</tr>
										<tr id="tr-kha">
											<td class="text-bold">Trung bình Khá</td>
											<td>Từ <span id="span-xltnTb"></span> đến dưới <input type="text" class="input-sm inline-input xltn"  name="xltnTbk" id="txt-xltnTbk" /></td>
											<td>-</td>
										</tr>
										<tr>
											<td class="text-bold">Khá</td>
											<td>Từ <span id="span-xltnTbk"></span> đến dưới <input type="text" class="input-sm inline-input xltn"  name="xltnKha" id="txt-xltnKha" /></td>
											<td>Từ <span id="span-xltnTb4"></span> đến dưới <input type="text" class="input-sm inline-input xltn"  name="xltnKha4" id="txt-xltnKha4" /></td>
										</tr>
										<tr>
											<td class="text-bold">Giỏi</td>
											<td>Từ <span id="span-xltnKha"></span> đến dưới <input type="text" class="input-sm inline-input xltn"  name="xltnGioi" id="txt-xltnGioi" /></td>
											<td>Từ <span id="span-xltnKha4"></span> đến dưới <input type="text" class="input-sm inline-input xltn"  name="xltnGioi4" id="txt-xltnGioi4" /></td>
										</tr>
										<tr>
											<td class="text-bold">Xuất sắc</td>
											<td>Từ <span id="span-xltnGioi"></span> đến 10</td>
											<td>Từ <span id="span-xltnGioi4"></span> đến 4</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<div class="panel box-solid box-default box collapsed-box">
							<div class="box-header with-border panel-header">
								<h4 class="box-title panel-title">Điều kiện dự thi tốt nghiệp</h4>
								<div class="box-tools pull-right">
									<!-- Collapse Button -->
									<button type="button" class="btn btn-box-tool" data-widget="collapse">
										<i class="fa fa-plus"></i>
									</button>
								</div>
								<!-- /.box-tools -->
							</div>
							<!-- /.box-header -->
							<div class="box-body">
								<div class="form-group">
									<label class="col-sm-6 control-label required" for="txt-ttnDtbtlMin">Điểm TBTL tối
										thiểu</label>
									<div class="col-sm-6 ">
										<input type="number" class="form-control" id="txt-ttnDtbtlMin"
											name="ttnDtbtlMin" min="0" onKeyUp="if(this.value<0){this.value='0';}">
										<span class="text-red hide" id="spn-ttnDtbtlMin"><i>Giá trị phải từ 0 đến 10
												!</i></span>
									</div>
								</div>
								<div class="form-group">
									<label class="col-sm-6 control-label" for="txt-ttnDrltlMin">Điểm RLTL tối
										thiểu</label>
									<div class="col-sm-6">
										<input type="number" class="form-control" id="txt-ttnDrltlMin"
											name="ttnDrltlMin" min="0" onKeyUp="if(this.value<0){this.value='0';}">
										<span class="text-red hide" id="spn-ttnDrltlMin"><i>Giá trị phải lớn hơn hoặc
												bằng 0 !</i></span>
									</div>
								</div>
								<div class="form-group">
									<label class="col-sm-6 control-label" for="txt-ttnNoDvhtMax">Số ĐVHT nợ tối
										đa</label>
									<div class="col-sm-6">
										<input type="number" class="form-control" id="txt-ttnNoDvhtMax"
											name="ttnNoDvhtMax" min="0" onKeyUp="if(this.value<0){this.value='0';}">
										<span class="text-red hide" id="spn-ttnNoDvhtMax"><i>Giá trị phải lớn hơn hoặc
												bằng 0 !</i></span>
									</div>
								</div>
								<div class="form-group">
									<label class="col-sm-6 control-label" for="txt-ttnKtHp">KT hoàn thành học phí
									</label>
									<div class="col-sm-6" style="padding-top: 7px;">
										<label class="switch">
											<input type="checkbox" id="txt-ttnKtHp">
											<span class="slider round"></span>
										</label>
										&nbsp;&nbsp;
										<span id="kt-ttnHp"></span>
									</div>
								</div>
								<div class="form-group">
									<label class="col-sm-6 control-label" for="txt-ttnKtKl">KT kỷ luật </label>
									<div class="col-sm-6" style="padding-top: 7px;">
										<label class="switch">
											<input type="checkbox" id="txt-ttnKtKl">
											<span class="slider round"></span>
										</label>
										&nbsp;&nbsp;
										<span id="kt-ttnKl"></span>
									</div>
								</div>
								<div class="form-group">
									<label class="col-sm-6 control-label" for="txt-ttnKtHhh">KT hết hạn học </label>
									<div class="col-sm-6" style="padding-top: 7px;">
										<label class="switch">
											<input type="checkbox" id="txt-ttnKtHhh">
											<span class="slider round"></span>
										</label>
										&nbsp;&nbsp;
										<span id="kt-ttnHhh"></span>
									</div>
								</div>
							</div>
						</div>

						<div class="panel box-solid box-default box collapsed-box">
							<div class="box-header with-border panel-header">
								<h4 class="box-title panel-title">Điều kiện tốt nghiệp</h4>
								<div class="box-tools pull-right">
									<!-- Collapse Button -->
									<button type="button" class="btn btn-box-tool" data-widget="collapse">
										<i class="fa fa-plus"></i>
									</button>
								</div>
								<!-- /.box-tools -->
							</div>
							<!-- /.box-header -->
							<div class="box-body">
								<div class="form-group">
									<label class="col-sm-6 control-label required" for="txt-xtnDtbttnMin">Điểm mỗi môn
										thi tốt nghiệp</label>
									<div class="col-sm-6">
										<input type="number" class="form-control" id="txt-xtnDtbttnMin"
											name="xtnDtbttnMin" min="0" onKeyUp="if(this.value<0){this.value='0';}">
										<span class="text-red hide" id="spn-xtnDtbttnMin"><i>Giá trị phải từ 0 đến 10
												!</i></span>
									</div>
								</div>
								<div class="form-group">
									<label class="col-sm-6 control-label" for="txt-xtnKtKl">KT kỷ luật </label>
									<div class="col-sm-6" style="padding-top: 7px;">
										<label class="switch">
											<input type="checkbox" id="txt-xtnKtKl">
											<span class="slider round"></span>
										</label>
										&nbsp;&nbsp;
										<span id="kt-xtnKl"></span>
									</div>
								</div>
							</div>
						</div>
					</div>
				</form>
			</div>
			<div class="modal-footer">
				<button class="btn btn-success btn-flat" type="button" id="btn-luuTSTT"><i
						class="fa fa-check-circle"></i> Lưu</button>
				<button class="btn btn-default btn-flat" type="button" id="btn-Dong" data-dismiss="modal"><i
						class="fa fa-times-circle"></i> Đóng</button>
			</div>
		</div><!-- /.modal-content -->
	</div><!-- /.modal-dialog -->
</div><!-- /.modal -->