<input type="hidden" id="idVaiTro" th:value="${MODEL.nguoiDung.vaiTro.idVaiTro}"/>
<div class="row">
    <div class="col-md-12">
        <div class="box box-primary box-solid" id="box-lst">
            <div class="box-header">
                <h3 class="box-title">Kế<PERSON> quả học tập</h3>
            </div>
            <div class="box-body">
                <form id="frmSearch">
                    <div class="row" th:if="${MODEL.nguoiDung.vaiTro.idVaiTro == 1}">
                        <div class="col-md-6 form-group">
                            <label>Năm học</label>
                            <select class="form-control select2" name="idNienKhoa" id="cmb-nienKhoa">
                            </select>
                        </div>
                        <div class="col-md-6 form-group">
                            <label><PERSON><PERSON><PERSON></label>
                            <select class="form-control select2" name="idHocKy" id="cmb-hocKy">
                            </select>
                        </div>
                        <input type="hidden" name="maSv" id="txt-maSv" value=""/>
                    </div>
                    <div class="row" th:if="${MODEL.nguoiDung.vaiTro.idVaiTro != 1}">
                        <div class="col-md-3 form-group">
                            <label>Năm học</label>
                            <select class="form-control select2" name="idNienKhoa" id="cmb-nienKhoa">
                            </select>
                        </div>
                        <div class="col-md-3 form-group">
                            <label>Học kỳ</label>
                            <select class="form-control select2" name="idHocKy" id="cmb-hocKy">
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label>Mã sinh viên</label>
                            <input type="text" name="maSv" id="txt-maSv" class="form-control" required/>
                        </div>
                        <div class="col-md-3 form-group">
                            <label class="invisible">Xem</label>
                            <button type="button" class="btn btn-primary btn-block" id="btn-view"><i class="fa fa-search"></i> Xem KQHT</button>
                        </div>
                    </div>
                </form>
            </div>
            <div class="box-footer">
                <div class="jdgrid" id="grid-ds"></div>
            </div>
        </div>
    </div>
</div>