
<!--Modal nhập điểm-->
<div class="modal modal-suc" role="dialog" id="addModal_2" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog modal-lg box-trans" role="document" id="box-detail" style="width: 90%;">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">Danh sách sinh viên đăng ký môn học:
					<span style="font-weight:bold;" id="maMonHocModal"></span> - 
					<span style="font-weight:bold;" id="tenMonHocModal"></span>
					(<span id="tenNhomModal" style="font-weight:bold;"></span>)
				</h4>
            </div>
            <div class="modal-body">
				<div style="padding: 10px; background-color: #f9f9f9; font-size: 12px; border: 1px solid #ddd; border-radius: 8px;">
					<p>Hướng dẫn nhập điểm:</p>
					<p style="margin: 0; color: #333; font-size: 12px;">
						- Điểm nhanh: <span style="color: #007BFF;">7.7 = 77</span> <br>
						- Vắng không phép : <span style="color: #FF0000;">0</span> <br>
						- Điểm đặc biệt:
						<span style="color: #FF0000;">
						Miễn = 140, Rút = 130,
						Cấm thi = 110, Vắng có phép = 150,
						Chưa nhận điểm = 160, Hoãn thi = 170,
						Nợ học phí = 200, Đạt = 210, Không đạt = 220
        </span>
					</p>
				</div>

				<div class="row">
					<form id="frm-filter">
					<div class="col-sm-5 form-group">
						<label>Tình trạng</label>
						<select class="form-control select2" name="idTinhTrang" id="cmb-tinhTrang-filter">
						</select>
					</div>
					<div class="col-sm-2">
						<label class="invisible">Lọc</label>
						<button type="button" class="btn btn-primary btn-flat btn-block" id="btn-filter"><i class="fa fa-filter"></i> Lọc</button>
					</div>
					</form>
				</div>
				
				<input type="hidden" id="idNhomDssv" name="idNhomDssv"> 
				<input type="hidden" id="lanDuyet" name="lanDuyet">
				<div class="jdgrid" id="grid-detail"></div>
				<div class="jdgrid" id="grid-detail-in" style="display: none"></div>
				<form enctype="multipart/form-data" id="frm-upload">
                    <input type="file" id="imp-file" name="file" accept=".xlsx" class="hide">
                </form>
				<form enctype="multipart/form-data" id="frm-upload-dt">
                    <input type="file" id="imp-file-dt" name="filedt" accept=".xlsx" class="hide">
                </form>
            </div>
            <div class="modal-footer">
				<button type="button" class="btn btn-default btn-flat pull-left" id="btn-browser"><i class="fa fa-upload text-success"></i><span class="text-success text-bold">Tải lên</span></button>
                 <button type="button" class="btn btn-default btn-flat pull-left" id="btn-browser-dt"><i class="fa fa-upload text-success"></i><span class="text-success text-bold">Tải lên điểm thi</span></button>
				<a href="javascript:void(0)" onclick="taiFileMau()" role="button" class="btn btn-default btn-flat pull-left" title="Tải file mẫu"><span class="text-primary text-bold"><i class="fa fa-download"></i> Tải file mẫu</span></a>
				<button type="button" class="btn btn-success btn-flat" id="btn-duyetDiemLan1"><i class="fa fa-check-square-o"></i> Duyệt điểm lần 1</button>
				<button type="button" class="btn btn-success btn-flat" id="btn-duyetDiemLan2"><i class="fa fa-check-square-o"></i> Duyệt điểm lần 2</button>
				<button type="button" class="btn btn-default btn-flat" id="btnPrint"><i class="fa fa-print"></i> In bảng điểm</button>
				<button type="button" class="btn btn-success btn-flat" id="btnExport"><i class="fa fa-file-excel-o"></i> Xuất bảng điểm</button>
				<button type="button" class="btn btn-success btn-flat" id="btn-luu-ct"><i class="fa fa-floppy-o"></i> Lưu</button>
                <button class="btn btn-default btn-flat" type="button" id="btn-Dong" data-dismiss="modal"><i class="fa fa-times-circle"></i> Đóng</button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<!--Modal dssv dự thi-->
<div class="modal modal-suc" role="dialog" id="addModal_3" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog modal-lg box-trans" role="document" id="box-detail" style="width: 1150px;">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">Danh sách sinh viên dự thi:
					<span style="font-weight:bold;" id="maMonHocModal2"></span> - 
					<span style="font-weight:bold;" id="tenMonHocModal2"></span>
					(<span id="tenNhomModal2" style="font-weight:bold;"></span>)
				</h4>
            </div>
            <div class="modal-body">
				<input type="hidden" id="idNhomDssv2" name="idNhomDssv2"> 
				<div class="jdgrid" id="grid-dssv"></div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-default btn-flat" type="button" id="btn-Dong" data-dismiss="modal"><i class="fa fa-times-circle"></i> Đóng</button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->
<!--Modal dssv cấm thi-->
<div class="modal modal-suc" role="dialog" id="addModal_4" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog modal-lg box-trans" role="document" id="box-detail" style="width: 1150px;">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">Danh sách sinh viên cấm thi:
					<span style="font-weight:bold;" id="maMonHocModal3"></span> - 
					<span style="font-weight:bold;" id="tenMonHocModal3"></span>
					(<span id="tenNhomModal3" style="font-weight:bold;"></span>)
				</h4>
            </div>
            <div class="modal-body">
				<input type="hidden" id="idNhomDssv3" name="idNhomDssv3"> 
				<div class="jdgrid" id="grid-dssv-cam-thi"></div>
            </div>
            <div class="modal-footer">
				<button type="button" class="btn btn-success btn-flat" id="btnExportCT"><i class="fa fa-check-circle"></i> Xuất danh sách cấm thi</button>
                <button class="btn btn-default btn-flat" type="button" id="btn-Dong" data-dismiss="modal"><i class="fa fa-times-circle"></i> Đóng</button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->
<!--Modal in bảng điểm-->
<div class="modal modal-suc" role="dialog" id="inBangDiemModal" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog modal-lg box-trans" role="document" id="box-bangdiem" style="width: 1150px;">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">Danh sách điểm môn học:
					<span style="font-weight:bold;" id="maMonHocInBangDiem"></span> - 
					<span style="font-weight:bold;" id="tenMonHocInBangDiem"></span>
				</h4>
            </div>
            <div class="modal-body">
				<input type="hidden" id="idNhomDssv" name="idNhomDssv"> 
				<input type="hidden" id="lanDuyet" name="lanDuyet">
				<input type="hidden" id="hocKyInBangDiem" name="hocKyInBangDiem">
				<input type="hidden" id="nienKhoaInBangDiem" name="nienKhoaInBangDiem">
				<div class="jdgrid" id="grid-bangdiem"></div>
            </div>
            <div class="modal-footer">
				<button type="button" class="btn btn-default btn-flat" id="btnPrintAll"><i class="fa fa-print"></i> In bảng điểm</button>
				<button type="button" class="btn btn-success btn-flat" id="btnExportAll"><i class="fa fa-file-excel-o"></i> Xuất bảng điểm</button>
                <button class="btn btn-default btn-flat" type="button" id="btn-Dong" data-dismiss="modal"><i class="fa fa-times-circle"></i> Đóng</button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->
<div class="row">
 		<div class="col-md-12">
 			<div class="box box-solid box-primary" id="box-ds">
				<div class="box-header">
					<h3 class="box-title">Nhập điểm môn học</h3>
				</div>
				 <div class="box-body">
					<form name="frmSearch" id="frmSearch">
						   <div class="row">
							   <div class="col-md-3 form-group">
								   <label>Năm học</label>
								   <select class="form-control select2" name="idKhoi" id="cmb-namhoc-search" >
								   </select>
							   </div>
							   <div class="col-md-3 form-group">
								<label>Học kỳ</label>
								<select class="form-control select2" name="idKhoi" id="cmb-hocky-search" >
								</select>
							</div>
							<div class="col-md-3 form-group">
								<label>Lớp</label>
								<select class="form-control select2" name="idLop" id="cmb-lop" >
								</select>
							</div>
							   <!-- <div class="col-md-3 form-group">
								   <label>Từ khóa</label>
								   <div class="input-group">
									   <input type="text" class="form-control" id="txt-keyword" name="keyword" placeholder="Mã môn học, tên môn học">
									   <span class="input-group-btn">
										   <button type="button" class="btn btn-primary btn-block btn-flat" id="btn-search" title="Tìm kiếm"><i class="fa fa-search"></i></button>
									   </span>
								   </div>
							   </div>
						   </div> -->
						   <div class="col-md-3 form-group">
							<label>Môn học</label>
							<select class="form-control select2" name="cmb-monhoc" id="cmb-monhoc" >
							</select>
							</div>
					   </form>
						<div class="row">
						   <div class="col-md-12 text-center">
							   <button type="button" class="btn btn-primary btn-flat" id="btn-search"><i class="fa fa-search" >&nbsp;</i>Tìm kiếm</button>
							   <button type="button" class="btn btn-success btn-flat" id="btn-print"><i class="fa fa-print" >&nbsp;</i>In bảng điểm theo môn học</button>
						   </div>
					   </div>
			   </div>
			   <div class="box-footer table-responsive">
				   <div class="jdgrid" id="grid-ds"></div>
			   </div>
			   <div class="box-footer">
				   <div class="jdpage" id="page-ds"></div>
			   </div>
 			</div>
 		</div>
 	</div>

<!--Modal chọn ngày in-->
<div class="modal modal-suc" role="dialog" id="chonNgayInModal" data-backdrop="static" data-keyboard="false">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
				<h4 class="modal-title">Chọn ngày in</h4>
			</div>
			<div class="modal-body">
				<form id="frm-chon-ngay">
					<div class="form-group">
						<label>Ngày in</label>
						<div class="row">
							<div class="col-sm-3">
								<select class="form-control" id="ngayIn">
									<!-- Options cho ngày 1-31 -->
									<option value="">Ngày</option>
									<script>
										for(let i = 1; i <= 31; i++) {
											document.write(`<option value="${i < 10 ? '0' + i : i}">${i}</option>`);
										}
									</script>
								</select>
							</div>
							<div class="col-sm-3">
								<select class="form-control" id="thangIn">
									<!-- Options cho tháng 1-12 -->
									<option value="">Tháng</option>
									<script>
										for(let i = 1; i <= 12; i++) {
											document.write(`<option value="${i < 10 ? '0' + i : i}">${i}</option>`);
										}
									</script>
								</select>
							</div>
							<div class="col-sm-4">
								<select class="form-control" id="namIn">
									<!-- Options cho năm -->
									<option value="">Năm</option>
									<script>
										const currentYear = new Date().getFullYear();
										for(let i = currentYear - 1; i <= currentYear + 1; i++) {
											document.write(`<option value="${i}">${i}</option>`);
										}
									</script>
								</select>
							</div>
						</div>
					</div>
				</form>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-success btn-flat" id="btn-xac-nhan-ngay"><i class="fa fa-check"></i> Xác nhận</button>
				<button class="btn btn-default btn-flat" type="button" data-dismiss="modal"><i class="fa fa-times-circle"></i> Đóng</button>
			</div>
		</div>
	</div>
</div>