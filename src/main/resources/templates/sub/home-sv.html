<!-- Student Information -->
<div class="row">
    <div class="col-md-7">
      <div class="row">
        <div class="col-md-12">
          <div class="box box-success">
            <div class="box-header with-border">
              <h3 class="box-title">Thông tin sinh viên</h3>
            </div>
            <div class="box-body">
              <div class="row">
                <div class="col-sm-3">
                  <img th:src="${MODEL.nguoiDung.nu?'/dist/img/user-female-160.jpg':'/dist/img/user-male-160.jpg'}" class="img-circle" alt="User Image">
                </div>
                <div class="col-sm-9">
                  <div class="row">
                    <div class="col-sm-6">
                      <p><strong>MSSV:</strong> <span id="mssv"></span> </p>
                      <p><strong>Họ tên:</strong> <span id="hoTen"></span></p>
                      <p><strong>Giới tính:</strong> <span id="gioiTinh"></span></p>
                      <p><strong>Ngày sinh:</strong> <span id="ngaySinh"></span></p>
                      <p><strong>Địa chỉ:</strong> <span id="noiSinh"></span></p>
                    </div>
                    <div class="col-sm-6">
                      <p><strong>Lớp:</strong> <span id="lop"></span></p>
                      <p><strong>Khóa:</strong> <span id="khoa"></span></p>
                      <p><strong>Bậc đào tạo:</strong> <span id="heDaoTao"></span></p>
                      <p><strong>Loại hình đào tạo:</strong> <span id="loaiHinhDaoTao"></span></p>
                      <p><strong>Ngành:</strong> <span id="nganh"></span></p>
                    </div>
                  </div>
                </div>
              </div> b
            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-7">
          <div class="box">
            <div class="box-header with-border">
              <h3 class="box-title">Kết quả học tập</h3>
            </div>
            <div class="box-body">
              <canvas id="barChart"></canvas>
            </div>
          </div>
        </div>
        <div class="col-md-5">
          <div class="box box-success">
            <div class="box-header with-border">
              <h3 class="box-title">Tiến độ học tập</h3>
            </div>
            <div class="box-body">
              <div class="chart-container">
                <canvas id="doughnutChart"></canvas>
              </div>
            </div>
          </div>
        </div>

      </div>

</div>
    <div class="col-md-5">
      <div class="box box-primary">
        <div class="box-header
        with-border">
          <h3 class="box-title">Thông báo</h3>
        </div>
        <div class="box-body">
          <div class="callout callout-info">
            <h4>Chú ý!</h4>
            <p>Đã cập nhật thông tin cá nhân của bạn. Vui lòng kiểm tra và cập nhật thông tin cá nhân.</p>
          </div>
          <div class="callout callout-warning">
            <h4>Chú ý!</h4>
            <p>Đã cập nhật thông tin cá nhân của bạn. Vui lòng kiểm tra và cập nhật thông tin cá nhân.</p>
          </div>
          <div class="callout callout-danger">
            <h4>Chú ý!</h4>
            <p>Đã cập nhật thông tin cá nhân của bạn. Vui lòng kiểm tra và cập nhật thông tin cá nhân.</p>
          </div>
        </div>
      </div>
    </div>
</div>

  <!-- Charts -->



<script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.9.4/Chart.min.js"></script>
<script>
  // Bar Chart
  var ctx = document.getElementById('barChart').getContext('2d');
  var barChart = new Chart(ctx, {
    type: 'bar',
    data: {
      labels: ['Môn 1', 'Môn 2', 'Môn 3', 'Môn 4', 'Môn 5'],
      datasets: [{
        label: 'Điểm của bạn',
        data: [6.6, 8.1, 6.3, 6.0, 5.4],
        backgroundColor: '#FF5733'
      }, {
        label: 'Điểm TB lớp học phần',
        data: [7.0, 7.5, 6.8, 6.5, 6.0],
        borderColor: '#FFC300',
        type: 'line'
      }]
    },
    options: {
      responsive: true,
      scales: {
        yAxes: [{
          ticks: {
            beginAtZero: true
          }
        }]
      }
    }
  });

  // Doughnut Chart
  var ctx = document.getElementById('doughnutChart').getContext('2d');
  var doughnutChart = new Chart(ctx, {
    type: 'doughnut',
    data: {
      labels: ['Completed', 'Remaining'],
      datasets: [{
        data: [80, 81],
        backgroundColor: ['#28a745', '#dc3545']
      }]
    },
    options: {
      responsive: true,
    }
  });
</script>

