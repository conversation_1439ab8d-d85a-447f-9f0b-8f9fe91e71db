<!--Modal Thêm khối-->
<div class="modal modal-pri" role="dialog" id="addModal" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog box-trans" role="document" id="box-add box-edit box-frm">
        <div class="modal-content box box-solid box-success">
            <div class="box-header with-border">
                <button type="button" class="close btn-flat" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">Thông tin ngành học</h4>
            </div>
            <div class="modal-body">
                 <form id="frm-1">
                			<div class="row">     
               					<div class="form-group col-sm-4">
		                            <label class="required">Mã ngành học</label>
		                            <input type="text" name="maNganhHoc" id="txt-maNganhHoc" class="form-control"/>
	    	                    </div>
	    	                    <div class="form-group col-sm-8">
		                            <label class="required">Tên ngành học</label>
	    	                        <input type="text" name="tenNganhHoc" id="txt-tenNganhHoc" class="form-control"/>
	        	                </div>
               				</div>
        	                <div class="row">
               					<div class="form-group col-sm-12">
		                            <label class="required">Tên đơn vị</label>
		                           <select name="tenDonVi" id="cmb-dvi" class="form-control select2">
 		 							</select>
		                        </div>
		                    </div>
                    <input type="hidden" name="idNganhHoc" id="txt-idNganhHoc"/>
                    <input type="hidden" name="idHeDaoTao" id="txt-idHeDaoTao"/>
                    
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-success btn-flat" type="button" id="btn-luu"><i class="fa fa-check-circle"></i> Lưu</button>
                <button class="btn btn-default btn-flat" type="button" id="btn-Dong" data-dismiss="modal"><i class="fa fa-times-circle"></i> Đóng</button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<!-----------------------------------------------------------------Giao diện chính----------------------------------------->
<div class="row">
    <div class="col-md-12">
        <div class="box box-solid box-primary" id="box-frm">
            <div class="box-header">
                <h3 class="box-title">Danh mục ngành học</h3>
            </div>
            <div class="box-body">
              <form name="frmSearch" id="frmSearch">
	                <div class="row">
	                    <div class="col-md-6 form-group">
	                        <label>Đơn vị</label>
	                        <select class="form-control select2" name="idDonVi" id="cmb-donVi-search">
	 		 					 <option value="-1">Tất cả</option>
	 		 				</select>
	                    </div>
	                    <div class="col-md-6 form-group">
		                        <label>Từ khóa</label>
		                        <div class="input-group">
		                            <input type="text" class="form-control" id="txt-keyword" name="keyword" placeholder="Mã ngành học, tên ngành học">
		                            <span class="input-group-btn">
		                                <button type="button" class="btn btn-primary btn-block btn-flat" id="btn-search" title="Tìm kiếm"><i class="fa fa-search"></i></button>
		                            </span>
		                        </div>
		                    </div>
	                    
	                </div>
                </form>
                 <div class="row">
                    <div class="col-md-12 text-center">
                        <button type="button" class="btn btn-primary btn-flat" data-target="#addModal" data-toggle="modal"><i class="fa fa-plus"></i> Thêm ngành học</button>
                    </div>
                </div>
            </div>
            <div class="box-footer table-responsive">
                <div class="jdgrid" id="grid-ds"></div>
            </div>
            <div class="box-footer">
                <div class="jdpage" id="page-ds"></div>
            </div>
        </div>
    </div>
</div>