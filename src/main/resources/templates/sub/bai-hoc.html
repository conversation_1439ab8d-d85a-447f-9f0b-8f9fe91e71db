<!-- Qu<PERSON><PERSON> lý <PERSON>ài học -->
<div class="row">
    <div class="col-md-12">
        <div class="box box-warning">
            <div class="box-header with-border">
                <h3 class="box-title">
                    <i class="fa fa-book"></i> Danh sách Bài học
                </h3>
                <div class="box-tools pull-right">
                    <button type="button" class="btn btn-warning btn-sm" data-toggle="modal" data-target="#addBaiHocModal">
                        <i class="fa fa-plus"></i> Thêm bài học
                    </button>
                </div>
            </div>
            
            <!-- Bộ lọc -->
            <div class="box-body">
                <form name="frmSearch" id="frmSearch">
                    <div class="row filter-section">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Môn học:</label>
                                <select class="form-control select2" id="cmb-monHoc-search" name="idMonHoc">
                                    <option value="-1">-- Tất cả --</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>Hình thức:</label>
                                <select class="form-control" id="cmb-hinhThuc-search" name="hinhThuc">
                                    <option value="">-- Tất cả --</option>
                                    <option value="LT">Lý thuyết</option>
                                    <option value="TH">Thực hành</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Từ khóa:</label>
                                <input type="text" class="form-control" id="txt-keyword" name="search"
                                       placeholder="Tìm theo mã, tên bài học...">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <div>
                                    <button type="button" class="btn btn-info" id="btn-search">
                                        <i class="fa fa-search"></i> Tìm kiếm
                                    </button>
                                    <button type="button" class="btn btn-default" onclick="resetFilter()">
                                        <i class="fa fa-refresh"></i> Làm mới
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
                
                <!-- Bảng danh sách bài học -->
                <div class="box-footer table-responsive">
                    <div class="jdgrid" id="grid-ds"></div>
                </div>
                <div class="box-footer">
                    <div class="jdpage" id="page-ds"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal thêm/sửa bài học -->
<div class="modal fade" id="addBaiHocModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title">
                    <i class="fa fa-book"></i> <span id="modalTitle">Thêm bài học</span>
                </h4>
            </div>
            <form id="baiHocForm">
                <div class="modal-body">
                    <input type="hidden" id="idBaiHoc" name="idBaiHoc">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="control-label">Môn học <span class="text-red">*</span></label>
                                <select class="form-control select2" id="cmb-monHoc" name="idMonHoc" required>
                                    <option value="">-- Chọn môn học --</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="control-label">Mã bài học <span class="text-red">*</span></label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="maBaiHoc" name="maBaiHoc" 
                                           placeholder="Nhập mã bài học" required>
                                    <span class="input-group-btn">
                                        <button type="button" class="btn btn-info" onclick="taoMaTuDong()">
                                            <i class="fa fa-magic"></i> Tự động
                                        </button>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="control-label">Tên bài học <span class="text-red">*</span></label>
                        <input type="text" class="form-control" id="tenBaiHoc" name="tenBaiHoc" 
                               placeholder="Nhập tên bài học" required>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="control-label">Hình thức <span class="text-red">*</span></label>
                                <select class="form-control" id="hinhThuc" name="hinhThuc" required>
                                    <option value="">-- Chọn hình thức --</option>
                                    <option value="LT">Lý thuyết</option>
                                    <option value="TH">Thực hành</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="control-label">Số tiết lý thuyết</label>
                                <input type="number" class="form-control" id="soTietLt" name="soTietLt" 
                                       min="0" max="20" value="0">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="control-label">Số tiết thực hành</label>
                                <input type="number" class="form-control" id="soTietTh" name="soTietTh" 
                                       min="0" max="20" value="0">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="control-label">Thứ tự</label>
                                <input type="number" class="form-control" id="thuTu" name="thuTu" 
                                       min="1" max="100" placeholder="Tự động nếu để trống">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="control-label">Tổng số tiết</label>
                                <input type="number" class="form-control" id="tongSoTiet" name="tongSoTiet" 
                                       readonly style="background-color: #f5f5f5;">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="control-label">Nội dung bài học</label>
                        <textarea class="form-control" id="noiDung" name="noiDung" rows="4" 
                                  placeholder="Mô tả nội dung bài học..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">
                        <i class="fa fa-times"></i> Hủy
                    </button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fa fa-save"></i> Lưu
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal sắp xếp bài học -->
<div class="modal fade" id="sapXepModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title">
                    <i class="fa fa-sort"></i> Sắp xếp bài học
                </h4>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label>Môn học:</label>
                    <select class="form-control" id="sapXepMonHoc">
                        <option value="">-- Chọn môn học --</option>
                    </select>
                </div>
                <div id="sapXepList" class="sortable-list">
                    <!-- Danh sách bài học để sắp xếp -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">
                    <i class="fa fa-times"></i> Hủy
                </button>
                <button type="button" class="btn btn-warning" onclick="luuSapXep()">
                    <i class="fa fa-save"></i> Lưu thứ tự
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.filter-section {
    background-color: #fff3cd;
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 5px;
    border-left: 4px solid #f39c12;
}

.table th {
    background-color: #f4f4f4;
    font-weight: 600;
}

.label {
    font-size: 11px;
    padding: 4px 8px;
}

.btn-xs {
    padding: 1px 5px;
    font-size: 12px;
    line-height: 1.5;
    border-radius: 3px;
}

.text-red {
    color: #dd4b39;
}

.modal-header {
    background-color: #f39c12;
    color: white;
}

.modal-header .close {
    color: white;
    opacity: 0.8;
}

.modal-header .close:hover {
    opacity: 1;
}

.sortable-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 10px;
}

.sortable-item {
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 3px;
    padding: 8px;
    margin: 5px 0;
    cursor: move;
}

.sortable-item:hover {
    background: #e9e9e9;
}

.lesson-stats {
    background: #f0f8ff;
    padding: 10px;
    border-radius: 5px;
    margin-bottom: 15px;
}

.stat-item {
    display: inline-block;
    margin-right: 20px;
    font-weight: 600;
}

.stat-item .fa {
    margin-right: 5px;
}
</style>
