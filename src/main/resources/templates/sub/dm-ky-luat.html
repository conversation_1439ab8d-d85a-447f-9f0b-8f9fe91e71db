<!--Modal Thêm lớp-->
<div class="modal modal-pri" role="dialog" id="addModal" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog box-trans" role="document" id="box-add box-edit box-frm">
        <div class="modal-content box box-solid box-success">
            <div class="box-header with-border">
                <button type="button" class="close btn-flat" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">Thông tin kỷ luật</h4>
            </div>
            <div class="modal-body">
                <form id="frm-1">
	                     <div class="row">
	          				<div class="form-group col-sm-4">
		                        <label class="required">Mã kỷ luật</label>
		                        <input type="text" name="maKyLuat" id="txt-maKyLuat" class="form-control"/>
	                    	</div>
	                    	<div class="form-group col-sm-8">
		                        <label class="required"><PERSON><PERSON><PERSON> thức kỷ luật</label>
		                        <input type="text" name="tenKyLuat" id="txt-tenKyLuat" class="form-control"/>
                      		</div>
	                    </div>
	                      <div class="row">
	                    	 <div class="form-group col-sm-12">
		                        <label class="col-sm-4 control-label" for="cmb-haBacTn">Hạ bậc tốt nghiệp</label>
		                        <div class="col-sm-8">
		                            <label class="switch">
		                                <input type="checkbox" name="haBacTn" id="cmb-haBacTn">
		                                <span class="slider round"></span>
		                            </label>
		                            &nbsp;&nbsp;
		                             <span id="haBacTn-sp"></span> 
		                        </div>
                    		</div>
                    	</div>
                    	
                    	<div class="row">
	                    	 <div class="form-group col-sm-12">
		                        <label class="col-sm-4 control-label" for="cmb-camHocBong">Cấm Học Bổng</label>
		                        <div class="col-sm-8">
		                            <label class="switch">
		                                <input type="checkbox" name="camHocBong" id="cmb-camHocBong">
		                                <span class="slider round"></span>
		                            </label>
		                            &nbsp;&nbsp;
		                             <span id="camHocBong-sp"></span> 
		                        </div>
                    		</div>
	                     </div>
	                     
	                     <div class="row">
	                    	 <div class="form-group col-sm-12">
		                        <label class="col-sm-4 control-label" for="cmb-camThi">Cấm thi</label>
		                        <div class="col-sm-8">
		                            <label class="switch">
		                                <input type="checkbox" name="camThi" id="cmb-camThi">
		                                <span class="slider round"></span>
		                            </label>
		                            &nbsp;&nbsp;
		                             <span id="camThi-sp"></span> 
		                        </div>
                    		</div>
	                     </div>
	                     
	                     <div class="row">
	                    	 <div class="form-group col-sm-12">
		                        <label class="col-sm-4 control-label" for="cmb-camThiTn">Cấm thi TN</label>
		                        <div class="col-sm-8">
		                            <label class="switch">
		                                <input type="checkbox" name="camThiTn" id="cmb-camThiTn">
		                                <span class="slider round"></span>
		                            </label>
		                            &nbsp;&nbsp;
		                             <span id="camThiTn-sp"></span> 
		                        </div>
                    		</div>
	                     </div>
	                     
	                     <div class="row">
	                    	 <div class="form-group col-sm-12">
		                        <label class="col-sm-4 control-label" for="cmb-khongDuocTn">Không Được xét TN</label>
		                        <div class="col-sm-8">
		                            <label class="switch">
		                                <input type="checkbox" name="khongDuocTn" id="cmb-khongDuocTn">
		                                <span class="slider round"></span>
		                            </label>
		                            &nbsp;&nbsp;
		                             <span id="khongDuocTn-sp"></span> 
		                        </div>
                    		</div>
	                     </div>
                    <input type="hidden" name="idKyLuat" id="txt-idKyLuat"/>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-success btn-flat" type="button" id="btn-luu"><i class="fa fa-check-circle"></i> Lưu</button>
                <button class="btn btn-default btn-flat" type="button" id="btn-Dong" data-dismiss="modal"><i class="fa fa-times-circle"></i> Đóng</button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<div class="row">
    <div class="col-md-12">
        <div class="box box-solid box-primary" id="box-frm">
            <div class="box-header">
                <h3 class="box-title">Danh mục kỷ luật</h3>
            </div>
               <div class="box-body">
              		<div class="row">
	                    <div class="col-md-12 text-center">
	                        <button type="button" class="btn btn-primary btn-flat" data-target="#addModal" data-toggle="modal"><i class="fa fa-plus"></i> Thêm hình thức kỷ luật</button>
	                    </div>
	                </div>
                </div>
            <div class="box-footer table-responsive">
                <div class="jdgrid" id="grid-ds"></div>
            </div>
            <div class="box-footer">
                <div class="jdpage" id="page-ds"></div>
            </div>
        </div>
    </div>
</div>
    