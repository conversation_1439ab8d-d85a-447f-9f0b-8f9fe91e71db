<!--<PERSON>dal nhập điểm-->
<div class="modal modal-suc" role="dialog" id="addModal_2" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog modal-lg box-trans" role="document" id="box-detail" style="width: 90%;">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">Danh sách điểm môn học:
					<span style="font-weight:bold;" id="maMonHocModal"></span> - 
					<span style="font-weight:bold;" id="tenMonHocModal"></span>
					(<span id="tenNhomModal" style="font-weight:bold;"></span>)
				</h4>
            </div>
            <div class="modal-body">
				<div class="row">
					<form id="frm-filter">
					<div class="col-sm-5 form-group">
						<label>Tình trạng</label>
						<select class="form-control select2" name="idTinhTrang" id="cmb-tinhTrang-filter">
						</select>
					</div>
					<div class="col-sm-2">
						<label class="invisible">Lọc</label>
						<button type="button" class="btn btn-primary btn-flat btn-block" id="btn-filter"><i class="fa fa-filter"></i> Lọc</button>
					</div>
					</form>
				</div>
				<input type="hidden" id="idNhomDssv" name="idNhomDssv"> 
				<input type="hidden" id="lanDuyet" name="lanDuyet">
				<div class="jdgrid" id="grid-detail"></div>
                <div class="jdgrid" id="grid-detail-in" style="display: none"></div>
				<form enctype="multipart/form-data" id="frm-upload">
                    <input type="file" id="imp-file" name="file" accept=".xlsx" class="hide">
                </form>
            </div>
            <div class="modal-footer">
				<button type="button" class="btn btn-default btn-flat" id="btnPrint"><i class="fa fa-print"></i> In bảng điểm</button>
                <button class="btn btn-default btn-flat" type="button" id="btn-Dong" data-dismiss="modal"><i class="fa fa-times-circle"></i> Đóng</button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->
<!--Modal dssv dự thi-->
<div class="modal modal-suc" role="dialog" id="addModal_3" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog modal-lg box-trans" role="document" id="box-detail" style="width: 1150px;">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">Danh sách sinh viên dự thi:
					<span style="font-weight:bold;" id="maMonHocModal2"></span> - 
					<span style="font-weight:bold;" id="tenMonHocModal2"></span>
					(<span id="tenNhomModal2" style="font-weight:bold;"></span>)
				</h4>
            </div>
            <div class="modal-body">
				<input type="hidden" id="idNhomDssv2" name="idNhomDssv2"> 
				<div class="jdgrid" id="grid-dssv"></div>
            </div>
            <div class="modal-footer">
				<button type="button" class="btn btn-success btn-flat" id="btnExportDT"><i class="fa fa-check-circle"></i> Xuất danh sách dự thi</button>
                <button class="btn btn-default btn-flat" type="button" id="btn-Dong" data-dismiss="modal"><i class="fa fa-times-circle"></i> Đóng</button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->
<!--Modal dssv cấm thi-->
<div class="modal modal-suc" role="dialog" id="addModal_4" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog modal-lg box-trans" role="document" id="box-detail" style="width: 1150px;">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">Danh sách sinh viên cấm thi:
					<span style="font-weight:bold;" id="maMonHocModal3"></span> - 
					<span style="font-weight:bold;" id="tenMonHocModal3"></span>
					(<span id="tenNhomModal3" style="font-weight:bold;"></span>)
				</h4>
            </div>
            <div class="modal-body">
				<input type="hidden" id="idNhomDssv3" name="idNhomDssv3"> 
				<div class="jdgrid" id="grid-dssv-cam-thi"></div>
            </div>
            <div class="modal-footer">
				<button type="button" class="btn btn-success btn-flat" id="btnExportCT"><i class="fa fa-check-circle"></i> Xuất danh sách cấm thi</button>
                <button class="btn btn-default btn-flat" type="button" id="btn-Dong" data-dismiss="modal"><i class="fa fa-times-circle"></i> Đóng</button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->
<!--Modal dssv cấm thi-->
<div class="modal modal-suc" role="dialog" id="addModal_5" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog modal-lg box-trans" role="document" id="box-detail" style="width: 1150px;">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">In danh sách sinh viên cấm thi theo môn học</h4>
            </div>
            <div class="modal-body">
				<form id="frmSearch">
                    <div class="row">
                        <div class="col-md-2 form-group">
                            <label>Năm học</label>
                            <select class="form-control select2" name="idNienKhoa" id="cmb-nienKhoa-ct">
                                
                            </select>
                        </div>
                        <div class="col-md-2 form-group">
                            <label>Học kỳ</label>
                            <select class="form-control select2" name="strHocKy" id="cmb-hocKy-ct">
                            </select>
						</div>
						<div class="col-md-3 form-group">
                            <label>Môn học</label>
                            <select class="form-control select2" name="strMonHoc" id="cmb-monHoc-ct">
                            </select>
						</div>
						<div class="col-md-3 form-group">
                            <label>Nhóm</label>
                            <select class="form-control select2" name="strNhom" id="cmb-nhom-ct">
                                <option value="0">Tất cả</option>
                            </select>
						</div>
                        <div class="col-sn-2 form-group" style="padding-top: 2%;">
							<button  type="button" class="btn btn-primary btn-flat" id="btn-view"><i class="fa fa-print"></i> In</button>
							<button type="button" class="btn btn-success btn-flat" id="btn-excel"><i class="fa fa-file-excel-o"></i> Xuất excel</button>
                        </div>
                    </div>
                </form>
			</div>
			<div  id="report-viewer"></div>
            <div class="modal-footer">
                <button class="btn btn-default btn-flat" type="button" id="btn-Dong" data-dismiss="modal"><i class="fa fa-times-circle"></i> Đóng</button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->
<!--Modal dssv cấm thi-->
<div class="modal modal-suc" role="dialog" id="addModal_6" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog modal-lg box-trans" role="document" id="box-detail" style="width: 1150px;">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">In danh sách sinh viên cấm thi theo lớp</h4>
            </div>
            <div class="modal-body">
				<form id="frmSearch">
                    <div class="row">
                        <div class="col-md-2 form-group">
                            <label>Năm học</label>
                            <select class="form-control select2" name="idNienKhoa" id="cmb-nienKhoa-ctlop">
                                
                            </select>
                        </div>
                        <div class="col-md-2 form-group">
                            <label>Học kỳ</label>
                            <select class="form-control select2" name="strHocKy" id="cmb-hocKy-ctlop">
                            </select>
						</div>
						<div class="col-md-6 form-group">
                            <label>Lớp</label>
                            <select class="form-control select2" name="strNhom" id="cmb-nhom-ctlop">
                                <option value="0">Tất cả</option>
                            </select>
						</div>
                        <div class="col-sn-2 form-group" style="padding-top: 2%;">
							<button  type="button" class="btn btn-primary btn-flat" id="btn-view-lop"><i class="fa fa-print"></i> In</button>
							<button type="button" class="btn btn-success btn-flat" id="btn-excel-lop"><i class="fa fa-file-excel-o"></i> Xuất excel</button>
                        </div>
                    </div>
                </form>
			</div>
			<div  id="report-viewer-lop"></div>
            <div class="modal-footer">
                <button class="btn btn-default btn-flat" type="button" id="btn-Dong" data-dismiss="modal"><i class="fa fa-times-circle"></i> Đóng</button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->
<!--Modal in bảng điểm-->
<div class="modal modal-suc" role="dialog" id="inBangDiemModal" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog modal-lg box-trans" role="document" id="box-bangdiem" style="width: 1150px;">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">Danh sách điểm môn học:
					<span style="font-weight:bold;" id="maMonHocInBangDiem"></span> - 
					<span style="font-weight:bold;" id="tenMonHocInBangDiem"></span>
				</h4>
            </div>
            <div class="modal-body">
				<input type="hidden" id="idNhomDssv" name="idNhomDssv"> 
				<input type="hidden" id="lanDuyet" name="lanDuyet">
				<input type="hidden" id="hocKyInBangDiem" name="hocKyInBangDiem">
				<input type="hidden" id="nienKhoaInBangDiem" name="nienKhoaInBangDiem">
				<div class="jdgrid" id="grid-bangdiem"></div>
            </div>
            <div class="modal-footer">
				<button type="button" class="btn btn-default btn-flat" id="btnPrintAll"><i class="fa fa-print"></i> In bảng điểm</button>
				<button type="button" class="btn btn-success btn-flat" id="btnExportAll"><i class="fa fa-file-excel-o"></i> Xuất bảng điểm</button>
                <button class="btn btn-default btn-flat" type="button" id="btn-Dong" data-dismiss="modal"><i class="fa fa-times-circle"></i> Đóng</button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->
<div class="row">
 		<div class="col-md-12">
 			<div class="box box-solid box-primary" id="box-ds">
				<div class="box-header">
					<h3 class="box-title">Thống kê nhập điểm</h3>
				</div>
				 <div class="box-body">
					<form name="frmSearch" id="frmSearch">
						   <div class="row">
							   <div class="col-md-2 form-group">
								   <label>Năm học</label>
								   <select class="form-control select2" name="idKhoi" id="cmb-namhoc-search" >
								   </select>
							   </div>
							   <div class="col-md-2 form-group">
								<label>Học kỳ</label>
								<select class="form-control select2" name="idKhoi" id="cmb-hocky-search" >
								</select>
							</div>
							<div class="col-md-2 form-group">
								<label>Trạng thái</label>
								<select class="form-control select2" name="trangThaiDuyetDiem" id="cmb-trangthai-search" >
									<option value="-1">Tất cả</option>
									<option value="0">Chưa duyệt điểm</option>
									<option value="1">Duyệt điểm kiểm tra</option>
									<option value="2">Duyệt điểm lần 1</option>
									<option value="3">Duyệt điểm lần 2</option>
								</select>
							</div>
                            <div class="col-md-3 form-group">
                                <label>Lớp</label>
                                <select class="form-control select2" name="idLop" id="cmb-lop" >
                                </select>
                            </div>
							   <div class="col-md-3 form-group">
								   <label>Từ khóa</label>
								   <div class="input-group">
									   <input type="text" class="form-control" id="txt-keyword" name="keyword" placeholder="Mã môn học, Tên môn học, Tên nhóm">
									   <span class="input-group-btn">
										   <button type="button" class="btn btn-primary btn-block btn-flat" id="btn-search" title="Tìm kiếm"><i class="fa fa-search"></i></button>
									   </span>
								   </div>
							   </div>
						   </div>
					   </form>
						<div class="row">
						   <div class="col-md-12 text-right">
							   <button type="button" class="btn btn-primary btn-flat" id="btn-print"><i class="fa fa-print" >&nbsp;</i>In danh sách</button>
							   <button type="button" class="btn btn-warning btn-flat" id="btn-print-dsct" data-target="#addModal_5" data-toggle="modal"><i class="fa fa-print">&nbsp;</i>In DSCT Theo Môn Học</button>
							   <button type="button" class="btn btn-warning btn-flat" id="btn-print-dscttheolop" data-target="#addModal_6" data-toggle="modal"><i class="fa fa-print">&nbsp;</i>In DSCT Theo Lớp</button>
							   <button type="button" class="btn btn-success btn-flat" id="btn-print-bd"><i class="fa fa-print" >&nbsp;</i>In bảng điểm theo môn học</button>   
							   <!-- <button type="button" class="btn btn-primary btn-flat" id="btn-excel"><i class="fa fa-file-excel-o" >&nbsp;</i>Xuất Excel</button> -->
						   </div>
					   </div>
			   </div>
			   <div class="box-footer table-responsive">
				   <div class="jdgrid" id="grid-ds"></div>
			   </div>
			   <div class="box-footer">
				   <div class="jdpage" id="page-ds"></div>
			   </div>
 			</div>
 		</div>
 	</div>
<!--Modal chọn ngày in-->
<div class="modal modal-suc" role="dialog" id="chonNgayInModal" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">Chọn ngày in</h4>
            </div>
            <div class="modal-body">
                <form id="frm-chon-ngay">
                    <div class="form-group">
                        <label>Ngày in</label>
                        <div class="row">
                            <div class="col-sm-3">
                                <select class="form-control" id="ngayIn">
                                    <!-- Options cho ngày 1-31 -->
                                    <option value="">Ngày</option>
                                    <script>
                                        for(let i = 1; i <= 31; i++) {
                                            document.write(`<option value="${i < 10 ? '0' + i : i}">${i}</option>`);
                                        }
                                    </script>
                                </select>
                            </div>
                            <div class="col-sm-3">
                                <select class="form-control" id="thangIn">
                                    <!-- Options cho tháng 1-12 -->
                                    <option value="">Tháng</option>
                                    <script>
                                        for(let i = 1; i <= 12; i++) {
                                            document.write(`<option value="${i < 10 ? '0' + i : i}">${i}</option>`);
                                        }
                                    </script>
                                </select>
                            </div>
                            <div class="col-sm-4">
                                <select class="form-control" id="namIn">
                                    <!-- Options cho năm -->
                                    <option value="">Năm</option>
                                    <script>
                                        const currentYear = new Date().getFullYear();
                                        for(let i = currentYear - 1; i <= currentYear + 1; i++) {
                                            document.write(`<option value="${i}">${i}</option>`);
                                        }
                                    </script>
                                </select>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-success btn-flat" id="btn-xac-nhan-ngay"><i class="fa fa-check"></i> Xác nhận</button>
                <button class="btn btn-default btn-flat" type="button" data-dismiss="modal"><i class="fa fa-times-circle"></i> Đóng</button>
            </div>
        </div>
    </div>
</div>