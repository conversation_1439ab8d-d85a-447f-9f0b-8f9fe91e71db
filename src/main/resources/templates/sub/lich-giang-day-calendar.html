<!-- Copy from lich-giang-day-calendar.html and modify for personal schedule -->
<!-- This will be created as lich-ca-nhan-calendar.html -->
    <style>
        .calendar-container {
            margin: 20px 0;
        }
        
        .week-navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        
        .week-info {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
        
        .calendar-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .calendar-table th {
            background: #007bff;
            color: white;
            padding: 15px 8px;
            text-align: center;
            font-weight: bold;
            border: 1px solid #dee2e6;
        }
        
        .calendar-table td {
            border: 1px solid #dee2e6;
            vertical-align: top;
            height: 120px;
            width: 14.28%;
            padding: 5px;
            position: relative;
        }
        
        .date-header {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
            text-align: center;
            padding: 5px;
            background: #f8f9fa;
            border-radius: 3px;
        }
        
        .today .date-header {
            background: #007bff;
            color: white;
        }
        
        .schedule-item {
            background: #e3f2fd;
            border-left: 3px solid #2196f3;
            margin: 2px 0;
            padding: 3px 5px;
            font-size: 11px;
            border-radius: 2px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .schedule-item:hover {
            background: #bbdefb;
            transform: translateX(2px);
        }
        
        .schedule-item.theory {
            background: #e8f5e8;
            border-left-color: #4caf50;
        }
        
        .schedule-item.practice {
            background: #fff3e0;
            border-left-color: #ff9800;
        }
        
        .schedule-time {
            font-weight: bold;
            color: #666;
        }
        
        .schedule-subject {
            color: #333;
            font-weight: 500;
        }
        
        .schedule-class {
            color: #666;
            font-size: 10px;
        }
        
        .schedule-room {
            color: #999;
            font-size: 10px;
        }
        
        .no-schedule {
            color: #999;
            font-style: italic;
            text-align: center;
            padding: 20px;
        }
        
        .filter-section {
            margin-bottom: 20px;
        }
        
        .btn-nav {
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }
        
        .btn-nav:hover {
            transform: translateY(-1px);
        }
        
        .btn-prev, .btn-next {
            background: #6c757d;
            color: white;
        }
        
        .btn-today {
            background: #007bff;
            color: white;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .legend {
            display: flex;
            gap: 20px;
            margin-top: 10px;
            font-size: 12px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .legend-color {
            width: 15px;
            height: 15px;
            border-radius: 2px;
        }
        
        .legend-theory {
            background: #4caf50;
        }
        
        .legend-practice {
            background: #ff9800;
        }

        .session-header {
            background: #f8f9fa;
            font-weight: bold;
            text-align: center;
            vertical-align: middle;
            border-right: 2px solid #dee2e6;
            color: #495057;
        }

        .session-morning {
            background: #e8f5e8;
            color: #2e7d32;
        }

        .session-afternoon {
            background: #fff3e0;
            color: #f57c00;
        }

        .session-evening {
            background: #e3f2fd;
            color: #1976d2;
        }

        @media (max-width: 768px) {
            .calendar-table {
                font-size: 10px;
            }
            
            .calendar-table td {
                height: 80px;
                padding: 2px;
            }
            
            .schedule-item {
                font-size: 9px;
                padding: 2px 3px;
            }
            
            .week-navigation {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
<!-- Quản lý Lịch giảng dạy - Calendar View -->
<div class="row">
    <div class="col-md-12">
        <div class="box box-info">
            <div class="box-header with-border">
                <h3 class="box-title">
                    <i class="fa fa-calendar"></i> Lịch giảng dạy - Calendar View
                </h3>
                <div class="box-tools pull-right">
                    <div class="btn-group">
                        <a href="/lich-giang-day" class="btn btn-primary btn-sm">
                            <i class="fa fa-list"></i> Danh sách
                        </a>
                        <a href="/lich-giang-day/calendar" class="btn btn-success btn-sm">
                            <i class="fa fa-calendar"></i> Calendar View
                        </a>
                    </div>
                </div>
            </div>

            <!-- Bộ lọc -->
            <div class="box-body">
                <div class="row filter-section">
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>Niên khóa:</label>
                            <select class="form-control" id="filterNienKhoa">
                                <option value="">-- Tất cả --</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>Học kỳ:</label>
                            <select class="form-control" id="filterHocKy" disabled>
                                <option value="">-- Chọn năm học trước --</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>Giảng viên:</label>
                            <select class="form-control select2" id="filterGiangVien">
                                <option value="">-- Tất cả --</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>Môn học:</label>
                            <select class="form-control select2" id="filterMonHoc">
                                <option value="">-- Tất cả --</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>Lớp:</label>
                            <select class="form-control select2" id="filterLop">
                                <option value="">-- Tất cả --</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>Phòng học:</label>
                            <select class="form-control select2" id="filterPhong">
                                <option value="">-- Tất cả --</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>Hình thức:</label>
                            <select class="form-control" id="filterHinhThuc">
                                <option value="">-- Tất cả --</option>
                                <option value="LT">Lý thuyết</option>
                                <option value="TH">Thực hành</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-1">
                        <div class="form-group">
                            <label>Buổi:</label>
                            <select class="form-control" id="filterBuoi">
                                <option value="">-- Tất cả --</option>
                                <option value="SANG">Sáng</option>
                                <option value="CHIEU">Chiều</option>
                                <option value="TOI">Tối</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-1">
                        <div class="form-group">
                            <label>Thứ:</label>
                            <select class="form-control" id="filterThu">
                                <option value="">-- Tất cả --</option>
                                <option value="1">CN</option>
                                <option value="2">T2</option>
                                <option value="3">T3</option>
                                <option value="4">T4</option>
                                <option value="5">T5</option>
                                <option value="6">T6</option>
                                <option value="7">T7</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-1">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <button type="button" class="btn btn-info btn-block" onclick="loadWeekSchedule()">
                                <i class="fa fa-search"></i> Tìm kiếm
                            </button>
                        </div>
                    </div>
                </div>

                <div class="legend" style="margin-bottom: 15px;">
                    <div class="legend-item">
                        <div class="legend-color legend-theory"></div>
                        <span>Lý thuyết</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color legend-practice"></div>
                        <span>Thực hành</span>
                    </div>
                </div>

                <!-- Calendar Container -->
                <div class="calendar-container">
                    <!-- Week Navigation -->
                    <div class="week-navigation">
                        <button class="btn btn-nav btn-prev" id="prevWeek">
                            <i class="fa fa-chevron-left"></i> Tuần trước
                        </button>

                        <div class="week-info" id="weekInfo">
                            Tuần từ 14/07/2025 đến 20/07/2025
                        </div>

                        <div>
                            <button class="btn btn-nav btn-today" id="todayBtn">Hôm nay</button>
                            <button class="btn btn-nav btn-next" id="nextWeek">
                                Tuần sau <i class="fa fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Calendar Table -->
                    <div id="calendarContent">
                        <div class="loading">
                            <i class="fa fa-spinner fa-spin"></i> Đang tải dữ liệu...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

    <!-- Schedule Detail Modal -->
    <div class="modal fade" id="scheduleDetailModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Chi tiết lịch giảng</h4>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body" id="scheduleDetailContent">
                    <!-- Content will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Đóng</button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
