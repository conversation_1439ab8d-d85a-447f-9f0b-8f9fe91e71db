<!--Modal Thêm bộ môn-->
<div class="modal modal-pri" role="dialog" id="addModal" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog box-trans" role="document" id="box-add box-edit box-frm" style="width: 30%">
        <div class="modal-content box box-solid box-success">
            <div class="box-header with-border">
                <button type="button" class="close btn-flat" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">Thông tin bộ môn</h4>
            </div>
            <div class="modal-body">
                <form id="frm-1">
                	        <div class="row">
               					<div class="form-group col-sm-12">
		                            <label class="required">Mã bộ môn</label>
		                            <input type="text" name="maBoMon" id="txt-maPbbm" class="form-control"/>
		                        </div>
		                        </div>
		                        <div class="row">
		                        <div class="form-group col-sm-12">
		                            <label class="required">Tên bộ môn</label>
		                            <input type="text" name="tenBoMon" id="txt-tenPbbm" class="form-control"/>
		                        </div>
		                    </div>
	                        <div class="row">
               					<div class="form-group col-sm-12">
		                            <label>Đơn vị</label>
		                           <select name="idDonVi" id="cmb-tenDonVi" class="form-control select2">
 		 							</select>
		                        </div>
	                        </div>
	                        <input type="hidden" name="idPbbm" id="txt-idPbbm"/>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-success btn-flat" type="button" id="btn-luu"><i class="fa fa-check-circle"></i> Lưu</button>
                <button class="btn btn-default btn-flat" type="reset" id="btn-Lammoi"><i class="fa fa-refresh"></i> Làm mới</button>
                <button class="btn btn-default btn-flat" type="button" id="btn-Dong" data-dismiss="modal"><i class="fa fa-times-circle"></i> Đóng</button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<div class="row">
    <div class="col-md-12">
        <div class="box box-solid box-primary" id="box-frm">
            <div class="box-header">
                <h3 class="box-title">Danh mục bộ môn</h3>
                </div>
                <div class="box-body">
                <form name="frmSearch" id="frmSearch">
                <div class="row">
                	<div class="col-md-6 form-group">
	                        <label>Đơn vị</label>
	                        <select class="form-control select2" name="idDonVi" id="cmb-dv-search">
	                        <option value="-1">Tất cả</option>
	                        </select>
	                </div>
	                	<div class="col-md-6 form-group">
	                	<label>Từ khóa</label>
	                	<div class="input-group">
	                    	<input type="text" class="form-control" id="txt-keyword" name="keyword" placeholder="Mã bộ môn, Tên bộ môn">
	                        	<span class="input-group-btn">
	                        	<button type="button" class="btn btn-primary btn-block btn-flat" id="btn-search"><i class="fa fa-search"></i></button>
	                            </span>
	                    </div>
	                	</div>
                </div>
                </form> 
                <div class="row">
	                    <div class="col-md-12 text-center">
	                        <button type="button" class="btn btn-primary btn-flat" data-target="#addModal" data-toggle="modal"><i class="fa fa-plus"></i> Thêm bộ môn</button>
	                    </div>
	                </div>
	                </div>
			<div class="box-footer table-responsive">
                <div class="jdgrid" id="grid-ds"></div>
            </div>
            <div class="box-footer">
                <div class="jdpage" id="page-ds"></div>
            </div>
        </div>
    </div>
</div>
