<!-- <PERSON><PERSON><PERSON> cá nhân giảng viên -->
<div class="row">
    <div class="col-md-12">
        <div class="box box-info">
            <div class="box-header with-border">
                <h3 class="box-title">
                    <i class="fa fa-calendar-user"></i> Lịch cá nhân
                </h3>
                <div class="box-tools pull-right">
                    <div class="btn-group">
                        <a href="/lich-ca-nhan/list" class="btn btn-primary btn-sm">
                            <i class="fa fa-list"></i> Danh sách
                        </a>
                        <a href="/lich-ca-nhan/calendar" class="btn btn-success btn-sm">
                            <i class="fa fa-calendar"></i> Calendar View
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Bộ lọc -->
            <div class="box-body">
                <div class="row filter-section">
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>Niên khóa:</label>
                            <select class="form-control" id="filterNienKhoa">
                                <option value="">-- Tất cả --</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>Học kỳ:</label>
                            <select class="form-control" id="filterHocKy" disabled>
                                <option value="">-- Chọn niên khóa trước --</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>Lớp:</label>
                            <select class="form-control select2" id="filterLop">
                                <option value="">-- Tất cả --</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>Phòng học:</label>
                            <select class="form-control select2" id="filterPhong">
                                <option value="">-- Tất cả --</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>Hình thức:</label>
                            <select class="form-control" id="filterHinhThuc">
                                <option value="">-- Tất cả --</option>
                                <option value="LT">Lý thuyết</option>
                                <option value="TH">Thực hành</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-1">
                        <div class="form-group">
                            <label>Buổi:</label>
                            <select class="form-control" id="filterBuoi">
                                <option value="">-- Tất cả --</option>
                                <option value="SANG">Sáng</option>
                                <option value="CHIEU">Chiều</option>
                                <option value="TOI">Tối</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-1">
                        <div class="form-group">
                            <label>Thứ:</label>
                            <select class="form-control" id="filterThu">
                                <option value="">-- Tất cả --</option>
                                <option value="1">CN</option>
                                <option value="2">T2</option>
                                <option value="3">T3</option>
                                <option value="4">T4</option>
                                <option value="5">T5</option>
                                <option value="6">T6</option>
                                <option value="7">T7</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>Từ ngày:</label>
                            <input type="date" class="form-control" id="filterTuNgay">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>Đến ngày:</label>
                            <input type="date" class="form-control" id="filterDenNgay">
                        </div>
                    </div>
                    <div class="col-md-1">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <button type="button" class="btn btn-info btn-block" onclick="timKiemLichCaNhan()">
                                <i class="fa fa-search"></i> Tìm kiếm
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Bảng hiển thị lịch -->
                <div class="table-responsive">
                    <table class="table table-bordered table-striped" id="lichCaNhanTable">
                        <thead>
                            <tr>
                                <th width="4%">STT</th>
                                <th width="8%">Ngày giảng</th>
                                <th width="6%">Thứ</th>
                                <th width="6%">Buổi</th>
                                <th width="8%">Tiết</th>
                                <th width="15%">Bài học</th>
                                <th width="12%">Môn học</th>
                                <th width="10%">Lớp</th>
                                <th width="8%">Phòng</th>
                                <th width="6%">Hình thức</th>
                                <th width="8%">Trạng thái</th>
                                <th width="9%">Thao tác</th>
                            </tr>
                        </thead>
                        <tbody id="lichCaNhanTableBody">
                            <tr>
                                <td colspan="12" class="text-center">Chưa có dữ liệu</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="dataTables_info" id="lichCaNhanInfo">
                            Hiển thị 0 đến 0 của 0 bản ghi
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="dataTables_paginate paging_simple_numbers" id="lichCaNhanPagination">
                            <!-- Pagination will be generated here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal chi tiết lịch -->
<div class="modal fade" id="lichDetailModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Chi tiết lịch giảng</h4>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body" id="lichDetailContent">
                <!-- Content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Đóng</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal cập nhật trạng thái -->
<div class="modal fade" id="updateStatusModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Cập nhật trạng thái</h4>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="updateStatusForm">
                    <input type="hidden" id="updateIdLichGiang">
                    <div class="form-group">
                        <label>Trạng thái:</label>
                        <select class="form-control" id="updateTrangThai" required>
                            <option value="">-- Chọn trạng thái --</option>
                            <option value="0">Chưa giảng</option>
                            <option value="1">Đã giảng</option>
                            <option value="2">Hủy</option>
                            <option value="3">Hoãn</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Hủy</button>
                <button type="button" class="btn btn-primary" onclick="luuTrangThai()">Lưu</button>
            </div>
        </div>
    </div>
</div>

<style>
.status-chua-giang {
    background-color: #f39c12;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
}

.status-da-giang {
    background-color: #00a65a;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
}

.status-huy {
    background-color: #dd4b39;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
}

.status-hoan {
    background-color: #3c8dbc;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
}

.form-theory {
    background-color: #d4edda;
    color: #155724;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
}

.form-practice {
    background-color: #fff3cd;
    color: #856404;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
}

.buoi-sang {
    background-color: #e8f5e8;
    color: #2e7d32;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
}

.buoi-chieu {
    background-color: #fff3e0;
    color: #f57c00;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
}

.buoi-toi {
    background-color: #e3f2fd;
    color: #1976d2;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
}
</style>
