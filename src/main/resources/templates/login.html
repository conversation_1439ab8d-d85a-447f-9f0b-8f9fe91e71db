<!DOCTYPE html>
<html xmlns:th="http://www.w3.org/1999/xhtml">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title><PERSON><PERSON><PERSON> nhập | Hệ Thống QLĐT</title>
	<meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
	<link rel="shortcut icon" href="dist/img/fav.ico">
	<link rel="stylesheet" href="bower_components/bootstrap/dist/css/bootstrap.min.css">
	<link rel="stylesheet" href="bower_components/font-awesome/css/font-awesome.min.css">
	<link rel="stylesheet" href="dist/css/AdminLTE.min.css">
	<link rel="stylesheet" href="bower_components/particles/css/style.css">
	<script src="https://www.google.com/recaptcha/api.js"></script>
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">

	<style>
		/* Input validation styles */
		.form-group.has-error .form-control {
			border-color: #e74c3c;
			padding-right: 42.5px;
		}

		.form-group.has-success .form-control {
			border-color: #2ecc71;
			padding-right: 42.5px;
		}

		.form-group.has-error .form-control-feedback {
			color: #e74c3c;
		}

		.form-group.has-success .form-control-feedback {
			color: #2ecc71;
		}

		.validation-message {
			display: none;
			margin-top: 5px;
			margin-left: 15px;
			font-size: 13px;
			color: #e74c3c;
			animation: fadeIn 0.3s ease-in;
		}

		.form-group.has-error .validation-message {
			display: block;
		}

		/* Toast message customization */
		.toast {
			background-color: rgba(236, 240, 241, 0.95);
			border-radius: 8px;
			box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
		}

		.toast-error {
			background-color: rgba(231, 76, 60, 0.95);
			color: white;
		}

		.toast-success {
			background-color: rgba(46, 204, 113, 0.95);
			color: white;
		}

		/* Form shake animation for errors */
		@keyframes shake {
			0%, 100% { transform: translateX(0); }
			10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
			20%, 40%, 60%, 80% { transform: translateX(5px); }
		}

		.shake {
			animation: shake 0.6s cubic-bezier(.36,.07,.19,.97) both;
		}

		/* Original styles */
		.login-logo, .login-logo a {
			color: #fff;
			font-size: 32px;
			text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
			letter-spacing: 1px;
		}

		.login-box {
			margin-top: 5%;
		}

		.login-box-msg {
			font-size: 22px;
			font-weight: 500;
			color: #2c3e50;
			margin-bottom: 25px;
			text-align: center;
		}

		.login-box-body {
			border-radius: 10px;
			border-top: 4px solid #3498db;
			background-color: rgba(255,255,255,0.95);
			-webkit-backdrop-filter: blur(10px);
			backdrop-filter: blur(10px);
			padding: 30px;
			-webkit-box-shadow: 0 5px 15px rgba(0,0,0,0.2);
			box-shadow: 0 5px 15px rgba(0,0,0,0.2);
		}

		.input-with-icon {
			position: relative;
		}

		.icon-left {
			position: absolute;
			left: 15px;
			top: 50%;
			transform: translateY(-50%);
			color: #7f8c8d;
			font-size: 16px;
			z-index: 2;
		}

		.icon-right {
			position: absolute;
			right: 15px;
			top: 50%;
			transform: translateY(-50%);
			color: #7f8c8d;
			font-size: 16px;
			cursor: pointer;
			transition: color 0.3s ease;
			z-index: 2;
		}

		.icon-right:hover {
			color: #3498db;
		}

		.input-with-icon .form-control {
			padding-left: 40px;
			padding-right: 40px;
		}

		.form-group.has-error .icon-left,
		.form-group.has-error .icon-right {
			color: #e74c3c;
		}

		.form-group.has-success .icon-left,
		.form-group.has-success .icon-right {
			color: #2ecc71;
		}

		.form-control {
			height: 45px;
			border-radius: 25px;
			padding: 10px 20px;
			font-size: 15px;
			border: 2px solid #e0e0e0;
			transition: all 0.3s ease;
		}

		.form-control:focus {
			border-color: #3498db;
			box-shadow: none;
		}

		.form-group {
			margin-bottom: 20px;
		}

		.form-control-feedback {
			line-height: 45px;
			color: #7f8c8d;
			right: 15px;
		}

		.toggle-password {
			position: absolute;
			top: 50%;
			transform: translateY(-50%);
			cursor: pointer;
			color: #7f8c8d;
			transition: color 0.3s ease;
			z-index: 2;
		}

		.toggle-password:hover {
			color: #3498db;
		}

		.password-container {
			position: relative;
		}

		.btn-primary {
			background-color: #3498db;
			border: none;
			padding: 12px;
			border-radius: 25px;
			font-size: 16px;
			text-transform: uppercase;
			letter-spacing: 1px;
			transition: all 0.3s ease;
			margin-top: 10px;
		}

		.btn-primary:hover, .btn-primary:focus {
			background-color: #2980b9;
			transform: translateY(-2px);
			box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
		}

		.remember-me {
			margin: 15px 0;
			display: flex;
			align-items: center;
			user-select: none;
		}

		.remember-me label {
			display: flex;
			align-items: center;
			color: #34495e;
			font-weight: 500;
			cursor: pointer;
			padding: 4px 0;
			font-size: 14px;
		}

		.remember-me input[type="checkbox"] {
			position: relative;
			appearance: none;
			-webkit-appearance: none;
			width: 18px;
			height: 18px;
			margin-right: 10px;
			border: 2px solid #3498db;
			border-radius: 4px;
			outline: none;
			cursor: pointer;
			transition: all 0.3s ease;
		}

		.remember-me input[type="checkbox"]:checked {
			background-color: #3498db;
			border-color: #3498db;
		}

		.remember-me input[type="checkbox"]:checked::before {
			content: '\f00c';
			font-family: 'FontAwesome';
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			color: white;
			font-size: 12px;
		}

		.remember-me input[type="checkbox"]:hover {
			border-color: #2980b9;
			box-shadow: 0 0 5px rgba(52, 152, 219, 0.3);
		}

		.remember-me label:hover input[type="checkbox"] {
			border-color: #2980b9;
		}

		.g-recaptcha {
			margin: 15px 0;
			transform: scale(0.95);
			transform-origin: 0 0;
		}

		#captcha-error {
			color: #e74c3c;
			display: none;
			margin-bottom: 15px;
			font-size: 14px;
		}

		/* Animation cho form */
		.login-box {
			animation: fadeIn 0.8s ease-out;
		}

		@keyframes fadeIn {
			from {
				opacity: 0;
				transform: translateY(-20px);
			}
			to {
				opacity: 1;
				transform: translateY(0);
			}
		}

		/* Hiệu ứng hover cho input */
		.form-group:hover .form-control {
			border-color: #bdc3c7;
		}

		/* Style cho copyright text */
		.copyright-text {
			position: fixed;
			z-index: 1;
			left: 0;
			bottom: 15px;
			width: 100%;
			color: #fff;
			text-align: center;
			text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
		}

		.copyright-text a {
			font-weight: bold;
			color: #fff;
			text-decoration: none;
			transition: color 0.3s ease;
		}

		.copyright-text a:hover {
			color: #3498db;
		}
	</style>
</head>
<body class="hold-transition login-page" id="particles-js">
<div class="login-box">
	<div class="login-logo">
		<a>HỆ THỐNG <BR/><b>QUẢN LÝ ĐÀO TẠO</b></a>
	</div>
	<div class="login-box-body">
		<p class="login-box-msg">Đăng nhập tài khoản</p>

		<form action="/dang-nhap" method="post" id="loginForm">
			<div class="form-group">
				<div class="input-with-icon">
					<i class="fa fa-user icon-left"></i>
					<input type="text" class="form-control" placeholder="Tên đăng nhập" name="username" id="username" required>
				</div>
				<div class="validation-message">Vui lòng nhập tên đăng nhập</div>
			</div>
			<div class="form-group">
				<div class="input-with-icon">
					<i class="fa fa-lock icon-left"></i>
					<input type="password" class="form-control" placeholder="Mật khẩu" name="password" id="password" required>
					<i class="toggle-password fa fa-eye icon-right"></i>
				</div>
				<div class="validation-message">Vui lòng nhập mật khẩu</div>
			</div>
			<div class="remember-me">
				<label>
					<input type="checkbox" id="rememberMe"> Ghi nhớ mật khẩu
				</label>
			</div>
			<div id="recaptcha-container" style="display: none;">
				<div class="g-recaptcha" data-sitekey="6Lfx5IIqAAAAALcyoObGfgeH8uqi1NtHWVWj8Mz2"></div>
				<div id="captcha-error">Vui lòng xác thực captcha</div>
			</div>
			<div class="row">
				<div class="col-xs-12">
					<button type="submit" class="btn btn-primary btn-block btn-flat" style="font-weight:600">
						<i class="fa fa-sign-in"></i> ĐĂNG NHẬP
					</button>
				</div>
			</div>
		</form>
	</div>
</div>
<p class="copyright-text">
	<span th:text="'&copy; '+${#dates.year(#dates.createNow())}"></span>
	<a href="http://camau.vnpt.vn" target="_blank" style="font-weight:bold;color:#fff">VNPT Cà Mau</a>
</p>

<script src="bower_components/jquery/dist/jquery.min.js"></script>
<script src="bower_components/bootstrap/dist/js/bootstrap.min.js"></script>
<script src="bower_components/particles/particles.js"></script>
<script src="bower_components/particles/js/app.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>

<script>
	let loginAttempts = parseInt(localStorage.getItem('loginAttempts')) || 0;
	$(function() {
		// Cấu hình toastr
		toastr.options = {
			"closeButton": true,
			"positionClass": "toast-top-right",
			"timeOut": "3000"
		};

		// Kiểm tra và điền thông tin đăng nhập đã lưu
		var savedCredentials = localStorage.getItem('savedCredentials');
		if (savedCredentials) {
			var credentials = JSON.parse(savedCredentials);
			$('#username').val(credentials.username);
			$('#password').val(credentials.password);
			$('#rememberMe').prop('checked', true);
		}

		if (window.location.pathname === '/') {
			localStorage.removeItem('loginAttempts');
		}

		var loginAttempts = localStorage.getItem('loginAttempts') || 0;
		if (loginAttempts >= 5) {
			$('#recaptcha-container').show();
		}

		// Hàm validate form
		function validateInput(input) {
			const $input = $(input);
			const $formGroup = $input.closest('.form-group');
			const value = $input.val().trim();

			if (!value) {
				$formGroup.removeClass('has-success').addClass('has-error');
				return false;
			} else {
				$formGroup.removeClass('has-error').addClass('has-success');
				return true;
			}
		}

		// Validate khi nhập
		$('#username, #password').on('input', function() {
			const $input = $(this);
			const $formGroup = $input.closest('.form-group');
			const value = $input.val().trim();

			if (!value) {
				$formGroup.removeClass('has-success').addClass('has-error');
			} else {
				$formGroup.removeClass('has-error').addClass('has-success');
			}
		});

		// Validate khi blur
		$('#username, #password').on('blur', function() {
			validateInput(this);
		});

		$('#loginForm').submit(function(e) {
			let isValid = true;

			// Validate all inputs
			$('#username, #password').each(function() {
				if (!validateInput(this)) {
					isValid = false;
				}
			});

			if (!isValid) {
				e.preventDefault();
				$('.login-box-body').addClass('shake');
				setTimeout(() => {
					$('.login-box-body').removeClass('shake');
				}, 650);
				return false;
			}

			// Kiểm tra captcha nếu cần
			if (loginAttempts >= 5) {
				var captchaResponse = grecaptcha.getResponse();
				if (!captchaResponse) {
					e.preventDefault();
					toastr.error('Vui lòng xác thực captcha');
					return false;
				}
			}

			// Lưu thông tin đăng nhập nếu checkbox được chọn
			if ($('#rememberMe').is(':checked')) {
				var credentials = {
					username: $('#username').val(),
					password: $('#password').val()
				};
				localStorage.setItem('savedCredentials', JSON.stringify(credentials));
			} else {
				localStorage.removeItem('savedCredentials');
			}
		});

		// Xử lý toggle password visibility
		$('.toggle-password').click(function() {
			const passwordInput = $('#password');
			const toggleIcon = $(this);

			// Toggle type của input
			if (passwordInput.attr('type') === 'password') {
				passwordInput.attr('type', 'text');
				toggleIcon.removeClass('fa-eye').addClass('fa-eye-slash');
			} else {
				passwordInput.attr('type', 'password');
				toggleIcon.removeClass('fa-eye-slash').addClass('fa-eye');
			}
		});
		if (loginAttempts >= 5) {
			var captchaResponse = grecaptcha.getResponse();
			if (!captchaResponse) {
				e.preventDefault();
				toastr.error('Vui lòng xác thực captcha');
				return false;
			}
		}

		// Lưu thông tin đăng nhập nếu checkbox được chọn
		if ($('#rememberMe').is(':checked')) {
			var credentials = {
				username: $('#username').val(),
				password: $('#password').val()
			};
			localStorage.setItem('savedCredentials', JSON.stringify(credentials));
		} else {
			localStorage.removeItem('savedCredentials');
		}
	});

	// Hiển thị thông báo lỗi
	if (window.location.search.includes('error=captcha')) {
		toastr.error('Vui lòng xác thực captcha!');
		loginAttempts++;
		localStorage.setItem('loginAttempts', loginAttempts);
		if (loginAttempts >= 5) {
			$('#recaptcha-container').show();
		}
	} else if (window.location.search.includes('error')) {
		loginAttempts++;
		localStorage.setItem('loginAttempts', loginAttempts);
		if (loginAttempts >= 5) {
			$('#recaptcha-container').show();
			toastr.error('Đăng nhập thất bại! Vui lòng xác thực captcha.');
		} else {
			toastr.error('Đăng nhập thất bại! Còn ' + (5 - loginAttempts) + ' lần thử.');
		}
	}

	// Background image
	var images = ['1', '2', '3', '4', '5', '6', '7','8'];
	$('#particles-js').css({'background-image': 'url(dist/img/' + getRandom(images) + '.jpg)'});

	function getRandom(arr) {
		return arr[Math.floor(Math.random() * arr.length)];
	}
</script>
</body>
</html>