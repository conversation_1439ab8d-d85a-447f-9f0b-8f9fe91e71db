<section class="py-5 bg-light">
	<!-- .container -->
	<div class="container">
		<div class="row">
			<!-- .col -->
			<div class="col-12 col-md-8 offset-md-2 text-center">
				<h2> PHIẾU THÔNG TIN THÍ SINH </h2>
				<p class="lead text-muted"> L<PERSON>u ý nhập: cẩn thận, đầy đủ và không được SAI SÓT. </p>
			</div><!-- /.col -->
		</div>
		<!-- .row -->
		<!-- .form -->
		<form id="frm-ts" class="needs-validation" novalidate="">
			<div class="row">
				<div class="card col-12 col-md-12">
					<!-- .card-body -->
					<div class="card-body">
						<!-- .fieldset -->
						<fieldset>
							<legend>THÔNG TIN THÍ SINH <small>(*) <PERSON>ác tr<PERSON>ờ<PERSON> hợp bắt buộc nhập</small></legend>
							<!-- .form-group -->
							<div class="form-row">
								<!-- grid column -->
								<div class="col-md-4 mb-3">
									<label for="txtHoTenDem">Họ & tên đệm (*)</label>
									<input type="text" class="form-control" id="txtHoTenDem" name="hoLot"
										required>
									<div class="invalid-feedback"> Vui lòng nhập Họ & tên đệm. </div>
								</div><!-- /grid column -->
								<div class="col-md-4 mb-3">
									<label for="txtTen">Tên (*)</label>
									<input type="text" class="form-control" id="txtTen" name="ten" required>
									<div class="invalid-feedback"> Vui lòng nhập Tên. </div>
								</div><!-- /grid column -->
								<!-- grid column -->
								<div class="col-md-4 mb-3">
									<label for="cmbGioiTinh">Giới tính (*)</label>
									<select class="custom-select d-block w-100" id="cmbGioiTinh" name="gioiTinh"
									required>
										<option value=""> Chọn giới tính</option>
										<option value="0"> Nam </option>
										<option value="1"> Nữ </option>
									</select>
									<div class="invalid-feedback"> Vui lòng chọn giới tính. </div>
								</div><!-- /grid column -->
							</div>
							<div class="form-row">
								<!-- grid column -->
								<div class="col-md-4 mb-3">
									<label for="txtNgaySinh">Ngày sinh (*)</label>
									<input type="date" class="form-control" id="txtNgaySinh" name="ngaySinh"
									required>
									<div class="invalid-feedback"> Vui lòng nhập ngày sinh. </div>
								</div><!-- /grid column -->
								<!-- grid column -->
								<div class="col-md-4 mb-3">
									<label for="txtNoiSinh">Nơi sinh (*)</label>
									<input type="text" class="form-control" id="txtNoiSinh" name="noiSinh" required>
									<div class="invalid-feedback"> Vui lòng nhập Nơi sinh. </div>
								</div><!-- /grid column -->
								<!-- grid column -->
								<div class="col-md-4 mb-3">
									<label for="txtSdt">Số điện thoại (*)</label>
									<input type="text" class="form-control" id="txtSdt" name="sdt" required>
									<div class="invalid-feedback"> Vui lòng nhập Số điện thoại. </div>
								</div><!-- /grid column -->
							</div>
							<div class="form-row">
								<!-- grid column -->
								<div class="col-md-4 mb-3">
									<label for="txtEmail">Email (*)</label>
									<input type="email" class="form-control" id="txtEmail" name="email" required>
									<div class="invalid-feedback"> Vui lòng nhập Email. </div>
								</div><!-- /grid column -->
								<!-- grid column -->
								<div class="col-md-4 mb-3">
									<label for="txtCMND">CMND/CCCD (*)</label>
									<input type="text" class="form-control" id="txtCMND" name="cmnd" required>
									<div class="invalid-feedback"> Vui lòng nhập CMND/CCCD. </div>
								</div><!-- /grid column -->
								<!-- grid column -->
								<div class="col-md-4 mb-3">
									<label for="txtNgayCap">Ngày cấp (*)</label>
									<input type="date" class="form-control" id="txtNgayCap" name="ngayCap" required>
									<div class="invalid-feedback"> Vui lòng nhập Ngày cấp. </div>
								</div><!-- /grid column -->
							</div>
							<div class="form-row">
								<!-- grid column -->
								<div class="col-md-4 mb-3">
									<label for="txtNoiCap">Nơi cấp CMND/CCCD (*)</label>
									<input type="text" class="form-control" id="txtNoiCap" name="noiCap" required>
									<div class="invalid-feedback"> Vui lòng nhập Nơi cấp CMND/CCCD. </div>
								</div><!-- /grid column -->
								<!-- grid column -->
								<div class="col-md-4 mb-3">
									<label for="fileCMNDTruoc">Ảnh mặt trước CMND/CCCD (*)</label>
									<input type="file" class="form-control" id="fileCMNDTruoc" name="matTruoc" accept="image/png, image/jpeg" required>
									<small id="tf1Help"
										class="form-text text-muted">Vui lòng tải lên file có định dạng là JPG, PNG và dung lượng ảnh nhỏ hơn hoặc bằng 5MB</small>
									<div class="invalid-feedback"> Vui lòng thêm file Ảnh mặt trước CMND/CCCD. </div>
								</div><!-- /grid column -->
								<div class="col-md-4 mb-3">
									<label for="fileCMNDSau">Ảnh mặt sau CMND/CCCD (*)</label>
									<input type="file" class="form-control" id="fileCMNDSau" name="matSau" accept="image/png, image/jpeg" required>
									<small id="tf1Help"
										class="form-text text-muted">Vui lòng tải lên file có định dạng là JPG, PNG và dung lượng ảnh nhỏ hơn hoặc bằng 5MB</small>
									<div class="invalid-feedback"> Vui lòng thêm file Ảnh mặt sau CMND/CCCD. </div>
								</div><!-- /grid column -->
							</div>
							<div class="form-row">
								<div class="col-md-8 mb-3">
									<label for="txtDiaChi">Địa chỉ liên hệ (*)</label>
									<input type="text" class="form-control" id="txtDiaChi" name="diaChi" required> <small id="tf1Help"
										class="form-text text-muted">Số nhà, tên đường (thôn, xóm, ấp), xã (phường),
										huyện, tỉnh (thành phố)</small>
									<div class="invalid-feedback"> Vui lòng nhâp Địa chỉ liên hệ. </div>
								</div><!-- /.form-group -->
								<div class="col-md-4 mb-3">
									<label for="cbmKhuVuc">Khu vực ưu tiên (*)</label>
									<select class="custom-select d-block w-100" id="cbmKhuVuc" name="kvUuTien" required>
										<option value=""> Chọn khu vực ưu tiên</option>
										<option value="1"> Khu vực 1</option>
										<option value="2"> Khu vực 2</option>
										<option value="3"> Khu vực 2 nông thôn</option>
										<option value="4"> Khu vực 3</option>
									</select>
									<div class="invalid-feedback"> Vui lòng chọn Khu vực ưu tiên. </div>
								</div><!-- /grid column -->
							</div>
						</fieldset><!-- /.fieldset -->
					</div><!-- /.card-body -->
					<!-- .card-body -->
				</div>
			</div><!-- /.container -->
			<div class="row">
				<div class="card col-12 col-md-12">
					<!-- .card-body -->
					<div class="card-body">
						<!-- .form -->
						<!-- .fieldset -->
						<fieldset>
							<legend>QUÁ TRÌNH HỌC TẬP <small>(*) Các trường hợp bắt buộc nhập</small></legend>
							<!-- .form-group -->
							<h6>Lớp 10</h6>
							<div class="form-row">
								<!-- grid column -->
								<div class="col-md-3 mb-3">
									<label for="cmbTinhLop10">Tỉnh/TP (*)</label>
									<!-- <select class="custom-select d-block w-100" id="cmbTinhLop10" name="lop10Tinh" required>
										<option value=""> Chọn Tỉnh/TP </option>
									</select> -->
									<input type="text" class="form-control" id="cmbTinhLop10" name="lop10Tinh" required>
									<div class="invalid-feedback"> Vui lòng nhập Tỉnh/TP. </div>
								</div><!-- /grid column -->
								<!-- grid column -->
								<div class="col-md-3 mb-3">
									<label for="cmbHuyenLop10">Huyện (*)</label>
									<!-- <select class="custom-select d-block w-100" id="cmbHuyenLop10" name="lop10Huyen" required>
										<option value=""> Chọn Huyện </option>
									</select> -->
									<input type="text" class="form-control" id="cmbHuyenLop10" name="lop10Huyen" required>
									<div class="invalid-feedback"> Vui lòng nhập Huyện. </div>
								</div><!-- /grid column -->
								<!-- grid column -->
								<div class="col-md-3 mb-3">
									<label for="cmbTruongLop10">Trường THPT (*)</label>
									<!-- <select class="custom-select d-block w-100" id="cmbTruongLop10" name="lop10Truong" required>
										<option value=""> Chọn Trường THPT lớp 10 </option>
									</select> -->
									<input type="text" class="form-control" id="cmbTruongLop10" name="lop10Truong" required>
									<div class="invalid-feedback"> Vui lòng nhập Trường THPT. </div>
								</div>
								<div class="col-md-3 mb-3">
									<label for="fileHocBaLop10">Ảnh học bạ (*)</label>
									<input type="file" class="form-control" id="fileHocBaLop10" name="lop10Hb" accept="image/png, image/jpeg" required>
									<small id="tf1Help"
										class="form-text text-muted">Vui lòng tải lên file có định dạng là JPG, PNG và dung lượng ảnh nhỏ hơn hoặc bằng 5MB</small>
									<div class="invalid-feedback"> Vui lòng tải lên Ảnh học bạ. </div>
								</div><!-- /grid column -->
							</div>
							<h6>Lớp 11</h6>
							<div class="form-row">
								<!-- grid column -->
								<div class="col-md-3 mb-3">
									<label for="cmbTinhLop11">Tỉnh/TP (*)</label>
									<!-- <select class="custom-select d-block w-100" id="cmbTinhLop11" name="lop11Tinh" required>
										<option value=""> Chọn Tỉnh/TP </option>
									</select> -->
									<input type="text" class="form-control" id="cmbTinhLop11" name="lop11Tinh" required>
									<div class="invalid-feedback"> Vui lòng nhập Tỉnh/TP. </div>
								</div><!-- /grid column -->
								<!-- grid column -->
								<div class="col-md-3 mb-3">
									<label for="cmbHuyenLop11">Huyện (*)</label>
									<!-- <select class="custom-select d-block w-100" id="cmbHuyenLop11" name="lop11Huyen" required>
										<option value=""> Chọn Huyện </option>
									</select> -->
									<input type="text" class="form-control" id="cmbHuyenLop11" name="lop11Huyen" required>
									<div class="invalid-feedback"> Vui lòng nhập Huyện. </div>
								</div><!-- /grid column -->
								<!-- grid column -->
								<div class="col-md-3 mb-3">
									<label for="cmbTruongLop11">Trường THPT (*)</label>
									<!-- <select class="custom-select d-block w-100" id="cmbTruongLop11" name="lop11Truong" required>
										<option value=""> Chọn Trường THPT lớp 11 </option>
									</select> -->
									<input type="text" class="form-control" id="cmbTruongLop11" name="lop11Truong" required>
									<div class="invalid-feedback"> Vui lòng nhập Trường THPT. </div>
								</div>
								<div class="col-md-3 mb-3">
									<label for="fileHocBaLop11">Ảnh học bạ (*)</label>
									<input type="file" class="form-control" id="fileHocBaLop11" name="lop11Hb" accept="image/png, image/jpeg" required>
									<small id="tf1Help"
										class="form-text text-muted">Vui lòng tải lên file có định dạng là JPG, PNG và dung lượng ảnh nhỏ hơn hoặc bằng 5MB</small>
									<div class="invalid-feedback"> Vui lòng tải lên Ảnh học bạ. </div>
								</div><!-- /grid column -->
							</div>
							<h6>Lớp 12</h6>
							<div class="form-row">
								<!-- grid column -->
								<div class="col-md-3 mb-3">
									<label for="cmbTinhLop12">Tỉnh/TP (*)</label>
									<!-- <select class="custom-select d-block w-100" id="cmbTinhLop12" name="lop12Tinh" required>
										<option value=""> Chọn Tỉnh/TP </option>
									</select> -->
									<input type="text" class="form-control" id="cmbTinhLop12" name="lop12Tinh" required>
									<div class="invalid-feedback"> Vui lòng nhập Tỉnh/TP. </div>
								</div><!-- /grid column -->
								<!-- grid column -->
								<div class="col-md-3 mb-3">
									<label for="cmbHuyenLop12">Huyện (*)</label>
									<!-- <select class="custom-select d-block w-100" id="cmbHuyenLop12" name="lop12Huyen" required>
										<option value=""> Chọn Huyện </option>
									</select> -->
									<input type="text" class="form-control" id="cmbHuyenLop12" name="lop12Huyen" required>
									<div class="invalid-feedback"> Vui lòng nhập Huyện. </div>
								</div><!-- /grid column -->
								<!-- grid column -->
								<div class="col-md-3 mb-3">
									<label for="cmbTruongLop12">Trường THPT (*)</label>
									<!-- <select class="custom-select d-block w-100" id="cmbTruongLop12" name="lop12Truong" required>
										<option value=""> Chọn Trường THPT lớp 12 </option>
									</select> -->
									<input type="text" class="form-control" id="cmbTruongLop12" name="lop12Truong" required>
									<div class="invalid-feedback"> Vui lòng nhập Trường THPT. </div>
								</div>
								<div class="col-md-3 mb-3">
									<label for="fileHocBaLop12">Ảnh học bạ (*)</label>
									<input type="file" class="form-control" id="fileHocBaLop12" name="lop12Hb" accept="image/png, image/jpeg" required>
									<small id="tf1Help"
										class="form-text text-muted">Vui lòng tải lên file có định dạng là JPG, PNG và dung lượng ảnh nhỏ hơn hoặc bằng 5MB</small>
									<div class="invalid-feedback"> Vui lòng tải lên Ảnh học bạ. </div>
								</div><!-- /grid column -->
							</div>
							<div class="form-row">
								<div class="col-md-6 mb-3">
									<label for="fileChungNhan">Giấy chứng nhận tạm thời</label>
									<input type="file" class="form-control" id="fileChungNhan" name="cntnTamThoi" accept="image/png, image/jpeg">
								</div><!-- /grid column -->
								<div class="col-md-6 mb-3">
									<label for="fileKhac">Giấy tờ ưu tiên khác (nếu có)</label>
									<input type="file" class="form-control" id="fileKhac" name="gtUuTien" accept="image/png, image/jpeg">
								</div><!-- /grid column -->
							</div>
						</fieldset><!-- /.fieldset -->
					</div><!-- /.card-body -->
				</div>
			</div><!-- /.row -->
			<div class="row">
				<div class="card col-12 col-md-12">
					<!-- .card-body -->
					<div class="card-body">
						<!-- .fieldset -->
						<fieldset>
							<div class="d-flex">
								<legend class="col-md-10 mb-3">NGUYỆN VỌNG ĐĂNG KÝ <small>(*) Thí sinh vui lòng chọn ít nhất 1 nguyện vọng</small></legend> <!-- .form-group -->
							</div>
							<div class="row table-responsive">
								<table class="table" id="tblNguyenVong">
									<tbody>
										<tr>
											<td>1. Nguyện vọng 1 (*)</td>
											<td>
												<select class="custom-select d-block w-100" name="nv1" id="cmbNV1" required
													>
													<option value=""> Chọn ngành </option>
												</select>
												<div class="invalid-feedback"> Vui lòng chọn nguyện vọng. </div>
											</td>
										</tr>
										<tr>
											<td>2. Nguyện vọng 2</td>
											<td>
												<select class="custom-select d-block w-100" name="nv2" id="cmbNV2"
													>
													<option value=""> Chọn ngành </option>
												</select>
											</td>
										</tr>
										<tr>
											<td>3. Nguyện vọng 3</td>
											<td>
												<select class="custom-select d-block w-100" name="nv3" id="cmbNV3"
													>
													<option value=""> Chọn ngành </option>
												</select>
											</td>
										</tr>
										<tr>
											<td>4. Nguyện vọng 4 </td>
											<td>
												<select class="custom-select d-block w-100" name="nv4" id="cmbNV4"
													>
													<option value=""> Chọn ngành </option>
												</select>
											</td>
										</tr>

									</tbody>
								</table>
							</div>
							<div class="row d-flex justify-content-center">
								<button type="button" class="btn btn-success" id="btnSave">NỘP HỒ SƠ</button>
							</div>
						</fieldset><!-- /.fieldset -->
					</div><!-- /.card-body -->
					<div class="card-footer">
						<p class="mt-3 mb-3" style="color: red;">(*) Thí sinh lưu ý kiểm tra thật kỹ thông tin (đặc biệt
							là Số điện thoại) trước khi bấm lệnh NỘP HỒ SƠ.</p>
					</div>
				</div>
			</div><!-- /.row -->
		</form>
</section>