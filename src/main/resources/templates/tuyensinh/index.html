<!-- hero -->
<section class="py-5">
	<!-- .container -->
	<div class="container container-fluid-xl">
	  <!-- .row -->
	  <div class="row align-items-center">
		<!-- .col-md-6 -->
		<div class="col-12 col-md-5 order-md-2" data-aos="fade-left">
		  <img class="img-fluid img-float-md-6 mb-5 mb-md-0 shadow-lg bg-white rounded" src="assets/images/welcome.jpg" alt="">
		</div><!-- /.col-md-6 -->
		<!-- .col-md-6 -->
		<div class="col-12 col-md-7 order-md-1" data-aos="fade-in">
		  <div class="col-fix pl-xl-3 ml-auto text-center text-sm-left">
			<h4 class="display-4 enable-responsive-font-size mb-4">  <strong>TUYỂN SINH NĂM 2022</strong></h4>
			<p class="lead text-muted mb-5">  </p><a href="/nop-ho-so" class="btn btn-xl btn-primary d-block d-sm-inline-block mr-sm-2 my-3" data-aos="zoom-in" data-aos-delay="200">Nộp Hồ Sơ <i class="fa fa-angle-right ml-2"></i></a> 
			
			<!-- <a href="/huong-dan" class="btn btn-lg btn-subtle-primary d-block d-sm-inline-block my-3" target="_blank" data-aos="zoom-in" data-aos-delay="300">Hướng Dẫn</a> -->
		  </div>
		</div><!-- /.col-md-6 -->
	  </div><!-- /.row -->
	</div><!-- /.container -->
  </section><!-- /hero -->
   <!-- sections title -->
   <section class="py-5">
	<!-- .container -->
	<div class="container">
	  <!-- .row -->
	  <div class="row">
		<!-- .col -->
		<div class="col-12 col-md-8 offset-md-2 text-center">
		  <h3> TẠI SAO NÊN CHỌN TRƯỜNG CAO ĐẲNG Y TẾ <br> CÀ MAU? </h3>
		</div><!-- /.col -->
	  </div><!-- /.row -->
	</div><!-- /.container -->
  </section><!-- /sections title -->
  <!-- .feature -->
  <section class="py-5">
	<!-- .container -->
	<div class="container">
	  <!-- .row -->
	  <div class="row text-center text-md-left">
		<!-- .col -->
		<div class="col-6 col-md-3 py-4 text-center" data-aos="fade-up" data-aos-delay="0">
		  <img class="mb-4" src="assets/images/illustration/lab.svg" alt="" height="72">
		  <h2 class="lead"> CHƯƠNG TRÌNH ĐÀO TẠO CHUẨN </h2>
		</div><!-- /.col -->
		<!-- .col -->
		<div class="col-6 col-md-3 py-4 text-center" data-aos="fade-up" data-aos-delay="100">
		  <img class="mb-4" src="assets/images/illustration/easy-config.svg" alt="" height="72">
		  <h2 class="lead"> BẰNG ĐỎ CỬ NHÂN </h2>
		</div><!-- /.col -->
		<!-- .col -->
		<div class="col-6 col-md-3 py-4 text-center" data-aos="fade-up" data-aos-delay="200">
		  <img class="mb-4" src="assets/images/illustration/scale.svg" alt="" height="72">
		  <h2 class="lead"> CƠ HỘI VIỆC LÀM </h2>
		</div><!-- /.col -->
		<!-- .col -->
		<div class="col-6 col-md-3 py-4 text-center" data-aos="fade-up" data-aos-delay="300">
		  <img class="mb-4" src="assets/images/illustration/support.svg" alt="" height="72">
		  <h2 class="lead"> KIẾN THỨC THỰC TIỄN </h2>
		</div><!-- /.col -->
	  </div><!-- /.row -->
	</div><!-- /.container -->
  </section><!-- /.feature -->
  <!-- divider -->
  <section class="position-relative">
	<!-- wave2.svg -->
	<svg xmlns="http://www.w3.org/2000/svg" width="100%" height="192" viewbox="0 0 1440 240" preserveaspectratio="none">
	  <path class="fill-light" fill-rule="evenodd" d="M0 240V0c19.985 5.919 41.14 11.008 63.964 14.89 40.293 6.855 82.585 9.106 125.566 9.106 74.151 0 150.382-6.697 222.166-8.012 13.766-.252 27.51-.39 41.21-.39 99.76 0 197.087 7.326 282.907 31.263C827.843 72.527 860.3 117.25 906.926 157.2c43.505 37.277 115.38 51.186 208.485 53.076 7.584.153 15.156.224 22.714.224 40.887 0 81.402-2.062 121.914-4.125 40.512-2.062 81.027-4.125 121.902-4.125 1.01 0 2.019.002 3.03.004 16.208.042 34.959.792 55.029 2.234V240H0z"></path>
	</svg>
  </section><!-- /divider -->
  <!-- feature -->
  <section class="position-relative pb-5 bg-light">
	<!-- .sticker -->
	<div class="sticker">
	  <div class="sticker-item sticker-top-right sticker-soften">
		<img src="assets/images/decoration/cubes.svg" alt="" data-aos="zoom-in">
	  </div>
	  <div class="sticker-item sticker-bottom-left sticker-soften scale-150">
		<img src="assets/images/decoration/cubes.svg" alt="" data-aos="zoom-in">
	  </div>
	</div><!-- /.sticker -->
	<!-- .container -->
	<div class="container position-relative">
	  <h2 class="text-center text-sm-center pb-5"> CÁC NGÀNH ĐÀO TẠO </h2>
	  <div class="card-deck-lg">
		<!-- .card -->
		<div class="card shadow" data-aos="fade-up" data-aos-delay="0">
		  <!-- .card-body -->
		  <div class="card-body p-4">
			<div class="d-sm-flex align-items-start text-center text-sm-left">
			  <img src="assets/images/illustration/svgexport-2.svg" class="mr-sm-4 mb-3 mb-sm-0" alt="" width="72">
			  <div class="flex-fill">
				<h5 class="mt-0"> Cao đẳng Điều dưỡng hệ chính quy </h5>
				<!-- <p class="text-muted font-size-lg"> Provides a tons of useful Components and Pages that allow you to focus on your application logic rather than re-touching pixels. </p> -->
			  </div>
			</div>
		  </div><!-- /.card-body -->
		</div><!-- /.card -->
		<!-- .card -->
		<div class="card shadow" data-aos="fade-up" data-aos-delay="100">
		  <!-- .card-body -->
		  <div class="card-body p-4">
			<div class="d-sm-flex align-items-start text-center text-sm-left">
			  <img src="assets/images/illustration/svgexport-2.svg" class="mr-sm-4 mb-3 mb-sm-0" alt="" width="72">
			  <div class="flex-fill">
				<h5 class="mt-0"> Cao đẳng Dược hệ chính quy </h5>
				<!-- <p class="text-muted font-size-lg"> Looper is not 1001 in 1 shit theme, there's not an obscene amount of wasted space, and we are carefully in adding useful features without going overboard. </p> -->
			  </div>
			</div>
		  </div><!-- /.card-body -->
		</div><!-- /.card -->
	  </div><!-- /.card-deck -->
	  <!-- .card-deck -->
	  <div class="card-deck-lg">
		<!-- .card -->
		<div class="card shadow" data-aos="fade-up" data-aos-delay="200">
		  <!-- .card-body -->
		  <div class="card-body p-4">
			<div class="d-sm-flex align-items-start text-center text-sm-left">
			  <img src="assets/images/illustration/svgexport-2.svg" class="mr-sm-4 mb-3 mb-sm-0" alt="" width="72">
			  <div class="flex-fill">
				<h5 class="mt-0"> Cao đẳng Điều dưỡng liên thông (đã tốt nghiệp Trung cấp Điều dưỡng) </h5>
				<!-- <p class="text-muted font-size-lg"> The design is clean, simple, modern, and essential, with Lots of refined graphics and tons of 3rd party support. </p> -->
			  </div>
			</div>
		  </div><!-- /.card-body -->
		</div><!-- /.card -->
		<!-- .card -->
		<div class="card shadow" data-aos="fade-up" data-aos-delay="300">
		  <!-- .card-body -->
		  <div class="card-body p-4">
			<div class="d-sm-flex align-items-start text-center text-sm-left">
			  <img src="assets/images/illustration/svgexport-2.svg" class="mr-sm-4 mb-3 mb-sm-0" alt="" width="72">
			  <div class="flex-fill">
				<h5 class="mt-0"> Cao đẳng Dược liên thông (đã tốt nghiệp Trung cấp Dược) </h5>
				<!-- <p class="text-muted font-size-lg"> We make sure you can make a magic by running just two command. It was very easy, extremely powerful, and modern. </p> -->
			  </div>
			</div>
		  </div><!-- /.card-body -->
		</div><!-- /.card -->
	  </div><!-- /.card-deck -->
	</div><!-- /.container -->
  </section><!-- /feature -->
  <!-- feature -->
  <section class="position-relative py-5 bg-light">
	<!-- .sticker -->
	<div class="sticker">
	  <div class="sticker-item sticker-top-right sticker-soften translate-x-50">
		<img src="assets/images/decoration/bubble1.svg" alt="" data-aos="fade-left">
	  </div>
	</div><!-- /.sticker -->
	<!-- .container -->
	<div class="container position-relative">
	  <h2 class="text-center"> THÔNG TIN TUYỂN SINH</h2>
	  <div class="row align-items-center">
		<!-- .col -->
		<div class="col-12 col-md-5 offset-md-1 py-md-4 pr-md-0">
		  <div class="card font-size-lg shadow-lg" data-aos="fade-up">
			<h5 class="card-header text-center text-success p-4 px-lg-5"> 1. ĐỐI TƯỢNG TUYỂN SINH </h5>
			<div class="card-body p-4 p-lg-5">
			  <p class="text-center text-muted mb-5"> Học viên từ 18 tuổi trở lên đủ các tiêu chuẩn dưới đây:</p>
			  <ul class="list-icons">
				<li class="mb-2 pl-1">
				  <span class="list-icon"><img class="mr-2" src="assets/images/decoration/check.svg" alt="" width="16"></span> Đã tốt nghiệp THPT, bổ túc THPT </li>
				<li class="mb-2 pl-1">
				  <span class="list-icon"><img class="mr-2" src="assets/images/decoration/check.svg" alt="" width="16"></span> Đã tốt nghiệp Trung cấp, Cao đẳng, Đại học </li>
				  <li class="mb-2 pl-1">
					<span class="list-icon"><img class="mr-2" src="assets/images/decoration/check.svg" alt="" width="16"></span> Không đang trong thời gian truy cứu trách nhiệm hình sự </li>
			  </ul>
			</div>
			<div class="card-footer">
			  <a href="/nop-ho-so" class="card-footer-item p-4 px-lg-5">NỘP HỒ SƠ</a>
			</div>
		  </div>
		</div><!-- /.col -->
		<!-- .col -->
		<div class="col-12 col-md-5 py-md-4 pl-md-0">
		  <div class="card font-size-lg card-inverse bg-primary shadow" data-aos="fade-up" data-aos-delay="200">
			<h5 class="card-header text-center p-4 px-lg-5" style="color: #ffff;"> 2. HÌNH THỨC TUYỂN SINH </h5>
			<div class="card-body p-0">
				<p class="text-center" style="color: #ffff;"> Cao đẳng hệ chính quy:</p>
			  <ul class="list-icons text-white">
				<li class="mb-2 pl-2">
				  <span class="list-icon"><img class="ml-2" src="assets/images/decoration/check.svg" alt="" width="16"></span> <p style="color: #ffff;">Xét tuyển học bạn năm lớp 12: Môn Toán, Hóa, Sinh</p></li>
			  </ul>
			</div>
			<div class="card-body p-0">
				<p class="text-center" style="color: #ffff;"> Cao đẳng hệ VL-VH:</p>
			  <ul class="list-icons">
				<li class="mb-2 pl-2 ">
				  <span class="list-icon text-white"><img class="ml-2" src="assets/images/decoration/check.svg" alt="" width="16"></span> <p style="color: #ffff;">Xét tuyển</p></li>
				<li class="mb-2 pl-2">
				  <span class="list-icon" ><img class="ml-2" src="assets/images/decoration/check.svg" alt="" width="16"></span><p style="color: #ffff;">Các môn xét tuyển:</p>  </li>
					<p style="color: #ffff;">+ Trung bình điểm thi tốt nghiệp phần chuyên môn (lý thuyết và thực hành)</p> 
					<p style="color: #ffff;"> + Xét tuyển học bạn năm lớp 12: Môn Toán, Hóa, Sinh</p>
			  </ul>
			</div>
			<div class="card-footer">
			  <a href="/nop-ho-so" class="card-footer-item text-white p-4 px-lg-5"><p style="color: #ffff;">NỘP HỒ SƠ</p></a>
			</div>
		  </div>
		</div><!-- /.col -->
	  </div><!-- /.row -->
	</div><!-- /.container -->
  </section><!-- /feature -->