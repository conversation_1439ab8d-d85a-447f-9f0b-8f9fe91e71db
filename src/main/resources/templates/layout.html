<!DOCTYPE html>
<html xmlns:th="http://www.w3.org/1999/xhtml">
<head>
  <base href="/">
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <title th:text="${MODEL.title}+' | Hệ Thống QLĐT Trường Cao Đẳng Y Tế Cà Mau'"></title>
  <!-- Tell the browser to be responsive to screen width -->
  <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
  <!-- shortcut icon -->
  <link rel="shortcut icon" href="/dist/img/fav.ico">
  <!-- Bootstrap 3.3.7 -->
  <link rel="stylesheet" href="/bower_components/bootstrap/dist/css/bootstrap.min.css">
  <!-- Font Awesome -->
  <link rel="stylesheet" href="/bower_components/font-awesome/css/font-awesome.min.css">
  <!-- Select2 -->
  <!--<link rel="stylesheet" href="/bower_components/select2/css/select2.min.css">-->
  <!-- Controller css -->
  <th:block th:each="href:${MODEL.css}">
    <link rel="stylesheet" th:href="${href}"/>
  </th:block>
  <!-- Theme style -->
  <link rel="stylesheet" href="/dist/css/AdminLTE.min.css">
  <!-- AdminLTE Skins. We have chosen the skin-blue for this starter
        page. However, you can choose any other skin. Make sure you
        apply the skin class to the body tag so the changes take effect. -->
  <link rel="stylesheet" href="/dist/css/skins/skin-blue.min.css">
  <!-- Style -->
  <link rel="stylesheet" href="/css/style.css">
  <!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
  <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
  <!--[if lt IE 9]>
  <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
  <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
  <![endif]-->

  <!-- Google Font -->
  <!--<link rel="stylesheet"
        href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,600,700,300italic,400italic,600italic">-->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Be+Vietnam+Pro:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2.0.0"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
</head>
<!--
BODY TAG OPTIONS:
=================
Apply one or more of the following classes to get the
desired effect
|---------------------------------------------------------|
| SKINS         | skin-blue                               |
|               | skin-black                              |
|               | skin-purple                             |
|               | skin-yellow                             |
|               | skin-red                                |
|               | skin-green                              |
|---------------------------------------------------------|
|LAYOUT OPTIONS | fixed                                   |
|               | layout-boxed                            |
|               | layout-top-nav                          |
|               | sidebar-collapse                        |
|               | sidebar-mini                            |
|---------------------------------------------------------|
-->
<body class="hold-transition skin-blue sidebar-mini" th:with="curUrl=${#httpServletRequest.requestURI}">
<div class="wrapper">

  <!-- Main Header -->
  <header class="main-header">

    <!-- Logo -->
    <a href="." class="logo">
      <!-- mini logo for sidebar mini 50x50 pixels -->
      <span class="logo-mini"><img src="/dist/img/vnpt-logo-white.png" title="Quản lý đào tạo" alt="VNPT Logo" style="width:30px"></span>
      <!-- logo for regular state and mobile devices -->
      <span class="logo-lg"><img src="/dist/img/vnpt-logo-white.png" title="Quản lý đào tạoz" alt="VNPT Logo" style="width:30px"> <b>QLĐT</b></span>
    </a>

    <!-- Header Navbar -->
    <nav class="navbar navbar-static-top" role="navigation">
      <!-- Sidebar toggle button-->
      <a href="#" class="sidebar-toggle" data-toggle="push-menu" role="button">
        <span class="sr-only">Toggle navigation</span>
      </a>
<!--      <div id="notification-banner" class="notification-banner">-->
<!--        <i class="fa fa-bell"></i> &lt;!&ndash; Notification Icon &ndash;&gt;-->
<!--        <span>Thông báo:</span>-->
<!--        <marquee behavior="scroll" direction="left" scrollamount="5">-->

<!--          &lt;!&ndash; The content of the notification will be injected here by JavaScript &ndash;&gt;-->
<!--        </marquee>-->
<!--      </div>-->

      <!-- Navbar Right Menu -->
      <div class="navbar-custom-menu">
        <ul class="nav navbar-nav">
          <li class="">
            <a href="thiet-lap" style="text-transform: uppercase">
              <i class="fa fa-graduation-cap"></i> Hệ: <span id="spn-hdt" th:text="${#session.getAttribute('HEDAOTAO').tenHeDaoTao}" th:ihdt="${#session.getAttribute('HEDAOTAO').idHeDaoTao}"></span>
              <!--<i class="fa fa-caret-right"></i> NH: <span id="spn-nh" th:text="${#session.getAttribute('HOCKY').namHoc.tenNamHoc}" th:inh="${#session.getAttribute('HOCKY').namHoc.idNamHoc}"></span>
              <i class="fa fa-caret-right"></i> HK: <span id="spn-hk" th:text="${#session.getAttribute('HOCKY').tenHocKy}" th:ihk="${#session.getAttribute('HOCKY').idHocKy}"></span>-->
            </a>
          </li>
          <!-- User Account Menu -->
          <li class="dropdown user user-menu">
            <!-- Menu Toggle Button -->
            <a href="#" class="dropdown-toggle" data-toggle="dropdown">
              <!-- The user image in the navbar-->
              <img th:src="${MODEL.nguoiDung.nu?'/dist/img/user-female-160.jpg':'/dist/img/user-male-160.jpg'}" class="user-image" alt="User Image">
              <!-- hidden-xs hides the username on small devices so only the image appears. -->
              <span class="hidden-xs" th:text="${MODEL.nguoiDung.hoTen}"></span>
            </a>
            <ul class="dropdown-menu">
              <!-- The user image in the menu -->
              <li class="user-header">
                <img th:src="${MODEL.nguoiDung.nu?'/dist/img/user-female-160.jpg':'/dist/img/user-male-160.jpg'}" class="img-circle" alt="User Image">
                <p>
                  <span th:text="${MODEL.nguoiDung.hoTen}"></span>
                  <small th:text="'['+${MODEL.nguoiDung.vaiTro.tenVaiTro}+']'"></small>
                </p>

              </li>
              <!-- Menu Footer-->
              <li class="user-footer">
                <div class="pull-left">
                  <a href="thong-tin-ca-nhan" class="btn btn-default btn-flat">Tài khoản</a>
                </div>
                <div class="pull-right">
                  <a href="/logout" class="btn btn-default btn-flat">Đăng xuất</a>
                </div>
              </li>
            </ul>
          </li>
        </ul>
      </div>
    </nav>
  </header>
  <!-- Left side column. contains the logo and sidebar -->
  <aside class="main-sidebar">

    <!-- sidebar: style can be found in sidebar.less -->
    <section class="sidebar">

      <!-- Sidebar Menu -->
      <ul class="sidebar-menu tree" data-widget="tree">
        <li class="header">CHỨC NĂNG</li>
        <!-- Optionally, you can add icons to the links -->
        <!--<li class="active"><a href="#"><i class="fa fa-link"></i> <span>Link</span></a></li>
        <li><a href="#"><i class="fa fa-link"></i> <span>Another Link</span></a></li>-->
        <th:block th:each="menu:${MODEL.nguoiDung.vaiTro.menus}">
          <li th:if="${menu.hienThi and menu.menu==null and menu.url!=null}" th:classappend="${curUrl=='/'+menu.url} ? active">
            <a th:href="${menu.url}"><i th:class="${menu.icon}"></i> <span th:text="${menu.tenMenu}"></span></a>
          </li>
          <li th:if="${menu.hienThi and menu.menu==null and menu.url==null}" class="treeview" th:classappend="${menu.isContainUrl(MODEL.nguoiDung.vaiTro.menus,menu.idMenu,curUrl)} ? 'active menu-open'">
            <a href="#"><i th:class="${menu.icon}"></i> <span th:text="${menu.tenMenu}"></span>
              <span class="pull-right-container">
                <i class="fa fa-angle-left pull-right"></i>
              </span>
            </a>
            <ul class="treeview-menu">
              <th:block th:each="m:${MODEL.nguoiDung.vaiTro.menus}">
                <li th:if="${m.menu==menu and m.hienThi}" th:classappend="${curUrl=='/'+m.url} ? active"><a th:href="${m.url}"><i th:class="${m.icon}"></i> <span th:text="${m.tenMenu}"></span></a></li>
              </th:block>
            </ul>
          </li>
        </th:block>
      </ul>
      <!-- /.sidebar-menu -->
    </section>
    <!-- /.sidebar -->
  </aside>

  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1 th:text="${MODEL.title}" ng-bind="title"></h1>
    </section>

    <!-- Main content -->
    <section class="content container-fluid">

      <div th:replace="${MODEL.content}"></div>

    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->

  <!-- Main Footer -->
  <footer class="main-footer">
    <!-- To the right -->
    <div class="pull-right hidden-xs">
      Phiên bản <span th:text="${@environment.getProperty('app.version')}"></span>
    </div>
    <!-- Default to the left -->
    <span><span th:text="'&copy; '+${#dates.year(#dates.createNow())}"></span> <a href="http://camau.vnpt.vn" target="_blank">VNPT Cà Mau</a></span>
  </footer>

  <!-- Control Sidebar -->
  <!--<aside class="control-sidebar control-sidebar-dark">
    &lt;!&ndash; Create the tabs &ndash;&gt;
    <ul class="nav nav-tabs nav-justified control-sidebar-tabs">
      <li class="active"><a href="#control-sidebar-home-tab" data-toggle="tab"><i class="fa fa-home"></i></a></li>
      <li><a href="#control-sidebar-settings-tab" data-toggle="tab"><i class="fa fa-gears"></i></a></li>
    </ul>
    &lt;!&ndash; Tab panes &ndash;&gt;
    <div class="tab-content">
      &lt;!&ndash; Home tab content &ndash;&gt;
      <div class="tab-pane active" id="control-sidebar-home-tab">
        <h3 class="control-sidebar-heading">Recent Activity</h3>
        <ul class="control-sidebar-menu">
          <li>
            <a href="javascript:;">
              <i class="menu-icon fa fa-birthday-cake bg-red"></i>

              <div class="menu-info">
                <h4 class="control-sidebar-subheading">Langdon's Birthday</h4>

                <p>Will be 23 on April 24th</p>
              </div>
            </a>
          </li>
        </ul>
        &lt;!&ndash; /.control-sidebar-menu &ndash;&gt;

        <h3 class="control-sidebar-heading">Tasks Progress</h3>
        <ul class="control-sidebar-menu">
          <li>
            <a href="javascript:;">
              <h4 class="control-sidebar-subheading">
                Custom Template Design
                <span class="pull-right-container">
                    <span class="label label-danger pull-right">70%</span>
                  </span>
              </h4>

              <div class="progress progress-xxs">
                <div class="progress-bar progress-bar-danger" style="width: 70%"></div>
              </div>
            </a>
          </li>
        </ul>
        &lt;!&ndash; /.control-sidebar-menu &ndash;&gt;

      </div>
      &lt;!&ndash; /.tab-pane &ndash;&gt;
      &lt;!&ndash; Stats tab content &ndash;&gt;
      <div class="tab-pane" id="control-sidebar-stats-tab">Stats Tab Content</div>
      &lt;!&ndash; /.tab-pane &ndash;&gt;
      &lt;!&ndash; Settings tab content &ndash;&gt;
      <div class="tab-pane" id="control-sidebar-settings-tab">
        <form method="post">
          <h3 class="control-sidebar-heading">General Settings</h3>

          <div class="form-group">
            <label class="control-sidebar-subheading">
              Report panel usage
              <input type="checkbox" class="pull-right" checked>
            </label>

            <p>
              Some information about this general settings option
            </p>
          </div>
          &lt;!&ndash; /.form-group &ndash;&gt;
        </form>
      </div>
      &lt;!&ndash; /.tab-pane &ndash;&gt;
    </div>
  </aside>-->
  <!-- /.control-sidebar -->
  <!-- Add the sidebar's background. This div must be placed
  immediately after the control sidebar -->
  <!--<div class="control-sidebar-bg"></div>-->
</div>
<!-- ./wrapper -->

<!-- REQUIRED JS SCRIPTS -->

<!-- jQuery 3 -->
<script src="/bower_components/jquery/dist/jquery.min.js"></script>
<!-- Bootstrap 3.3.7 -->
<script src="/bower_components/bootstrap/dist/js/bootstrap.min.js"></script>
<!-- Js Cookie -->
<script src="/bower_components/js-cookie/js.cookie.min.js"></script>
<!-- Select2 -->
<!--<script src="/bower_components/select2/js/select2.min.js"></script>-->
<!-- AdminLTE App -->
<script src="/dist/js/adminlte.min.js"></script>
<!-- Script -->
<script src="/js/script.js"></script>
<!-- Controller js -->
<th:block th:each="src:${MODEL.js}">
  <script th:src="${src}" type="text/javascript"></script>
</th:block>
<script src="/bower_components/js-device-detector/jquery.device.detector.min.js"></script>
<!-- Optionally, you can add Slimscroll and FastClick plugins.
     Both of these plugins are recommended to enhance the
     user experience. -->
    <div th:replace="sub/viewReport.html"></div>
<script type="text/javascript">
  $(function() {
    // Kiểm tra đăng nhập thành công
    if (window.location.pathname === '/') {
      localStorage.removeItem('loginAttempts'); // Reset số lần đăng nhập thất bại
    }

    var loginAttempts = localStorage.getItem('loginAttempts') || 0;
    if (loginAttempts >= 5) {
      $('#recaptcha-container').show();
    }

    $('form').submit(function(e) {
      if (loginAttempts >= 5) {
        var captchaResponse = grecaptcha.getResponse();
        if (!captchaResponse) {
          e.preventDefault();
          $('#captcha-error').show();
          return false;
        }
      }
    });

    if (window.location.search.includes('error')) {
      loginAttempts++;
      localStorage.setItem('loginAttempts', loginAttempts);
      if (loginAttempts >= 5) {
        $('#recaptcha-container').show();
      }
    }
  });
</script>
</body>
</html>