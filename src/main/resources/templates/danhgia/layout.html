<!DOCTYPE html>
<html lang="en">
<html xmlns:th="http://www.w3.org/1999/xhtml">
  <head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"><!-- End Required meta tags -->
    <!-- Begin SEO tag -->
	<title th:text="${MODEL.title}+' | Trường Cao Đẳng Y Tế Cà Mau'"></title>
    <meta property="og:title" content="Trường Cao Đẳng Y Tế Cà Mau">
    <meta name="author" content="VNPT Cà Mau">
    <meta property="og:locale" content="vi_VN">
    <meta name="description" content="Tuyển sinh Trường Cao Đẳng Y Tế Cà Mau">
    <meta property="og:description" content="Tuyển sinh Trường Cao Đẳng Y Tế Cà Mau">
    <link rel="canonical" href="http://qldt.vnptcamau.vn/">
    <meta property="og:url" content="http://qldt.vnptcamau.vn/">
    <meta property="og:site_name" content="Trường Cao Đẳng Y Tế Cà Mau">
    <!-- FAVICONS -->
    <link rel="apple-touch-icon" sizes="144x144" href="/dist/img/fav.ico">
    <link rel="shortcut icon" href="/dist/img/fav.ico">
    <meta name="theme-color" content="#3063A0"><!-- End FAVICONS -->
    <!-- GOOGLE FONT -->
    <link href="https://fonts.googleapis.com/css?family=Fira+Sans:400,500,600" rel="stylesheet"><!-- End GOOGLE FONT -->
    <!-- BEGIN PLUGINS STYLES -->
    <link rel="stylesheet" href="assets/vendor/aos/aos.css">
	<link rel="stylesheet" href="assets/vendor/@fortawesome/fontawesome-free/css/all.min.css">
	<!-- END PLUGINS STYLES -->
    <!-- BEGIN THEME STYLES -->
    <link rel="stylesheet" href="assets/stylesheets/theme.min.css" data-skin="default">
    <link rel="stylesheet" href="assets/stylesheets/theme-dark.min.css" data-skin="dark">
    <link rel="stylesheet" href="assets/stylesheets/custom.css">
    <script>
      var skin = localStorage.getItem('skin') || 'default';
      var isCompact = JSON.parse(localStorage.getItem('hasCompactMenu'));
      var disabledSkinStylesheet = document.querySelector('link[data-skin]:not([data-skin="' + skin + '"])');
      // Disable unused skin immediately
      disabledSkinStylesheet.setAttribute('rel', '');
      disabledSkinStylesheet.setAttribute('disabled', true);
      // add flag class to html immediately
      if (isCompact == true) document.querySelector('html').classList.add('preparing-compact-menu');
    </script><!-- END THEME STYLES -->
  </head>
  <body>
    <!-- .app -->
    <main class="app app-site">
      <!--[if lt IE 10]>
      <div class="page-message" role="alert">You are using an <strong>outdated</strong> browser. Please <a class="alert-link" href="http://browsehappy.com/">upgrade your browser</a> to improve your experience and security.</div>
      <![endif]-->
      <!-- site header -->
      <!-- .navbar -->
      <nav class="navbar navbar-expand-lg navbar-light py-4">
        <!-- .container -->
        <div class="container">
          <!-- .hamburger -->
          <!-- <button class="hamburger hamburger-squeeze hamburger-light d-flex d-lg-none" type="button" data-toggle="collapse" data-target="#navbarTogglerDemo01" aria-controls="navbarTogglerDemo01" aria-expanded="false" aria-label="Toggle navigation"><span class="hamburger-box"><span class="hamburger-inner"></span></span></button> /.hamburger -->
          <!-- .navbar-brand -->
          <a class="navbar-brand ml-0 mr-auto mx-auto " href="/danh-gia-khao-sat" style="color: #346cb0;">
			<h4 class="text-wrap text-center p-0"><img src="/assets/images/logo.png" style="height: 70px;"> ĐÁNH GIÁ KHẢO SÁT TRỰC TUYẾN</h4>
		  </a> <!-- /.navbar-brand -->
		  <!-- <a class="navbar-btn btn btn-subtle-success ml-auto order-lg-2" href="/nop-ho-so">NỘP HỒ SƠ</a> .navbar-collapse  -->
		  <!-- <a class="navbar-btn btn btn-subtle-primary ml-1 order-lg-2" href="#" target="_blank">ĐĂNG NHẬP / ĐĂNG KÝ</a>  -->
		  <!-- .navbar-collapse -->
		  
          <div class="navbar-collapse collapse" id="navbarTogglerDemo01">
            <ul class="navbar-nav mx-auto">
              <!-- <li class="nav-item mr-lg-3 active">
                <a class="nav-link py-2" href="/tuyen-sinh">TRANG CHỦ</a>
              </li>
              <li class="nav-item mr-lg-3">
                <a class="nav-link py-2" href="/tra-cuu-kq">TRA CỨU KẾT QUẢ</a>
              </li>
              <li class="nav-item mr-lg-3">
                <a class="nav-link py-2" href="#">HƯỚNG DẪN</a>
              </li> -->
            </ul>
          </div><!-- /.navbar-collapse -->
        </div><!-- /.container -->
      </nav><!-- /.navbar -->
      <!-- /site header -->
      
	  <div th:replace="${MODEL.content}"></div>
	
      <!-- footer -->
      <section class="py-5 bg-black text-white">
        <!-- .container -->
        <div class="container text-center">
			<div class="row">
				<!-- .col -->
				<div class="col-12 col-md-3 col-lg-3"></div>
				<div class="col-12 col-md-6 col-lg-6">
				  <p class="mb-2">TRƯỜNG CAO ĐẲNG Y TẾ CÀ MAU</p>
				  <p class="mb-2">  Địa chỉ: 146 Nguyễn Trung Trực - phường 8 - Tp.Cà Mau - tỉnh Cà Mau</p>
				  <p class="mb-2">  Email: <EMAIL></p> 
				  <address>
				   Điện thoại: 02903 828575, 02903 828304 - Fax: 02903 581476 </address><!-- Social -->
				  
				</div><!-- /.col -->
				<div class="col-12 col-md-3 col-lg-3"></div>
			  </div>
          <p class="mt-4"> ©2022 Trường Cao Đẳng Y Tế Cà Mau. All rights reserved. </p>
        </div><!-- /.container -->
      </section><!-- /footer -->
    </main><!-- /.app -->
    <!-- BEGIN BASE JS -->
    <script src="assets/vendor/jquery/jquery.min.js"></script>
    <script src="assets/vendor/popper.js/umd/popper.min.js"></script>
    <script src="assets/vendor/bootstrap/js/bootstrap.min.js"></script> <!-- END BASE JS -->
    <!-- BEGIN PLUGINS JS -->
     <script src="assets/vendor/aos/aos.js"></script>
	 <script src="assets/vendor/jquery-nested-form/jquery-nested-form.js"></script>
	 <script src="assets/vendor/stacked-menu/js/stacked-menu.min.js"></script>
	 <script src="assets/vendor/parsleyjs/parsley.min.js"></script>
	 <script src="assets/vendor/vanilla-text-mask/vanillaTextMask.js"></script>
	 <script src="assets/vendor/text-mask-addons/textMaskAddons.js"></script>
	 <script src="assets/vendor/sweet-alert2/sweetalert2.js"></script> 
	 <script src="assets/js/theme.min.js"></script> <!-- END THEME JS -->
	<!-- END PLUGINS JS -->
	<!-- Controller js -->
	<th:block th:each="src:${MODEL.js}">
		<script th:src="${src}" type="text/javascript"></script>
	</th:block>
</body>
</html>