<!-- hero -->
<section class="py-5">
	<!-- .container -->
	<div class="container container-fluid-xl">
	  <!-- .row -->
	  <div class="row align-items-center">
		<!-- .col-md-6 -->
		<div class="col-12 col-md-5 order-md-2" data-aos="fade-left">
		  <img class="img-fluid img-float-md-6 mb-5 mb-md-0" src="assets/images/welcome-2.jpg" alt="">
		</div><!-- /.col-md-6 -->
		<!-- .col-md-6 -->
		<div class="col-12 col-md-7 order-md-1" data-aos="fade-in">
		  <div class="col-fix pl-xl-3 ml-auto text-center text-sm-left">
			<h4 class="display-5 enable-responsive-font-size mb-4"> Chào mừng đến với Hệ thống Đánh giá và <PERSON>o sát trực tuyến của Trường Cao Đẳng Y Tế Cà Mau.
				</strong></h4>
			<p class="lead text-muted mb-5"> M<PERSON>i ý kiến đóng góp về chức năng của phần mềm, xin gởi mail về địa chỉ <EMAIL>. </p>
			
		  </div>
		</div><!-- /.col-md-6 -->
	  </div><!-- /.row -->
	</div><!-- /.container -->
  </section><!-- /hero -->
  <!-- divider -->
  <section class="position-relative">
	<!-- wave2.svg -->
	<svg xmlns="http://www.w3.org/2000/svg" width="100%" height="192" viewbox="0 0 1440 240" preserveaspectratio="none">
	  <path class="fill-light" fill-rule="evenodd" d="M0 240V0c19.985 5.919 41.14 11.008 63.964 14.89 40.293 6.855 82.585 9.106 125.566 9.106 74.151 0 150.382-6.697 222.166-8.012 13.766-.252 27.51-.39 41.21-.39 99.76 0 197.087 7.326 282.907 31.263C827.843 72.527 860.3 117.25 906.926 157.2c43.505 37.277 115.38 51.186 208.485 53.076 7.584.153 15.156.224 22.714.224 40.887 0 81.402-2.062 121.914-4.125 40.512-2.062 81.027-4.125 121.902-4.125 1.01 0 2.019.002 3.03.004 16.208.042 34.959.792 55.029 2.234V240H0z"></path>
	</svg>
  </section><!-- /divider -->
  <!-- feature -->
  <section class="position-relative pb-5 bg-light">
	<!-- .sticker -->
	<div class="sticker">
	  <div class="sticker-item sticker-top-right sticker-soften">
		<img src="assets/images/decoration/cubes.svg" alt="" data-aos="zoom-in">
	  </div>
	  <div class="sticker-item sticker-bottom-left sticker-soften scale-150">
		<img src="assets/images/decoration/cubes.svg" alt="" data-aos="zoom-in">
	  </div>
	</div><!-- /.sticker -->
	<!-- .container -->
	<div class="container position-relative">
	  <h2 class="text-center text-sm-center pb-5"> DANH SÁCH ĐÁNH GIÁ TRỰC TUYẾN </h2>
	  <div class="card-deck-lg">
		<!-- .card -->
		<div class="card shadow" data-aos="fade-up" data-aos-delay="0">
		  <!-- .card-body -->
		  <div class="card-body p-4">
			<div class="list-group list-group-flush list-group-divider" id="dsDotDanhGia">

			</div>
		  </div><!-- /.card-body -->
		</div><!-- /.card -->
		
	  </div><!-- /.card-deck -->
	  <!-- .card-deck -->
	  
	</div><!-- /.container -->
	<!-- .container -->
	<div class="container position-relative">
		<h2 class="text-center text-sm-center pt-5 pb-5"> DANH SÁCH KHẢO SÁT TRỰC TUYẾN </h2>
		<div class="card-deck-lg">
		  <!-- .card -->
		  <div class="card shadow" data-aos="fade-up" data-aos-delay="0">
			<!-- .card-body -->
			<div class="card-body p-4">
			  <div class="list-group list-group-flush list-group-divider" id="dsDotKhaoSat">
  
			  </div>
			</div><!-- /.card-body -->
		  </div><!-- /.card -->
		  
		</div><!-- /.card-deck -->
		<!-- .card-deck -->
		
	  </div><!-- /.container -->
  </section><!-- /feature -->

  <div class="modal fade bd-example-modal-lg" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" aria-hidden="true" id="addDanhGia">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header">
			  <h5 class="modal-title" id="exampleModalLongTitle">Phiếu trả lời đánh giá trực tuyến</h5>
			  <button type="button" class="close" data-dismiss="modal" aria-label="Close">
				<span aria-hidden="true">&times;</span>
			  </button>
			</div>
			<div class="modal-body">
				<form id="frm-addDg" class="needs-validation" novalidate="">
						<!-- .form-row -->
						<div class="form-row">
						  <!-- form grid -->
						  <div class="col-md-6 mb-3">
							<label for="txtHoTenDg">Họ tên <abbr title="Required">*</abbr></label> <input type="text" class="form-control" id="txtHoTenDg" required>
							<div id="valid-tooltip" class="invalid-feedback"> Vui lòng nhập Họ tên </div>
						  </div><!-- /form grid -->
						  <!-- form grid -->
						  <div class="col-md-6 mb-3">
							<label for="txtSdtDg">Số điện thoại <abbr title="Required">*</abbr></label> <input type="text" class="form-control" id="txtSdtDg"  required>
							<div id="valid-tooltip" class="invalid-feedback"> Vui lòng nhập Số điện thoại! </div>
						  </div><!-- /form grid -->
						  <!-- form grid -->
						  <div class="col-md-12 mb-3">
							<label for="txtEmailDg">Email <abbr title="Required">*</abbr></label> <input type="email" class="form-control" id="txtEmailDg" required>
							<div id="valid-tooltip" class="invalid-feedback"> Vui lòng nhập Email. </div>
						  </div><!-- /form grid -->
						  <div class="col-md-12 mb-3">
							<label for="txtDiaChiDg">Địa chỉ <abbr title="Required">*</abbr></label> <input type="text" class="form-control" id="txtDiaChiDg" required>
							<div id="txtDiaChiDg" class="invalid-feedback"> Vui lòng nhập Địa chỉ. </div>
						  </div><!-- /form grid -->
						</div><!-- /.form-row -->
						
                    <div class="row" style="padding-top: 5px;">
                        <div class="col-md-12">
                            <label style="color:blue;font-weight:bold;">Danh sách các câu hỏi thăm dò ý kiến:</label>
							</br>
							<p style="color:red;font-weight:lighter;">(Mức độ : 1=Rất không hài lòng; 2=Không hài lòng; 3=Không ý kiến; 4=Hài lòng; 5=Rất hài lòng)
							</p>
							<div class="table-responsive">
								<!-- <div class="jdgrid" id="grid-ds-cauhoi"></div> -->
								<table class="table table-bordered table-striped table-hover tbl-primary" id="dsCauHoiDg">
									<thead>
									   <tr>
									    <th class="text-center" width="2%">TT</th>
									    <th class="text-left" width="auto">Câu hỏi</th>
										<th class="text-center" width="5%">1</th>
										<th class="text-center" width="5%">2</th>
										<th class="text-center" width="5%">3</th>
										<th class="text-center" width="5%">4</th>
										<th class="text-center" width="5%">5</th>
									  </tr>
									</thead>
									<tbody>
									</tbody>
								  </table>
							</div>
                            <input type="hidden" id="txt-idDotDg">
							<input type="hidden" id="txt-soLuongCauHoiDg">
                        </div> 
                    </div>
                </form>
			</div>
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary" data-dismiss="modal">Đóng</button>
			  <button type="button" class="btn btn-primary" id="btnGuiDg">Gửi đánh giá</button>
			</div>
		  </div>
	</div>
  </div>

  <div class="modal fade bd-example-modal-lg" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" aria-hidden="true" id="addKhaoSat">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header">
			  <h5 class="modal-title" id="exampleModalLongTitle">Phiếu trả lời khảo sát trực tuyến</h5>
			  <button type="button" class="close" data-dismiss="modal" aria-label="Close">
				<span aria-hidden="true">&times;</span>
			  </button>
			</div>
			<div class="modal-body">
				<form id="frm-addKs" class="needs-validation" novalidate="">
						<!-- .form-row -->
						<div class="form-row">
						  <!-- form grid -->
						  <div class="col-md-6 mb-3">
							<label for="txtHoTenKs">Họ tên <abbr title="Required">*</abbr></label> <input type="text" class="form-control" id="txtHoTenKs" required>
							<div id="valid-tooltip" class="invalid-feedback"> Vui lòng nhập Họ tên </div>
						  </div><!-- /form grid -->
						  <!-- form grid -->
						  <div class="col-md-6 mb-3">
							<label for="txtSdtKs">Số điện thoại <abbr title="Required">*</abbr></label> <input type="text" class="form-control" id="txtSdtKs"  required>
							<div id="valid-tooltip" class="invalid-feedback"> Vui lòng nhập Số điện thoại! </div>
						  </div><!-- /form grid -->
						  <!-- form grid -->
						  <div class="col-md-12 mb-3">
							<label for="txtEmailKs">Email <abbr title="Required">*</abbr></label> <input type="email" class="form-control" id="txtEmailKs" required>
							<div id="valid-tooltip" class="invalid-feedback"> Vui lòng nhập Email. </div>
						  </div><!-- /form grid -->
						  <div class="col-md-12 mb-3">
							<label for="txtDiaChiKs">Địa chỉ <abbr title="Required">*</abbr></label> <input type="text" class="form-control" id="txtDiaChiKs" required>
							<div id="valid-tooltip" class="invalid-feedback"> Vui lòng nhập Địa chỉ. </div>
						  </div><!-- /form grid -->
						</div><!-- /.form-row -->
						
                    <div class="row" style="padding-top: 5px;">
                        <div class="col-md-12">
                            <label style="color:blue;font-weight:bold;">Danh sách các câu hỏi thăm dò ý kiến:</label>
							</br>
							<div class="table-responsive">
								<!-- <div class="jdgrid" id="grid-ds-cauhoi"></div> -->
								<table class="table table-bordered table-striped table-hover tbl-primary" id="dsCauHoiKs">
									<thead>
									   <tr>
									    <th class="text-center" width="2%">TT</th>
									    <th class="text-left" width="40%">Câu hỏi</th>
										<th class="text-left" width="58%">Câu trả lời</th>
									  </tr>
									</thead>
									<tbody>
									</tbody>
								  </table>
							</div>
                            <input type="hidden" id="txt-idDotKs">
							<input type="hidden" id="txt-soLuongCauHoiKs">
                        </div> 
                    </div>
                </form>
			</div>
			<div class="modal-footer">
			  <button type="button" class="btn btn-secondary" data-dismiss="modal">Đóng</button>
			  <button type="button" class="btn btn-primary" id="btnGuiKs">Gửi khảo sát</button>
			</div>
		  </div>
	</div>
  </div>
  