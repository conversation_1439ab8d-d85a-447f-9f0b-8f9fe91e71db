<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Thiết đặt Tuần Dạ<PERSON></title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; background: #f9f9f9; }
        .result { margin: 10px 0; padding: 10px; background: #fff; border: 1px solid #ccc; }
        button { margin: 5px; padding: 8px 15px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        pre { background: #f8f9fa; padding: 10px; border: 1px solid #e9ecef; overflow-x: auto; }
        .step { margin: 10px 0; padding: 10px; border-left: 4px solid #007bff; background: #f8f9fa; }
    </style>
</head>
<body>
    <h1>🔧 Debug Thiết đặt Tuần Dạy</h1>
    
    <div class="debug-section">
        <h3>📋 Checklist Debug</h3>
        <div class="step">
            <strong>Bước 1:</strong> Kiểm tra database schema
            <button onclick="checkDatabaseInfo()">Kiểm tra Database</button>
            <div id="db-info" class="result" style="display: none;"></div>
        </div>
        
        <div class="step">
            <strong>Bước 2:</strong> Test API lấy niên khóa
            <button onclick="testNienKhoa()">Test Niên Khóa</button>
            <div id="nien-khoa-result" class="result" style="display: none;"></div>
        </div>
        
        <div class="step">
            <strong>Bước 3:</strong> Test API lấy học kỳ
            <input type="number" id="test-id-nien-khoa" placeholder="ID Niên Khóa" value="1">
            <button onclick="testHocKy()">Test Học Kỳ</button>
            <div id="hoc-ky-result" class="result" style="display: none;"></div>
        </div>
        
        <div class="step">
            <strong>Bước 4:</strong> Test API thiết đặt tuần dạy
            <input type="number" id="test-id-hoc-ky" placeholder="ID Học Kỳ" value="1">
            <input type="date" id="test-ngay-bat-dau" value="2024-09-02">
            <button onclick="testThietDat()">Test Thiết Đặt</button>
            <div id="thiet-dat-result" class="result" style="display: none;"></div>
        </div>
        
        <div class="step">
            <strong>Bước 5:</strong> Test giao diện components
            <button onclick="testComponents()">Test Components</button>
            <div id="components-result" class="result" style="display: none;"></div>
        </div>
    </div>

    <div class="debug-section">
        <h3>🔍 Console Logs</h3>
        <div id="console-logs" class="result">
            <pre id="log-content">Logs sẽ hiển thị ở đây...</pre>
        </div>
        <button onclick="clearLogs()">Clear Logs</button>
    </div>

    <script>
        // Override console.log to capture logs
        var originalLog = console.log;
        var originalError = console.error;
        var logs = [];
        
        console.log = function() {
            logs.push('[LOG] ' + Array.prototype.slice.call(arguments).join(' '));
            updateLogDisplay();
            originalLog.apply(console, arguments);
        };
        
        console.error = function() {
            logs.push('[ERROR] ' + Array.prototype.slice.call(arguments).join(' '));
            updateLogDisplay();
            originalError.apply(console, arguments);
        };
        
        function updateLogDisplay() {
            document.getElementById('log-content').textContent = logs.slice(-20).join('\n');
        }
        
        function clearLogs() {
            logs = [];
            updateLogDisplay();
        }
        
        function showResult(elementId, content, type = 'info') {
            var element = document.getElementById(elementId);
            element.innerHTML = content;
            element.className = 'result ' + type;
            element.style.display = 'block';
        }
        
        function checkDatabaseInfo() {
            console.log('Checking database info...');
            showResult('db-info', '⏳ Đang kiểm tra database...', 'warning');
            
            // Simulate database check (in real scenario, this would be an API call)
            setTimeout(function() {
                var info = `
                    <strong>Database Schema Check:</strong><br>
                    ✅ Bảng HOC_KY tồn tại<br>
                    ❓ Cột NGAY_BAT_DAU_TUAN_DAY: Cần kiểm tra<br>
                    ❓ Cột NGAY_KET_THUC_TUAN_DAY: Cần kiểm tra<br>
                    <br>
                    <em>Chạy script check_database_schema.sql để kiểm tra chi tiết</em>
                `;
                showResult('db-info', info, 'warning');
            }, 1000);
        }
        
        function testNienKhoa() {
            console.log('Testing Nien Khoa API...');
            showResult('nien-khoa-result', '⏳ Đang test API niên khóa...', 'warning');
            
            $.ajax({
                url: '/lich-giang-day/lay-nien-khoa',
                type: 'GET',
                success: function(response) {
                    console.log('Nien Khoa Response:', response);
                    
                    var html = '<strong>✅ API Success</strong><br>';
                    html += 'Response Code: ' + (response.resCode || response.status) + '<br>';
                    html += 'Message: ' + (response.resMsg || response.message || 'N/A') + '<br>';
                    html += 'Data Count: ' + (response.resData ? response.resData.length : (response.data ? response.data.length : 0)) + '<br>';
                    
                    if (response.resData && response.resData.length > 0) {
                        html += '<strong>Sample Data:</strong><br>';
                        html += '<pre>' + JSON.stringify(response.resData[0], null, 2) + '</pre>';
                    } else if (response.data && response.data.length > 0) {
                        html += '<strong>Sample Data:</strong><br>';
                        html += '<pre>' + JSON.stringify(response.data[0], null, 2) + '</pre>';
                    }
                    
                    showResult('nien-khoa-result', html, 'success');
                },
                error: function(xhr, status, error) {
                    console.error('Nien Khoa Error:', xhr, status, error);
                    var html = '<strong>❌ API Error</strong><br>';
                    html += 'Status: ' + status + '<br>';
                    html += 'Error: ' + error + '<br>';
                    html += 'Response: ' + xhr.responseText;
                    showResult('nien-khoa-result', html, 'error');
                }
            });
        }
        
        function testHocKy() {
            var idNienKhoa = document.getElementById('test-id-nien-khoa').value;
            console.log('Testing Hoc Ky API with idNienKhoa:', idNienKhoa);
            showResult('hoc-ky-result', '⏳ Đang test API học kỳ...', 'warning');
            
            $.ajax({
                url: '/lich-giang-day/lay-hoc-ky-theo-nien-khoa',
                type: 'GET',
                data: { idNienKhoa: idNienKhoa },
                success: function(response) {
                    console.log('Hoc Ky Response:', response);
                    
                    var html = '<strong>✅ API Success</strong><br>';
                    html += 'Response Code: ' + (response.resCode || response.status) + '<br>';
                    html += 'Data Count: ' + (response.resData ? response.resData.length : (response.data ? response.data.length : 0)) + '<br>';
                    
                    if (response.resData && response.resData.length > 0) {
                        html += '<strong>Sample Data:</strong><br>';
                        html += '<pre>' + JSON.stringify(response.resData[0], null, 2) + '</pre>';
                    }
                    
                    showResult('hoc-ky-result', html, 'success');
                },
                error: function(xhr, status, error) {
                    console.error('Hoc Ky Error:', xhr, status, error);
                    var html = '<strong>❌ API Error</strong><br>';
                    html += 'Status: ' + status + '<br>';
                    html += 'Error: ' + error + '<br>';
                    html += 'Response: ' + xhr.responseText;
                    showResult('hoc-ky-result', html, 'error');
                }
            });
        }
        
        function testThietDat() {
            var idHocKy = document.getElementById('test-id-hoc-ky').value;
            var ngayBatDau = document.getElementById('test-ngay-bat-dau').value;
            
            console.log('Testing Thiet Dat API with:', { idHocKy, ngayBatDau });
            showResult('thiet-dat-result', '⏳ Đang test API thiết đặt...', 'warning');
            
            $.ajax({
                url: '/lich-giang-day/thiet-dat-tuan-day',
                type: 'POST',
                data: {
                    idHocKy: idHocKy,
                    ngayBatDauTuanDay: ngayBatDau
                },
                success: function(response) {
                    console.log('Thiet Dat Response:', response);
                    
                    var html = '<strong>✅ API Success</strong><br>';
                    html += 'Response Code: ' + (response.resCode || response.status) + '<br>';
                    html += 'Message: ' + (response.resMsg || response.message || 'N/A') + '<br>';
                    html += '<strong>Response Data:</strong><br>';
                    html += '<pre>' + JSON.stringify(response.resData || response.data, null, 2) + '</pre>';
                    
                    showResult('thiet-dat-result', html, 'success');
                },
                error: function(xhr, status, error) {
                    console.error('Thiet Dat Error:', xhr, status, error);
                    var html = '<strong>❌ API Error</strong><br>';
                    html += 'Status: ' + status + '<br>';
                    html += 'Error: ' + error + '<br>';
                    html += 'Response: ' + xhr.responseText;
                    showResult('thiet-dat-result', html, 'error');
                }
            });
        }
        
        function testComponents() {
            console.log('Testing UI Components...');
            var html = '<strong>🔧 Component Check:</strong><br>';
            
            // Check jQuery
            if (typeof $ !== 'undefined') {
                html += '✅ jQuery loaded<br>';
            } else {
                html += '❌ jQuery not loaded<br>';
            }
            
            // Check Select2
            if (typeof $.fn.select2 !== 'undefined') {
                html += '✅ Select2 available<br>';
            } else {
                html += '❌ Select2 not available<br>';
            }
            
            // Check DatePicker
            if (typeof $.fn.datepicker !== 'undefined') {
                html += '✅ DatePicker available<br>';
            } else {
                html += '❌ DatePicker not available<br>';
            }
            
            showResult('components-result', html, 'info');
        }
        
        // Auto-run basic checks on page load
        $(document).ready(function() {
            console.log('Debug page loaded');
            testComponents();
        });
    </script>
</body>
</html>
