# Maven
target/
!.mvn/wrapper/maven-wrapper.jar
.mvn/wrapper/maven-wrapper.properties
**/mvnw
**/mvnw.cmd

# Gradle (uncomment if you use Gradle instead of <PERSON>ven)
# build/
# .gradle/
# **/gradlew
# **/gradlew.bat
# gradle/wrapper/gradle-wrapper.jar
# gradle/wrapper/gradle-wrapper.properties

# Logs
*.log

# OS-specific files
.DS_Store
Thumbs.db

# IntelliJ IDEA
.idea/
*.iml
out/

# Eclipse
.project
.classpath
.settings/
bin/

# VS Code
.vscode/

# Spring Boot specific
# (e.g. local configuration files, generated metadata)
*.pid
*.seed
*.pid.lock

# Temporary files
*.tmp
*.bak
*.swp
