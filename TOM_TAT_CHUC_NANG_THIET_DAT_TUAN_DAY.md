# Tóm tắt chức năng Thiết đặt tuần dạy

## <PERSON><PERSON> tả chức năng
Đã bổ sung chức năng thiết đặt tuần dạy của học kỳ, cho phép:
- Thiết đặt ngày bắt đầu tuần 1 cho từng học kỳ
- Tự động tính toán và hiển thị tuần học dựa trên ngày giảng
- Hiể<PERSON> thị cột "Tuần" trong danh sách lịch giảng dạy
- Công cụ tính tuần học cho ngày bất kỳ

## Các file đã được tạo/sửa đổi

### 1. Backend (Java)

#### Model
- **HocKy.java**: Thêm các trường và method:
  - `ngayBatDauTuanDay`: Ng<PERSON>y bắt đầu tuần dạy đầu tiên
  - `ngayKetThucTuanDay`: <PERSON><PERSON><PERSON> kết thúc tuần dạy cuối cùng
  - `tinh<PERSON><PERSON><PERSON>(Date)`: <PERSON><PERSON><PERSON> tuần học từ ngày cho trước
  - `layNgayBatDauTuan(Integer)`: Lấy ngày bắt đầu của tuần cụ thể
  - `layNgayKetThucTuan(Integer)`: Lấy ngày kết thúc của tuần cụ thể

- **LichGiangDay.java**: Thêm method:
  - `getTuanGiangText()`: Hiển thị tuần giảng dạng "T1", "T2"...

#### Controller
- **LichGiangDayController.java**: Thêm các endpoint:
  - `GET /thiet-dat-tuan-day`: Trang thiết đặt tuần dạy
  - `POST /thiet-dat-tuan-day`: API thiết đặt tuần dạy
  - `GET /lay-thong-tin-tuan-day`: API lấy thông tin tuần dạy
  - `GET /tinh-tuan-hoc`: API tính tuần học từ ngày

#### Service
- **LichGiangDayServiceImp.java**: Cập nhật method `luu()`:
  - Tự động tính tuần giảng khi lưu lịch giảng dạy

### 2. Frontend

#### HTML Templates
- **thiet-dat-tuan-day.html**: Trang giao diện thiết đặt tuần dạy
  - Form chọn niên khóa, học kỳ, ngày bắt đầu tuần 1
  - Bảng hiển thị danh sách tuần với trạng thái
  - Công cụ tính tuần học

- **lich-giang-day.html**: Cập nhật:
  - Thêm nút "Thiết đặt tuần dạy" vào toolbar

#### JavaScript
- **thiet-dat-tuan-day.js**: Logic xử lý trang thiết đặt:
  - Load dữ liệu niên khóa, học kỳ
  - Thiết đặt tuần dạy
  - Hiển thị thông tin tuần dạy
  - Tính tuần học

- **lich-giang-day.js**: Cập nhật:
  - Thêm cột "Tuần" vào grid
  - Xử lý dữ liệu để hiển thị tuần học
  - Các hàm utility tính tuần

### 3. Database
- **database_update_tuan_day.sql**: Script cập nhật database:
  - Thêm cột `NGAY_BAT_DAU_TUAN_DAY` vào bảng `HOC_KY`
  - Thêm cột `NGAY_KET_THUC_TUAN_DAY` vào bảng `HOC_KY`
  - Tạo index tối ưu hóa

### 4. Documentation
- **HUONG_DAN_THIET_DAT_TUAN_DAY.md**: Hướng dẫn sử dụng chi tiết

## Luồng hoạt động

### 1. Thiết đặt tuần dạy
1. Admin truy cập `/lich-giang-day/thiet-dat-tuan-day`
2. Chọn niên khóa và học kỳ
3. Chọn ngày bắt đầu tuần 1 (thường là thứ 2)
4. Hệ thống tự động tính ngày kết thúc dựa trên số tuần
5. Lưu thiết đặt vào database

### 2. Hiển thị tuần học
1. Khi tạo/sửa lịch giảng dạy, hệ thống tự động tính tuần
2. Tuần được tính dựa trên: `(ngayGiang - ngayBatDauTuanDay) / 7 + 1`
3. Hiển thị trong cột "Tuần" của danh sách lịch giảng

### 3. Công cụ tính tuần
1. Nhập ngày bất kỳ
2. Hệ thống tính tuần học tương ứng
3. Hiển thị khoảng thời gian của tuần đó

## Ưu điểm của giải pháp

### 1. Tính chính xác
- Tuần học được tính dựa trên thiết đặt cụ thể của từng học kỳ
- Không phụ thuộc vào tuần trong năm (WEEK_OF_YEAR)

### 2. Linh hoạt
- Mỗi học kỳ có thể có thiết đặt tuần dạy riêng
- Có thể thay đổi thiết đặt trong quá trình học kỳ

### 3. Tự động hóa
- Tự động tính tuần khi tạo lịch giảng dạy
- Tự động hiển thị trong danh sách

### 4. Trực quan
- Hiển thị trạng thái tuần (đã qua/hiện tại/sắp tới)
- Giao diện thân thiện, dễ sử dụng

## Cách triển khai

### 1. Cập nhật database
```sql
-- Chạy script database_update_tuan_day.sql
```

### 2. Deploy code
- Deploy các file Java đã cập nhật
- Deploy các file frontend mới

### 3. Thiết đặt ban đầu
- Truy cập trang thiết đặt tuần dạy
- Cấu hình cho các học kỳ hiện tại

## Kiểm thử

### 1. Test cases cơ bản
- Thiết đặt tuần dạy cho học kỳ mới
- Tính tuần học cho các ngày khác nhau
- Hiển thị tuần trong danh sách lịch giảng dạy

### 2. Test cases nâng cao
- Thay đổi thiết đặt tuần dạy
- Xử lý trường hợp ngày ngoài khoảng học kỳ
- Kiểm tra hiệu suất với dữ liệu lớn

## Lưu ý khi sử dụng

### 1. Quyền truy cập
- Chỉ admin mới có quyền thiết đặt tuần dạy
- Giảng viên có thể xem thông tin tuần dạy

### 2. Thời điểm thiết đặt
- Nên thiết đặt trước khi bắt đầu học kỳ
- Có thể điều chỉnh trong quá trình học kỳ nếu cần

### 3. Backup dữ liệu
- Backup database trước khi chạy script cập nhật
- Kiểm tra kỹ trước khi deploy production

## Hỗ trợ và bảo trì
- Tài liệu hướng dẫn chi tiết đã được tạo
- Code được comment đầy đủ
- Có thể mở rộng thêm tính năng trong tương lai
