-- <PERSON><PERSON><PERSON> cập nhật database để thêm chức năng thiết đặt tuần dạy
-- Ng<PERSON>y tạo: 2025-01-21

-- 1. Th<PERSON><PERSON> cột thiết đặt tuần dạy vào bảng HOC_KY
ALTER TABLE HOC_KY 
ADD COLUMN NGAY_BAT_DAU_TUAN_DAY DATE COMMENT 'Ngày bắt đầu tuần dạy đầu tiên (tuần 1)';

ALTER TABLE HOC_KY 
ADD COLUMN NGAY_KET_THUC_TUAN_DAY DATE COMMENT 'Ng<PERSON><PERSON> kết thúc tuần dạy cuối cùng';

-- 2. C<PERSON><PERSON> nhật dữ liệu mẫu cho các học kỳ hiện có (tùy chọn)
-- Ví dụ: Thiết đặt tuần dạy cho học kỳ hiện tại
-- UPDATE HOC_KY 
-- SET NGAY_BAT_DAU_TUAN_DAY = '2024-09-02'  -- Thứ 2 đầu tiên của học kỳ
-- WHERE HIEN_TAI = 1 AND NGAY_BAT_DAU_TUAN_DAY IS NULL;

-- 3. Tạo index để tối ưu hóa truy vấn
CREATE INDEX idx_hoc_ky_tuan_day ON HOC_KY(NGAY_BAT_DAU_TUAN_DAY, NGAY_KET_THUC_TUAN_DAY);

-- 4. Thêm ràng buộc để đảm bảo tính hợp lệ của dữ liệu
-- ALTER TABLE HOC_KY 
-- ADD CONSTRAINT chk_tuan_day_valid 
-- CHECK (NGAY_KET_THUC_TUAN_DAY IS NULL OR NGAY_BAT_DAU_TUAN_DAY IS NULL OR NGAY_KET_THUC_TUAN_DAY >= NGAY_BAT_DAU_TUAN_DAY);

-- 5. Cập nhật comment cho bảng
ALTER TABLE HOC_KY COMMENT = 'Bảng quản lý học kỳ với thiết đặt tuần dạy';

-- 6. Hiển thị cấu trúc bảng sau khi cập nhật
DESCRIBE HOC_KY;

-- 7. Kiểm tra dữ liệu
SELECT 
    ID_HOC_KY,
    TEN_HOC_KY,
    NGAY_BAT_DAU,
    NGAY_KET_THUC,
    SO_TUAN,
    NGAY_BAT_DAU_TUAN_DAY,
    NGAY_KET_THUC_TUAN_DAY,
    HIEN_TAI
FROM HOC_KY 
ORDER BY ID_HOC_KY DESC 
LIMIT 5;

-- Hướng dẫn sử dụng:
-- 1. Chạy script này để cập nhật cấu trúc database
-- 2. Sử dụng giao diện "Thiết đặt tuần dạy" để cấu hình cho từng học kỳ
-- 3. Hệ thống sẽ tự động tính tuần học dựa trên ngày bắt đầu tuần dạy

-- Lưu ý:
-- - NGAY_BAT_DAU_TUAN_DAY: Ngày bắt đầu của tuần 1 (thường là thứ 2)
-- - NGAY_KET_THUC_TUAN_DAY: Ngày kết thúc của tuần cuối cùng (tự động tính)
-- - Tuần học được tính từ 1 đến SO_TUAN
-- - Nếu chưa thiết đặt, hệ thống sẽ không hiển thị tuần học
