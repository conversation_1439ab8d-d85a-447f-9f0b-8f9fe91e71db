-- Script kiểm tra schema database cho chức năng thiết đặt tuần dạy
-- Chạy script này để kiểm tra xem các cột đã được thêm vào chưa

-- 1. <PERSON><PERSON><PERSON> tra cấu trúc bảng HOC_KY
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'HOC_KY'
ORDER BY ORDINAL_POSITION;

-- 2. <PERSON>ểm tra xem các cột mới đã tồn tại chưa
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'CỘT NGAY_BAT_DAU_TUAN_DAY ĐÃ TỒN TẠI'
        ELSE 'CỘT NGAY_BAT_DAU_TUAN_DAY CHƯA TỒN TẠI - CẦN CHẠY SCRIPT CẬP NHẬT'
    END as status_ngay_bat_dau
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'HOC_KY' 
AND COLUMN_NAME = 'NGAY_BAT_DAU_TUAN_DAY';

SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'CỘT NGAY_KET_THUC_TUAN_DAY ĐÃ TỒN TẠI'
        ELSE 'CỘT NGAY_KET_THUC_TUAN_DAY CHƯA TỒN TẠI - CẦN CHẠY SCRIPT CẬP NHẬT'
    END as status_ngay_ket_thuc
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'HOC_KY' 
AND COLUMN_NAME = 'NGAY_KET_THUC_TUAN_DAY';

-- 3. Kiểm tra dữ liệu mẫu
SELECT 
    ID_HOC_KY,
    TEN_HOC_KY,
    NGAY_BAT_DAU,
    NGAY_KET_THUC,
    SO_TUAN,
    NGAY_BAT_DAU_TUAN_DAY,
    NGAY_KET_THUC_TUAN_DAY,
    HIEN_TAI
FROM HOC_KY 
ORDER BY ID_HOC_KY DESC 
LIMIT 5;

-- 4. Đếm số học kỳ đã thiết đặt tuần dạy
SELECT 
    COUNT(*) as total_hoc_ky,
    SUM(CASE WHEN NGAY_BAT_DAU_TUAN_DAY IS NOT NULL THEN 1 ELSE 0 END) as da_thiet_dat,
    SUM(CASE WHEN NGAY_BAT_DAU_TUAN_DAY IS NULL THEN 1 ELSE 0 END) as chua_thiet_dat
FROM HOC_KY;

-- 5. Hiển thị thông tin chi tiết các học kỳ
SELECT 
    nk.TEN_NIEN_KHOA,
    hk.TEN_HOC_KY,
    hk.NGAY_BAT_DAU as ngay_bat_dau_hoc_ky,
    hk.NGAY_BAT_DAU_TUAN_DAY as ngay_bat_dau_tuan_1,
    hk.SO_TUAN,
    CASE 
        WHEN hk.NGAY_BAT_DAU_TUAN_DAY IS NOT NULL THEN 'Đã thiết đặt'
        ELSE 'Chưa thiết đặt'
    END as trang_thai_thiet_dat
FROM HOC_KY hk
JOIN NIEN_KHOA nk ON hk.ID_NIEN_KHOA = nk.ID_NIEN_KHOA
ORDER BY nk.NAM DESC, hk.TEN_HOC_KY;
